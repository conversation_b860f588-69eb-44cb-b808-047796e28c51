using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Keyless]
[Table("geometries")]
public partial class Geometry
{
    [Column("name", TypeName = "character varying")]
    public string? Name { get; set; }

    [Column("geom")]
    public Point? Geom { get; set; }
}
