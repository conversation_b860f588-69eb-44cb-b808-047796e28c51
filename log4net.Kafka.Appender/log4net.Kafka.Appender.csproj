<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<PackageTags>Kafka;log4net-kafka-appender;librdkafka;kafka-appender;log4net;appender;partition</PackageTags>
		<Title>Log4net Kafka Appender</Title>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<Version>1.4.3</Version>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="Confluent.Kafka" Version="2.10.1" />
		<PackageReference Include="log4net" Version="3.1.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
	</ItemGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<GenerateSerializationAssemblies>Auto</GenerateSerializationAssemblies>
		<DocumentationFile>bin\Debug\netstandard2.0\log4net.Kafka.Core.xml</DocumentationFile>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<DocumentationFile>bin\Release\netstandard2.0\log4net.Kafka.Core.xml</DocumentationFile>
	</PropertyGroup>
</Project>