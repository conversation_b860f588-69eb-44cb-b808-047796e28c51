using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("es_queue")]
public partial class EsQueue
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("es_jobid", TypeName = "character varying")]
    public string? EsJobid { get; set; }

    [Column("create_date", TypeName = "timestamp without time zone")]
    public DateTime CreateDate { get; set; }

    [Column("update_date", TypeName = "timestamp without time zone")]
    public DateTime UpdateDate { get; set; }

    [Column("status")]
    public int Status { get; set; }

    [Column("description", TypeName = "character varying")]
    public string? Description { get; set; }

    [Column("hangfire_job_id", TypeName = "character varying")]
    public string? HangfireJobId { get; set; }

    [Column("places_queue_id")]
    public long PlacesQueueId { get; set; }

    [Column("scan_config_range")]
    public int ScanConfigRange { get; set; }

    [Column("scan_config_distance")]
    public int? ScanConfigDistance { get; set; }

    [Column("order_num")]
    public int OrderNum { get; set; }
}
