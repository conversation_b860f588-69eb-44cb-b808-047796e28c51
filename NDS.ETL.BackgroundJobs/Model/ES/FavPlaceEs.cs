using Elastic.Clients.Elasticsearch.Mapping;
using KnowledgeBase.Core.Extensions;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Infrastructure.Extensions;
namespace NDS.ETL.BackgroundJobs.Model.ElasticSearch;

public class FavPlaceEs : IElasticSearchModel, IPlaceEsConvertible
{
    public int Id { get; set; }
    public bool Active { get; set; }
    public string? Address { get; set; }
    public long BookingCount { get; set; }
    public string? City { get; set; }
    public string? DataFrom { get; set; }
    public string? District { get; set; }
    public string? FullAddress { get; set; }
    public string? Keywords { get; set; }
    public Location? Location { get; set; }
    public string? Name { get; set; }
    public string? ParentAddress { get; set; }
    public string? ParentId { get; set; }
    public string? PhoneNumber { get; set; }
    public string? PlaceDetailId { get; set; }
    public string? PlaceId { get; set; }
    public bool Removed { get; set; }
    public string? Route { get; set; }
    public string? StreetNumber { get; set; }
    public string? TagInput { get; set; }
    public int Type { get; set; }

    public string? Ward { get; set; }
    public string? UserId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }


    public string MasterAddress
    {
        get
        {
            string str = FullAddress;
            if (string.IsNullOrWhiteSpace(str))
            {
                str = Address ?? "";
            }
            else if (!string.IsNullOrWhiteSpace(Address) && !str.ToUnsign().Contains(Address.ToUnsign()))
            {
                str = Address + ", " + str;
            }

            if (!string.IsNullOrWhiteSpace(Name) && !str.ToUnsign().Contains(Name.ToUnsign()))
            {
                str = Name + ", " + str;
            }

            ProcessKeywords();
            var keywordProcesed = !string.IsNullOrWhiteSpace(Keywords) ? $"{str}, {Keywords}" : str;
            AddressPermuted = AddressPermutator.GenerateOptimizedPermutations(keywordProcesed, 15);

            var insertItems = VietnameseAddressPermutator.GenerateOptimizedPermutations(keywordProcesed, 3);
            AddressPermuted.InsertRange(0, insertItems);
            return str;
        }
    }

    public void ProcessKeywords()
    {
        string? keywords = Keywords;
        string fullAddress = FullAddress;
        if (string.IsNullOrEmpty(keywords)) return;

        var keywordList = keywords.Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(k => k.Trim())
            .Where(k => !string.IsNullOrEmpty(k))
            .ToList();

        // Loại bỏ các từ khóa trùng lặp không dấu
        var uniqueKeywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
        var result = new List<string>();

        foreach (var keyword in keywordList)
        {
            var unsignedKeyword = keyword.ToUnsign().ToLower();
            if (!uniqueKeywords.Contains(unsignedKeyword) &&
                !string.IsNullOrEmpty(fullAddress) &&
                !fullAddress.ToUnsign().ToLower().Contains(unsignedKeyword))
            {
                uniqueKeywords.Add(unsignedKeyword);
                result.Add(keyword);
            }
        }
        Keywords = result.Any() ? string.Join(",", result) : null;
    }

    public List<string> AddressPermuted { get; set; }


    public static void MappingProperties(PropertiesDescriptor<FavPlaceEs> d)
    {
        d.IntegerNumber(t => t.Id)

            .Boolean(t => t.Active)

            .LongNumber(l => l.BookingCount)

            .Keyword(t => t.City, t => t.Index(false).Store(false))

            .Keyword(t => t.DataFrom, t => t.Index(false).Store(false))

            .Text(t => t.District, t => t.Index(false).Store(false))

            .GeoPoint(g => g.Location)

            .Keyword(k => k.ParentId)

            .Text(t => t.UserId,
                t =>
                    t.Analyzer("icu")
                        .Fields(ff => ff
                            .Keyword("keyword",
                                st => st.IgnoreAbove(256))
                        ))

            .Text(t => t.PhoneNumber,
                t =>
                    t.Analyzer("icu")
                        .Fields(ff => ff
                            .Keyword("keyword",
                                st => st.IgnoreAbove(256))
                        ))

            .Text(t => t.Name,
                t =>
                    t.Analyzer("icu")
                        .Fields(ff => ff
                            .Keyword("keyword",
                                st => st.IgnoreAbove(256))
                        ))

            .Text(t => t.ParentAddress,
                t =>
                    t.Analyzer("icu")
                        .Fields(ff => ff
                            .Keyword("keyword",
                                st => st.IgnoreAbove(256))
                        ))

            .Text(t => t.Address,
                t =>
                    t.Analyzer("icu")
                        .Fields(ff => ff
                            .Keyword("keyword",
                                st => st.IgnoreAbove(256))
                        ))

            .Text(t => t.FullAddress,
                t =>
                    t.Analyzer("icu")
                        .Fields(ff => ff
                            .Keyword("keyword",
                                st => st.IgnoreAbove(256))
                        ))

            .Keyword(k => k.PlaceDetailId, t => t.Index(false).Store(false))

            .Keyword(k => k.PlaceId)

            .Keyword(k => k.Removed, t => t.Index(false).Store(false))

            .Keyword(k => k.Route, t => t.Index(false).Store(false))

            .Keyword(k => k.StreetNumber, t => t.Index(false).Store(false))

            .Keyword(k => k.TagInput, t => t.Index(false).Store(false))

            .IntegerNumber(i => i.Type)

            .Keyword(k => k.Ward, t => t.Index(false).Store(false))

            .Date(d => d.CreatedAt)

            .Date(d => d.UpdatedAt);


    }

    public PlacesEs ToPlacesEs()
    {
        return new PlacesEs
        {
            Id = Id,
            PlaceId = PlaceId,
            PlaceDetailId = PlaceDetailId,
            Name = Name,
            Address = Address,
            FullAddress = FullAddress,
            StreetNumber = StreetNumber,
            Route = Route,
            Ward = Ward,
            District = District,
            City = City,
            Type = Type,
            Active = Active,
            Keywords = Keywords,
            Location = Location,
            BookingCount = (int)BookingCount,
            Removed = Removed,
            Hidden = false, // Default value
            Popular = false, // Default value
            Processed = false, // Default value
            Tags = null, // Default value
            TagInput = TagInput,
            CreatedAt = CreatedAt,
            UpdatedAt = UpdatedAt,
            DataFrom = DataFrom
        };
    }
}