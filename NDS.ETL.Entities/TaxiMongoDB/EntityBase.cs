using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using NDS.ETL.Entities.TaxiMongoDB.MongoAttributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NDS.ETL.Entities.TaxiMongoDB;
public class FSize
{
    public static string Small = "small", Medium = "medium", Large = "large";
}

[BsonIgnoreExtraElements]
public class MongoEntity
{
    public MongoEntity()
    {
        Active = true;
    }

    public MongoEntity(MongoEntity item)
    {
        Id = item.Id;
        Active = item.Active;
        Order = item.Order;
        Priority = item.Priority;
        CreatedAt = item.CreatedAt;
        UpdatedAt = item.UpdatedAt;
        CreatorId = item.CreatorId;
    }

    [BsonElement("id")] [Key] public ObjectId Id { get; set; }

    [BsonElement("active")]
    [Display(Name = "Trạng thái")]
    public bool Active { get; set; }

    [BsonElement("order")]
    [Display(Name = "Thứ tự")]
    public int Order { get; set; }

    [BsonElement("priority")]
    [Display(Name = "Ưu tiên")]
    public string Priority { get; set; }

    [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
    [CreatedDate]
    [BsonElement("createdAt")]
    [Display(Name = "Ngày tạo", Order = 99)]
    public DateTime CreatedAt { get; set; }

    [UpdatedDate]
    [BsonElement("updatedAt")]
    [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
    [Display(Name = "Ngày cập nhật", Order = 100)]
    public DateTime UpdatedAt { get; set; }

    [BsonElement("creatorId")]
    [Display(Name = "Người tạo")]
    [MaxLength(450)]
    public string CreatorId { get; set; }

    public virtual string FormSize => "medium";
}

[BsonIgnoreExtraElements]
public class EntityBase
{
    public EntityBase()
    {
        Active = true;
    }

    [Key] public string Id { get; set; }


    [Display(Name = "Trạng thái")] public bool Active { get; set; }

    [Display(Name = "Thứ tự")] public int Order { get; set; }

    [Display(Name = "Ngày tạo", Order = 99)]
    public DateTime CreatedAt { get; set; }

    [Display(Name = "Ngày cập nhật", Order = 100)]
    public DateTime UpdatedAt { get; set; }

    [Display(Name = "Người tạo")]
    [MaxLength(450)]
    public string CreatorId { get; set; }

    //[ForeignKey(nameof(CreatorId))] 
    //public User Creator { get; set; }

    public virtual string FormSize => FSize.Medium;
}