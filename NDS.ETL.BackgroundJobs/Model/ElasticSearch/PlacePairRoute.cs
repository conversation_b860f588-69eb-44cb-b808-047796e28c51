using NDS.ETL.Infrastructure.Extensions;
using Elastic.Clients.Elasticsearch;

namespace NDS.ETL.BackgroundJobs.Model.ElasticSearch
{
    /// <summary>
    /// Đối tượng đại diện cho một cặp địa điểm đi và đến đầy đủ
    /// </summary>
    public class PlacePairRoute
    {
        /// <summary>
        /// ID của cặp địa điểm trong hệ thống
        /// </summary>
        public long BookingPlacePairId { get; set; }

        /// <summary>
        /// ID của người dùng
        /// </summary>
        public string? CustomerId { get; set; }

        /// <summary>
        /// Số điện thoại của người dùng
        /// </summary>
        public string? CustomerPhone { get; set; }

        /// <summary>
        /// Địa điểm đón
        /// </summary>
        public PlacePairPoint Pickup { get; set; }

        /// <summary>
        /// Địa điểm đến
        /// </summary>
        public PlacePairPoint Arrival { get; set; }

        /// <summary>
        /// Số lần đặt xe trên tuyến đường này
        /// </summary>
        public int BookingCount { get; set; }

        /// <summary>
        /// Thời gian đặt xe gần nhất
        /// </summary>
        public DateTime? LastBookingAt { get; set; }

        /// <summary>
        /// Thời gian tìm kiếm gần nhất
        /// </summary>
        public DateTime? LastSearchAt { get; set; }

        /// <summary>
        /// Số tiền ước tính gần nhất
        /// </summary>
        public string? LastEstimateAmount { get; set; }

        /// <summary>
        /// Ghi chú gần nhất
        /// </summary>
        public string? LastNote { get; set; }

        /// <summary>
        /// Khoảng cách từ vị trí hiện tại đến điểm đón (mét)
        /// </summary>
        public double? DistanceToPickup { get; set; }

        /// <summary>
        /// Khởi tạo từ cặp PlacePairEs
        /// </summary>
        public static PlacePairRoute FromPlacePairTuple(long bookingPlacePairId, PlacePairEs? pickup, PlacePairEs? arrival)
        {
            // Lấy thông tin booking từ đối tượng nào không null (ưu tiên pickup)
            var source = pickup ?? arrival;
            if (source == null)
            {
                return new PlacePairRoute { BookingPlacePairId = bookingPlacePairId };
            }

            // Tạo đối tượng PlacePairRoute từ thông tin có sẵn
            var result = new PlacePairRoute
            {
                BookingPlacePairId = bookingPlacePairId,
                CustomerId = source.CustomerId,
                CustomerPhone = source.CustomerPhone,
                BookingCount = source.BookingCount,
                LastBookingAt = source.LastBookingAt,
                LastSearchAt = source.LastSearchAt,
                LastEstimateAmount = source.LastEstimateAmount,
                LastNote = source.LastNote
            };

            // Thêm thông tin địa điểm đón nếu có
            if (pickup != null)
            {
                result.Pickup = new PlacePairPoint
                {
                    PlaceId = pickup.PlaceId,
                    FullAddress = pickup.FullAddress,
                    Location = pickup.Location,
                };
            }

            // Thêm thông tin địa điểm đến nếu có
            if (arrival != null)
            {
                result.Arrival = new PlacePairPoint
                {
                    PlaceId = arrival.PlaceId,
                    FullAddress = arrival.FullAddress,
                    Location = arrival.Location,
                };
            }

            return result;
        }
    }
} 