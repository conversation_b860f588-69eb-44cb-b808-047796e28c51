using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Core.Search;
using NDS.ETL.BackgroundJobs.Extensions;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Model.ES.Dto;
using NDS.ETL.Infrastructure.Extensions;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text.Json;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using RegexOptions = System.Text.RegularExpressions.RegexOptions;

// Added for Stopwatch

namespace NDS.ETL.BackgroundJobs.Services.ES
{
    /// <summary>
    /// Service thực hiện autocomplete địa điểm sử dụng Elasticsearch
    /// </summary>
    public class ESAutocompleteService : IESAutocompleteService
    {
        private readonly ElasticsearchClient _elasticClient;
        private readonly string _placeIdxName;
        private readonly string _placePairIdxName;
        private readonly string _placeFavIdxName;
        private readonly IESPlaceDetailsService _detailsService;
        private readonly ILogger<ESAutocompleteService> _logger;

        /// <summary>
        /// Khởi tạo service với DI
        /// </summary>
        public ESAutocompleteService(
            IESClientFactory iesClientFactory,
            IESPlaceDetailsService detailsService,
            ILogger<ESAutocompleteService> logger)
        {
            _detailsService = detailsService;
            _logger = logger;

            // Lấy client và thông tin index từ factory
            _elasticClient = iesClientFactory.GetClient();
            _placeIdxName = iesClientFactory.GetPlaceIndexName();
            _placePairIdxName = iesClientFactory.GetPlacePairIndexName();
            _placeFavIdxName = iesClientFactory.GetPlaceFavoriteIndexName();
        }

        // Loại bỏ các tiền tố như "số", "so", "no", "." nếu có
        private static readonly Regex prefixRegex = new(@"^(số|so|no|\.)\s+(?=\d+)", RegexOptions.IgnoreCase);
        /// <summary>
        /// Chuẩn hóa từ khóa để thêm dấu cách giữa số và chữ, cũng như giữa các từ bị viết liền nhau có chữ hoa
        /// Ví dụ: "22Láng" -> "22 Láng", "123ĐườngLáng" -> "123 Đường Láng", "ĐườngLáng" -> "Đường Láng"
        /// </summary>
        /// <param name="keyword">Từ khóa cần chuẩn hóa</param>
        /// <returns>Từ khóa đã được chuẩn hóa</returns>
        private static string NormalizeKeywordWithDigits(string keyword)
        {
            if (string.IsNullOrWhiteSpace(keyword))
                return keyword;

            var result = keyword;

            var keywordWithoutPrefix = prefixRegex.Replace(result, "");

            // Bước 2: Thêm dấu cách trước bất kỳ chữ cái nào (không chỉ 'L') nếu nó được đứng trước bởi một chữ cái khác.
            // Ví dụ: "ĐườngLáng" -> "Đường Láng", "MyWord" -> "My Word", "ABC" -> "A B C".
            var letterFollowedByUppercasePattern = new Regex(@"(?<=\p{L})(?=\p{Lu})");
            result = letterFollowedByUppercasePattern.Replace(keywordWithoutPrefix, " ");

            return result;
        }

        /// <summary>
        /// Tìm kiếm autocomplete địa điểm dựa trên query và vị trí
        /// </summary>
        public async Task<List<PlacesEs>> SearchAutocompleteAsync(string userId,
            string customerPhone,
            string query,
            double latitude,
            double longitude,
            int? h3Resolution,
            int limit = 10,
            bool extendedRadius = false)
        {
            var stopwatch = Stopwatch.StartNew(); // Đo thời gian xử lý
            var priorityResultDict = new ConcurrentDictionary<string, PlacesEs>();
            var hitsResultDict = new ConcurrentDictionary<long, PlacesEs>();
            var suggestionsResultDict = new ConcurrentDictionary<long, PlacesEs>();
            var extendedSearchResults = new ConcurrentDictionary<long, bool>();

            // Chuẩn hóa từ khóa để thêm dấu cách giữa số và chữ nếu cần thiết
            var keyword = NormalizeKeywordWithDigits(query);
            try
            {

                var normalizedQuery = VietnameseAddressPermutator.NormalizeVietnameseAddress(keyword);
                var currentLocation = GeoLocation.LatitudeLongitude(
                    new LatLonGeoLocation { Lat = latitude, Lon = longitude });

                // Tạo các task tìm kiếm song song
                var searchTasks = CreateSearchTasks(
                    userId, customerPhone, normalizedQuery, currentLocation, h3Resolution, limit, extendedRadius,
                    hitsResultDict, suggestionsResultDict, extendedSearchResults, priorityResultDict);

                await Task.WhenAll(searchTasks);

                // Nếu chưa đủ kết quả thì mở rộng bán kính tìm kiếm
                if (h3Resolution == null)
                    await ExtendSearchIfNecessary(hitsResultDict, suggestionsResultDict, normalizedQuery, currentLocation, null, limit, extendedSearchResults, extendedRadius);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi tìm kiếm autocomplete với query: {Query}", query);
            }

            // Tổng hợp và sắp xếp kết quả trả về
            var sortedResults = await AggregateAndSortResults(
                priorityResultDict, hitsResultDict, suggestionsResultDict, extendedSearchResults, limit, extendedRadius, keyword);

            stopwatch.Stop();
            _logger.LogInformation(
                "Trả về {ResultCount} địa điểm cho từ khóa '{Query}'. Thời gian xử lý: {ProcessingTime}ms",
                sortedResults.Count, query, stopwatch.ElapsedMilliseconds);

            return sortedResults;
        }


        #region Nhóm hàm private hỗ trợ

        /// <summary>
        /// Loại bỏ từ "Vietnam" và "Việt Nam" từ địa chỉ (không phân biệt hoa thường và dấu)
        /// </summary>
        /// <param name="address">Địa chỉ cần làm sạch</param>
        /// <returns>Địa chỉ đã loại bỏ "Vietnam" và "Việt Nam"</returns>
        private static string RemoveVietnamFromAddress(string address)
        {
            if (string.IsNullOrWhiteSpace(address))
                return address;

            // Loại bỏ "Vietnam" và "Việt Nam" (không phân biệt hoa thường và dấu)
            var cleaned = address;
            
            // Sử dụng regex để loại bỏ các biến thể của "Vietnam" và "Việt Nam"
            cleaned = Regex.Replace(cleaned, @"\b(vietnam|viet\s*nam|việt\s*nam)\b", "", RegexOptions.IgnoreCase);
            
            // Loại bỏ dấu phẩy thừa và khoảng trắng
            cleaned = Regex.Replace(cleaned, @",\s*,", ",");
            cleaned = Regex.Replace(cleaned, @"^,\s*|,\s*$", "");
            cleaned = Regex.Replace(cleaned, @"\s{2,}", " ");
            
            return cleaned.Trim();
        }

        /// <summary>
        /// Tạo các task tìm kiếm song song (query, favorite, history)
        /// </summary>
        private Task[] CreateSearchTasks(
            string? userId,
            string? customerPhone,
            string normalizedQuery,
            GeoLocation currentLocation,
            int? h3Resolution,
            int limit,
            bool extendedRadius,
            ConcurrentDictionary<long, PlacesEs> hitsResultDict,
            ConcurrentDictionary<long, PlacesEs> suggestionsResultDict,
            ConcurrentDictionary<long, bool> extendedSearchResults,
            ConcurrentDictionary<string, PlacesEs> priorityResultDict)
        {
            // Gom logic tạo task tìm kiếm favorite/history thành 1 hàm riêng cho gọn
            var searchTasks = new List<Task>
            {
                SearchMainPlacesAsync(normalizedQuery, currentLocation, limit, h3Resolution, extendedRadius, hitsResultDict, suggestionsResultDict, extendedSearchResults)
            };

            if (!string.IsNullOrWhiteSpace(userId) || !string.IsNullOrWhiteSpace(customerPhone))
            {
                searchTasks.Add(SearchFavoritePlacesAsync(userId, customerPhone, normalizedQuery, currentLocation, 2, h3Resolution, extendedRadius, priorityResultDict));
                searchTasks.Add(SearchHistoryPlacesAsync(userId, customerPhone, normalizedQuery, currentLocation, 5, h3Resolution, extendedRadius, priorityResultDict));
            }
            return searchTasks.ToArray();
        }

        /// <summary>
        /// Task tìm kiếm chính trên index địa điểm
        /// </summary>
        private async Task SearchMainPlacesAsync(
            string normalizedQuery,
            GeoLocation currentLocation,
            int limit,
            int? h3Resolution,
            bool extendedRadius,
            ConcurrentDictionary<long, PlacesEs> hitsResultDict,
            ConcurrentDictionary<long, PlacesEs> suggestionsResultDict,
            ConcurrentDictionary<long, bool> extendedSearchResults)
        {
            bool forceSearch = false;
Search:
            var searchRequest = _elasticClient.CreateAutocompleteQuery(
                1,
                currentLocation,
                normalizedQuery,
                limit,
                _placeIdxName,
                h3Resolution,
                extendedRadius,
                forceSearch);

            var response = await _elasticClient.SearchAsync(searchRequest);

            if (response.IsValidResponse)
            {
                if (!forceSearch && response.Hits.Count == 0
                                 && (response.Suggest == null || !response.Suggest.TryGetValue("suggest", out var suggestions)
                                                              || suggestions.All(x => ((CompletionSuggest<PlacesEs>)x).Options.Count == 0)))
                {
                    // Nếu không có kết quả, thử lại với forceSearch = true
                    forceSearch = true;
                    goto Search;
                }

                await ProcessHitsAsync(response.Hits, hitsResultDict, extendedSearchResults, extendedRadius);
                await ProcessSuggestionsAsync(response.Suggest, suggestionsResultDict, extendedSearchResults, extendedRadius);


                _logger.LogInformation(
               "SearchMainPlacesAsync hitsResultDict Places {list}  with keyword '{normalizedQuery}' at {time}",
               string.Join(", ", hitsResultDict.Select(m => m.Value.PlaceId)), normalizedQuery, DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss"));


                _logger.LogInformation(
               "SearchMainPlacesAsync suggestionsResultDict Places {list}  with keyword '{normalizedQuery}' at {time}",
               string.Join(", ", suggestionsResultDict.Select(m => m.Value.PlaceId)),  normalizedQuery, DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss"));
            }
        }

        /// <summary>
        /// Task tìm kiếm địa điểm yêu thích
        /// </summary>
        private async Task SearchFavoritePlacesAsync(
            string? userId,
            string? customerPhone,
            string normalizedQuery,
            GeoLocation currentLocation,
            int limit,
            int? h3Resolution,
            bool extendedRadius,
            ConcurrentDictionary<string, PlacesEs> priorityResultDict)
        {
            var favSearchRequest = _elasticClient.CreateFavPlaceSearchQuery(
                1,
                userId,
                customerPhone,
                currentLocation,
                normalizedQuery,
                limit,
                _placeFavIdxName,
                h3Resolution,
                extendedRadius);

            var response = await _elasticClient.SearchAsync(favSearchRequest);
            if (response.IsValidResponse)
            {
                _logger.LogInformation(
               "Favorite Places {list} by phone '{customerPhone}' with keyword '{normalizedQuery}' at {time}",
               string.Join(", ", response.Documents.Select(m => m.PlaceId)), customerPhone, normalizedQuery, DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss"));
                foreach (var item in response.Documents)
                {
                    var placesEs = item.ToPlacesEs();
                    placesEs.TagInput = "Favorite";
                    placesEs.Type = PlaceType.Favorite;
                    priorityResultDict.TryAdd(item.PlaceId!, placesEs);
                }
            }
        }

        /// <summary>
        /// Task tìm kiếm địa điểm lịch sử
        /// </summary>
        private async Task SearchHistoryPlacesAsync(
            string? userId,
            string? customerPhone,
            string normalizedQuery,
            GeoLocation currentLocation,
            int limit,
            int? h3Resolution,
            bool extendedRadius,
            ConcurrentDictionary<string, PlacesEs> priorityResultDict)
        {
            var pairsSearchRequest = _elasticClient.CreatePlacePairSearchQuery(
                1,
                userId,
                customerPhone,
                currentLocation,
                normalizedQuery,
                limit,
                _placePairIdxName,
                h3Resolution,
                extendedRadius);

            var response = await _elasticClient.SearchAsync(pairsSearchRequest);
            if (response.IsValidResponse)
            {
                _logger.LogInformation(
               "History Places {list} by phone '{customerPhone}' with keyword '{normalizedQuery}' at {time}",
               string.Join(", ", response.Documents.Select(m => m.PlaceId)), customerPhone, normalizedQuery, DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss"));
                foreach (var item in response.Documents)
                {
                    var placesEs = item.ToPlacesEs();
                    placesEs.TagInput = "History";
                    placesEs.Type = PlaceType.BookingHistory;
                    priorityResultDict.TryAdd(item.PlaceId!, placesEs);
                }
            }
        }

        /// <summary>
        /// Nếu chưa có kết quả thì mở rộng bán kính tìm kiếm
        /// </summary>
        private async Task ExtendSearchIfNecessary(
            ConcurrentDictionary<long, PlacesEs> hitsResultDict,
            ConcurrentDictionary<long, PlacesEs> suggestionsResultDict,
            string normalizedQuery,
            GeoLocation currentLocation,
            int? h3Resolution,
            int limit,
            ConcurrentDictionary<long, bool> extendedSearchResults,
            bool extendedRadius)
        {
            if (!extendedRadius)
            {
                bool anyResultContainsAllKeywords = false;
                List<PlacesEs>? allResults = null;
                if (!anyResultContainsAllKeywords)
                {
                    allResults = hitsResultDict.Values.Concat(suggestionsResultDict.Values).ToList();

                    if (allResults.Any())
                    {
                        var hasVietnameseDiacritics = normalizedQuery.HasVietnameseDiacritics();

                        var keywordWithoutPrefix = prefixRegex.Replace(normalizedQuery, "");

                        // Sử dụng xử lý song song để kiểm tra nhanh hơn
                        anyResultContainsAllKeywords = allResults.AsParallel().Any(place =>
                        {
                            //foreach (var item in place.AddressPermuted.AsParallel())
                            //{
                            // Kết hợp Name và FullAddress, loại bỏ ký tự đặc biệt chỉ giữ lại số và chữ
                            //var combinedText = item.ToLower();
                            var combinedText = place.FullAddress.ToLower();
                            // Nếu query không có dấu thì combinedText cũng phải loại bỏ dấu
                            if (!hasVietnameseDiacritics)
                            {
                                combinedText = VietnameseAddressPermutator.RemoveVietnameseDiacritics(combinedText);
                            }

                            // Kiểm tra xem tất cả các từ khóa có nằm trong combinedText không
                            if (combinedText.Contains(keywordWithoutPrefix))
                            {
                                return true; // Nếu tìm thấy từ khóa thì trả về true ngay lập tức
                            }

                            //}
                            return false;
                        });
                    }

                }

                // Nếu không có kết quả nào chứa đủ từ khóa, mở rộng bán kính tìm kiếm
                if (!anyResultContainsAllKeywords)
                {
#if DEBUG
                    if (allResults != null)
                    {
                        _logger.LogInformation($"50 km Results: {JsonConvert.SerializeObject(allResults.Select(x => new { x.Name, x.Address, x.FullAddress }), Formatting.None)}");
                    }
#endif
                    _logger.LogInformation("Không tìm thấy kết quả phù hợp với tất cả từ khóa, mở rộng bán kính tìm kiếm.");
                    var extendedSearchRequest = _elasticClient.CreateAutocompleteQuery(
                        1,
                        currentLocation,
                        normalizedQuery,
                        limit,
                        _placeIdxName,
                        h3Resolution,
                        true);

                    var extendedResponse = await _elasticClient.SearchAsync(extendedSearchRequest);

                    if (extendedResponse.IsValidResponse)
                    {
                        await ProcessHitsAsync(extendedResponse.Hits, hitsResultDict, extendedSearchResults, true);
                        await ProcessSuggestionsAsync(extendedResponse.Suggest, suggestionsResultDict, extendedSearchResults, true);
                    }
                }
            }
        }

        /// <summary>
        /// Tổng hợp và sắp xếp kết quả trả về
        /// </summary>
        private async Task<List<PlacesEs>> AggregateAndSortResults(
            ConcurrentDictionary<string, PlacesEs> priorityResultDict,
            ConcurrentDictionary<long, PlacesEs> hitsResultDict,
            ConcurrentDictionary<long, PlacesEs> suggestionsResultDict,
            ConcurrentDictionary<long, bool> extendedSearchResults,
            int limit,
            bool extendedRadius,
            string originalQuery)
        {
            // Gộp kết quả từ hits và suggest, loại trùng theo PlaceId
            var mergedResults = hitsResultDict.Values
                .Concat(suggestionsResultDict.Values)
                .GroupBy(p => p.PlaceId)
                .Select(g => g.First())
                .GroupBy(x => 
                {
                    // Tạo key để group mà không thay đổi dữ liệu gốc
                    var fullAddress = x.FullAddress;
                    return x.Name + x.Address + RemoveVietnamFromAddress(fullAddress);
                })
                .Select(g => g.OrderByDescending(x => x.BookingCount).First())
                .ToList();

            var normalizedOriginalQuery = VietnameseAddressPermutator.NormalizeVietnameseAddress(originalQuery);

            // Tính toán điểm khớp BM25 cho mỗi kết quả
            BM25Scoring.CalculateBM25Scores(mergedResults, normalizedOriginalQuery, _logger);

            // Sắp xếp kết quả
            var results = SortResults(mergedResults, extendedRadius, extendedSearchResults, limit);

            // Ưu tiên kết quả favorite/history lên đầu
            if (!priorityResultDict.IsEmpty)
            {
                await ApplyPriorityResults(results, mergedResults, priorityResultDict);
            }

            return results;
        }

        /// <summary>
        /// Sắp xếp kết quả dựa trên các điểm số và trả về list đã cắt theo limit
        /// </summary>
        private List<PlacesEs> SortResults(List<PlacesEs> mergedResults, bool extendedRadius, ConcurrentDictionary<long, bool> extendedSearchResults, int limit)
        {
            // Tìm điểm số cao nhất trong các kết quả 
            double maxScore = mergedResults.Any() ? mergedResults.Max(p => p.Score ?? 0) : 0;

            // Nếu có điểm cao, thiết lập ngưỡng tương đối (ví dụ: 5% của điểm cao nhất hoặc tối thiểu 20)
            double minScoreThreshold = Math.Max(maxScore * 0.05, 20);
            double forceMinScore = 10;

            // Lọc bỏ các kết quả có điểm quá thấp so với kết quả tốt nhất
            var filteredResults = mergedResults
                .Where(p => (p.SubPlaces?.Count > 0 && p.Score > forceMinScore) || p.Score >= minScoreThreshold || p.DataFrom?.Contains("Priority") == true)
                .ToList();

            _logger.LogDebug("Lọc bỏ {RemovedCount} kết quả có điểm dưới ngưỡng {Threshold} (max score: {MaxScore})",
                mergedResults.Count - filteredResults.Count, minScoreThreshold, maxScore);

            // Nếu filteredResults rỗng, lấy 3 kết quả đầu tiên từ mergedResults đã sắp xếp theo score
            if (filteredResults.Count == 0)
            {
                _logger.LogInformation("Không có kết quả sau khi lọc, lấy 3 kết quả đầu tiên theo score");
                return mergedResults
                    .Where(p => p.Score > forceMinScore)
                    .OrderByDescending(p => p.SubPlaces?.Count > 0)
                    .ThenByDescending(p => p.Popular)
                    .ThenByDescending(p => p.Score)
                    .Take(Math.Min(3, limit))
                    .ToList();
            }

            return extendedRadius
                ? filteredResults
                    .OrderByDescending(p => p.Popular)
                    .ThenByDescending(p => p.Score) // Ưu tiên theo BM25 score đã được tăng cường
                    .ThenBy(p => p.Distance)
                    .ThenBy(p => p.DataFrom != "Suggest") //false (tức là 0) nếu là "Suggest" → lên đầu.
                    .ThenByDescending(p => extendedSearchResults.TryGetValue(p.Id, out var isExtended) && isExtended)
                    .ThenByDescending(p => p.SubPlaces?.Count > 0)
                    .ThenByDescending(p => p.Popular)
                    .ThenByDescending(p => p.Processed)
                    .Take(limit)
                    .ToList()
                : filteredResults
                    .OrderByDescending(p => p.Popular)
                    .ThenByDescending(p => p.Score) // Ưu tiên theo BM25 score đã được tăng cường
                    .ThenBy(p => p.Distance)
                    .ThenBy(p => p.DataFrom != "Suggest") //false (tức là 0) nếu là "Suggest" → lên đầu.
                    .ThenByDescending(p => p.SubPlaces?.Count > 0)
                    .ThenByDescending(p => p.Popular)
                    .ThenByDescending(p => p.Processed)
                    .Take(limit)
                    .ToList();
        }

        /// <summary>
        /// Áp dụng logic ưu tiên cho kết quả favorite/history 
        /// </summary>
        private async Task ApplyPriorityResults(List<PlacesEs> results, List<PlacesEs> mergedResults, ConcurrentDictionary<string, PlacesEs> priorityResultDict)
        {
            var priorityPlaceIds = priorityResultDict.Keys.ToList();
            var existPlaces = mergedResults.Where(x => priorityPlaceIds.Contains(x.PlaceId)).ToList();

            foreach (var item in existPlaces)
            {
                AddPriorityItem(priorityResultDict, item, results, removeItem: item);
            }

            // Nếu có priority mà chưa có trong mergedResults thì lấy từ DB
            var tokenPlaceIds = priorityPlaceIds.Where(priorityPlaceId => existPlaces.All(e => e.PlaceId != priorityPlaceId)).ToList();
            if (tokenPlaceIds.Any())
            {
                var dbPlaces = await _detailsService.GetPlacesDetailsByIdsAsync(tokenPlaceIds!);
                foreach (var item in dbPlaces)
                {
                    var removeItem = results.Count > 0 ? results[^1] : null;
                    AddPriorityItem(priorityResultDict, item, results, removeItem);
                }
            }
        }

        /// <summary>
        /// Đưa kết quả ưu tiên (favorite/history) lên đầu danh sách
        /// </summary>
        private static void AddPriorityItem(
            ConcurrentDictionary<string, PlacesEs> priorityResultDict,
            PlacesEs item,
            List<PlacesEs> results,
            PlacesEs? removeItem = null)
        {
            if (priorityResultDict.ContainsKey(item.PlaceId))
            {
                item.DataFrom = $"{priorityResultDict[item.PlaceId].TagInput} - Priority";
                if (removeItem != null)
                {
                    item.Score = removeItem.Score;
                    results.Remove(removeItem);
                }
                results.Insert(0, item);
            }
            
        }

        /// <summary>
        /// Xử lý kết quả hits trả về từ Elasticsearch (song song)
        /// </summary>
        private Task ProcessHitsAsync(
            IReadOnlyCollection<Hit<PlacesEs>> hits,
            ConcurrentDictionary<long, PlacesEs> resultDict,
            ConcurrentDictionary<long, bool> extendedSearchResults,
            bool isFromExtendedSearch = false)
        {
            return Task.Run(() =>
            {
                Parallel.ForEach(hits, hit =>
                {
                    try
                    {
                        if (hit.Source == null) return;

                        // Tính khoảng cách nếu có
                        double distance = 0;
                        if (hit.Fields != null && hit.Fields.TryGetValue("distance", out var distanceValue))
                        {
                            distance = ((JsonElement)distanceValue).GetDistance();
                        }

                        hit.Source.Type = PlaceType.Normal;
                        hit.Source.DataFrom = "Query";
                        hit.Source.Distance = distance;
                        hit.Source.Score = hit.Score;
                        hit.Source.Keywords = null;
                        hit.Source.TagInput = null;
                        hit.Source.MasterAddress = string.Empty;
                        CleanSubPlaces(hit.Source.SubPlaces);
                        resultDict.TryAdd(hit.Source.Id, hit.Source);

                        // Đánh dấu nếu là kết quả từ tìm kiếm mở rộng
                        if (isFromExtendedSearch)
                        {
                            extendedSearchResults.TryAdd(hit.Source.Id, true);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý hit với ID: {Id}", hit.Id);
                    }
                });
            });
        }

        // Xóa các trường không cần thiết để giảm dung lượng
        private void CleanSubPlaces(List<SubPlacesEs>? subPlaces)
        {
            if (subPlaces == null || subPlaces.Count == 0) return;
            foreach (var detailedPlace in subPlaces.AsParallel())
            {
                // Xóa các trường không cần thiết để giảm dung lượng
                detailedPlace.Keywords = null;
                detailedPlace.MasterAddress = String.Empty;
                detailedPlace.AddressPermuted?.Clear();
            }
        }

        /// <summary>
        /// Xử lý kết quả suggest trả về từ Elasticsearch (song song)
        /// </summary>
        private Task ProcessSuggestionsAsync(
            SuggestDictionary<PlacesEs>? suggest,
            ConcurrentDictionary<long, PlacesEs> resultDict,
            ConcurrentDictionary<long, bool> extendedSearchResults,
            bool isFromExtendedSearch = false)
        {
            return Task.Run(() =>
            {
                if (suggest == null || !suggest.TryGetValue("suggest", out var suggestions) || suggestions == null)
                {
                    return;
                }

                var completionSuggestions = (IList<CompletionSuggest<PlacesEs>>)suggestions;

                Parallel.ForEach(completionSuggestions, suggestion =>
                {
                    Parallel.ForEach(suggestion.Options, option =>
                    {
                        try
                        {
                            if (option.Source == null) return;

                            // Tính khoảng cách nếu có
                            double distance = 0;
                            if (option.Fields != null && option.Fields.TryGetValue("distance", out var distanceValue))
                            {
                                distance = ((JsonElement)distanceValue).GetDistance();
                            }

                            option.Source.Type = PlaceType.Normal;
                            option.Source.DataFrom = "Suggest";
                            option.Source.Distance = distance;
                            option.Source.TagInput = "Suggestion";
                            option.Source.Keywords = null;
                            option.Source.Score = option.Score;
                            option.Source.MasterAddress = String.Empty;
                            CleanSubPlaces(option.Source.SubPlaces);
                            resultDict.TryAdd(option.Source.Id, option.Source);

                            // Đánh dấu nếu là kết quả từ tìm kiếm mở rộng
                            if (isFromExtendedSearch)
                            {
                                extendedSearchResults.TryAdd(option.Source.Id, true);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Lỗi khi xử lý suggestion với text: {Text}", option.Text);
                        }
                    });
                });
            });
        }

        #endregion
    }
}
