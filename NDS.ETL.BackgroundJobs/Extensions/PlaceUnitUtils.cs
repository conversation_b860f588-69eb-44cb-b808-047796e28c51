using Microsoft.EntityFrameworkCore;
using NDS.ETL.Entities.Context;
using NetTopologySuite.Geometries;
using System.Text.RegularExpressions;
using KnowledgeBase.Core.Extensions;

namespace NDS.ETL.Infrastructure.Utils;

/// <summary>
/// Lớp tiện ích chứa các phương thức làm việc với ranh giới đơn vị hành chính và tên
/// Cung cấp các phương thức để tìm tỉnh/thành, quận/huyện, phường/xã theo tọa độ (kiểm tra ranh giới)
/// hoặc theo tên (so khớp chuỗi)
/// </summary>
public static class PlaceUnitUtils
{
    /// <summary>
    /// Lấy tất cả tỉnh/thành phố cùng dữ liệu ranh giới từ cơ sở dữ liệu
    /// </summary>
    public static async Task<List<ProvinceWithBoundary>> GetProvincesWithBoundariesAsync(
        NpgPlaceContext dbContext, 
        CancellationToken cancellationToken = default)
    {
        // Trước hết lấy tỉnh/thành với ranh giới từ boundary_geom
        var provinces = await dbContext.Provinces
            .AsNoTracking()
            .Where(p => p.BoundaryGeom != null)
            .Select(p => new ProvinceWithBoundary
            {
                Id = p.Id,
                Code = p.Code,
                Name = p.Name,
                BoundaryGeom = p.BoundaryGeom,
                NormalizedName = p.Name != null ? StringExtensions.ToUnsign(p.Name).ToLower() : null
            })
            .ToListAsync(cancellationToken);

        // Nếu một số tỉnh/thành không có boundary_geom, dùng boundary làm phương án dự phòng
        if (provinces.Any(p => string.IsNullOrEmpty(p.BoundaryGeom)))
        {
            var missingBoundaries = await dbContext.Provinces
                .AsNoTracking()
                .Where(p => p.BoundaryGeom == null && p.Boundary != null)
                .Select(p => new ProvinceWithBoundary
                {
                    Id = p.Id,
                    Code = p.Code,
                    Name = p.Name,
                    BoundaryGeom = p.Boundary,
                    NormalizedName = p.Name != null ? StringExtensions.ToUnsign(p.Name).ToLower() : null
                })
                .ToListAsync(cancellationToken);

            provinces.AddRange(missingBoundaries);
        }

        return provinces.Where(p => !string.IsNullOrEmpty(p.BoundaryGeom)).ToList();
    }

    /// <summary>
    /// Lấy tất cả quận/huyện cùng dữ liệu ranh giới từ cơ sở dữ liệu
    /// </summary>
    public static async Task<List<DistrictWithBoundary>> GetDistrictsWithBoundariesAsync(
        NpgPlaceContext dbContext, 
        CancellationToken cancellationToken = default)
    {
        // Trước hết lấy quận/huyện với ranh giới từ boundary_geom
        var districts = await dbContext.Districts
            .AsNoTracking()
            .Where(d => d.BoundaryGeom != null && d.ProvinceId.HasValue)
            .Select(d => new DistrictWithBoundary
            {
                Id = d.Id,
                Code = d.Code,
                Name = d.Name,
                ProvinceId = d.ProvinceId.Value,
                BoundaryGeom = d.BoundaryGeom,
                NormalizedName = d.Name != null ? StringExtensions.ToUnsign(d.Name).ToLower() : null,
                NoSpaceName = d.NoSpaceName
            })
            .ToListAsync(cancellationToken);

        // Nếu một số quận/huyện không có boundary_geom, dùng boundary làm phương án dự phòng
        if (districts.Any(d => string.IsNullOrEmpty(d.BoundaryGeom)))
        {
            var missingBoundaries = await dbContext.Districts
                .AsNoTracking()
                .Where(d => d.BoundaryGeom == null && d.Boundary != null && d.ProvinceId.HasValue)
                .Select(d => new DistrictWithBoundary
                {
                    Id = d.Id,
                    Code = d.Code,
                    Name = d.Name,
                    ProvinceId = d.ProvinceId.Value,
                    BoundaryGeom = d.Boundary,
                    NormalizedName = d.Name != null ? StringExtensions.ToUnsign(d.Name).ToLower() : null,
                    NoSpaceName = d.NoSpaceName
                })
                .ToListAsync(cancellationToken);

            districts.AddRange(missingBoundaries);
        }

        return districts.Where(d => !string.IsNullOrEmpty(d.BoundaryGeom)).ToList();
    }

    /// <summary>
    /// Lấy tất cả phường/xã cùng dữ liệu ranh giới từ cơ sở dữ liệu
    /// </summary>
    public static async Task<List<WardWithBoundary>> GetWardsWithBoundariesAsync(
        NpgPlaceContext dbContext, 
        CancellationToken cancellationToken = default)
    {
        // Trước hết lấy phường/xã với ranh giới từ boundary_geom
        var wards = await dbContext.Wards
            .AsNoTracking()
            .Where(w => w.BoundaryGeom != null && w.DistrictId.HasValue)
            .Select(w => new WardWithBoundary
            {
                Id = w.Id,
                Code = w.Code,
                Name = w.Name,
                DistrictId = w.DistrictId.Value,
                BoundaryGeom = w.BoundaryGeom,
                NormalizedName = w.Name != null ? StringExtensions.ToUnsign(w.Name).ToLower() : null,
                NoSpaceName = w.NoSpaceName
            })
            .ToListAsync(cancellationToken);

        // Nếu một số phường/xã không có boundary_geom, dùng boundary làm phương án dự phòng
        if (wards.Any(w => string.IsNullOrEmpty(w.BoundaryGeom)))
        {
            var missingBoundaries = await dbContext.Wards
                .AsNoTracking()
                .Where(w => w.BoundaryGeom == null && w.Boundary != null && w.DistrictId.HasValue)
                .Select(w => new WardWithBoundary
                {
                    Id = w.Id,
                    Code = w.Code,
                    Name = w.Name,
                    DistrictId = w.DistrictId.Value,
                    BoundaryGeom = w.Boundary,
                    NormalizedName = w.Name != null ? StringExtensions.ToUnsign(w.Name).ToLower() : null,
                    NoSpaceName = w.NoSpaceName
                })
                .ToListAsync(cancellationToken);

            wards.AddRange(missingBoundaries);
        }

        return wards.Where(w => !string.IsNullOrEmpty(w.BoundaryGeom)).ToList();
    }

    /// <summary>
    /// Tìm tỉnh/thành phố chứa điểm tọa độ được chỉ định sử dụng PostGIS ST_Contains
    /// </summary>
    public static async Task<ProvinceWithBoundary?> FindProvinceByPointAsync(
        Point point,
        List<ProvinceWithBoundary> provinces,
        CancellationToken cancellationToken = default)
    {
        // Sử dụng truy vấn cơ sở dữ liệu trực tiếp với SRID 4326 (WGS84)
        string pointWkt = $"POINT({point.X} {point.Y})";
        
        // Tối ưu: Duyệt danh sách theo thứ tự để kiểm tra từng ranh giới
        foreach (var province in provinces)
        {
            // Bỏ qua tỉnh không có ranh giới
            if (string.IsNullOrEmpty(province.BoundaryGeom)) 
                continue;
                
            // Sử dụng phép so sánh chuỗi đơn giản để xử lý nhanh hơn
            if (await IsPointInWktPolygonAsync(pointWkt, province.BoundaryGeom, cancellationToken))
            {
                return province;
            }
        }
        
        return null; // Không tìm thấy tỉnh/thành phố chứa điểm
    }

    /// <summary>
    /// Tìm quận/huyện chứa điểm tọa độ được chỉ định sử dụng PostGIS ST_Contains
    /// </summary>
    public static async Task<DistrictWithBoundary?> FindDistrictByPointAsync(
        Point point,
        List<DistrictWithBoundary> districts,
        CancellationToken cancellationToken = default)
    {
        // Sử dụng truy vấn cơ sở dữ liệu trực tiếp với SRID 4326 (WGS84)
        string pointWkt = $"POINT({point.X} {point.Y})";
        
        foreach (var district in districts)
        {
            // Bỏ qua quận/huyện không có ranh giới
            if (string.IsNullOrEmpty(district.BoundaryGeom)) 
                continue;
                
            // Sử dụng phép so sánh chuỗi đơn giản để xử lý nhanh hơn
            if (await IsPointInWktPolygonAsync(pointWkt, district.BoundaryGeom, cancellationToken))
            {
                return district;
            }
        }
        
        return null; // Không tìm thấy quận/huyện chứa điểm
    }

    /// <summary>
    /// Tìm phường/xã chứa điểm tọa độ được chỉ định sử dụng PostGIS ST_Contains
    /// </summary>
    public static async Task<WardWithBoundary?> FindWardByPointAsync(
        Point point,
        List<WardWithBoundary> wards,
        CancellationToken cancellationToken = default)
    {
        // Sử dụng truy vấn cơ sở dữ liệu trực tiếp với SRID 4326 (WGS84)
        string pointWkt = $"POINT({point.X} {point.Y})";
        
        foreach (var ward in wards)
        {
            // Bỏ qua phường/xã không có ranh giới
            if (string.IsNullOrEmpty(ward.BoundaryGeom)) 
                continue;
                
            // Sử dụng phép so sánh chuỗi đơn giản để xử lý nhanh hơn
            if (await IsPointInWktPolygonAsync(pointWkt, ward.BoundaryGeom, cancellationToken))
            {
                return ward;
            }
        }
        
        return null; // Không tìm thấy phường/xã chứa điểm
    }

    /// <summary>
    /// Kiểm tra xem một điểm ở định dạng WKT có nằm trong một đa giác ở định dạng WKT hay không
    /// </summary>
    private static async Task<bool> IsPointInWktPolygonAsync(
        string pointWkt, 
        string polygonWkt, 
        CancellationToken cancellationToken)
    {
        // Kiểm tra đơn giản để giảm kết quả dương tính giả
        if (string.IsNullOrEmpty(polygonWkt) || !polygonWkt.Contains("POLYGON"))
            return false;
            
        try
        {
            // Lấy tọa độ từ WKT của điểm
            var pointMatch = Regex.Match(pointWkt, @"POINT\(([0-9.-]+)\s+([0-9.-]+)\)");
            if (!pointMatch.Success) return false;
            
            double lon = double.Parse(pointMatch.Groups[1].Value);
            double lat = double.Parse(pointMatch.Groups[2].Value);
            
            // Kiểm tra hộp bao đơn giản trước khi chạy kiểm tra đa giác phức tạp
            // Trích xuất tọa độ từ WKT của đa giác
            var coords = Regex.Matches(polygonWkt, @"([0-9.-]+)\s+([0-9.-]+)")
                .Select(m => (X: double.Parse(m.Groups[1].Value), Y: double.Parse(m.Groups[2].Value)))
                .ToList();
                
            if (coords.Count == 0) return false;
            
            // Tìm min/max cho kiểm tra hộp bao nhanh
            double minX = coords.Min(p => p.X);
            double maxX = coords.Max(p => p.X);
            double minY = coords.Min(p => p.Y);
            double maxY = coords.Max(p => p.Y);
            
            // Kiểm tra nhanh xem điểm có nằm ngoài hộp bao không
            if (lon < minX || lon > maxX || lat < minY || lat > maxY)
                return false;
                
            // Nếu điểm nằm trong hộp bao, thực hiện kiểm tra đa giác chính xác
            // sử dụng thuật toán ray casting (point in polygon)
            bool isInside = IsPointInPolygon(lon, lat, coords);
            return isInside;
        }
        catch
        {
            // Nếu xảy ra lỗi khi phân tích, mặc định trả về false
            return false;
        }
    }
    
    /// <summary>
    /// Thực hiện thuật toán ray casting để xác định xem một điểm có nằm trong đa giác hay không
    /// </summary>
    private static bool IsPointInPolygon(double x, double y, List<(double X, double Y)> polygon)
    {
        bool isInside = false;
        int j = polygon.Count - 1;
        
        for (int i = 0; i < polygon.Count; i++)
        {
            if (((polygon[i].Y > y) != (polygon[j].Y > y)) && 
                (x < (polygon[j].X - polygon[i].X) * (y - polygon[i].Y) / 
                 (polygon[j].Y - polygon[i].Y) + polygon[i].X))
            {
                isInside = !isInside;
            }
            j = i;
        }
        
        return isInside;
    }

    /// <summary>
    /// Tìm tỉnh/thành phố theo tên sử dụng so sánh chuỗi
    /// </summary>
    public static ProvinceWithBoundary? FindProvinceByName(string name, List<ProvinceWithBoundary> provinces)
    {
        if (string.IsNullOrWhiteSpace(name))
            return null;

        // Làm sạch và chuẩn hóa tên đầu vào
        var normalizedInput = StringExtensions.ToUnsign(name).ToLower();
        normalizedInput = PlaceUtils.RemovePrefixProvince(normalizedInput);
        
        foreach (var province in provinces)
        {
            if (string.IsNullOrEmpty(province.Name))
                continue;
                
            // Thử khớp chính xác trước
            if (province.NormalizedName == normalizedInput)
            {
                return province;
            }
            
            // Thử không có tiền tố
            var normalizedProvince = PlaceUtils.RemovePrefixProvince(province.NormalizedName);
            if (normalizedProvince == normalizedInput)
            {
                return province;
            }
        }
        
        // Thử khớp một phần nếu không tìm thấy khớp chính xác
        foreach (var province in provinces)
        {
            if (string.IsNullOrEmpty(province.Name))
                continue;
                
            // Kiểm tra xem tên tỉnh/thành chuẩn hóa có chứa đầu vào hoặc ngược lại không
            var normalizedProvince = PlaceUtils.RemovePrefixProvince(province.NormalizedName);
            
            if (normalizedProvince?.Contains(normalizedInput) == true ||
                normalizedInput.Contains(normalizedProvince ?? ""))
            {
                return province;
            }
        }
        
        return null;
    }
    
    /// <summary>
    /// Tìm tỉnh/thành phố theo tên đã chuẩn hóa
    /// </summary>
    public static ProvinceWithBoundary? FindProvinceByNormalizedName(string? normalizedName, List<ProvinceWithBoundary> provinces)
    {
        if (string.IsNullOrWhiteSpace(normalizedName))
            return null;
            
        // Làm sạch và chuẩn hóa tên đầu vào
        normalizedName = normalizedName.ToLower();
        
        foreach (var province in provinces)
        {
            if (string.IsNullOrEmpty(province.NormalizedName))
                continue;
                
            // Thử khớp chính xác trước
            if (province.NormalizedName.Contains(normalizedName) || 
                normalizedName.Contains(province.NormalizedName))
            {
                return province;
            }
            
            // Thử không có tiền tố
            var normalizedProvince = PlaceUtils.RemovePrefixProvince(province.NormalizedName);
            if (normalizedProvince == normalizedName)
            {
                return province;
            }
        }
        
        return null;
    }

    /// <summary>
    /// Tìm quận/huyện theo tên sử dụng so sánh chuỗi
    /// </summary>
    public static DistrictWithBoundary? FindDistrictByName(string name, List<DistrictWithBoundary> districts)
    {
        if (string.IsNullOrWhiteSpace(name))
            return null;

        // Làm sạch và chuẩn hóa tên đầu vào
        var normalizedInput = StringExtensions.ToUnsign(name).ToLower();
        normalizedInput = PlaceUtils.RemovePrefixDistrict(normalizedInput);
        
        // Trước tiên thử khớp chính xác
        foreach (var district in districts)
        {
            if (string.IsNullOrEmpty(district.Name))
                continue;
                
            var normalizedDistrict = StringExtensions.ToUnsign(district.Name).ToLower();
            normalizedDistrict = PlaceUtils.RemovePrefixDistrict(normalizedDistrict);
                
            // Thử khớp chính xác
            if (normalizedDistrict == normalizedInput)
            {
                return district;
            }
            
            // Thử tên không có khoảng trắng nếu có
            if (!string.IsNullOrEmpty(district.NoSpaceName))
            {
                var normalizedNoSpace = StringExtensions.ToUnsign(district.NoSpaceName).ToLower();
                if (normalizedNoSpace == normalizedInput.Replace(" ", ""))
                {
                    return district;
                }
            }
        }
        
        // Nếu không có khớp chính xác, thử khớp một phần
        foreach (var district in districts)
        {
            if (string.IsNullOrEmpty(district.Name))
                continue;
                
            var normalizedDistrict = StringExtensions.ToUnsign(district.Name).ToLower();
            normalizedDistrict = PlaceUtils.RemovePrefixDistrict(normalizedDistrict);
                
            if (normalizedDistrict.Contains(normalizedInput) || 
                normalizedInput.Contains(normalizedDistrict))
            {
                return district;
            }
        }
        
        return null;
    }
    
    /// <summary>
    /// Tìm quận/huyện theo tên đã chuẩn hóa
    /// </summary>
    public static DistrictWithBoundary? FindDistrictByNormalizedName(string? normalizedName, List<DistrictWithBoundary> districts)
    {
        if (string.IsNullOrWhiteSpace(normalizedName))
            return null;
            
        // Làm sạch và chuẩn hóa tên đầu vào
        normalizedName = normalizedName.ToLower();
        
        foreach (var district in districts)
        {
            if (string.IsNullOrEmpty(district.NormalizedName))
                continue;
                
            // So sánh tên đã chuẩn hóa
            var normalizedDistrict = PlaceUtils.RemovePrefixDistrict(district.NormalizedName);
            
            if (normalizedDistrict == normalizedName || 
                normalizedDistrict?.Contains(normalizedName) == true || 
                normalizedName.Contains(normalizedDistrict ?? ""))
            {
                return district;
            }
        }
        
        return null;
    }

    /// <summary>
    /// Tìm phường/xã theo tên sử dụng so sánh chuỗi
    /// </summary>
    public static WardWithBoundary? FindWardByName(string name, List<WardWithBoundary> wards)
    {
        if (string.IsNullOrWhiteSpace(name))
            return null;

        // Làm sạch và chuẩn hóa tên đầu vào
        var normalizedInput = PlaceUtils.RemovePrefixWard(name?.ToLower());
        normalizedInput = StringExtensions.ToUnsign(normalizedInput!).ToLower();
        
        // Trước tiên thử khớp chính xác
        foreach (var ward in wards)
        {
            if (string.IsNullOrEmpty(ward.Name))
                continue;
                
            var normalizedWard = PlaceUtils.RemovePrefixWard(ward.Name.ToLower());
            normalizedWard = StringExtensions.ToUnsign(normalizedWard!).ToLower();
                
            // Thử khớp chính xác
            if (normalizedWard.Contains(normalizedInput))
            {
                return ward;
            }
            
            // Thử tên không có khoảng trắng nếu có
            if (!string.IsNullOrEmpty(ward.NoSpaceName))
            {
                var normalizedNoSpace = StringExtensions.ToUnsign(ward.NoSpaceName).ToLower();
                if (normalizedNoSpace.Contains(normalizedInput.Replace(" ", "")))
                {
                    return ward;
                }
            }
        }
        
        // Nếu không có khớp chính xác, thử khớp một phần
        foreach (var ward in wards)
        {
            if (string.IsNullOrEmpty(ward.Name))
                continue;
                
            var normalizedWard = PlaceUtils.RemovePrefixWard(ward.Name.ToLower());
            normalizedWard = StringExtensions.ToUnsign(normalizedWard!).ToLower();
                
            if (normalizedWard.Contains(normalizedInput) || 
                normalizedInput.Contains(normalizedWard))
            {
                return ward;
            }
        }
        
        return null;
    }
    
    /// <summary>
    /// Tìm phường/xã theo tên đã chuẩn hóa
    /// </summary>
    public static WardWithBoundary? FindWardByNormalizedName(string? normalizedName, List<WardWithBoundary> wards)
    {
        if (string.IsNullOrWhiteSpace(normalizedName))
            return null;
            
        // Làm sạch và chuẩn hóa tên đầu vào
        normalizedName = normalizedName.ToLower();
        
        foreach (var ward in wards)
        {
            if (string.IsNullOrEmpty(ward.NormalizedName))
                continue;
                
            // So sánh tên đã chuẩn hóa
            var normalizedWard = PlaceUtils.RemovePrefixWard(ward.NormalizedName);
            
            if (normalizedWard == normalizedName || 
                normalizedWard?.Contains(normalizedName) == true || 
                normalizedName.Contains(normalizedWard ?? ""))
            {
                return ward;
            }
        }
        
        return null;
    }
}

/// <summary>
/// Class representing a province with boundary geometry
/// </summary>
public class ProvinceWithBoundary
{
    public int Id { get; set; }
    public string? Code { get; set; }
    public string? Name { get; set; }
    public string? BoundaryGeom { get; set; }
    public string? NormalizedName { get; set; }
}

/// <summary>
/// Class representing a district with boundary geometry
/// </summary>
public class DistrictWithBoundary
{
    public int Id { get; set; }
    public string? Code { get; set; }
    public string? Name { get; set; }
    public int ProvinceId { get; set; }
    public string? BoundaryGeom { get; set; }
    public string? NormalizedName { get; set; }
    public string? NoSpaceName { get; set; }
}

/// <summary>
/// Class representing a ward with boundary geometry
/// </summary>
public class WardWithBoundary
{
    public int Id { get; set; }
    public string? Code { get; set; }
    public string? Name { get; set; }
    public int DistrictId { get; set; }
    public string? BoundaryGeom { get; set; }
    public string? NormalizedName { get; set; }
    public string? NoSpaceName { get; set; }
}