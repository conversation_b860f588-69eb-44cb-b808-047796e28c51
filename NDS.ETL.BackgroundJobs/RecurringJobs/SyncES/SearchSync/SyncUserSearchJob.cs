using Elastic.Clients.Elasticsearch;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.SearchSync;
using NDS.ETL.Entities.TaxiMongoDB;
using NDS.ETL.Infrastructure.Common;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch.FavPlaceSync;
using System.Diagnostics;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.SearchSync;

/// <summary>
/// Job đồng bộ dữ liệu lịch sử tìm kiếm từ MongoDB sang Elasticsearch (refactor theo FavPlaceSync)
/// </summary>
public class SyncUserSearchJob : RecurringJobBaseElastic
{
    private readonly ILogger<SyncUserSearchJob> _logger;
    private readonly ElasticsearchDataService _esService;
    private readonly DbDataService _dbService;
    private readonly SyncProcessor _syncProcessor;
    private readonly IDistributedLock _distributedLock;
    private readonly string _indexName;
    private const int PAGE_SIZE = 100_000;
    private const int LOCK_TIMEOUT_SECONDS = 3600;
    private const string LOCK_KEY = "sync_search_history_lock";
    private readonly IMongoCollection<UserSearchHistory> _userSearchHistory;

    public SyncUserSearchJob(
        IConfiguration configuration,
        ILogger<SyncUserSearchJob> logger,
        ElasticsearchClient elasticClient,
        IDistributedLock distributedLock,
        IScopedResolver<IMongoDatabase> dbContext,
        ILoggerFactory loggerFactory) 
        : base(configuration, logger, elasticClient, null)
    {
        _logger = logger;
        _userSearchHistory = dbContext
            .ResolveAsync(m => Task.FromResult(m.GetCollection<UserSearchHistory>("user_search_histories")))
            .GetAwaiter().GetResult();
        _distributedLock = distributedLock;
        _indexName = GetIndexName(configuration);
        _dbService = new DbDataService(_userSearchHistory, logger);
        _esService = new ElasticsearchDataService(logger, elasticClient, GetIndexName(configuration));
        _syncProcessor = new SyncProcessor(_dbService, _esService, loggerFactory.CreateLogger<SyncProcessor>(), this, _indexName);
    }

    private string GetIndexName(IConfiguration configuration)
    {
        // Lấy tên index cho lịch sử tìm kiếm từ config hoặc mặc định
        var indexNames = configuration.GetSection("ElasticSearchConfig:Index").Get<string[]>() ?? [];
        return indexNames.FirstOrDefault(x => x.Contains("user-place")) ?? "user-place";
    }

    public override string JobId => "SyncUserSearch";
    public override string ConfigSection => "JobPgSyncPlace";

    public override async Task DoWorkAsync(CancellationToken stoppingToken)
    {
        var lockAcquired = false;
        try
        {
            //await _distributedLock.ForceReleaseLockAsync(LOCK_KEY);

            _logger.LogInformation("Attempting to acquire lock for SearchHistory sync");
            lockAcquired = await _distributedLock.TryAcquireLockAsync(LOCK_KEY, TimeSpan.FromSeconds(LOCK_TIMEOUT_SECONDS));
            if (!lockAcquired)
            {
                _logger.LogWarning("Could not acquire lock - another sync job is already running");
                return;
            }
            _logger.LogInformation("Lock acquired. Starting SearchHistory sync");
            await RunSynchronizationJob(stoppingToken);
        }
        catch (Exception ex)
        {
            if (ex is OperationCanceledException && stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("SearchHistory sync job canceled");
            }
            else
            {
                _logger.LogError(ex, "Error in SearchHistory sync job");
                throw;
            }
        }
        finally
        {
            if (lockAcquired)
            {
                try
                {
                    await _distributedLock.ReleaseLockAsync(LOCK_KEY);
                    _logger.LogInformation("Lock released for SearchHistory sync");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error releasing lock for SearchHistory sync");
                }
            }
        }
    }

    private async Task RunSynchronizationJob(CancellationToken stoppingToken)
    {
        var globalStopwatch = Stopwatch.StartNew();
        _logger.LogInformation("Starting SearchHistory sync to Elasticsearch");

        var syncStats = new SyncStats();
        var maxRetries = 3;
        var syncLock = new object();

        try
        {
            // Lấy thời gian nhỏ nhất từ MongoDB
            var earliest = await _dbService.GetEarliestCreatedAsync();
            var fromDate = earliest?.CreatedAt.ToLocalTime() ?? DateTime.Now.AddYears(-1); // Convert to local time
            var toDate = DateTime.Now;

            if (fromDate >= toDate)
            {
                _logger.LogInformation("No valid time range to sync.");
                return;
            }

            // Lấy tổng số lượng bản ghi từ MongoDB
            var totalCount = await _dbService.GetTotalCountAsync();
            _logger.LogInformation("MongoDB total search histories: {Total}", totalCount);

            // Lấy thời gian cập nhật mới nhất từ MongoDB
            var latest = await _dbService.GetLatestUpdatedAsync();
            var maxCreatedAt = latest?.CreatedAt.ToLocalTime() ?? DateTime.MinValue; // Convert to local time
            _logger.LogInformation("MongoDB max CreatedAt: {MaxCreatedAt:yyyy-MM-dd HH:mm:ss.fff}", maxCreatedAt);

            // Lấy thông tin từ Elasticsearch (nếu cần)
            var esCount = await _esService.GetTotalCountAsync(stoppingToken);
            _logger.LogInformation("Elasticsearch total search histories: {Total}", esCount);

            // Lấy thời gian CreatedAt mới nhất từ Elasticsearch
            var esLatestCreatedAt = await _esService.GetLatestCreatedAtAsync(stoppingToken);
            _logger.LogInformation("Elasticsearch max CreatedAt: {MaxCreatedAt:yyyy-MM-dd HH:mm:ss.fff}", esLatestCreatedAt);

            // Nếu ES chưa có dữ liệu, thực hiện full sync
            if (esLatestCreatedAt == null)
            {
                _logger.LogInformation("🔄 Performing FULL sync of SearchHistory data");
                try
                {
                    await _syncProcessor.PerformFullSync(
                        fromDate,
                        toDate,
                        stoppingToken,
                        PAGE_SIZE,
                        maxRetries);
                }
                catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
                {
                    _logger.LogInformation("Full sync canceled");
                    return;
                }
            }
            // Nếu MongoDB có bản ghi mới hơn ES, thực hiện incremental sync
            else if (maxCreatedAt > esLatestCreatedAt)
            {
                _logger.LogInformation("🔄 Performing INCREMENTAL sync of SearchHistory data");
                try
                {
                    // Chỉ lấy các bản ghi mới hơn esLatestCreatedAt
                    await _syncProcessor.PerformIncrementalSync(
                        esLatestCreatedAt.Value,
                        toDate,
                        stoppingToken,
                        PAGE_SIZE,
                        maxRetries);
                }
                catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
                {
                    _logger.LogInformation("Incremental sync canceled");
                    return;
                }
            }
            else
            {
                _logger.LogInformation("✅ No new records to sync. Data is up to date.");
            }

            syncStats.TotalCount = (int)totalCount;
        }
        catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
        {
            _logger.LogInformation("Sync job canceled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SearchHistory sync");
            throw;
        }
        finally
        {
            globalStopwatch.Stop();
            var totalTimeSeconds = globalStopwatch.Elapsed.TotalSeconds;
            var avgRecordsPerSecond = totalTimeSeconds > 0 ? syncStats.TotalProcessed / totalTimeSeconds : 0;
            _logger.LogInformation(
                $"✅ Completed sync of {syncStats.TotalProcessed:N0}/{syncStats.TotalCount:N0} records. ⏱ {totalTimeSeconds:F2}s ({avgRecordsPerSecond:F0} rec/sec). " +
                $"Batches: {syncStats.BatchSuccessCount} OK, {syncStats.BatchFailureCount} failed, {syncStats.RetryCount} retries");
        }
    }
} 