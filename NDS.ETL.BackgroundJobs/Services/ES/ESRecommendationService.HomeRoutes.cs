using Elastic.Clients.Elasticsearch;
using NDS.ETL.BackgroundJobs.Extensions;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using System.Diagnostics;

namespace NDS.ETL.BackgroundJobs.Services.ES
{
    public partial class ESRecommendationService
    {
        #region Public API - Route Recommendations

        /// <summary>
        /// Lấy lộ trình gợi ý cho người dùng trên màn hình home (tối đa 3 hành trình)
        /// </summary>
        public async Task<List<RecommendedRoute>> GetRecommendedRoutesAsync(string userId,
            string phoneNumber,
            double? latitude = null,
            double? longitude = null,
            int? h3Resolution = null,
            int limit = 3, // Giới hạn mặc định 3 cho màn hình home
            int page = 1,
            string lang = "en")
        {
            var mainStopwatch = Stopwatch.StartNew();
            _logger.LogInformation("Getting recommended routes for home screen - user: {UserId}, phone: {PhoneNumber}",
                userId, phoneNumber);

            // Tối ưu: Giảm timeout từ 5s xuống 3s cho home screen
            const int taskTimeoutMs = 3000;

            try
            {
                // Bước 1: Ưu tiên lấy lộ trình gần đây trước (nguồn dữ liệu quan trọng nhất)
                _logger.LogInformation("[HOME] Getting user recent routes...");
                var recentRoutesStopwatch = Stopwatch.StartNew();

                var recentRoutes = await GetUserRecentRoutesAsync(
                    userId, phoneNumber, limit, page, lang, latitude, longitude, MinRadiusMeters);

                recentRoutesStopwatch.Stop();
                _logger.LogInformation("[HOME] Got {Count} recent routes in {ElapsedMs}ms",
                    recentRoutes.Count, recentRoutesStopwatch.ElapsedMilliseconds);

                // Nếu đã có đủ lộ trình từ lịch sử, trả về luôn
                List<RecommendedRoute>? finalRoutes;
                if (recentRoutes.Count >= limit)
                {
                    finalRoutes = recentRoutes.Take(limit).ToList();
                    await EnrichRoutesWithPlaceDetailsAsync(finalRoutes);

                    mainStopwatch.Stop();
                    _logger.LogInformation("[HOME] Completed with {Count} routes in {ElapsedMs}ms (recent routes only)",
                        finalRoutes.Count, mainStopwatch.ElapsedMilliseconds);
                    return finalRoutes;
                }

                // Bước 2: Nếu không có vị trí, chỉ trả về lộ trình gần đây
                if (!latitude.HasValue || !longitude.HasValue)
                {
                    await EnrichRoutesWithPlaceDetailsAsync(recentRoutes);
                    mainStopwatch.Stop();
                    _logger.LogInformation("[HOME] Completed with {Count} routes in {ElapsedMs}ms (no location)",
                        recentRoutes.Count, mainStopwatch.ElapsedMilliseconds);
                    return recentRoutes;
                }

                // Bước 3: Lấy thêm điểm đến phổ biến và lộ trình theo thời gian song song
                var additionalRoutesNeeded = Math.Max(3, limit - recentRoutes.Count);
                _logger.LogInformation("[HOME] Need {Count} more routes, getting popular destinations and time-based routes...",
                    additionalRoutesNeeded);

                h3Resolution ??= H3Helper.GetH3ResolutionFromRadiusKm(30); // Giảm bán kính tìm kiếm cho home screen

                // Tối ưu: Khởi động tất cả task song song ngay lập tức với timeout tối ưu
                var remainingTimeMs = Math.Max(1500, taskTimeoutMs - (int)recentRoutesStopwatch.ElapsedMilliseconds - 500);
                var taskTimeoutMs_Individual = remainingTimeMs / 2; // Chia đều thời gian

                // Khởi động các task song song
                var popularDestinationsTask = GetPopularDestinationsAsync(
                    userId, phoneNumber, latitude.Value, longitude.Value,
                    h3Resolution, additionalRoutesNeeded, MinRadiusMeters, recentRoutes, lang, taskTimeoutMs_Individual);

                var timeBasedRoutesTask = GetFrequentRoutesByTimeOfDayAsync(
                    userId, phoneNumber, DateTime.Now, additionalRoutesNeeded, lang, latitude, longitude, taskTimeoutMs_Individual);

                // Tối ưu: Chuẩn bị current location song song để tiết kiệm thời gian
                var currentLocationTask = PreparateCurrentLocationAsync(
                    userId, phoneNumber, latitude.Value, longitude.Value, recentRoutes, lang, 500);

                // Tối ưu: Chờ tất cả task hoàn thành hoặc timeout
                var additionalTasksStopwatch = Stopwatch.StartNew();

                var allAdditionalTasks = new Task[] { popularDestinationsTask, timeBasedRoutesTask, currentLocationTask };
                await Task.WhenAll(allAdditionalTasks.Select(async task =>
                {
                    try
                    {
                        await task;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "[HOME] Task failed: {Message}", ex.Message);
                    }
                }));

                additionalTasksStopwatch.Stop();
                _logger.LogInformation("[HOME] Additional tasks completed in {ElapsedMs}ms",
                    additionalTasksStopwatch.ElapsedMilliseconds);

                // Xử lý kết quả từ các task bổ sung
                var allRoutes = new List<RecommendedRoute>(recentRoutes);
                var currentLocationOrigin = currentLocationTask.IsCompletedSuccessfully ? 
                    await currentLocationTask : recentRoutes.OrderBy(x => x.Distance).FirstOrDefault()?.Origin;

                // Thêm điểm đến phổ biến nếu task đã hoàn thành
                if (popularDestinationsTask.IsCompletedSuccessfully)
                {
                    var popularDestinations = await popularDestinationsTask;

                    var routesFromCurrent = popularDestinations
                        .Where(x => x.PlaceId != currentLocationOrigin?.PlaceId)
                        .Select(dest =>
                        {
                            double rawDistance = H3Helper.CalculateDistance(latitude.Value, longitude.Value, dest.Location.Lat, dest.Location.Lon) / 1000;

                            // Làm tròn khoảng cách đến 0.1km (100m)
                            double roundedDistance = Math.Round(rawDistance, 1);

                            dest.Distance = rawDistance;

                            var route = new RecommendedRoute
                            {
                                Origin = currentLocationOrigin,
                                Destination = dest,
                                EstimateAmount = string.Empty,
                                Desc = IsVietnamese(lang) ? "Điểm đến phổ biến" : "Popular destination",
                                Seq = 2,
                                Distance = roundedDistance
                            };

                            if (!route.Destination.FullAddress.StartsWith(route.Destination.Address))
                            {
                                route.Destination.FullAddress = $"{route.Destination.Address}, {route.Destination.FullAddress}";
                            }

                            return route;
                        })
                        .Where(x => x.Distance > 1.0)// Chỉ lấy những điểm đến cách xa hơn 1kmkd
                        .OrderBy(x => x.Distance)
                        .Take(additionalRoutesNeeded)
                        .ToList();

                    allRoutes.AddRange(routesFromCurrent);
                    _logger.LogInformation("[HOME] Added {Count} routes from popular destinations", routesFromCurrent.Count);
                }

                // Thêm lộ trình theo thời gian nếu vẫn cần thêm và task đã hoàn thành
                if (allRoutes.Count < limit && timeBasedRoutesTask.IsCompletedSuccessfully)
                {
                    var timeBasedRoutes = await timeBasedRoutesTask;
                    var remainingSlots = limit - allRoutes.Count;

                    var uniqueTimeRoutes = timeBasedRoutes
                        .Where(tr => !allRoutes.Any(ar =>
                            ar.Origin.PlaceId == tr.Origin.PlaceId &&
                            ar.Destination.PlaceId == tr.Destination.PlaceId))
                        .Take(remainingSlots)
                        .ToList();

                    foreach (var route in uniqueTimeRoutes)
                    {
                        route.Seq = 3;
                        route.Distance = H3Helper.CalculateDistance(
                            route.Origin.Location.Lat, route.Origin.Location.Lon,
                            route.Destination.Location.Lat, route.Destination.Location.Lon) / 1000;
                    }

                    allRoutes.AddRange(uniqueTimeRoutes);
                    _logger.LogInformation("[HOME] Added {Count} routes from time-based suggestions", uniqueTimeRoutes.Count);
                }

                // Sắp xếp và giới hạn kết quả cuối cùng
                finalRoutes = allRoutes
                    .GroupBy(x => new { OriginId = x.Origin.PlaceId, DestinationId = x.Destination.PlaceId })
                    .Select(g => g.OrderBy(x => x.Seq).First()) // Loại bỏ trùng lặp, ưu tiên Seq thấp
                    .OrderBy(x => x.Seq)
                    .ThenBy(x => x.Distance)
                    .Take(limit)
                    .ToList();

                // Cập nhật Seq theo thứ tự ưu tiên cuối cùng
                for (int i = 0; i < finalRoutes.Count; i++)
                {
                    finalRoutes[i].Seq = i + 1;
                }

                // Lấy thông tin chi tiết place
                await EnrichRoutesWithPlaceDetailsAsync(finalRoutes);

                mainStopwatch.Stop();
                _logger.LogInformation("[HOME] Completed with {Count} routes in {ElapsedMs}ms (total)",
                    finalRoutes.Count, mainStopwatch.ElapsedMilliseconds);

                return finalRoutes;
            }
            catch (OperationCanceledException)
            {
                mainStopwatch.Stop();
                _logger.LogWarning("[HOME] Operation timed out after {ElapsedMs}ms", mainStopwatch.ElapsedMilliseconds);
                return new List<RecommendedRoute>();
            }
            catch (Exception ex)
            {
                mainStopwatch.Stop();
                _logger.LogError(ex, "[HOME] Error getting recommended routes after {ElapsedMs}ms: {Message}",
                    mainStopwatch.ElapsedMilliseconds, ex.Message);
                return new List<RecommendedRoute>();
            }
        }

        #endregion

        #region Private Methods - Route Recommendations

        /// <summary>
        /// Lấy lộ trình gần đây của người dùng cho màn hình home
        /// </summary>
        private async Task<List<RecommendedRoute>> GetUserRecentRoutesAsync(
            string userId,
            string phoneNumber,
            int limit,
            int page = 1,
            string lang = "en",
            double? latitude = null,
            double? longitude = null,
            double? minDistanceMeters = null)
        {
            var stopwatch = Stopwatch.StartNew();
            _logger.LogInformation("[HOME] Getting recent routes for user: {UserId}, phone: {PhoneNumber}", userId, phoneNumber);

            var result = new List<RecommendedRoute>();
            const int taskTimeoutMs = 2000; // Giảm timeout xuống 2 giây cho home screen

            try
            {
                // Tạo đối tượng vị trí nếu có tọa độ
                LatLonGeoLocation? currentLocation = null;
                if (latitude.HasValue && longitude.HasValue)
                {
                    currentLocation = new LatLonGeoLocation
                    {
                        Lat = latitude.Value,
                        Lon = longitude.Value
                    };
                }

                // Gọi service với timeout ngắn hơn
                using var cts = new CancellationTokenSource(taskTimeoutMs);
                var placePairRoutesTask = _placePairsService.GetRecentRoutesWithPairedPlaces(
                    userId, phoneNumber, limit, page, currentLocation, minDistanceMeters, cts.Token);

                // Chờ với timeout ngắn
                var timeoutTask = Task.Delay(taskTimeoutMs, cts.Token);
                var completedTask = await Task.WhenAny(placePairRoutesTask, timeoutTask);

                if (completedTask == timeoutTask || !placePairRoutesTask.IsCompleted)
                {
                    _logger.LogWarning("[HOME] GetRecentRoutesWithPairedPlaces timed out after {Timeout}ms", taskTimeoutMs);
                    return result;
                }

                var placePairRoutes = await placePairRoutesTask;
                _logger.LogInformation("[HOME] Found {Count} place pair routes from service", placePairRoutes.Count);

                if (placePairRoutes.Count == 0)
                {
                    return result;
                }

                // Xử lý nhanh các route - chỉ lấy những route hợp lệ
                var validRoutes = placePairRoutes
                    .Where(route => route?.Pickup?.PlaceId != null && route?.Arrival?.PlaceId != null)
                    .OrderByDescending(x => x.LastSearchAt)
                    .ThenByDescending(x => x.LastBookingAt)
                    .Take(limit) // Giới hạn ngay từ đầu để tiết kiệm thời gian xử lý
                    .ToList();

                // Xử lý song song nhưng đơn giản hóa
                result = validRoutes
                    .AsParallel()
                    .WithDegreeOfParallelism(Math.Min(3, validRoutes.Count)) // Giới hạn độ song song
                    .Select(x =>
                    {
                        // Tạo mô tả đơn giản
                        string desc = IsVietnamese(lang) ? "Lộ trình gần đây" : "Recent route";
                        if (x is { LastBookingAt: not null, BookingCount: > 1 })
                        {
                            desc = IsVietnamese(lang)
                                ? $"Đã đặt {x.BookingCount} lần"
                                : $"Booked {x.BookingCount} times";
                        }

                        double rawDistance = 0;
                        double roundedDistance = 0;
                        if (currentLocation != null)
                        {

                            rawDistance = H3Helper.CalculateDistance(currentLocation.Lat, currentLocation.Lon, x.Arrival.Location.Lat, x.Arrival.Location.Lon) / 1000;

                            // Làm tròn khoảng cách đến 0.1km (100m)
                            roundedDistance = Math.Round(rawDistance, 1);

                        }

                        return new RecommendedRoute
                        {
                            Origin = new RecommendedPlaceLocation
                            {
                                PlaceId = x.Pickup.PlaceId,
                                Location = new LocationDto
                                {
                                    Lat = x.Pickup.Location?.Lat ?? 0,
                                    Lon = x.Pickup.Location?.Lon ?? 0
                                },
                                Address = ExtractAddress(x.Pickup.FullAddress ?? ""),
                                FullAddress = x.Pickup.FullAddress ?? ""
                            },
                            Destination = new RecommendedPlaceLocation
                            {
                                PlaceId = x.Arrival.PlaceId,
                                Location = new LocationDto
                                {
                                    Lat = x.Arrival.Location?.Lat ?? 0,
                                    Lon = x.Arrival.Location?.Lon ?? 0
                                },
                                Address = ExtractAddress(x.Arrival.FullAddress ?? ""),
                                FullAddress = x.Arrival.FullAddress ?? "",
                                Distance = rawDistance
                            },
                            Distance = roundedDistance,
                            EstimateAmount = x.LastEstimateAmount ?? "",
                            Note = x.LastNote ?? "",
                            Desc = desc,
                            Seq = 1 // Ưu tiên cao nhất cho lộ trình gần đây
                        };
                    })
                    .ToList();

                _logger.LogInformation("[HOME] Processed {Count} recent routes in {ElapsedMs}ms",
                    result.Count, stopwatch.ElapsedMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[HOME] Error getting user recent routes: {Message}", ex.Message);
                return result;
            }
        }

        /// <summary>
        /// Chuẩn bị current location origin một cách async
        /// </summary>
        private async Task<RecommendedPlaceLocation?> PreparateCurrentLocationAsync(
            string userId, string phoneNumber, double latitude, double longitude,
            List<RecommendedRoute> recentRoutes, string lang, int timeoutMs = 500)
        {
            try
            {
                var currentLocationOrigin = recentRoutes.OrderBy(x => x.Distance).FirstOrDefault()?.Origin;

                if (currentLocationOrigin?.Distance > 1.0) //Nếu điểm đón xa hơn vị trí hiện tại > 1km thì gọi nearyBy lấy điểm gần
                {
                    using var cts = new CancellationTokenSource(timeoutMs);
                    var nearByPlaces = await _nearbyService.SearchNearbyAsync(userId, phoneNumber, latitude, longitude, null, 1, null, 500);
                    var firstNearBy = nearByPlaces.FirstOrDefault();
                    var currentLocation = IsVietnamese(lang) ? "Vị trí hiện tại" : "Current location";
                    currentLocationOrigin = new RecommendedPlaceLocation
                    {
                        PlaceId = firstNearBy?.PlaceId ?? "current_location",
                        Location = new LocationDto { Lat = latitude, Lon = longitude },
                        Address = firstNearBy?.Address ?? currentLocation,
                        FullAddress = firstNearBy?.FullAddress ?? currentLocation,
                    };

                    if (!currentLocationOrigin.FullAddress.StartsWith(currentLocationOrigin.Address))
                    {
                        currentLocationOrigin.FullAddress = $"{currentLocationOrigin.Address}, {currentLocationOrigin.FullAddress}";
                    }
                }

                return currentLocationOrigin;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[HOME] Error preparing current location: {Message}", ex.Message);
                return null;
            }
        }

        /// <summary>
        /// Lấy các điểm đến phổ biến từ vị trí hiện tại cho màn hình home
        /// </summary>
        private async Task<List<RecommendedPlaceLocation>> GetPopularDestinationsAsync(string userId,
            string phoneNumber,
            double latitude,
            double longitude,
            int? h3Resolution,
            int limit,
            double? minRadiusMeters = null,
            List<RecommendedRoute>? excludedRoutes = null,
            string lang = "en",
            int timeoutMs = 1500)
        {
            var stopwatch = Stopwatch.StartNew();
            _logger.LogInformation("[HOME] Getting popular destinations for location: {Lat}, {Lon}", latitude, longitude);

            try
            {
                // Tối ưu: Giảm bán kính tối đa để tăng tốc
                var maxRadiusInMeters = 15 * 1000; // Giảm từ 30km xuống 15km
                h3Resolution ??= H3Helper.GetH3ResolutionFromRadiusInMeters(maxRadiusInMeters);

                var result = new List<RecommendedPlaceLocation>();

                using var cts = new CancellationTokenSource(timeoutMs);

                // Tìm kiếm địa điểm gần đó với timeout
                var nearbyPlacesTask = _nearbyService.SearchNearbyAsync(
                    userId, phoneNumber, latitude, longitude, h3Resolution,
                    Math.Min(15, limit), // Lấy ít hơn để xử lý nhanh
                    popular: true, maxRadiusInMeters, minRadiusMeters);

                var timeoutTask = Task.Delay(timeoutMs, cts.Token);
                var completedTask = await Task.WhenAny(nearbyPlacesTask, timeoutTask);

                if (completedTask == timeoutTask || !nearbyPlacesTask.IsCompleted)
                {
                    _logger.LogWarning("[HOME] SearchNearbyAsync timed out after {Timeout}ms", timeoutMs);
                    return result;
                }

                var nearbyPlaces = await nearbyPlacesTask;

                // Tạo set các place id đã có để lọc nhanh
                var excludedPlaceIds = new HashSet<string>();
                if (excludedRoutes != null)
                {
                    foreach (var route in excludedRoutes)
                    {
                        excludedPlaceIds.Add(route.Destination.PlaceId);
                    }
                }

                result = nearbyPlaces
                    .Where(p =>
                        (minRadiusMeters == null || p.Distance >= minRadiusMeters) && // Địa điểm không quá gần
                        !excludedPlaceIds.Contains(p.PlaceId)// Không trùng với lộ trình đã có
                        ) // Tên không bắt đầu bằng số
                    .OrderByDescending(p => p.Popular) // Ưu tiên địa điểm phổ biến
                    .ThenByDescending(p => !string.IsNullOrEmpty(p.Name) && // Có tên
                                          !char.IsDigit(p.Name[0])) // Ưu tiên địa điểm phổ biến
                    .ThenBy(p => p.Distance) // Sau đó ưu tiên gần hơn
                    .Take(limit) // Giới hạn kết quả
                    .Select(place => new RecommendedPlaceLocation
                    {
                        PlaceId = place.PlaceId,
                        Location = new LocationDto
                        {
                            Lat = place.Location?.Lat ?? 0,
                            Lon = place.Location?.Lon ?? 0
                        },
                        Address = !string.IsNullOrWhiteSpace(place.Name) ? place.Name : ExtractAddress(place.FullAddress),
                        FullAddress = !string.IsNullOrWhiteSpace(place.Address) && !place.FullAddress.StartsWith(place.Address)
                            ? place.Address + ", " + place.FullAddress
                            : place.FullAddress
                    })
                    .ToList();

                _logger.LogInformation("[HOME] Found {Count} popular destinations in {ElapsedMs}ms",
                    result.Count, stopwatch.ElapsedMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[HOME] Error getting popular destinations: {Message}", ex.Message);
                return new List<RecommendedPlaceLocation>();
            }
        }

        /// <summary>
        /// Lấy các lộ trình thường xuyên sử dụng vào thời điểm tương tự trong ngày cho màn hình home
        /// </summary>
        private async Task<List<RecommendedRoute>> GetFrequentRoutesByTimeOfDayAsync(
            string userId,
            string phoneNumber,
            DateTime currentTime,
            int limit,
            string lang = "en",
            double? latitude = null,
            double? longitude = null,
            int timeoutMs = 3000)
        {
            var stopwatch = Stopwatch.StartNew();
            var hourOfDay = currentTime.Hour;
            var dayOfWeek = (int)currentTime.DayOfWeek;

            _logger.LogInformation("[HOME] Finding frequent routes for hour {Hour}, day {Day}", hourOfDay, dayOfWeek);

            try
            {
                var geoLocation = latitude.HasValue && longitude.HasValue ?
                    new LatLonGeoLocation { Lat = latitude.Value, Lon = longitude.Value } : null;

                using var cts = new CancellationTokenSource(timeoutMs);

                // Gọi service với timeout
                var placePairRoutesTask = _placePairsService.GetFrequentRoutesByTimeAndDay(
                    userId, phoneNumber, hourOfDay, dayOfWeek, limit, geoLocation, cts.Token);

                var timeoutTask = Task.Delay(timeoutMs, cts.Token);
                var completedTask = await Task.WhenAny(placePairRoutesTask, timeoutTask);

                if (completedTask == timeoutTask || !placePairRoutesTask.IsCompleted)
                {
                    _logger.LogWarning("[HOME] GetFrequentRoutesByTimeAndDay timed out after {Timeout}ms", timeoutMs);
                    return new List<RecommendedRoute>();
                }

                var placePairRoutes = await placePairRoutesTask;

                // Nếu không có kết quả cho giờ cụ thể, thử mở rộng khung giờ nhanh
                if (placePairRoutes.Count == 0)
                {
                    _logger.LogInformation("[HOME] No routes found for exact hour, trying extended time window");

                    var expandedTimeoutMs = Math.Min(timeoutMs / 2, 750);
                    using var expandedCts = new CancellationTokenSource(expandedTimeoutMs);

                    var expandedTask = _placePairsService.GetFrequentRoutesByTimeRange(
                        userId, phoneNumber, hourOfDay - 1, hourOfDay + 1, dayOfWeek,
                        limit, geoLocation, expandedCts.Token);

                    var expandedTimeoutTask = Task.Delay(expandedTimeoutMs, expandedCts.Token);
                    var expandedCompletedTask = await Task.WhenAny(expandedTask, expandedTimeoutTask);

                    if (expandedCompletedTask == expandedTimeoutTask || !expandedTask.IsCompleted)
                    {
                        _logger.LogWarning("[HOME] Extended time search also timed out");
                        return new List<RecommendedRoute>();
                    }

                    placePairRoutes = await expandedTask;
                }

                if (placePairRoutes.Count == 0)
                {
                    return new List<RecommendedRoute>();
                }

                // Xử lý đơn giản và nhanh
                var result = placePairRoutes
                    .Where(route => route?.Pickup?.PlaceId != null && route?.Arrival?.PlaceId != null)
                    .Take(limit) // Giới hạn ngay từ đầu
                    .Select(route => new RecommendedRoute
                    {
                        Origin = new RecommendedPlaceLocation
                        {
                            PlaceId = route.Pickup.PlaceId,
                            Location = new LocationDto
                            {
                                Lat = route.Pickup.Location?.Lat ?? 0,
                                Lon = route.Pickup.Location?.Lon ?? 0
                            },
                            Address = ExtractAddress(route.Pickup.FullAddress ?? ""),
                            FullAddress = route.Pickup.FullAddress ?? ""
                        },
                        Destination = new RecommendedPlaceLocation
                        {
                            PlaceId = route.Arrival.PlaceId,
                            Location = new LocationDto
                            {
                                Lat = route.Arrival.Location?.Lat ?? 0,
                                Lon = route.Arrival.Location?.Lon ?? 0
                            },
                            Address = ExtractAddress(route.Arrival.FullAddress ?? ""),
                            FullAddress = route.Arrival.FullAddress ?? ""
                        },
                        EstimateAmount = route.LastEstimateAmount ?? "",
                        Desc = IsVietnamese(lang)
                            ? $"Lộ trình thường dùng {GetTimeOfDayText(hourOfDay, lang)}"
                            : $"Frequent route {GetTimeOfDayText(hourOfDay, lang)}"
                    })
                    .ToList();

                _logger.LogInformation("[HOME] Found {Count} frequent routes by time in {ElapsedMs}ms",
                    result.Count, stopwatch.ElapsedMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[HOME] Error getting frequent routes by time: {Message}", ex.Message);
                return new List<RecommendedRoute>();
            }
        }

        #endregion

        #region Helper Methods - Optimized for Home Screen

        /// <summary>
        /// Lấy thông tin chi tiết của nhiều địa điểm theo place id (Tối ưu cho home screen)
        /// </summary>
        private async Task<Dictionary<string, PlacesEs>> GetESPlaceDetailsBatchAsync(List<string> placeIds)
        {
            if (!placeIds.Any())
            {
                return new Dictionary<string, PlacesEs>();
            }

            var result = new Dictionary<string, PlacesEs>();
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.LogInformation("[HOME] Getting details for {Count} places", placeIds.Count);

                // Lấy thông tin place bằng cách truyền toàn bộ danh sách placeId
                var places = await GetESPlace(placeIds.Distinct().ToArray());

                // Thêm kết quả vào dictionary
                if (places.Item2?.Any() == true)
                {
                    foreach (var place in places.Item2)
                    {
                        result[place.PlaceId] = place;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[HOME] Error getting place details: {Message}", ex.Message);
            }
            finally
            {
                stopwatch.Stop();
                _logger.LogInformation("[HOME] Retrieved {Count}/{TotalRequested} place details in {ElapsedMs}ms",
                    result.Count, placeIds.Count, stopwatch.ElapsedMilliseconds);
            }

            return result;
        }

        /// <summary>
        /// Cập nhật thông tin chi tiết cho các route (Tối ưu cho home screen)
        /// </summary>
        private async Task EnrichRoutesWithPlaceDetailsAsync(List<RecommendedRoute> routes)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                // Lấy danh sách place id duy nhất
                var allPlaceIds = routes
                    .SelectMany(route => new[]
                    {
                        route.Origin.PlaceId != "current_location" ? route.Origin.PlaceId : null,
                        route.Destination.PlaceId
                    })
                    .Where(id => !string.IsNullOrEmpty(id))
                    .Distinct()
                    .ToList();

                if (allPlaceIds.Count == 0)
                {
                    return;
                }

                // Lấy thông tin chi tiết
                var placeDetails = await GetESPlaceDetailsBatchAsync(allPlaceIds);

                // Cập nhật thông tin cho các route
                foreach (var route in routes)
                {
                    // Cập nhật origin
                    if (!string.IsNullOrEmpty(route.Origin.PlaceId) &&
                        route.Origin.PlaceId != "current_location" &&
                        placeDetails.TryGetValue(route.Origin.PlaceId, out var originDetails))
                    {
                        route.Origin.Address = originDetails.Name?.Trim() ?? route.Origin.Address;
                        route.Origin.FullAddress = originDetails.FullAddress ?? route.Origin.FullAddress;
                        if (!route.Origin.FullAddress.StartsWith(route.Origin.Address))
                        {
                            route.Origin.FullAddress = $"{route.Origin.Address}, {route.Origin.FullAddress}".Trim();
                        }
                    }

                    // Cập nhật destination
                    if (!string.IsNullOrEmpty(route.Destination.PlaceId) &&
                        placeDetails.TryGetValue(route.Destination.PlaceId, out var destDetails))
                    {
                        route.Destination.Address = destDetails.Name?.Trim() ?? route.Destination.Address;
                        route.Destination.FullAddress = destDetails.FullAddress ?? route.Destination.FullAddress;

                        if (!route.Destination.FullAddress.StartsWith(route.Destination.Address))
                        {
                            route.Destination.FullAddress = $"{route.Destination.Address}, {route.Destination.FullAddress}".Trim();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[HOME] Error enriching routes: {Message}", ex.Message);
            }
            finally
            {
                stopwatch.Stop();
                _logger.LogInformation("[HOME] Place details enriched in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            }
        }

        #endregion
    }
}