using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Table("districts1")]
public partial class Districts1
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("code")]
    [StringLength(254)]
    public string? Code { get; set; }

    [Column("name")]
    [StringLength(254)]
    public string? Name { get; set; }

    [Column("provinceCode")]
    [StringLength(254)]
    public string? ProvinceCode { get; set; }

    [Column("type")]
    public int? Type { get; set; }

    [Column("boundary")]
    public string? Boundary { get; set; }

    [Column("no_space_name")]
    [StringLength(254)]
    public string? NoSpaceName { get; set; }

    [Column("center_location")]
    [StringLength(254)]
    public string? CenterLocation { get; set; }

    [Column("processed")]
    public bool? Processed { get; set; }

    [Column("boundary_geom")]
    public string? BoundaryGeom { get; set; }

    [Column("province_code")]
    [StringLength(254)]
    public string? ProvinceCode1 { get; set; }
}
