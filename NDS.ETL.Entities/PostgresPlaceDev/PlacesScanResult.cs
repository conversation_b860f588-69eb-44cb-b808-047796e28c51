using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Table("places_scan_result")]
public partial class PlacesScanResult
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("placeid")]
    [StringLength(500)]
    public string? Placeid { get; set; }

    [Column("source_placeid")]
    [StringLength(500)]
    public string? SourcePlaceid { get; set; }

    [Column("hangfire_job_id", TypeName = "character varying")]
    public string? HangfireJobId { get; set; }

    [Column("compare_score_max")]
    public decimal CompareScoreMax { get; set; }

    [Column("total_scans")]
    public int TotalScans { get; set; }

    [Column("create_at", TypeName = "timestamp without time zone")]
    public DateTime CreateAt { get; set; }

    [Column("update_at", TypeName = "timestamp without time zone")]
    public DateTime UpdateAt { get; set; }

    [Column("scan_type", TypeName = "character varying")]
    public string? ScanType { get; set; }

    [Column("note")]
    public string? Note { get; set; }

    [Column("reason")]
    [StringLength(200)]
    public string? Reason { get; set; }

    [Column("reason_operations")]
    public List<int>? ReasonOperations { get; set; }

    [Column("scan_processed")]
    public int? ScanProcessed { get; set; }
}
