using System;

namespace NDS.ETL.BackgroundJobs.Model.ElasticSearch.FavPlaceSync
{
    /// <summary>
    /// Lưu trữ thống kê về quá trình đồng bộ dữ liệu
    /// </summary>
    public class SyncStats
    {
        /// <summary>
        /// Tổng số bản ghi cần xử lý
        /// </summary>
        public int TotalCount { get; set; }
        
        /// <summary>
        /// Số bản ghi đã xử lý thành công
        /// </summary>
        public int TotalProcessed { get; set; }
        
        /// <summary>
        /// Số batch xử lý thành công
        /// </summary>
        public int BatchSuccessCount { get; set; }
        
        /// <summary>
        /// Số batch xử lý thất bại
        /// </summary>
        public int BatchFailureCount { get; set; }
        
        /// <summary>
        /// Số lần thử lại
        /// </summary>
        public int RetryCount { get; set; }
    }
    
    /// <summary>
    /// Thông tin trạng thái của index
    /// </summary>
    public class IndexState
    {
        /// <summary>
        /// Số lượng document trong index
        /// </summary>
        public long DocumentCount { get; set; }

        /// <summary>
        /// Thời gian tạo index
        /// </summary>
        public DateTime CreationTime { get; set; }
    }
} 