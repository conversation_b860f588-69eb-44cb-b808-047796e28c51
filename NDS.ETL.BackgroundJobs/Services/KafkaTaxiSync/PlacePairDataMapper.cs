using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch.Dto;
using NDS.ETL.BackgroundJobs.Model.KafkaSyncModel;
using NDS.ETL.Infrastructure.Utils;
using Newtonsoft.Json.Linq;
using System;
using NetTopologySuite.Geometries;

namespace NDS.ETL.BackgroundJobs.Services.KafkaTaxiSync;

// Lớp ánh xạ dữ liệu từ Ka<PERSON>ka sang ElasticSearch cho cặp địa điểm (PlacePair).
// Chuyển đổi dữ liệu lịch sử di chuyển từ định dạng Kafka sang định dạng ElasticSearch.
// Tạo ra hai bản ghi ElasticSearch cho mỗi cặp địa điểm - một cho điểm xuất phát và một cho điểm đến.
public class PlacePairDataMapper
{
    public static List<PlacePairEs> MapToPlacePairEs(JToken data)
    {
        // Deserialize the data from JToken
        var historyPlace = data.ToObject<KafkaHistoryPlaceModel>();
        if (historyPlace == null)
        {
            return null;
        }

        var pairDto = new BookingPlacePairDto
        {
            Id = historyPlace.Id,
            CustomerId = historyPlace.CustomerId,
            CustomerPhone = historyPlace.CustomerPhone,
            PickupPlaceId = historyPlace.PickupPlaceId,
            ArrivalPlaceId = historyPlace.ArrivalPlaceId,
            // Chuyển đổi Location1 và Location2 sang Point đúng định dạng
            Location1 = historyPlace.Location1 is { Length: >= 2 } 
                ? new Point(historyPlace.Location1[0], historyPlace.Location1[1]) 
                : null,
            Location2 = historyPlace.Location2 is { Length: >= 2 } 
                ? new Point(historyPlace.Location2[0], historyPlace.Location2[1]) 
                : null,
            FromAddress = historyPlace.FromAddress,
            ToAddress = historyPlace.ToAddress,
            FromFullAddress = historyPlace.FromFullAddress,
            ToFullAddress = historyPlace.ToFullAddress,
            FromCity = historyPlace.FromCity,
            ToCity = historyPlace.ToCity,
            Distance = historyPlace.Distance,
            BookingCount = historyPlace.BookingCount,
            SearchCount = historyPlace.SearchCount,
            // Sử dụng DateTimeHelper để chuyển đổi timestamp sang DateTime local
            CreatedAt = DateTimeHelper.FromUnixTimeMillisecondsToLocal(historyPlace.CreatedAt),
            UpdateAt = DateTimeHelper.FromUnixTimeMillisecondsToLocal(historyPlace.UpdateAt),
            LastBookingAt = DateTimeHelper.FromUnixTimeMillisecondsToLocal(historyPlace.LastBookingAt),
            LastSearchAt = DateTimeHelper.FromUnixTimeMillisecondsToLocal(historyPlace.LastSearchAt),
            LastEstimateAmount = historyPlace.LastEstimateAmount,
            LastNote = historyPlace.LastNote
        };

        return
        [
            PlacePairEs.CreateHistoryBooking(pairDto, true),
            PlacePairEs.CreateHistoryBooking(pairDto, false)
        ];
    }
} 