using NDS.ETL.BackgroundJobs.Model.ElasticSearch;

namespace NDS.ETL.BackgroundJobs.Services.ES
{
    /// <summary>
    /// Service interface cho chức năng gợi ý địa điểm và lộ trình trên Elasticsearch
    /// </summary>
    public interface IESRecommendationService
    {
        /// <summary>
        /// Lấy địa điểm gợi ý cho người dùng
        /// </summary>
        /// <param name="userId">ID của người dùng</param>
        /// <param name="phoneNumber">Số điện thoại người dùng</param>
        /// <param name="latitude">Vĩ độ hiện tại (tùy chọn)</param>
        /// <param name="longitude">Kinh độ hiện tại (tùy chọn)</param>
        /// <param name="h3Resolution"><PERSON><PERSON> phân giải H3 (gi<PERSON> trị mặc định là 8)</param>
        /// <param name="limit"><PERSON><PERSON> lượng kết quả tối đa</param>
        /// <param name="page">Trang kết quả (bắt đầu từ 0)</param>
        /// <param name="lang">Ngôn ngữ hiển thị (vi/vn cho Tiếng Việt, mặc định là Tiếng Anh)</param>
        /// <returns>Danh sách địa điểm được gợi ý</returns>
        Task<List<RecommendedPlace>> GetRecommendedPlacesAsync(string userId,
            string phoneNumber,
            double? latitude = null,
            double? longitude = null,
            int? h3Resolution = 8,
            int limit = 10,
            int page = 0,
            string lang = "en");

        /// <summary>
        /// Lấy gợi ý tìm kiếm cho người dùng
        /// </summary>
        /// <param name="userId">ID của người dùng</param>
        /// <param name="phoneNumber">Số điện thoại người dùng</param>
        /// <param name="latitude">Vĩ độ hiện tại (tùy chọn)</param>
        /// <param name="longitude">Kinh độ hiện tại (tùy chọn)</param>
        /// <param name="h3Resolution">Độ phân giải H3 (giá trị mặc định là 8)</param>
        /// <param name="limit">Số lượng kết quả tối đa</param>
        /// <param name="lang">Ngôn ngữ hiển thị (vi/vn cho Tiếng Việt, mặc định là Tiếng Anh)</param>
        /// <returns>Danh sách gợi ý tìm kiếm</returns>
        Task<RecommendSearchItem> GetRecommendedSearchItemsAsync(string userId,
            string phoneNumber,
            double? latitude = null,
            double? longitude = null,
            int? h3Resolution = null,
            int limit = 5,
            string lang = "en");

        /// <summary>
        /// Lấy lộ trình gợi ý cho người dùng
        /// </summary>
        /// <param name="userId">ID của người dùng</param>
        /// <param name="phoneNumber">Số điện thoại người dùng</param>
        /// <param name="latitude">Vĩ độ hiện tại (tùy chọn)</param>
        /// <param name="longitude">Kinh độ hiện tại (tùy chọn)</param>
        /// <param name="h3Resolution">Độ phân giải H3 (giá trị mặc định là 8)</param>
        /// <param name="limit">Số lượng kết quả tối đa</param>
        /// <param name="page">Trang kết quả (bắt đầu từ 0)</param>
        /// <param name="lang">Ngôn ngữ hiển thị (vi/vn cho Tiếng Việt, mặc định là Tiếng Anh)</param>
        /// <returns>Danh sách lộ trình được gợi ý</returns>
        Task<List<RecommendedRoute>> GetRecommendedRoutesAsync(string userId,
            string phoneNumber,
            double? latitude = null,
            double? longitude = null,
            int? h3Resolution = null,
            int limit = 10,
            int page = 0,
            string lang = "en");
    }
} 