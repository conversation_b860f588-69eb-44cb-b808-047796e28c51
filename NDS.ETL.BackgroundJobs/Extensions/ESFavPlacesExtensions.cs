using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Core.Search;
using Elastic.Clients.Elasticsearch.QueryDsl;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;

namespace NDS.ETL.BackgroundJobs.Extensions
{
    /// <summary>
    /// Extension methods for the Elasticsearch client to simplify common query patterns
    /// </summary>
    public static class ESFavPlacesExtensions
    {
        /// <summary>
        /// Tạo các điều kiện tìm kiếm cho truy vấn địa điểm ưa thích
        /// </summary>
        /// <param name="keyword">Từ khóa tìm kiếm gốc</param>
        /// <param name="normalizedKeyword">Từ khóa đã được chuẩn hóa</param>
        /// <param name="query">Query descriptor</param>
        /// <param name="forceSearch">Search hỗ trợ sai chính tả</param>
        /// <returns><PERSON><PERSON> sách các điều kiện tìm kiếm</returns>
        public static QueryDescriptor<FavPlaceEs> GetSearchConditions(string keyword, string normalizedKeyword, QueryDescriptor<FavPlaceEs> query, bool forceSearch = false)
        {
            string[] terms = keyword.Trim().Split(' ', StringSplitOptions.RemoveEmptyEntries);
            bool isShortQuery = terms.Length <= 2;

            // Tạo các function truy vấn
            var exactMatchQuery = CreateExactMatchQuery(keyword);

            // Chỉ thêm MultiMatch và PhrasePrefix cho từ khóa dài để tránh kết quả sai lệch
            if (!isShortQuery || keyword.Length > 5 || forceSearch)
            {
                // Điều kiện 2: Tìm kiếm đa trường với từ khóa gốc
                var multiMatchQuery = CreateMultiMatchQuery(keyword, 5.0f);

                // Điều kiện 3: Tìm kiếm với tiền tố cụm từ
                var phrasePrefixQuery = CreatePhrasePrefixQuery(normalizedKeyword);

                // Sử dụng lambda để chuyển đổi hàm thành Action
                return query.Bool(b => b
                    .Should(
                        q => exactMatchQuery(q),
                        q => multiMatchQuery(q),
                        q => phrasePrefixQuery(q)
                    )
                    .MinimumShouldMatch(MinimumShouldMatch.Fixed(1)) // Yêu cầu khớp ít nhất 1 điều kiện
                );
            }
            else
            {
                // Với từ khóa ngắn, chỉ dùng khớp chính xác để tăng độ chính xác
                return query.Bool(b => b
                    .Should(q => exactMatchQuery(q))
                    .MinimumShouldMatch(MinimumShouldMatch.Fixed(1))
                );
            }
        }

        /// <summary>
        /// Tạo truy vấn khớp chính xác cho từ khóa - tối ưu cho tìm kiếm địa điểm ưa thích
        /// </summary>
        private static Func<QueryDescriptor<FavPlaceEs>, QueryDescriptor<FavPlaceEs>> CreateExactMatchQuery(string keyword)
        {
            string[] terms = keyword.Trim().Split(' ', StringSplitOptions.RemoveEmptyEntries);
            bool hasTwoOrMoreTerms = terms.Length >= 2 && terms.Any(x => x.Length > 2);

            // Nếu từ khóa quá ngắn, sử dụng các matching linh hoạt hơn
            if (terms.Length <= 2)
            {
                var shouldQueries = new List<Func<QueryDescriptor<FavPlaceEs>, QueryDescriptor<FavPlaceEs>>>();

                // Thêm các truy vấn cho từ khóa đầy đủ
                shouldQueries.Add(s => s.Prefix(p => p
                    .Field(f => f.AddressPermuted)
                    .Value(keyword)
                    .Boost(50)
                ));

                shouldQueries.Add(s => s.Wildcard(w => w
                    .Field(f => f.AddressPermuted)
                    .Value($"*{keyword}*")
                    .Boost(40)
                ));

                // Thêm truy vấn cho từng từ riêng lẻ trong từ khóa
                if (terms.Length > 1)
                {
                    // Thay thế SpanNear bằng MatchPhrase với Slop
                    shouldQueries.Add(s => s.MatchPhrase(mp => mp
                        .Field(f => f.AddressPermuted)
                        .Query(keyword)
                        .Slop(5) // Cho phép các từ cách nhau tối đa 5 vị trí
                        .Boost(60) // Ưu tiên cao nhất cho các từ xuất hiện gần nhau
                    ));

                    // Thêm truy vấn cho từng từ riêng lẻ
                    foreach (var term in terms)
                    {
                        if (term.Length >= 2) // Chỉ tìm các từ có ít nhất 2 ký tự
                        {
                            shouldQueries.Add(s => s.Prefix(p => p
                                .Field(f => f.AddressPermuted)
                                .Value(term)
                                .Boost(35)
                            ));

                            shouldQueries.Add(s => s.Wildcard(w => w
                                .Field(f => f.AddressPermuted)
                                .Value($"*{term}*")
                                .Boost(30)
                            ));
                        }
                    }

                    // Thêm truy vấn match cho từng từ với toán tử OR
                    shouldQueries.Add(s => s.Match(m => m
                        .Field(f => f.AddressPermuted)
                        .Query(keyword)
                        .Operator(Operator.Or) // Chỉ cần khớp một trong các từ
                        .MinimumShouldMatch("50%") // Yêu cầu ít nhất 50% số từ phải khớp
                        .Boost(25)
                    ));
                }

                // Thay thế Term bằng MatchPhrase để khớp chính xác
                shouldQueries.Add(s => s.MatchPhrase(mp => mp
                    .Field(f => f.AddressPermuted)
                    .Query(keyword)
                    .Boost(20)
                ));

                return q => q.Bool(b =>
                {
                    foreach (var query in shouldQueries)
                    {
                        b.Should(query(new QueryDescriptor<FavPlaceEs>()));
                    }
                    b.MinimumShouldMatch(1);
                });
            }
            else
            {
                return q => q
                    .Bool(b =>
                    {
                        b.Should(
                            // Ưu tiên cao nhất cho khớp chính xác phần đầu của địa chỉ đầy đủ
                            s => s.MatchPhrasePrefix(p => p
                                .Field(f => f.FullAddress)
                                .Query(keyword)
                                .Boost(50)
                                .MaxExpansions(5) // Giảm số lượng mở rộng cho prefix
                            ),
                            // Khớp chính xác với địa chỉ đầy đủ
                            s => s.MatchPhrase(p => p
                                .Field(f => f.FullAddress)
                                .Query(keyword)
                                .Boost(40)
                                .Slop(0) // Yêu cầu khớp chính xác thứ tự từ
                            ),
                            // Tìm kiếm với tiền tố trong địa chỉ được hoán vị
                            s => s.Prefix(p => p
                                .Field(f => f.AddressPermuted)
                                .Value(keyword)
                                .Boost(30)
                            ),
                            // Tìm kiếm cụm từ trong địa chỉ hoán vị với tiền tố
                            s => s.MatchPhrasePrefix(mpp => mpp
                                .Field(f => f.AddressPermuted)
                                .Query(keyword)
                                .Boost(20)
                                .MaxExpansions(5) // Giảm số lượng mở rộng cho prefix
                            ),
                            // Tìm kiếm cụm từ trong địa chỉ hoán vị
                            s => s.MatchPhrase(m => m
                                .Field(f => f.AddressPermuted)
                                .Query(keyword)
                                .Boost(10)
                                .Slop(0) // Khớp chính xác thứ tự từ
                            ),
                            // Chỉ dùng match khi từ khóa có ít nhất 2 từ
                            s => s.Match(m => m
                                .Field(f => f.AddressPermuted)
                                .Query(keyword)
                                .Operator(Operator.And) // Tất cả các từ phải xuất hiện
                                .Boost(5)
                            ),
                            // Boost thêm cho địa điểm có số lần đặt xe nhiều
                            s => s.FunctionScore(fs =>
                            {
                                fs.Query(q => q
                                    .MatchPhrase(m => m
                                        .Field(f => f.FullAddress)
                                        .Query(keyword)
                                    )
                                )
                                .Functions(f => f
                                    .FieldValueFactor(fvf => fvf
                                        .Field(ff => ff.BookingCount)
                                        .Factor(0.5)
                                        .Modifier(FieldValueFactorModifier.Log1p)
                                        .Missing(1)
                                    )
                                )
                                .BoostMode(FunctionBoostMode.Multiply)
                                .ScoreMode(FunctionScoreMode.Sum);
                            })
                        );
                        b.MinimumShouldMatch(hasTwoOrMoreTerms ? 2 : 1); // Tăng MinimumShouldMatch khi có 2 từ trở lên
                    });
            }
        }

        /// <summary>
        /// Tạo truy vấn đa trường cho từ khóa
        /// </summary>
        private static Func<QueryDescriptor<FavPlaceEs>, QueryDescriptor<FavPlaceEs>> CreateMultiMatchQuery(string query, float boost = 1.0f)
        {
            string[] terms = query.Trim().Split(' ', StringSplitOptions.RemoveEmptyEntries);

            // Nếu chỉ có 1 từ ngắn, sử dụng Match thay vì Term để tránh kết quả sai lệch
            if (terms.Length == 1 && terms[0].Length <= 3)
            {
                return s => s.Match(m => m
                    .Field(f => f.AddressPermuted)
                    .Query(query)
                    .Operator(Operator.And)
                    .Boost(boost)
                );
            }

            // Nếu chỉ có 1 từ dài hơn, sử dụng Match chính xác
            if (terms.Length == 1)
            {
                return s => s.Match(m => m
                    .Field(f => f.AddressPermuted)
                    .Query(query)
                    .Operator(Operator.And)
                    .Boost(boost)
                );
            }

            // Nếu có nhiều từ, sử dụng MultiMatch với các trường phù hợp
            return s => s.MultiMatch(mm =>
            {
                mm.Query(query)
                .Fields(Fields.FromFields([
                    Field.FromString("fullAddress^5")!, // Ưu tiên cao nhất cho địa chỉ đầy đủ
                    Field.FromString("addressPermuted.suggest^2")!,
                    Field.FromString("addressPermuted^4")!, // Thay thế .keyword bằng boost cao hơn
                    Field.FromString("keywords^2")!
                ]))
                .Type(TextQueryType.BestFields) // Thay đổi từ most_fields sang best_fields
                .Operator(Operator.And)
                .MinimumShouldMatch("100%") // Phải khớp hoàn toàn
                .TieBreaker(0.3)
                .Boost(boost);
            });
        }

        /// <summary>
        /// Tạo truy vấn tiền tố cụm từ cho từ khóa
        /// </summary>
        private static Func<QueryDescriptor<FavPlaceEs>, QueryDescriptor<FavPlaceEs>> CreatePhrasePrefixQuery(string normalizedKeyword)
        {
            string[] terms = normalizedKeyword.Trim().Split(' ', StringSplitOptions.RemoveEmptyEntries);

            // Nếu từ khóa chỉ có 1 từ, sử dụng Prefix thay vì PhrasePrefix
            if (terms.Length == 1)
            {
                return s => s.Prefix(p => p
                    .Field(f => f.AddressPermuted)
                    .Value(normalizedKeyword)
                    .Boost(10.0f)
                );
            }

            // Với từ khóa dài, sử dụng PhrasePrefix với cài đặt tối ưu 
            return s => s.MultiMatch(mm =>
            {
                mm.Query(normalizedKeyword)
                .Fields(Fields.FromFields([
                    Field.FromString("fullAddress^5")!, // Ưu tiên cao nhất cho địa chỉ đầy đủ
                    Field.FromString("addressPermuted.suggest^2")!,
                    Field.FromString("addressPermuted^4")!, // Thay thế .keyword bằng boost cao hơn
                    Field.FromString("keywords^2")!
                ]))
                .Type(TextQueryType.PhrasePrefix)
                .MaxExpansions(2) // Tăng từ 1 lên 2 để cải thiện kết quả
                .Slop(1) // Cho phép linh hoạt hơn một chút trong thứ tự từ
                .Operator(Operator.And)
                .MinimumShouldMatch("100%")
                .TieBreaker(0.3)
                .Boost(10.0f);
            });
        }

        /// <summary>
        /// Creates an autocomplete search query for location data
        /// </summary>
        /// <param name="client">The Elasticsearch client</param>
        /// <param name="page">Page number (1-based)</param>
        /// <param name="userId">User ID</param>
        /// <param name="phoneNumber">Phone number</param>
        /// <param name="currentLocation">Current location as GeoLocation</param>
        /// <param name="keyword">Search keyword</param>
        /// <param name="pageSize">Number of results per page</param>
        /// <param name="indexName">Index name</param>
        /// <param name="h3Resolution">H3 resolution level (default: 8)</param>
        /// <param name="extendedRadius">Whether to use extended radius</param>
        /// <param name="forceSearch">Use fuzzy search after no results found with exact search</param>
        /// <param name="minDistanceMeters">Minimum distance in meters</param>
        /// <returns>SearchRequest for the autocomplete query</returns>
        public static SearchRequestDescriptor<FavPlaceEs> CreateFavPlaceSearchQuery(this ElasticsearchClient client,
            int page,
            string? userId,
            string? phoneNumber,
            GeoLocation? currentLocation,
            string keyword,
            int pageSize,
            string indexName,
            int? h3Resolution = null,
            bool extendedRadius = false,
            bool forceSearch = false,
            double? minDistanceMeters = null
        )
        {
            // Loại bỏ khoảng trắng thừa và chuẩn hóa từ khóa
            var cleanKeyword = keyword?.Trim() ?? string.Empty;
            var normalizedKeyword = cleanKeyword;

            LatLonGeoLocation? latlng = null;
            currentLocation?.TryGetLatitudeLongitude(out latlng); // Lấy tọa độ lat/lng từ vị trí hiện tại

            var descriptor = new SearchRequestDescriptor<FavPlaceEs>()
                .Index(indexName) // Chỉ định index cần tìm kiếm
                .Size(pageSize); // Giới hạn số lượng kết quả trả về

            // Thêm query chính
            AddMainSearchQuery(descriptor, cleanKeyword, normalizedKeyword, currentLocation, userId, phoneNumber, h3Resolution, extendedRadius, forceSearch, minDistanceMeters);

            // Thêm collapse, source config và script fields
            ConfigureResultOptions(descriptor, latlng);

            // Thêm sorting
            AddSorting(descriptor, currentLocation);

            return descriptor;
        }

        /// <summary>
        /// Thêm query chính cho tìm kiếm
        /// </summary>
        private static void AddMainSearchQuery(SearchRequestDescriptor<FavPlaceEs> descriptor, string keyword,
            string normalizedKeyword, GeoLocation? currentLocation, string? userId, string? phoneNumber,
            int? h3Resolution, bool extendedRadius, bool forceSearch = false, double? minDistanceMeters = null)
        {
            descriptor.Query(q =>
            {
                var queryDescriptor = new QueryDescriptor<FavPlaceEs>();

                // Nếu từ khóa quá ngắn (1 ký tự), trả về kết quả trống

                var searchQuery = keyword.Length <= 1 ? null : GetSearchConditions(keyword, normalizedKeyword, queryDescriptor, forceSearch);

                q.Bool(b =>
                {

                    if (searchQuery != null) b.Must(searchQuery);

                    void filterConfig(QueryDescriptor<FavPlaceEs> fq)
                    {
                        if (!string.IsNullOrWhiteSpace(userId))
                        {
                            // Thay thế Term bằng Match cho UserId
                            fq.Match(m => m
                                .Field(x => x.UserId!.Suffix("keyword"))
                                .Query(userId)
                                .Operator(Operator.And));
                        }
                        else if (!string.IsNullOrWhiteSpace(phoneNumber))
                        {
                            // Thay thế Term bằng Match cho PhoneNumber
                            fq.Match(m => m
                                .Field(x => x.PhoneNumber!.Suffix("keyword"))
                                .Query(phoneNumber)
                                .Operator(Operator.And));
                        }
                    }

                    // Tạo danh sách các điều kiện filter
                    var filterConditions = new List<Action<QueryDescriptor<FavPlaceEs>>>();
                    
                    // Thêm filter user/phone
                    filterConditions.Add(filterConfig);

                    // Bỏ qua GeoDistance filter nếu extendedRadius = true
                    if (currentLocation != null)
                    {
                        //// Lấy tọa độ lat/lng từ vị trí hiện tại
                        currentLocation.TryGetLatitudeLongitude(out var latlng);

                        if (h3Resolution.HasValue && !extendedRadius)
                        {
                            // Tính toán bán kính từ h3Resolution
                            double radiusInKm = H3Helper.GetRadiusKmFromH3Res(h3Resolution.Value); //50km

                            var h3CellsInRadius = H3Helper.GetH3CellsInRadius(latlng.Lat, latlng.Lon, radiusInKm, out var res);

                            // Thêm H3 filter
                            filterConditions.Add(mu => mu.Terms(t => t
                                .Field($"h3.{res}.keyword")
                                .Terms(new TermsQueryField(h3CellsInRadius.Select(h3Idx => (FieldValue)h3Idx).ToArray()))
                            ));

                            // Thêm max distance filter
                            filterConditions.Add(mu => mu.GeoDistance(g => g
                                .Field(p => p.Location)
                                .Distance($"{radiusInKm}km") // Bán kính tối đa
                                .Location(currentLocation)
                            ));
                            
                        }

                        // Thêm minimum distance filter nếu có
                        if (minDistanceMeters is > 0)
                        {
                            filterConditions.Add(mu => mu.Script(s => s
                                .Script(ss => ss
                                    .Source("doc['location'].arcDistance(params.lat, params.lon) > params.radius")
                                    .Params(p => p
                                        .Add("lat", latlng.Lat)
                                        .Add("lon", latlng.Lon)
                                        .Add("radius", minDistanceMeters.Value)
                                    )
                                )
                            ));
                        }
                    }

                    // Áp dụng tất cả các filter conditions
                    b.Filter(f => f.Bool(bb => bb.Must(filterConditions.ToArray())));
                });
            });
        }

        /// <summary>
        /// Cấu hình các tùy chọn kết quả
        /// </summary>
        private static void ConfigureResultOptions(SearchRequestDescriptor<FavPlaceEs> descriptor, LatLonGeoLocation? latlng)
        {
            descriptor
                .Collapse(c => c.Field(x => x.FullAddress!.Suffix("keyword")))
                //.Source(new SourceConfig(true)) // Trả về toàn bộ thông tin
                .SourceExcludes(Fields.FromFields(
                    [
                        "h3",
                        "addressPermuted",
                        "masterAddress",
                        "keywords"
                    ]
                )); // Loại bỏ các trường không cần thiết trong kết quả

            //if (latlng != null)
            //{

            //    descriptor.ScriptFields(sf => // Thêm trường tính toán
            //        sf.Add("distance", fd =>
            //        {
            //            fd.Script(s => s
            //                .Source("doc['location'].arcDistance(params.lat, params.lon)/1000") // Tính khoảng cách theo km
            //                .Params(p => p
            //                        .Add("lat", latlng.Lat) // Truyền lat vào params
            //                        .Add("lon", latlng.Lon) // Truyền lon vào params
            //                )
            //            );
            //        })
            //    );
            //}
        }

        /// <summary>
        /// Thêm sắp xếp kết quả
        /// </summary>
        private static void AddSorting(SearchRequestDescriptor<FavPlaceEs> descriptor, GeoLocation? currentLocation)
        {
            List<Action<SortOptionsDescriptor<FavPlaceEs>>> sorts = [
                x => // Sắp xếp kết quả
                {
                    x.Score(sc => sc.Order(SortOrder.Desc)); // Theo điểm số
                },
                x =>
                {
                    x.Field(f => f.BookingCount, o => o.Order(SortOrder.Desc)); // Theo số lần đặt xe (ưu tiên địa điểm đặt thường xuyên)
                }];
            if (currentLocation != null)
            {
                sorts.Add(x =>
                {
                    x.GeoDistance(g => g // Theo khoảng cách
                        .Field(p => p.Location)
                        .DistanceType(GeoDistanceType.Plane) // Tính toán theo mặt phẳng
                        .Unit(DistanceUnit.Kilometers) // Đơn vị km
                        .Order(SortOrder.Asc) // Tăng dần
                        .Location(new List<GeoLocation?> { currentLocation }!)
                    );
                });
            }

            descriptor.Sort(sorts.ToArray());
        }
    }
}
