//using System;
//using TaxiCMS.CommonEnum;

//namespace NDS.ETL.Entities.TaxiMongoDB.MongoAttributes
//{
//	[AttributeUsage(AttributeTargets.Property)]
//	public class IndexAttribute : Attribute, IEntityIndex
//	{
//		public string Name { get; private set; }
//		public bool IsUnique { get; set; }
//		public IndexSortOrder SortOrder { get; private set; }
//		public int IndexPriority { get; set; }

//		public IndexAttribute(IndexSortOrder sortOrder)
//		{
//			SortOrder = sortOrder;
//		}

//		public IndexAttribute(string name, IndexSortOrder sortOrder)
//		{
//			Name = name;
//			SortOrder = sortOrder;
//		}
//	}
//    public interface IEntityIndex
//    {
//        string Name { get; }
//        bool IsUnique { get; }
//        IndexSortOrder SortOrder { get; }
//    }
//}
