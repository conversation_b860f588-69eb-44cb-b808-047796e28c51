using System;
using System.Reflection;

namespace NDS.ETL.Entities.TaxiMongoDB.MongoAttributes
{
	[AttributeUsage(AttributeTargets.Property)]
	public class CreatedDateAttribute : MutatePropertyAttribute
	{
		public override void OnInsert(object target, PropertyInfo property)
		{
			if (property.PropertyType != typeof(DateTime))
			{
				throw new ArgumentException("Property is not of type DateTime");
			}

			property.SetValue(target, DateTime.UtcNow);
		}
	}
}
