using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Registrations;
using NDS.ETL.Infrastructure.Extensions;
using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.IndexManagement;

namespace TestProject
{
    /// <summary>
    /// Lớp hỗ trợ thiết lập môi trường test cho ElasticSearch
    /// </summary>
    public class ElasticSearchTestSetup
    {
        private readonly ElasticsearchClient _elasticClient;
        private readonly string _testIndex;
        
        // Biến tĩnh để tạo ID không trùng cho toàn bộ lớp
        private static long _idCounter = 0;

        /// <summary>
        /// Khởi tạo trình thiết lập môi trường test
        /// </summary>
        /// <param name="elasticClient">Client ElasticSearch</param>
        /// <param name="testIndex">Tên index test</param>
        public ElasticSearchTestSetup(ElasticsearchClient elasticClient, string testIndex)
        {
            _elasticClient = elasticClient ?? throw new ArgumentNullException(nameof(elasticClient));
            _testIndex = testIndex ?? throw new ArgumentNullException(nameof(testIndex));
        }

        /// <summary>
        /// Tạo index test và nạp dữ liệu mẫu
        /// </summary>
        /// <returns>True nếu thành công, False nếu thất bại</returns>
        public async Task<bool> SetupTestEnvironment()
        {
            try
            {
                // Xóa index cũ nếu tồn tại
                var existsResponse = await _elasticClient.Indices.ExistsAsync(_testIndex);
                if (existsResponse.Exists)
                {
                    var deleteResponse = await _elasticClient.Indices.DeleteAsync(_testIndex);
                    if (!deleteResponse.IsSuccess())
                    {
                        Console.WriteLine($"Lỗi khi xóa index cũ: {deleteResponse.DebugInformation}");
                        return false;
                    }
                }

                // Tạo index mới với cấu hình tương tự như trong ESRegistration
                var createResponse = await _elasticClient.Indices.CreateAsync(_testIndex, c => c
                    .Settings(s => s
                        .NumberOfShards(1) // Chỉ dùng 1 shard cho môi trường test
                        .NumberOfReplicas(0) // Không dùng replica cho môi trường test
                        .RefreshInterval("1s") // Refresh nhanh hơn cho môi trường test
                        .Translog(t => t
                            .Durability(TranslogDurability.Async)
                            .FlushThresholdSize(new ByteSize("1gb"))
                        )
                        .Codec("best_compression")
                        .Mapping(m => m
                            .TotalFields(tf => tf.Limit(500))
                            .NestedFields(nf => nf.Limit(30))
                        )
                        .Analysis(ESRegistration.ConfigureAnalysis)
                    )
                    .Mappings(m => m
                        .Properties<PlacesEs>(ESRegistration.ConfigurePropertiesSettings)
                    )
                );

                if (!createResponse.IsSuccess())
                {
                    Console.WriteLine($"Lỗi khi tạo index mới: {createResponse.DebugInformation}");
                    return false;
                }

                // Thêm dữ liệu mẫu vào index test
                var sampleData = GetSamplePlaceData();
                var bulkResponse = await _elasticClient.BulkAsync(b => b
                    .Index(_testIndex)
                    .IndexMany(sampleData)
                );

                if (!bulkResponse.IsSuccess())
                {
                    Console.WriteLine($"Lỗi khi thêm dữ liệu mẫu: {bulkResponse.DebugInformation}");
                    return false;
                }

                // Refresh index để đảm bảo dữ liệu có thể tìm kiếm ngay lập tức
                var refreshResponse = await _elasticClient.Indices.RefreshAsync(_testIndex);
                if (!refreshResponse.IsValidResponse)
                {
                    Console.WriteLine($"Lỗi khi refresh index: {refreshResponse.DebugInformation}");
                    return false;
                }

                Console.WriteLine($"Thiết lập môi trường test thành công: Đã tạo index {_testIndex} với {sampleData.Count} mẫu dữ liệu");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi thiết lập môi trường test: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
                return false;
            }
        }

        /// <summary>
        /// Tạo dữ liệu mẫu cho test
        /// </summary>
        /// <returns>Danh sách địa điểm mẫu</returns>
        private List<PlacesEs> GetSamplePlaceData()
        {
            // Tạo mảng các địa điểm mẫu để test
            var samplePlaces = new List<PlacesEs>
            {
                // Các địa điểm ở TP.HCM
                CreatePlaceEs(
                    "Quán Ăn Bắc Lợi", 
                    "Quán Ăn Bắc Lợi, Ba Tháng Hai, Phường 10, Quận 10, Thành phố Hồ Chí Minh, Việt Nam",
                    10.772031, 106.667506,
                    "nhà hàng,quán ăn,quận 10,ba tháng hai"
                ),
                CreatePlaceEs(
                    "The Coffee House", 
                    "The Coffee House, 86-88 Cao Thắng, Phường 4, Quận 3, Thành phố Hồ Chí Minh, Việt Nam",
                    10.773467, 106.680677,
                    "cà phê,coffee,quận 3,cao thắng"
                ),
                CreatePlaceEs(
                    "Green House Coffee", 
                    "Green House Coffee, 420 Nguyễn Thái Sơn, Phường 5, Gò Vấp, Thành phố Hồ Chí Minh, Việt Nam",
                    10.850121, 106.655389,
                    "cà phê,coffee,gò vấp,nguyễn thái sơn"
                ),
                CreatePlaceEs(
                    "Vincom Mega Mall Thảo Điền", 
                    "Vincom Mega Mall Thảo Điền, 161 Xa Lộ Hà Nội, Thảo Điền, Quận 2, Thành phố Hồ Chí Minh, Việt Nam",
                    10.805126, 106.744347,
                    "siêu thị,trung tâm thương mại,shopping mall,quận 2,thảo điền"
                ),
                CreatePlaceEs(
                    "Bệnh viện Quận Gò Vấp", 
                    "Bệnh viện Quận Gò Vấp, 651 Quang Trung, Phường 11, Gò Vấp, Thành phố Hồ Chí Minh, Việt Nam",
                    10.834121, 106.649323,
                    "bệnh viện,y tế,gò vấp,quang trung"
                ),
                CreatePlaceEs(
                    "Trường THPT Nguyễn Thị Minh Khai", 
                    "Trường THPT Nguyễn Thị Minh Khai, 275 Điện Biên Phủ, Phường 7, Quận 3, Thành phố Hồ Chí Minh, Việt Nam",
                    10.779233, 106.687723,
                    "trường học,thpt,quận 3,điện biên phủ"
                ),
                CreatePlaceEs(
                    "Siêu thị BigC Quận A", 
                    "Siêu thị BigC Quận 10, 222 Đường 3/2, Phường 12, Quận 10, Thành phố Hồ Chí Minh, Việt Nam",
                    10.767235, 106.664329,
                    "siêu thị,mua sắm,quận 10,ba tháng hai"
                ),
                
                // Các địa điểm ở Hà Nội
                CreatePlaceEs(
                    "Highland Coffee Láng Hạ", 
                    "Highland Coffee, 22 Láng Hạ, Láng Hạ, Đống Đa, Hà Nội, Việt Nam",
                    21.018358, 105.809268,
                    "cà phê,coffee,đống đa,láng hạ"
                ),
                CreatePlaceEs(
                    "Trường THPT Chu Văn An", 
                    "Trường THPT Chu Văn An, 10 Thuỵ Khuê, Tây Hồ, Hà Nội, Việt Nam",
                    21.044125, 105.828236,
                    "trường học,thpt,tây hồ,thụy khuê"
                ),
                CreatePlaceEs(
                    "Bệnh viện Bạch Mai", 
                    "Bệnh viện Bạch Mai, 78 Giải Phóng, Đống Đa, Hà Nội, Việt Nam",
                    21.000103, 105.841003,
                    "bệnh viện,y tế,đống đa,giải phóng"
                ),
                CreatePlaceEs(
                    "AEON Mall Long Biên", 
                    "AEON Mall Long Biên, 27 Cổ Linh, Long Biên, Hà Nội, Việt Nam",
                    21.034582, 105.901423,
                    "siêu thị,trung tâm thương mại,long biên,cổ linh"
                ),
                CreatePlaceEs(
                    "Nhà hàng Sân Đình", 
                    "Nhà hàng Sân Đình, 31 Đường Thành, Hoàn Kiếm, Hà Nội, Việt Nam",
                    21.031219, 105.853027,
                    "nhà hàng,quán ăn,hoàn kiếm,phố cổ"
                ),
                CreatePlaceEs(
                    "Siêu thị Vinmart Đống Đa", 
                    "Siêu thị Vinmart, 54 Láng Hạ, Đống Đa, Hà Nội, Việt Nam",
                    21.019125, 105.810983,
                    "siêu thị,mua sắm,đống đa,láng hạ"
                ),
                CreatePlaceEs(
                    "Trường Đại học Bách Khoa Hà Nội", 
                    "Trường Đại học Bách Khoa Hà Nội, Số 1 Đại Cồ Việt, Hai Bà Trưng, Hà Nội, Việt Nam",
                    21.007831, 105.845657,
                    "trường học,đại học,hai bà trưng,đại cồ việt"
                ),
                CreatePlaceEs(
                    "Bệnh viện Đống Đa", 
                    "Bệnh viện Đống Đa, 192 Nguyễn Lương Bằng, Đống Đa, Hà Nội, Việt Nam",
                    21.016762, 105.824581,
                    "bệnh viện,y tế,đống đa,nguyễn lương bằng"
                )
            };

            return samplePlaces;
        }

        /// <summary>
        /// Tạo đối tượng PlacesEs từ các thông tin cơ bản
        /// </summary>
        private PlacesEs CreatePlaceEs(string name, string address, double latitude, double longitude, string keywords)
        {
            // Phương thức để lấy ID tăng dần
            long GetNextId()
            {
                // Tăng giá trị biến tĩnh của lớp
                return ++_idCounter;
            }

            var placesEs = new PlacesEs
            {
                Id = GetNextId(), // Sử dụng ID kiểu long tăng dần
                Name = name,
                Location = new Location{Lat = latitude,Lon= longitude},
                Keywords = keywords,
                MasterAddress = address,
                // Tạo danh sách AddressPermuted từ địa chỉ gốc
                AddressPermuted = new List<string>()
            };

            // Tạo các biến thể địa chỉ sử dụng AddressPermutator
            string masterAddress = address;
            if (!string.IsNullOrWhiteSpace(name) && !address.Contains(name, StringComparison.OrdinalIgnoreCase))
            {
                masterAddress = $"{name}, {address}";
            }
            
            // Thêm từ khóa vào địa chỉ để tối ưu hoán vị
            if (!string.IsNullOrWhiteSpace(keywords))
            {
                masterAddress = $"{masterAddress}, {keywords}";
            }
            
            // Sử dụng AddressPermutator để tạo hoán vị tối ưu (tối đa 15 hoán vị)
            placesEs.AddressPermuted = AddressPermutator.GenerateOptimizedPermutations(masterAddress, 15);
            
            return placesEs;
        }
    }
} 