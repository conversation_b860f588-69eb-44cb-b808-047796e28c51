{
  "PathBase": "/backgroundjob",
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore.Hosting.Diagnostics": "Warning",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft": "Warning",
      "Hangfire": "Warning",
      "Microsoft.Hosting.Lifetime": "Warning",
      "Microsoft.EntityFrameworkCore": "Warning",
      "Microsoft.EntityFrameworkCore.Database.Command": "Warning",
      "Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker": "Warning"
    }
  },
  "ConnectionStrings": {
    "Redis": "*************:6379,*************:6379,*************:6379,password=redisvnpay,abortConnect=false,connectRetry=3,connectTimeout=5000,syncTimeout=5000,keepAlive=180",
    "PgPlace": "Host=************;Port=5444;Database=******;Username=******;Password=******;Timeout=600;Command Timeout=10000", //SIT
    //"PgPlace": "Host=*************;Port=5444;Database=******;Username=******;Password=******;Timeout=600;Command Timeout=10000" //UAT
    //"PgPlace": "Host=***********;Port=9432;Database=******;Username=******;Password=******;Timeout=600;Command Timeout=10000" //DEV
    "HangfireSchema": "es-backend-log"
  },
  "ElasticSearchConfig": {
    "Index": [
      "place-sit",
      "fav-place-sit",
      "user-place-sit",
      "place-pair-sit"
      //"place-uat2",
      //"fav-place-uat",
      //"user-place-uat",
      //"place-pair-uat"
    ],
    "LogIndex": "place-log-1",
    "Url": [
      "http://*************:9200/",
      "http://*************:9200/",
      "http://*************:9200/"
    ],
    "UserName": "elastic",
    "Password": ""
  },
  //  "ElasticSearchConfig": {
  //    "Index": [
  //      "place-staging",
  //      "favorite-place-staging",
  //      "booking-place-pair-staging",
  //      "user-place-history-staging"
  //    ],
  //    "LogIndex": "place-log-2",
  //    "Url": [
  //      "https://*************:9200/"
  //    ],
  //    "UserName": "elastic",
  //    "Password": "F+J0aT1XQzy8Rnc8N0Zv"
  //  },
  "KafkaConfig": {
    "Disable": "false",
    "Connection": "10.22.17.162:6091,10.22.17.162:6092,10.22.17.162:6093",
    "GroupSyncPlaces": "sync_es_places_gr",
    "TopicSyncPlaces": "sync_es_places"
  },
  "JobPgSyncPlace": [
    {
      "CronExpression": "* * 31 2 *",
      "JobId": "SyncFavPlaces",
      "Disable": false,
      "Immediately": false
    },
    {

      "CronExpression": "* * 31 2 *",
      "JobId": "SyncPlacePairs",
      "Disable": false,
      "Immediately": false
    },
    {
      "CronExpression": "*/30 * * * *",
      "JobId": "SyncPlaces",
      "Disable": false,
      "Immediately": false
    },
    {
      "CronExpression": "0 23 * * *",
      "JobId": "CompareScoreChangePlacesJob",
      "Disable": false,
      "Immediately": false
    },
    {
      "CronExpression": "30 11 04,05 * *",
      "JobId": "SyncPlaceAfterApproveJob",
      "Disable": false,
      "Immediately": false
    },
    {
      "CronExpression": "0 1 * * *",
      "JobId": "UpdateAdminUnitId",
      "Disable": false,
      "Immediately": false
    },
    {
      "CronExpression": "* * 31 2 *",
      "JobId": "UpdateAddressPermuted",
      "Disable": false,
      "Immediately": false
    },
    {
      "CronExpression": "* * 31 2 *",
      "JobId": "InitPlace",
      "Disable": false,
      "Immediately": false
    },
    {
      "CronExpression": "* * 31 2 *",
      "JobId": "SyncPlaceScanResult2",
      "Disable": false,
      "Immediately": false
    },
    {
      "CronExpression": "* * 31 2 *",
      "JobId": "SyncPlaceScanResult",
      "Disable": false,
      "Immediately": false
    },
    {
      "CronExpression": "* * 31 2 *",
      "JobId": "ScanPlaces",
      "Disable": false,
      "Immediately": false
    },
    {
      "CronExpression": "* * 31 2 *",
      "JobId": "SyncDataScannedToEs",
      "Disable": false,
      "Immediately": false
    },
    {
      "CronExpression": "* * 31 2 *",
      "JobId": "SyncUserSearch",
      "Disable": false,
      "Immediately": false
    },
    {
      "CronExpression": "* * 31 2 *",
      "JobId": "SyncDetermineAddressLevel",
      "Disable": false,
      "Immediately": false
    },
    {
      "CronExpression": "* * 31 2 *",
      "JobId": "InsertPlacesScanResult",
      "Disable": false,
      "Immediately": false
    },
    {
      "CronExpression": "* * 31 2 *",
      "JobId": "ProcessDuplicateAddress",
      "Disable": false,
      "Immediately": false
    },
    {
      "CronExpression": "* * 31 2 *",
      "JobId": "ScanPlacesOrchestrator",
      "Disable": false,
      "Immediately": false
    }
  ],
  "AllowedHosts": "*"
}