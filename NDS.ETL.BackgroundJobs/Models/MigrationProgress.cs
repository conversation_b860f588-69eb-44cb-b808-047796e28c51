namespace NDS.ETL.BackgroundJobs.Models;

/// <summary>
/// Represents the progress of an address migration operation
/// </summary>
public class MigrationProgress
{
    /// <summary>
    /// Unique identifier for the migration job
    /// </summary>
    public string MigrationId { get; set; } = string.Empty;

    /// <summary>
    /// Current status of the migration
    /// </summary>
    public MigrationStatus Status { get; set; } = MigrationStatus.NotStarted;

    /// <summary>
    /// Total number of records to be migrated
    /// </summary>
    public long TotalRecords { get; set; }

    /// <summary>
    /// Number of records successfully processed
    /// </summary>
    public long ProcessedRecords { get; set; }

    /// <summary>
    /// Number of records that failed processing
    /// </summary>
    public long FailedRecords { get; set; }

    /// <summary>
    /// Number of records skipped (already migrated)
    /// </summary>
    public long SkippedRecords { get; set; }

    /// <summary>
    /// Current batch being processed
    /// </summary>
    public int CurrentBatch { get; set; }

    /// <summary>
    /// Total number of batches
    /// </summary>
    public int TotalBatches { get; set; }

    /// <summary>
    /// Percentage of completion (0-100)
    /// </summary>
    public double ProgressPercentage => TotalRecords > 0 ? (double)ProcessedRecords / TotalRecords * 100 : 0;

    /// <summary>
    /// When the migration started
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// When the migration was last updated
    /// </summary>
    public DateTime LastUpdateTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Estimated completion time based on current progress
    /// </summary>
    public DateTime? EstimatedCompletionTime { get; set; }

    /// <summary>
    /// Average processing rate (records per second)
    /// </summary>
    public double ProcessingRate { get; set; }

    /// <summary>
    /// Last processed record ID for resumption
    /// </summary>
    public long LastProcessedId { get; set; }

    /// <summary>
    /// Error message if migration failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Detailed error information
    /// </summary>
    public string? ErrorDetails { get; set; }

    /// <summary>
    /// Thread or worker ID processing the migration
    /// </summary>
    public string? WorkerId { get; set; }

    /// <summary>
    /// Additional metadata about the migration
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Calculate estimated completion time based on current progress
    /// </summary>
    public void UpdateEstimatedCompletion()
    {
        if (ProcessedRecords > 0 && TotalRecords > ProcessedRecords)
        {
            var elapsed = DateTime.UtcNow - StartTime;
            var remainingRecords = TotalRecords - ProcessedRecords;
            var avgTimePerRecord = elapsed.TotalSeconds / ProcessedRecords;
            var estimatedRemainingSeconds = remainingRecords * avgTimePerRecord;
            
            EstimatedCompletionTime = DateTime.UtcNow.AddSeconds(estimatedRemainingSeconds);
            ProcessingRate = ProcessedRecords / elapsed.TotalSeconds;
        }
    }
}

/// <summary>
/// Status of a migration operation
/// </summary>
public enum MigrationStatus
{
    NotStarted = 0,
    Running = 1,
    Paused = 2,
    Completed = 3,
    Failed = 4,
    Cancelled = 5,
    Resuming = 6
}
