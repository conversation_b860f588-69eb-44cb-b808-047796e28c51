using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Table("osm_way_batches")]
[Index("Name", Name = "osm_way_batches_name")]
public partial class OsmWayBatch
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("polyline")]
    public string? Polyline { get; set; }

    [Column("name")]
    public string? Name { get; set; }

    [Column("en_name")]
    public string? EnName { get; set; }

    [Column("highway")]
    public string? Highway { get; set; }

    [Column("tprovince_code", TypeName = "character varying[]")]
    public List<string>? TprovinceCode { get; set; }

    [Column("tdistrict_code", TypeName = "character varying[]")]
    public List<string>? TdistrictCode { get; set; }

    [Column("same_administration")]
    public bool? SameAdministration { get; set; }

    [Column("alley_of")]
    public long? AlleyOf { get; set; }
}
