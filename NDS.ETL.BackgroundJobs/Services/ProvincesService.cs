using Microsoft.EntityFrameworkCore;
using NDS.ETL.Entities.Context;
using NDS.ETL.Entities.PostgresPlace;

namespace NDS.ETL.BackgroundJobs.Services;

/// <summary>
/// Implementation of the provinces service
/// </summary>
public class ProvincesService : IProvincesService
{
    private readonly NpgPlaceContext _context;
    private readonly ILogger<ProvincesService> _logger;
    private readonly ICacheManager _cacheManager;

    // Các khóa cache với tiền tố để tránh xung đột
    private const string PROVINCE_ALL_CACHE_KEY = "provinces_all";
    private const string PROVINCE_ID_CACHE_PREFIX = "provinces_id_";
    private const string PROVINCE_CODE_CACHE_PREFIX = "provinces_code_";
    private const string PROVINCE_NAME_CACHE_PREFIX = "provinces_name_";
    private const string PROVINCE_TYPE_CACHE_PREFIX = "provinces_type_";
    
    // Thời gian cache hết hạn (provinces hiếm khi thay đổi)
    private static readonly TimeSpan CacheExpirationTime = TimeSpan.FromHours(4);

    public ProvincesService(
        NpgPlaceContext context, 
        ILogger<ProvincesService> logger,
        ICacheManager cacheManager)
    {
        _context = context;
        _logger = logger;
        _cacheManager = cacheManager;
    }

    /// <summary>
    /// Gets all provinces
    /// </summary>
    public async Task<List<ProvincesNew>> GetAllProvincesAsync()
    {
        // Kiểm tra dữ liệu trong cache trước
        var cachedProvinces = _cacheManager.GetFromCache(PROVINCE_ALL_CACHE_KEY) as List<ProvincesNew>;
        
        if (cachedProvinces != null)
        {
            _logger.LogInformation("Cache hit: Retrieved all provinces from cache");
            return cachedProvinces;
        }
        
        _logger.LogInformation("Cache miss: Getting all provinces from database");
        
        // Lấy dữ liệu từ database nếu không có trong cache
        var provinces = await _context.ProvincesNews
            .Include(p => p.ProvincesBridges)
                .ThenInclude(pb => pb.ProvincesOldCodeNavigation)
            .OrderBy(p => p.Name)
            .ToListAsync();
            
        // Lưu vào cache để sử dụng sau này
        _cacheManager.AddToCache(PROVINCE_ALL_CACHE_KEY, provinces, CacheExpirationTime);
        _logger.LogInformation("Added all provinces to cache with expiration of {ExpirationTime} hours", 
            CacheExpirationTime.TotalHours);
        
        return provinces;
    }

    /// <summary>
    /// Gets a province by its ID
    /// </summary>
    public async Task<ProvincesNew?> GetProvinceByIdAsync(int id)
    {
        // Tạo khóa cache cụ thể cho province theo ID
        string cacheKey = $"{PROVINCE_ID_CACHE_PREFIX}{id}";
        
        // Kiểm tra cache trước
        var cachedProvince = _cacheManager.GetFromCache(cacheKey) as ProvincesNew;
        
        if (cachedProvince != null)
        {
            _logger.LogInformation("Cache hit: Retrieved province with id {Id} from cache", id);
            return cachedProvince;
        }
        
        _logger.LogInformation("Cache miss: Getting province with id {Id} from database", id);
        
        // Truy vấn database nếu không có trong cache
        var province = await _context.ProvincesNews
            .Include(p => p.ProvincesBridges)
                .ThenInclude(pb => pb.ProvincesOldCodeNavigation)
            .FirstOrDefaultAsync(p => p.Id == id);
            
        // Nếu tìm thấy province, lưu vào cache
        if (province != null)
        {
            _cacheManager.AddToCache(cacheKey, province, CacheExpirationTime);
            _logger.LogInformation("Added province with id {Id} to cache", id);
        }
        
        return province;
    }

    /// <summary>
    /// Gets a province by its code
    /// </summary>
    public async Task<ProvincesNew?> GetProvinceByCodeAsync(string code)
    {
        if (string.IsNullOrEmpty(code))
        {
            _logger.LogWarning("Province code parameter is null or empty");
            return null;
        }
        
        // Tạo khóa cache cụ thể cho province theo code
        string cacheKey = $"{PROVINCE_CODE_CACHE_PREFIX}{code.ToLower()}";
        
        // Kiểm tra cache trước
        var cachedProvince = _cacheManager.GetFromCache(cacheKey) as ProvincesNew;
        
        if (cachedProvince != null)
        {
            _logger.LogInformation("Cache hit: Retrieved province with code {Code} from cache", code);
            return cachedProvince;
        }
        
        _logger.LogInformation("Cache miss: Getting province with code {Code} from database", code);
        
        // Truy vấn database nếu không có trong cache
        var province = await _context.ProvincesNews
            .Include(p => p.ProvincesBridges)
                .ThenInclude(pb => pb.ProvincesOldCodeNavigation)
            .FirstOrDefaultAsync(p => p.Code != null && p.Code.ToLower() == code.ToLower());
            
        // Nếu tìm thấy province, lưu vào cache
        if (province != null)
        {
            _cacheManager.AddToCache(cacheKey, province, CacheExpirationTime);
            _logger.LogInformation("Added province with code {Code} to cache", code);
        }
        
        return province;
    }

    /// <summary>
    /// Gets provinces by name
    /// </summary>
    public async Task<List<ProvincesNew>> GetProvincesByNameAsync(string name)
    {
        if (string.IsNullOrEmpty(name))
        {
            _logger.LogWarning("Search name parameter is null or empty");
            return new List<ProvincesNew>();
        }
        
        // Tạo khóa cache cho tìm kiếm theo tên
        string cacheKey = $"{PROVINCE_NAME_CACHE_PREFIX}{name.ToLower()}";
        
        // Kiểm tra cache trước
        var cachedProvinces = _cacheManager.GetFromCache(cacheKey) as List<ProvincesNew>;
        
        if (cachedProvinces != null)
        {
            _logger.LogInformation("Cache hit: Retrieved provinces by name: {Name} from cache", name);
            return cachedProvinces;
        }
        
        _logger.LogInformation("Cache miss: Searching provinces by name: {Name} from database", name);

        // Truy vấn database nếu không có trong cache
        var provinces = await _context.ProvincesNews
                .Include(p => p.ProvincesBridges)
                    .ThenInclude(pb => pb.ProvincesOldCodeNavigation)
                .Where(p => (p.Name != null &&
                            EF.Functions.Like(p.Name.ToLower(), $"%{name.ToLower()}%")) ||
                           (p.ProvincesBridges.Any(pb => pb.ProvincesOldCodeNavigation != null &&
                                                        pb.ProvincesOldCodeNavigation.Name != null &&
                                                        EF.Functions.Like(pb.ProvincesOldCodeNavigation.Name.ToLower(), $"%{name.ToLower()}%"))))
                .OrderBy(p => p.Name)
                .ToListAsync();

        // Lưu kết quả vào cache
        _cacheManager.AddToCache(cacheKey, provinces, CacheExpirationTime);
        _logger.LogInformation("Added search results for name: {Name} to cache with {Count} provinces", 
            name, provinces.Count);
        
        return provinces;
    }

    /// <summary>
    /// Gets provinces by type
    /// </summary>
    public async Task<List<ProvincesNew>> GetProvincesByTypeAsync(string type)
    {
        if (string.IsNullOrEmpty(type))
        {
            _logger.LogWarning("Type parameter is null or empty");
            return new List<ProvincesNew>();
        }
        
        // Tạo khóa cache cho tìm kiếm theo loại
        string cacheKey = $"{PROVINCE_TYPE_CACHE_PREFIX}{type.ToLower()}";
        
        // Kiểm tra cache trước
        var cachedProvinces = _cacheManager.GetFromCache(cacheKey) as List<ProvincesNew>;
        
        if (cachedProvinces != null)
        {
            _logger.LogInformation("Cache hit: Retrieved provinces by type: {Type} from cache", type);
            return cachedProvinces;
        }
        
        _logger.LogInformation("Cache miss: Getting provinces by type: {Type} from database", type);
        
        // Truy vấn database nếu không có trong cache
        var provinces = await _context.ProvincesNews
            .Include(p => p.ProvincesBridges)
                .ThenInclude(pb => pb.ProvincesOldCodeNavigation)
            .Where(p => p.Type != null && 
                        EF.Functions.Like(p.Type.ToLower(), $"%{type.ToLower()}%"))
            .OrderBy(p => p.Name)
            .ToListAsync();
            
        // Lưu kết quả vào cache
        _cacheManager.AddToCache(cacheKey, provinces, CacheExpirationTime);
        _logger.LogInformation("Added provinces with type: {Type} to cache with {Count} provinces", 
            type, provinces.Count);
        
        return provinces;
    }
    
    /// <summary>
    /// Invalidates all province related caches
    /// </summary>
    public void InvalidateAllProvinceCache()
    {
        _logger.LogInformation("Invalidating all province caches");
        
        // Xóa tất cả các cache có tiền tố provinces
        _cacheManager.ClearCacheByPrefix("provinces_");
        
        _logger.LogInformation("All province caches have been invalidated");
    }
}