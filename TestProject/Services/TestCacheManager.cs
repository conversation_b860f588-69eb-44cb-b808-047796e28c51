using NDS.ETL.BackgroundJobs.Services;
using System;
using System.Collections.Generic;

namespace TestProject
{
    /// <summary>
    /// Implementation of ICacheManager for testing purposes
    /// </summary>
    public class TestCacheManager : ICacheManager
    {
        // Dictionary to track cached items
        private readonly Dictionary<string, object> _cache = new Dictionary<string, object>();
        private readonly Dictionary<string, DateTime> _expirations = new Dictionary<string, DateTime>();
        
        // Counter for analytics
        public int GetCacheHitCount { get; private set; } = 0;
        public int AddToCacheCount { get; private set; } = 0;
        public int RemoveFromCacheCount { get; private set; } = 0;
        public int ClearCacheCount { get; private set; } = 0;
        
        // For inspection in tests
        public IReadOnlyDictionary<string, object> CachedItems => _cache;
        
        public void AddToCache(string key, object value, TimeSpan expiration)
        {
            _cache[key] = value;
            _expirations[key] = DateTime.UtcNow.Add(expiration);
            AddToCacheCount++;
        }

        public void RemoveFromCache(string key)
        {
            if (_cache.ContainsKey(key))
            {
                _cache.Remove(key);
                _expirations.Remove(key);
                RemoveFromCacheCount++;
            }
        }

        public void ClearCacheByPrefix(string prefix)
        {
            var keysToRemove = new List<string>();
            
            foreach (var key in _cache.Keys)
            {
                if (key.StartsWith(prefix))
                {
                    keysToRemove.Add(key);
                }
            }
            
            foreach (var key in keysToRemove)
            {
                RemoveFromCache(key);
            }
            
            ClearCacheCount++;
        }

        public object GetFromCache(string key)
        {
            // Check if the item exists in cache
            if (!_cache.TryGetValue(key, out var value))
            {
                return null;
            }
            
            // Check if the item has expired
            if (_expirations.TryGetValue(key, out var expiration) && expiration < DateTime.UtcNow)
            {
                // Remove expired item
                RemoveFromCache(key);
                return null;
            }
            
            // Return valid item from cache
            GetCacheHitCount++;
            return value;
        }
    }
}
