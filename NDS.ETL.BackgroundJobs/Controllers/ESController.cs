using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NDS.ETL.BackgroundJobs.Authentication;
using NDS.ETL.BackgroundJobs.Extensions;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Services;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Entities.ESEntities;
using Swashbuckle.AspNetCore.Annotations;

namespace NDS.ETL.BackgroundJobs.Controllers;

/// <summary>
/// Controller providing Elasticsearch-based location services
/// Protected by ApiKey authentication - requires specific permission for each endpoint
/// or PermissionName.All for access to all endpoints
/// </summary>
[ApiController]
[Route("api/[controller]")]
[ApiKey] // Controller-level attribute for common auth requirements
public class ESController : ESControllerBase
{
    private readonly ICacheManager _cacheManager;
    private readonly IApiKeyValidationService _apiKeyValidationService;
    private readonly IESAutocompleteService _autocompleteService;
    private readonly IESNearbyService _nearbyService;
    private readonly IESRecommendationService _recommendationService;
    private readonly IESPlaceDetailsService _placeDetailsService;

    public ESController(
        ICacheManager cacheManager,
        IApiKeyValidationService apiKeyValidationService,
        IRequestInfoLogger requestInfoLogger,
        IESAutocompleteService autocompleteService,
        IESNearbyService nearbyService,
        IESRecommendationService recommendationService,
        IESPlaceDetailsService placeDetailsService,
        ILogger<ESController> logger)
        : base(requestInfoLogger, logger)
    {
        _cacheManager = cacheManager;
        _apiKeyValidationService = apiKeyValidationService;
        _autocompleteService = autocompleteService;
        _nearbyService = nearbyService;
        _recommendationService = recommendationService;
        _placeDetailsService = placeDetailsService;
    }

    /// <summary>
    /// Performs location autocomplete search based on query text and coordinates
    /// Requires Autocomplete permission or All permission
    /// </summary>
    /// <param name="query">Search text for location finding</param>
    /// <param name="latitude">Current latitude position</param>
    /// <param name="longitude">Current longitude position</param>
    /// <param name="radius">Search radius in kilometers</param>
    /// <param name="limit">Maximum number of results to return</param>
    [HttpGet("autocomplete")]
    [ApiKey(nameof(PermissionName.Autocomplete))]
    [SwaggerOperation(
        Summary = "Search for locations by text",
        Description = "Performs location autocomplete search based on query text and coordinates",
        OperationId = "Autocomplete"
    )]
    [SwaggerResponse(200, "Returns a list of places matching the search criteria")]
    [SwaggerResponse(401, "Unauthorized - Invalid API Key")]
    [SwaggerResponse(500, "Internal server error")]
    public async Task<ActionResult<List<PlacesEs>>> Autocomplete(
        [FromQuery] [SwaggerParameter(Description = "Search text")]
        string query = "coffee",

        [FromQuery] [SwaggerParameter(Description = "Current latitude")]
        double latitude = 21.021673,

        [FromQuery] [SwaggerParameter(Description = "Current longitude")]
        double longitude = 105.803715,

        [FromQuery] [SwaggerParameter(Description = "Search radius in kilometers (optional, will use default H3 resolution 8 if not specified)")]
        double? radius = null,

        [FromQuery] [SwaggerParameter(Description = "Maximum number of results")]
        int limit = 10)
    {
        var requestInfo = ProcessRequestInfo("autocomplete");
        (latitude, longitude) = UpdateCoordinates(requestInfo, latitude, longitude);

        // Chuyển đổi radius (km) sang h3Resolution sử dụng code có sẵn
        int? h3Resolution = null;
        if (radius.HasValue)
        {
            h3Resolution = H3Helper.GetH3ResolutionFromRadiusKm(radius.Value);
        }

        return await ExecuteWithErrorHandling(
            () => _autocompleteService.SearchAutocompleteAsync(
                requestInfo.UserId ?? "",
                requestInfo.PhoneNumber ?? "",
                query,
                latitude,
                longitude,
                h3Resolution,
                limit),
            "autocomplete");
    }

    /// <summary>
    /// Finds nearby places based on coordinates and radius
    /// Requires NearBy permission or All permission
    /// </summary>
    [HttpGet("nearby")]
    [ApiKey(nameof(PermissionName.NearBy))]
    [SwaggerOperation(
        Summary = "Find nearby places",
        Description = "Returns a list of places near the specified coordinates using radius in meters",
        OperationId = "NearBy"
    )]
    [SwaggerResponse(200, "Returns a list of places near the specified location")]
    [SwaggerResponse(401, "Unauthorized - Invalid API Key")]
    [SwaggerResponse(500, "Internal server error")]
    public async Task<ActionResult<List<PlacesEs>>> NearBy(
        [FromQuery] [SwaggerParameter(Description = "Current latitude")]
        double latitude = 21.021673,

        [FromQuery] [SwaggerParameter(Description = "Current longitude")]
        double longitude = 105.803715,

        [FromQuery] [SwaggerParameter(Description = "Search radius in meters (optional, will use default H3 resolution 8 if not specified)")]
        double? radius = null,

        [FromQuery] [SwaggerParameter(Description = "Maximum number of results")]
        int limit = 5) // Mặc định 5 kết quả
    {
        var requestInfo = ProcessRequestInfo("nearby");
        (latitude, longitude) = UpdateCoordinates(requestInfo, latitude, longitude);

        if (limit > 5) limit = 5;

        // Chuyển đổi radius (meters) sang h3Resolution sử dụng code có sẵn
        int? h3Resolution = null;
        if (radius.HasValue)
        {
            h3Resolution = H3Helper.GetH3ResolutionFromRadiusInMeters(radius.Value);
        }

        return await ExecuteWithErrorHandling(() => _nearbyService.SearchNearbyAsync(
                requestInfo.UserId ?? "",
                requestInfo.PhoneNumber ?? "",
                latitude,
                longitude,
                h3Resolution,
                limit,
                null,
                radius),
            "nearby");
    }

    /// <summary>
    /// Gets recommended places for a user based on history and location
    /// Requires Recommendation permission or All permission
    /// </summary>
    [HttpGet("recommendPlaces")]
    [ApiKey(nameof(PermissionName.Recommendation))]
    [SwaggerOperation(
        Summary = "Get recommended places",
        Description = "Returns a list of recommended places for the user based on history and location",
        OperationId = "RecommendPlaces"
    )]
    [SwaggerResponse(200, "Returns a list of recommended places")]
    [SwaggerResponse(401, "Unauthorized - Invalid API Key")]
    [SwaggerResponse(500, "Internal server error")]
    public async Task<ActionResult<List<RecommendedPlace>>> RecommendPlaces(
        [FromQuery] [SwaggerParameter(Description = "User ID (optional)")]
        string userId = "",

        [FromQuery] [SwaggerParameter(Description = "User phone number")]
        string phoneNumber = "",

        [FromQuery] [SwaggerParameter(Description = "Current latitude (optional)")]
        double? latitude = null,

        [FromQuery] [SwaggerParameter(Description = "Current longitude (optional)")]
        double? longitude = null,

        [FromQuery] [SwaggerParameter(Description = "Search radius in kilometers (optional, will use default H3 resolution 8 if not specified)")]
        double? radius = null,

        [FromQuery] [SwaggerParameter(Description = "Maximum number of results")]
        int limit = 5,

        [FromQuery] [SwaggerParameter(Description = "Page number (0-based)")]
        int page = 1)
    {
        var requestInfo = ProcessRequestInfo("recommendPlaces");
        (userId, phoneNumber) = UpdateUserInfo(requestInfo, userId, phoneNumber);
        (latitude, longitude) = UpdateNullableCoordinates(requestInfo, latitude, longitude);

        // Chuyển đổi radius (km) sang h3Resolution
        int? h3Resolution = null;
        if (radius.HasValue) h3Resolution = H3Helper.GetH3ResolutionFromRadiusKm(radius.Value);
        if (page < 1) page = 1;
        limit = 3;

        return await ExecuteWithErrorHandling(
            () => _recommendationService.GetRecommendedPlacesAsync(
                userId,
                phoneNumber,
                latitude,
                longitude,
                h3Resolution,
                limit,
                page,
                requestInfo.Lang ?? "vi"),
            "recommend places");
    }

    /// <summary>
    /// Gets recommended search items for a user based on search history
    /// Requires Recommendation permission or All permission
    /// </summary>
    [HttpGet("recommendSearch")]
    [ApiKey(nameof(PermissionName.Recommendation))]
    [SwaggerOperation(
        Summary = "Get recommended search items",
        Description = "Returns a list of recommended search items based on user's search history",
        OperationId = "RecommendSearch"
    )]
    [SwaggerResponse(200, "Returns a list of recommended search items")]
    [SwaggerResponse(401, "Unauthorized - Invalid API Key")]
    [SwaggerResponse(500, "Internal server error")]
    public async Task<ActionResult<RecommendSearchItem>> RecommendSearch(
        [FromQuery] [SwaggerParameter(Description = "User ID (optional)")]
        string userId = "",

        [FromQuery] [SwaggerParameter(Description = "User phone number")]
        string phoneNumber = "",

        [FromQuery] [SwaggerParameter(Description = "Current latitude (optional)")]
        double? latitude = null,

        [FromQuery] [SwaggerParameter(Description = "Current longitude (optional)")]
        double? longitude = null,

        [FromQuery] [SwaggerParameter(Description = "Search radius in kilometers (optional, will use default H3 resolution 8 if not specified)")]
        double? radius = null,

        [FromQuery] [SwaggerParameter(Description = "Maximum number of results")]
        int limit = 10)
    {
        var requestInfo = ProcessRequestInfo("recommendSearch");
        (userId, phoneNumber) = UpdateUserInfo(requestInfo, userId, phoneNumber);
        (latitude, longitude) = UpdateNullableCoordinates(requestInfo, latitude, longitude);

        limit = 10;

        // Chuyển đổi radius (km) sang h3Resolution
        int? h3Resolution = null;
        if (radius.HasValue) h3Resolution = H3Helper.GetH3ResolutionFromRadiusKm(radius.Value);

        return await ExecuteWithErrorHandling(
            () => _recommendationService.GetRecommendedSearchItemsAsync(
                userId,
                phoneNumber,
                latitude,
                longitude,
                h3Resolution,
                limit,
                requestInfo.Lang ?? "vi"),
            "recommend search");
    }

    /// <summary>
    /// Gets recommended routes for a user based on travel history
    /// Requires Recommendation permission or All permission
    /// </summary>
    [HttpGet("recommendRoutes")]
    [ApiKey(nameof(PermissionName.Recommendation))]
    [SwaggerOperation(
        Summary = "Get recommended routes",
        Description = "Returns a list of recommended routes based on user's travel history",
        OperationId = "RecommendRoutes"
    )]
    [SwaggerResponse(200, "Returns a list of recommended routes")]
    [SwaggerResponse(401, "Unauthorized - Invalid API Key")]
    [SwaggerResponse(500, "Internal server error")]
    public async Task<ActionResult<List<RecommendedRoute>>> RecommendRoutes(
        [FromQuery] [SwaggerParameter(Description = "User ID (optional)")]
        string userId = "",

        [FromQuery] [SwaggerParameter(Description = "User phone number")]
        string phoneNumber = "",

        [FromQuery] [SwaggerParameter(Description = "Current latitude (optional)")]
        double? latitude = null,

        [FromQuery] [SwaggerParameter(Description = "Current longitude (optional)")]
        double? longitude = null,

        [FromQuery] [SwaggerParameter(Description = "Search radius in kilometers (optional, will use default H3 resolution 8 if not specified)")]
        double? radius = null,

        [FromQuery] [SwaggerParameter(Description = "Maximum number of results")]
        int limit = 3,

        [FromQuery] [SwaggerParameter(Description = "Page number (0-based)")]
        int page = 1)
    {
        var requestInfo = ProcessRequestInfo("recommendRoutes");
        (userId, phoneNumber) = UpdateUserInfo(requestInfo, userId, phoneNumber);
        (latitude, longitude) = UpdateNullableCoordinates(requestInfo, latitude, longitude);

        // Chuyển đổi radius (km) sang h3Resolution
        int? h3Resolution = null;
        if (radius.HasValue) h3Resolution = H3Helper.GetH3ResolutionFromRadiusKm(radius.Value);
        if (page < 1) page = 1;
        limit = 3;

        return await ExecuteWithErrorHandling(() => _recommendationService.GetRecommendedRoutesAsync(
                userId,
                phoneNumber,
                latitude,
                longitude,
                h3Resolution,
                limit,
                page,
                requestInfo.Lang ?? "vi"),
            "recommend routes");
    }

    /// <summary>
    /// Gets place details for a list of place IDs
    /// Requires PlaceDetails permission or All permission
    /// </summary>
    [HttpGet("placeDetails")]
    [ApiKey(nameof(PermissionName.PlaceDetails))]
    [SwaggerOperation(
        Summary = "Get place details",
        Description = "Returns details for a list of place IDs",
        OperationId = "PlaceDetails"
    )]
    [SwaggerResponse(200, "Returns a list of place details")]
    [SwaggerResponse(401, "Unauthorized - Invalid API Key")]
    [SwaggerResponse(500, "Internal server error")]
    public async Task<ActionResult<List<PlacesEs>>> PlaceDetails(
        [FromQuery] [SwaggerParameter(Description = "List of place IDs separated by commas")]
        string placeIds = "")
    {
        var requestInfo = ProcessRequestInfo("placeDetails");
        
        // Log trace với thông tin từ requestInfo
        Logger.LogTrace("PlaceDetails: User={UserId}, Phone={PhoneNumber}, PlaceIds={PlaceIds}", 
            requestInfo.UserId ?? "unknown", 
            requestInfo.PhoneNumber ?? "unknown", 
            placeIds);
        
        if (string.IsNullOrWhiteSpace(placeIds))
        {
            return new List<PlacesEs>();
        }

        var placeIdList = placeIds.Split(',')
            .Where(id => !string.IsNullOrWhiteSpace(id))
            .Select(id => id.Trim())
            .ToList();

        return await ExecuteWithErrorHandling(
            () => _placeDetailsService.GetPlacesDetailsByIdsAsync(placeIdList),
            "place details");
    }

    /// <summary>
    /// Clears memory cache for API keys
    /// Requires ClearCache permission or All permission
    /// </summary>
    [HttpPost("clearApiKeysCache")]
    [AllowAnonymous]
    [SwaggerOperation(
        Summary = "Clear API key cache",
        Description = "Clears the memory cache for API keys to force reloading from database",
        OperationId = "ClearApiKeysCache"
    )]
    [SwaggerResponse(200, "Cache successfully cleared")]
    [SwaggerResponse(500, "Internal server error")]
    public IActionResult ClearApiKeysCache()
    {
        ProcessRequestInfo("clearApiKeysCache");
        Logger.LogInformation("Clearing API key cache");
        _apiKeyValidationService.ClearApiKeysCache();

        return Ok(new { message = "API key cache cleared successfully." });
    }
}