using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Table("booking_histories")]
public partial class BookingHistory
{
    [Key]
    [Column("id", TypeName = "character varying")]
    public string Id { get; set; } = null!;

    [Column("customer_id", TypeName = "character varying")]
    public string? CustomerId { get; set; }

    [Column("customer_phone", TypeName = "character varying")]
    public string? CustomerPhone { get; set; }

    [Column("origin_place_id", TypeName = "character varying")]
    public string? OriginPlaceId { get; set; }

    [Column("origin_full_address", TypeName = "character varying")]
    public string? OriginFullAddress { get; set; }

    [Column("des_place_id", TypeName = "character varying")]
    public string? DesPlaceId { get; set; }

    [Column("des_address", TypeName = "character varying")]
    public string? DesAddress { get; set; }

    [Column("des_full_address", TypeName = "character varying")]
    public string? DesFullAddress { get; set; }

    [Column("last_booking_at", TypeName = "timestamp(0) without time zone")]
    public DateTime? LastBookingAt { get; set; }

    [Column("booking_count")]
    public int? BookingCount { get; set; }

    [Column("last_search_at", TypeName = "timestamp(0) without time zone")]
    public DateTime? LastSearchAt { get; set; }

    [Column("search_count")]
    public int? SearchCount { get; set; }

    [Column("last_paid_at", TypeName = "timestamp(0) without time zone")]
    public DateTime? LastPaidAt { get; set; }

    [Column("created_at", TypeName = "timestamp(0) without time zone")]
    public DateTime? CreatedAt { get; set; }

    [Column("origin_address", TypeName = "character varying")]
    public string? OriginAddress { get; set; }
}
