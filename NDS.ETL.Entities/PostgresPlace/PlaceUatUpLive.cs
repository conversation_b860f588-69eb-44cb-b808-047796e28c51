using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("place_uat_up_live")]
[Index("Type", Name = "place_uat_up_live_type_idx")]
public partial class PlaceUatUpLive
{
    [Key]
    [Column("placeid", TypeName = "character varying")]
    public string Placeid { get; set; } = null!;

    [Column("type")]
    public int Type { get; set; }
}
