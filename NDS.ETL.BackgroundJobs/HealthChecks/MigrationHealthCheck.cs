using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Options;
using NDS.ETL.BackgroundJobs.Configuration;
using NDS.ETL.BackgroundJobs.Models;
using NDS.ETL.BackgroundJobs.Services.Interfaces;

namespace NDS.ETL.BackgroundJobs.HealthChecks;

/// <summary>
/// Health check for monitoring address migration process
/// </summary>
public class MigrationHealthCheck : IHealthCheck
{
    private readonly ILogger<MigrationHealthCheck> _logger;
    private readonly IAddressMigrationService _migrationService;
    private readonly AddressMigrationConfig _config;

    public MigrationHealthCheck(
        ILogger<MigrationHealthCheck> logger,
        IAddressMigrationService migrationService,
        IOptions<AddressMigrationConfig> config)
    {
        _logger = logger;
        _migrationService = migrationService;
        _config = config.Value;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var data = new Dictionary<string, object>();
            var isHealthy = true;
            var healthMessages = new List<string>();

            // Check if migration is enabled
            data["migration_enabled"] = _config.Enabled;
            if (!_config.Enabled)
            {
                healthMessages.Add("Migration is disabled in configuration");
            }

            // Get current migration progress
            var currentMigration = await _migrationService.GetMigrationProgressAsync(_config.MigrationJobId, cancellationToken);
            if (currentMigration != null)
            {
                data["migration_id"] = currentMigration.MigrationId;
                data["migration_status"] = currentMigration.Status.ToString();
                data["progress_percentage"] = Math.Round(currentMigration.ProgressPercentage, 2);
                data["processed_records"] = currentMigration.ProcessedRecords;
                data["total_records"] = currentMigration.TotalRecords;
                data["failed_records"] = currentMigration.FailedRecords;
                data["processing_rate"] = Math.Round(currentMigration.ProcessingRate, 2);
                data["start_time"] = currentMigration.StartTime;
                data["last_update"] = currentMigration.LastUpdateTime;

                if (currentMigration.EstimatedCompletionTime.HasValue)
                {
                    data["estimated_completion"] = currentMigration.EstimatedCompletionTime.Value;
                }

                // Check migration health based on status
                switch (currentMigration.Status)
                {
                    case MigrationStatus.Running:
                        // Check if migration is stuck (no updates for too long)
                        var timeSinceLastUpdate = DateTime.UtcNow - currentMigration.LastUpdateTime;
                        if (timeSinceLastUpdate > TimeSpan.FromMinutes(30))
                        {
                            isHealthy = false;
                            healthMessages.Add($"Migration appears stuck - no updates for {timeSinceLastUpdate.TotalMinutes:F0} minutes");
                        }
                        else
                        {
                            healthMessages.Add("Migration is running normally");
                        }
                        break;

                    case MigrationStatus.Failed:
                        isHealthy = false;
                        healthMessages.Add($"Migration failed: {currentMigration.ErrorMessage}");
                        data["error_message"] = currentMigration.ErrorMessage;
                        break;

                    case MigrationStatus.Completed:
                        healthMessages.Add("Migration completed successfully");
                        var duration = currentMigration.LastUpdateTime - currentMigration.StartTime;
                        data["completion_duration"] = duration.ToString();
                        break;

                    case MigrationStatus.Paused:
                        healthMessages.Add("Migration is paused");
                        break;

                    case MigrationStatus.Cancelled:
                        healthMessages.Add("Migration was cancelled");
                        break;

                    default:
                        healthMessages.Add($"Migration status: {currentMigration.Status}");
                        break;
                }

                // Check error rate
                if (currentMigration.ProcessedRecords > 0)
                {
                    var errorRate = (double)currentMigration.FailedRecords / currentMigration.ProcessedRecords * 100;
                    data["error_rate_percentage"] = Math.Round(errorRate, 2);

                    if (errorRate > 10) // More than 10% error rate
                    {
                        isHealthy = false;
                        healthMessages.Add($"High error rate: {errorRate:F2}%");
                    }
                    else if (errorRate > 5) // More than 5% error rate
                    {
                        healthMessages.Add($"Elevated error rate: {errorRate:F2}%");
                    }
                }
            }
            else
            {
                data["migration_status"] = "No active migration";
                healthMessages.Add("No active migration found");
            }

            // Get recent migration history
            var allMigrations = await _migrationService.GetAllMigrationProgressAsync(cancellationToken);
            var recentMigrations = allMigrations
                .Where(m => m.StartTime > DateTime.UtcNow.AddDays(-7))
                .OrderByDescending(m => m.StartTime)
                .Take(5)
                .ToList();

            data["recent_migrations_count"] = recentMigrations.Count;
            if (recentMigrations.Any())
            {
                var recentMigrationData = recentMigrations.Select(m => new
                {
                    id = m.MigrationId,
                    status = m.Status.ToString(),
                    start_time = m.StartTime,
                    progress = Math.Round(m.ProgressPercentage, 2),
                    processed = m.ProcessedRecords,
                    failed = m.FailedRecords
                }).ToArray();

                data["recent_migrations"] = recentMigrationData;

                // Check for repeated failures
                var recentFailures = recentMigrations.Count(m => m.Status == MigrationStatus.Failed);
                if (recentFailures >= 3)
                {
                    isHealthy = false;
                    healthMessages.Add($"Multiple recent failures: {recentFailures} in the last 7 days");
                }
            }

            // Check system resources (basic checks)
            await CheckSystemResourcesAsync(data, healthMessages, cancellationToken);

            var status = isHealthy ? HealthStatus.Healthy : HealthStatus.Unhealthy;
            var description = string.Join("; ", healthMessages);

            return new HealthCheckResult(status, description, data: data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during migration health check");
            
            var errorData = new Dictionary<string, object>
            {
                ["error"] = ex.Message,
                ["error_type"] = ex.GetType().Name
            };

            return new HealthCheckResult(
                HealthStatus.Unhealthy, 
                $"Health check failed: {ex.Message}", 
                ex, 
                errorData);
        }
    }

    private async Task CheckSystemResourcesAsync(
        Dictionary<string, object> data, 
        List<string> healthMessages, 
        CancellationToken cancellationToken)
    {
        try
        {
            // Check available memory
            var process = System.Diagnostics.Process.GetCurrentProcess();
            var workingSet = process.WorkingSet64;
            var privateMemory = process.PrivateMemorySize64;

            data["memory_working_set_mb"] = Math.Round(workingSet / 1024.0 / 1024.0, 2);
            data["memory_private_mb"] = Math.Round(privateMemory / 1024.0 / 1024.0, 2);

            // Check if memory usage is too high (over 2GB)
            if (workingSet > 2L * 1024 * 1024 * 1024)
            {
                healthMessages.Add($"High memory usage: {workingSet / 1024.0 / 1024.0:F0} MB");
            }

            // Check CPU usage (simplified)
            var cpuTime = process.TotalProcessorTime;
            data["cpu_time_seconds"] = cpuTime.TotalSeconds;

            // Check thread count
            var threadCount = process.Threads.Count;
            data["thread_count"] = threadCount;

            if (threadCount > 100)
            {
                healthMessages.Add($"High thread count: {threadCount}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Could not check system resources");
            data["system_check_error"] = ex.Message;
        }
    }
}

/// <summary>
/// Extension methods for registering migration health checks
/// </summary>
public static class MigrationHealthCheckExtensions
{
    /// <summary>
    /// Add migration health check to the health check builder
    /// </summary>
    /// <param name="builder">Health checks builder</param>
    /// <param name="name">Health check name</param>
    /// <param name="failureStatus">Status to report on failure</param>
    /// <param name="tags">Tags for the health check</param>
    /// <returns>Health checks builder</returns>
    public static IServiceCollection AddMigrationHealthCheck(
        this IServiceCollection services,
        string name = "migration",
        HealthStatus? failureStatus = null,
        IEnumerable<string>? tags = null)
    {
        return services.AddHealthChecks()
            .AddCheck<MigrationHealthCheck>(
                name,
                failureStatus,
                tags)
            .Services;
    }
}
