using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;
using NodaTime;

namespace NDS.ETL.Entities.PostgresPlace;

[Keyless]
[Table("uat_place_migrate2")]
[Index("PlaceId", Name = "uat_place_migrate2_placeid_idx", IsUnique = true)]
[Index("PlaceId", Name = "uat_place_migrate2_unique", IsUnique = true)]
public partial class UatPlaceMigrate2
{
    [Column("placeId")]
    public string? PlaceId { get; set; }

    [Column("name")]
    public string? Name { get; set; }

    [Column("name_old")]
    [StringLength(500)]
    public string? NameOld { get; set; }

    [Column("address")]
    public string? Address { get; set; }

    [Column("address_old")]
    [StringLength(1000)]
    public string? AddressOld { get; set; }

    [Column("location", TypeName = "geometry(Point,4326)")]
    public Point? Location { get; set; }

    [Column("location_old", TypeName = "geometry(Point,4326)")]
    public Point? LocationOld { get; set; }

    [Column("update_user")]
    public string? UpdateUser { get; set; }

    [Column("districtId")]
    public int? DistrictId { get; set; }

    [Column("provinceId")]
    public int? ProvinceId { get; set; }

    [Column("wardId")]
    public int? WardId { get; set; }

    [Column("update_at")]
    public LocalDateTime? UpdateAt { get; set; }
}
