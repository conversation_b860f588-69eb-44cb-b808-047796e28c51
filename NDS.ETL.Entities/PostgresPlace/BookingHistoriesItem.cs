using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("booking_histories_item")]
public partial class BookingHistoriesItem
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("customer_phone", TypeName = "character varying")]
    public string? CustomerPhone { get; set; }

    [Column("booking_history_id", TypeName = "character varying")]
    public string? BookingHistoryId { get; set; }

    [Column("booking_time", TypeName = "timestamp(0) without time zone")]
    public DateTime? BookingTime { get; set; }

    [Column("search_time", TypeName = "timestamp(0) without time zone")]
    public DateTime? SearchTime { get; set; }
}
