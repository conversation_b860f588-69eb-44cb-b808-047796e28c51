using Elastic.Clients.Elasticsearch;

namespace NDS.ETL.BackgroundJobs.Model.ElasticSearch
{
    /// <summary>
    /// Thông tin cơ bản về một địa điểm trong cặp hành trình
    /// </summary>
    public class PlacePairPoint
    {
        /// <summary>
        /// PairId địa điểm
        /// </summary>
        public long PairId { get; set; }

        /// <summary>
        /// ID địa điểm
        /// </summary>
        public string PlaceId { get; set; }

        /// <summary>
        /// Địa chỉ đầy đủ
        /// </summary>
        public string FullAddress { get; set; }

        /// <summary>
        /// Vị trí địa lý
        /// </summary>
        public Location Location { get; set; }

        /// <summary>
        /// Khoảng cách từ vị trí hiện tại (mét)
        /// </summary>
        public double? Distance { get; set; }

        public string? Name { get; set; }
        public string Address { get; set; }
    }
} 