using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("esconfigs")]
public partial class Esconfig
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("placeIndex")]
    [StringLength(255)]
    public string PlaceIndex { get; set; } = null!;

    [Column("placeQuery")]
    [StringLength(10000)]
    public string PlaceQuery { get; set; } = null!;

    [Column("favouritePlaceQuery")]
    [StringLength(10000)]
    public string FavouritePlaceQuery { get; set; } = null!;

    [Column("favoritePlaceIndex")]
    [StringLength(255)]
    public string FavoritePlaceIndex { get; set; } = null!;

    [Column("historyPlaceIndex")]
    [StringLength(255)]
    public string HistoryPlaceIndex { get; set; } = null!;

    [Column("historyPlaceQuery")]
    [StringLength(10000)]
    public string HistoryPlaceQuery { get; set; } = null!;

    [Column("placeQueryEn")]
    [StringLength(10000)]
    public string PlaceQueryEn { get; set; } = null!;

    [Column("searchHistoryIndex")]
    [StringLength(255)]
    public string SearchHistoryIndex { get; set; } = null!;

    [Column("searchHistoryQuery")]
    [StringLength(10000)]
    public string SearchHistoryQuery { get; set; } = null!;
}
