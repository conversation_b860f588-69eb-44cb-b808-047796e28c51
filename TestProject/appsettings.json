{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore.Hosting.Diagnostics": "Information", "Microsoft.AspNetCore": "Information", "Microsoft": "Information", "Hangfire": "Information", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Information", "Microsoft.EntityFrameworkCore.Database.Command": "Information", "Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker": "Information", "NDS.ETL.BackgroundJobs.Services.ES.ESRecommendationService": "Information"}}, "ConnectionStrings": {"Redis": "*************:6379,*************:6379,*************:6379,password=redisvnpay,abortConnect=false,connectRetry=3,connectTimeout=5000,syncTimeout=5000,keepAlive=180", "PgPlace": "Host=************;Port=5444;Database=******;Username=******;Password=******;Timeout=600;Command Timeout=10000"}, "ElasticSearchConfig": {"Index": ["place-sit", "fav-place-sit", "user-place-sit", "place-pair-sit"], "LogIndex": "place-log-2", "Url": ["http://localhost:9201/"], "UserName": "elastic", "Password": ""}, "AllowedHosts": "*", "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Debug"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "System": "Information", "Hangfire": "Information", "System.Net.Http.HttpClient": "Information", "Microsoft.EntityFrameworkCore": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {CorrelationId} {Level:u3} {Message:lj}{Exception}{NewLine}"}}, {"Name": "Debug", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {CorrelationId} {Level:u3} {Message:lj}{Exception}{NewLine}"}}]}}