using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("places_queue")]
public partial class PlacesQueue
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("districtid")]
    public long Districtid { get; set; }

    [Column("provinceid")]
    public long Provinceid { get; set; }

    [Column("status")]
    public int Status { get; set; }

    [Column("create_date", TypeName = "timestamp without time zone")]
    public DateTime CreateDate { get; set; }

    [Column("update_date", TypeName = "timestamp without time zone")]
    public DateTime UpdateDate { get; set; }

    [Column("place_type", TypeName = "character varying")]
    public string? PlaceType { get; set; }

    [Column("creatorUserId")]
    public string? CreatorUserId { get; set; }

    [Column("lastUpdateUser")]
    public string? LastUpdateUser { get; set; }
}
