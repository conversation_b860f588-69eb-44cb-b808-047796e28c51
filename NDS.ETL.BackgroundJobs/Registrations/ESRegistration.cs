using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.IndexManagement;
using Elastic.Clients.Elasticsearch.Mapping;
using Elastic.Transport;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.Infrastructure.Utils;

namespace NDS.ETL.BackgroundJobs.Registrations;

public static class ESRegistration
{
    public static async Task<IServiceCollection> CreateElasticSearchIndices(this IServiceCollection services,
        ElasticSearchConfig config, IWebHostEnvironment env)
    {
        Console.WriteLine(env.EnvironmentName);

        ElasticsearchClient client;

        if (env.IsDevelopment() || env.IsStaging() || env.EnvironmentName == "Test")
        {
            var settings = new ElasticsearchClientSettings(new Uri(config.Url[0]))
                .DefaultIndex(config.Index[0])
                .RequestTimeout(TimeSpan.FromMinutes(1));

#if DEBUG
            settings.EnableDebugMode();
#endif

            if (!string.IsNullOrEmpty(config.UserName))
            {
                settings.Authentication(new BasicAuthentication(config.UserName, config.Password));
                settings.ServerCertificateValidationCallback((sender, cert, chain, errors) => true);
            }

            client = new ElasticsearchClient(settings);
        }
        else
        {
            var uris = config.Url.Select(x => new Uri(x)).ToArray();
            var pool = new StaticNodePool(uris);
            var settings = new ElasticsearchClientSettings(pool)
                .DefaultIndex(config.Index[0])
                .RequestTimeout(TimeSpan.FromMinutes(5))
                .EnableDebugMode();

            client = new ElasticsearchClient(settings);
        }

        services.AddSingleton(client);
        await CreateIndices(client, config.Index);

        return services;
    }

    private const int NumberOfShards = 6;
    //private static int CalculateNumberOfReplicas(int nodeCount)
    //{
    //    // Nếu chỉ có 1 node, không cần replicas
    //    if (nodeCount <= 1) return 0;

    //    // Nếu có 2-3 nodes, sử dụng 1 replica
    //    if (nodeCount <= 3) return 1;

    //    // Nếu có nhiều hơn 3 nodes, sử dụng 2 replicas (giảm từ 3 xuống 2)
    //    return 2;
    //}

    private static async Task CreateIndices(ElasticsearchClient elasticClient, List<string> configIndex)
    {
        var clusterHealth = await elasticClient.Cluster.HealthAsync();
        var nodeCount = clusterHealth.NumberOfNodes;

        //var numberOfReplicas = CalculateNumberOfReplicas(nodeCount);
        //Bật lại replicas sau khi đã tạo xong index để tăng tốc lúc index
        //PUT /your-index/_settings
        //{
        //    "refresh_interval": "5s",
        //    "number_of_replicas": 1
        //}
        var numberOfReplicas = 0;


        Console.WriteLine($"=== Elasticsearch Cluster Information ===");
        Console.WriteLine($"Number of nodes in cluster: {nodeCount}");
        Console.WriteLine($"Cluster status: {clusterHealth.Status}");
        Console.WriteLine($"Active primary shards: {clusterHealth.ActivePrimaryShards}");
        Console.WriteLine($"Active shards: {clusterHealth.ActiveShards}");
        Console.WriteLine($"Relocating shards: {clusterHealth.RelocatingShards}");
        Console.WriteLine($"Initializing shards: {clusterHealth.InitializingShards}");
        Console.WriteLine($"Unassigned shards: {clusterHealth.UnassignedShards}");
        Console.WriteLine($"Calculated number of replicas: {numberOfReplicas}");
        Console.WriteLine($"========================================");

        foreach (var idx in configIndex)
        {
            if (elasticClient.Indices.Exists(idx).Exists) continue;
            if (idx.StartsWith("fav-place"))
            {
                Console.WriteLine($"=== Creating index: {idx} ===");
                Console.WriteLine($"Index type: Favorite Place");
                Console.WriteLine($"Settings: 1 shard, {numberOfReplicas} replicas");
                var response2 = await elasticClient.Indices.CreateAsync(idx, c => c
                    .Settings(s => s
                        .NumberOfShards(2)
                        .NumberOfReplicas(numberOfReplicas)
                        .MaxInnerResultWindow(20_000)
                        .RefreshInterval("10s")
                        .Translog(t => t
                            .Durability(TranslogDurability.Async)
                            .FlushThresholdSize(new ByteSize("1gb"))
                        )
                        .Codec("best_compression")
                        .Mapping(m => m
                            .TotalFields(tf => tf.Limit(500))
                            .NestedFields(nf => nf.Limit(30))
                        )
                        .Analysis(ConfigureAnalysis)
                    )
                    .Mappings(m => m
                        .Properties<FavPlaceEs>(ConfigurePropertiesSettings)
                    )
                );

                Console.WriteLine(response2.IsValidResponse
                    ? $"---> Successfully created elastic search index: {idx}"
                    : $"---> Failed to create elastic search index: {idx}. Error: {response2.DebugInformation}");
            }
            else if (idx.StartsWith("place-pair"))
            {
                Console.WriteLine($"=== Creating index: {idx} ===");
                Console.WriteLine($"Index type: Booking Place Pair");
                Console.WriteLine($"Settings: 2 shards, {numberOfReplicas} replicas");
                var response3 = await elasticClient.Indices.CreateAsync(idx, c => c
                    .Settings(s => s
                        .NumberOfShards(2)
                        .NumberOfReplicas(numberOfReplicas)
                        .MaxInnerResultWindow(20_000)
                        .RefreshInterval("10s")
                        .Translog(t => t
                            .Durability(TranslogDurability.Async)
                            .FlushThresholdSize(new ByteSize("1gb"))
                        )
                        .Codec("best_compression")
                        .Mapping(m => m
                            .TotalFields(tf => tf.Limit(500))
                            .NestedFields(nf => nf.Limit(30))
                        )
                        .Analysis(ConfigureAnalysis)
                    )
                    .Mappings(m => m
                        .Properties<PlacePairEs>(ConfigurePropertiesSettings)
                    )
                );

                Console.WriteLine(response3.IsValidResponse
                    ? $"---> Successfully created elastic search index: {idx}"
                    : $"---> Failed to create elastic search index: {idx}. Error: {response3.DebugInformation}");
            }
            else if (idx.StartsWith("user-place"))
            {
                Console.WriteLine($"=== Creating index: {idx} ===");
                Console.WriteLine($"Index type: User Place History");
                Console.WriteLine($"Settings: 2 shards, {numberOfReplicas} replicas");
                var responseSearchHistory = await elasticClient.Indices.CreateAsync(idx, c => c
                    .Settings(s => s
                        .NumberOfShards(2)
                        .NumberOfReplicas(numberOfReplicas)
                        .MaxInnerResultWindow(20_000)
                        .RefreshInterval("10s")
                        .Translog(t => t
                            .Durability(TranslogDurability.Async)
                            .FlushThresholdSize(new ByteSize("1gb"))
                        )
                        .Codec("best_compression")
                        .Mapping(m => m
                            .TotalFields(tf => tf.Limit(500))
                            .NestedFields(nf => nf.Limit(30))
                        )
                        .Analysis(ConfigureAnalysis)
                    )
                    .Mappings(m => m
                        .Properties<UserSearchHistoryEs>(ConfigurePropertiesSettings)
                    )
                );

                Console.WriteLine(responseSearchHistory.IsValidResponse
                    ? $"---> Successfully created elastic search index: {idx}"
                    : $"---> Failed to create elastic search index: {idx}. Error: {responseSearchHistory.DebugInformation}");
            }
            else if (idx.StartsWith("place"))
            {
                Console.WriteLine($"=== Creating index: {idx} ===");
                Console.WriteLine($"Index type: Place");
                Console.WriteLine($"Settings: {NumberOfShards} shards, {numberOfReplicas} replicas");
                var response = await elasticClient.Indices.CreateAsync(idx, c => c
                    .Settings(s => s
                        .NumberOfShards(NumberOfShards)
                        .NumberOfReplicas(numberOfReplicas)
                        .MaxInnerResultWindow(20_000)
                        .RefreshInterval("10s")
                        .Translog(t => t
                            .Durability(TranslogDurability.Async)
                            .FlushThresholdSize(new ByteSize("1gb"))
                        )
                        .Codec("best_compression")
                        .Mapping(m => m
                            .TotalFields(tf => tf.Limit(500))
                            .NestedFields(nf => nf.Limit(30))
                        )
                        .Analysis(ConfigureAnalysis)
                    )
                    .Mappings(m => m
                        .Properties<PlacesEs>(ConfigurePropertiesSettings)
                    )
                );

                Console.WriteLine(response.IsSuccess()
                    ? $"---> Successfully created elastic search index: {idx}"
                    : $"---> Failed to create elastic search index: {idx}. Error: {response.DebugInformation}");
            }
        }
    }

    public static void ConfigureAnalysis(IndexSettingsAnalysisDescriptor d)
    {
        d.TokenFilters(tf => tf
            // Gộp nhiều khoảng trắng thành một
            .PatternReplace("whitespace_cleanup", pr => pr
                .Pattern("\\s+")
                .Replacement(" "))

            // Chuẩn hóa từ viết tắt địa chỉ (đảo từ, không dấu)
            //.SynonymGraph("address_synonyms", sg => sg
            //    .Synonyms(new[]
            //    {
            //    "duong, d, đường, đ",
            //    "phuong, p, phường, ph",
            //    "quan, q, quận, qu",
            //    "thanh pho, tp, thành phố",
            //    "huyen, h, huyện",
            //    "xa, x, xã",
            //    "thi tran, tt, thị trấn"
            //    }))

            // Từ dừng tiếng Việt phổ biến
            .Stop("vietnamese_stop", st => st
                .Stopwords(new[] { "va", "cua", "cho", "tai", "tu", "den", "trong", "ngoai", "tren", "duoi", "la" }))

            // Shingle hỗ trợ đảo từ (grab-style)
            .Shingle("address_shingle", sh => sh
                .MinShingleSize(2)
                .MaxShingleSize(4)
                .OutputUnigrams(true))
        );

        d.Analyzers(a => a
            // Dùng cho full-text search: xử lý synonym + shingle
            .Custom("address_search", ca => ca
                .Tokenizer("standard")
                .Filter(new[]
                {
                "lowercase",
                "whitespace_cleanup",
                //"address_synonyms",
                "vietnamese_stop",
                "address_shingle"
                }))

            // Dùng cho autocomplete suggest
            .Custom("address_suggest", ca => ca
                .Tokenizer("standard")
                .Filter(new[]
                {
                "lowercase",
                "whitespace_cleanup"
                }))

            // Dùng để normalize tiếng Việt khác (nếu cần)
            .Custom("icu", ac => ac
                .Tokenizer("standard")
                .Filter(new[]
                {
                "lowercase",
                "asciifolding",
                "whitespace_cleanup"
                }))
        );
    }


    public static void ConfigurePropertiesSettings<T>(PropertiesDescriptor<T> d) where T : IElasticSearchModel
    {

        d.Text(p => p.AddressPermuted, t => t
            .Analyzer("address_search")
            .SearchAnalyzer("address_search")
            .IndexOptions(IndexOptions.Positions)
            .Norms(false)
            .TermVector(TermVectorOption.WithPositionsOffsets) // có thể dùng để highlight
            .Fields(ff => ff
                .Text("suggest", st => st
                    .Analyzer("address_suggest")
                    .SearchAnalyzer("address_search")
                    .IndexOptions(IndexOptions.Positions))
                .Completion("completion_with_location", c => c
                    .Analyzer("address_suggest")
                    .Contexts([
                        new SuggestContext
                        {
                            Name = "location_50km",
                            Type = "category",  // Sử dụng category thay vì geo
                            Path = new Field("h3.4")  // Trường h3 cấp độ 4 cho bán kính 50km
                        },
                        //new SuggestContext
                        //{
                        //    Name = "location",
                        //    Path = "location", // Trường location dạng geo_point
                        //    Type = "geo",
                        //    Precision = 5 // Tốt nhất cho yêu cầu <50km

                        //    //| Precision | Bán kính gần đúng |
                        //    //| --------- | ----------------- |
                        //    //| 1         | ~5,000 km        |
                        //    //| 2         | ~625 km          |
                        //    //| 3         | ~123 km          |
                        //    //| 4         | ~20 km           |
                        //    //| 5         | ~4.8 km          |
                        //    //| 6         | ~1.2 km          |
                        //    //| 7         | ~150 m           |

                        //}
                    ])
                    .PreservePositionIncrements(true)
                    .PreserveSeparators(true)
                    .MaxInputLength(80))
                //.Completion("completion", c => c
                //    .Analyzer("address_suggest")
                //    .PreservePositionIncrements(true)
                //    .PreserveSeparators(true)
                //    .MaxInputLength(80))
            ))

            .Keyword(p => p.MasterAddress, k => k
                .Index(true)
                .IgnoreAbove(256)
                .DocValues(false)
                .Store(false)
                .Norms(false)
                .EagerGlobalOrdinals(false));

        d.Text(p => p.Keywords, t => t
            .Analyzer("address_search") // Analyzer chính cho tìm kiếm
            .SearchAnalyzer("address_search")
            .IndexOptions(IndexOptions.Positions)
            .Norms(false)
            .Fields(ff => ff
                    .Keyword("raw", k => k
                        .IgnoreAbove(256)) // Giữ nguyên bản gốc (dạng keyword)
                    .Text("icu", st => st
                        .Analyzer("icu")) // Thêm subfield dùng ICU cho Unicode/tìm kiếm tiếng Việt tốt
            )
        );

        switch (typeof(T))
        {
            case var _ when typeof(T) == typeof(PlacesEs):
                var pd = d as PropertiesDescriptor<PlacesEs>;
                pd.Nested(n => n.SubPlaces, np => np
                    .Properties(SubPlacesEs.MappingProperties)
                );
                PlacesEs.MappingProperties(pd);
                break;
            case var _ when typeof(T) == typeof(FavPlaceEs):
                FavPlaceEs.MappingProperties(d as PropertiesDescriptor<FavPlaceEs>);
                break;
            case var _ when typeof(T) == typeof(UserSearchHistoryEs):
                UserSearchHistoryEs.MappingProperties(d as PropertiesDescriptor<UserSearchHistoryEs>);
                break;
            case var _ when typeof(T) == typeof(PlacePairEs):
                PlacePairEs.MappingProperties(d as PropertiesDescriptor<PlacePairEs>);
                break;
        }

    }
}