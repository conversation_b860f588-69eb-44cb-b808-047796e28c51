using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Core.Bulk;
using Elastic.Clients.Elasticsearch.IndexManagement;
using Microsoft.Extensions.Logging;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch.FavPlaceSync;
using IndexState = NDS.ETL.BackgroundJobs.Model.ElasticSearch.FavPlaceSync.IndexState;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.FavPlaceSync;

/// <summary>
/// Dịch vụ làm việc với dữ liệu Elasticsearch
/// </summary>
public class ElasticsearchDataService
{
    private readonly ILogger _logger;
    private readonly ElasticsearchClient _elasticClient;
    private readonly string _indexName;
    private const int FULL_SYNC_DAYS_THRESHOLD = 7; // <PERSON><PERSON> ngày kể từ lần sync cuối để trigger full sync

    public ElasticsearchDataService(
        ILogger logger,
        ElasticsearchClient elasticClient,
        string indexName)
    {
        _logger = logger;
        _elasticClient = elasticClient;
        _indexName = indexName;
    }

    /// <summary>
    /// Lấy giá trị ID lớn nhất từ Elasticsearch
    /// </summary>
    public async Task<long?> GetMaxIdFromElasticsearch(CancellationToken stoppingToken)
    {
        try
        {
            // Kiểm tra xem index có tồn tại hay không
            var indexExistsResponse = await _elasticClient.Indices.ExistsAsync(_indexName, stoppingToken);
            if (!indexExistsResponse.Exists)
            {
                _logger.LogWarning($"ES index '{_indexName}' doesn't exist");
                return null;
            }

            // Tìm giá trị ID lớn nhất sử dụng max aggregation
            var searchResponse = await _elasticClient.SearchAsync<FavPlaceEs>(s => s
                .Index(_indexName)
                .Size(0) // Không cần trả về documents
                .Aggregations(aggs => aggs
                    .Add("max_id", d =>
                    {
                        d.Max(x =>
                        {
                            x.Field(f => f.Id);
                        });
                    })
                ), stoppingToken);

            if (!searchResponse.IsValidResponse)
            {
                _logger.LogError($"Error retrieving max ID from ES: {searchResponse.DebugInformation}");
                return null;
            }

            // Lấy kết quả từ aggregation
            if (searchResponse.Aggregations == null)
            {
                _logger.LogWarning("No aggregations returned from ES");
                return null;
            }
            
            var maxAgg = searchResponse.Aggregations.GetMax("max_id");
            if (maxAgg == null || maxAgg.Value == null)
            {
                _logger.LogWarning("No max aggregation results found");
                return null;
            }

            double? maxIdDouble = maxAgg.Value.GetValueOrDefault();
            if (!(maxIdDouble > 0))
            {
                _logger.LogWarning("Invalid max ID value returned from aggregation");
                return null;
            }

            return (long)maxIdDouble;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving max ID from ES");
            return null;
        }
    }

    /// <summary>
    /// Phương thức lấy thông tin về index trong Elasticsearch
    /// </summary>
    public async Task<IndexState?> GetIndexStats(CancellationToken stoppingToken)
    {
        try
        {
            var indexExistsResponse = await _elasticClient.Indices.ExistsAsync(_indexName, stoppingToken);
            if (!indexExistsResponse.Exists)
            {
                return null;
            }

            var statsResponse = await _elasticClient.Indices.StatsAsync(stoppingToken);
            if (!statsResponse.IsValidResponse)
            {
                _logger.LogError($"Error retrieving index stats: {statsResponse.DebugInformation}");
                return null;
            }

            if (statsResponse.Indices == null || !statsResponse.Indices.TryGetValue(_indexName, out var indexStats))
            {
                _logger.LogError($"Index '{_indexName}' not found in stats response");
                return null;
            }

            // Lấy thời gian tạo index
            long docCount = 0;
            if (indexStats.Total != null && indexStats.Total.Docs != null)
            {
                docCount = indexStats.Total.Docs.Count;
            }
            else
            {
                _logger.LogWarning("Index stats information is incomplete");
            }

            var idxResponse = await _elasticClient.Indices.GetAsync(_indexName, stoppingToken);
            idxResponse.Indices.TryGetValue(_indexName, out var indexState);
            var indexCreationDate = indexState?.Settings?.Index?.CreationDate;
            var creationDateTime = indexCreationDate.HasValue
                ? DateTimeOffset.FromUnixTimeMilliseconds(indexCreationDate.Value).UtcDateTime
                : DateTime.UtcNow.AddDays(-FULL_SYNC_DAYS_THRESHOLD - 1);

            return new IndexState 
            { 
                DocumentCount = docCount,
                CreationTime = creationDateTime
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving index stats from ES");
            return null;
        }
    }

    /// <summary>
    /// Lấy thời gian đồng bộ cuối cùng từ ElasticSearch
    /// </summary>
    public async Task<DateTime> GetLastSyncTimeFromEs(CancellationToken stoppingToken)
    {
        try
        {
            // Kiểm tra xem index có tồn tại hay không
            var indexExistsResponse = await _elasticClient.Indices.ExistsAsync(_indexName, stoppingToken);
            if (!indexExistsResponse.Exists)
            {
                // Nếu index chưa tồn tại, trả về thời gian rất xa trong quá khứ để đảm bảo tất cả bản ghi đều được xử lý
                return DateTime.MinValue;
            }

            // Tìm giá trị UpdatedAt lớn nhất sử dụng max aggregation
            var searchResponse = await _elasticClient.SearchAsync<FavPlaceEs>(s => s
                .Index(_indexName)
                .Size(0) // Không cần trả về documents
                .Aggregations(aggs => aggs
                    .Add("max_updated_at", d =>
                    {
                        d.Max(x =>
                        {
                            x.Field(f => f.UpdatedAt);
                        });
                    })
                ), stoppingToken);

            if (!searchResponse.IsValidResponse)
            {
                _logger.LogError($"Error retrieving max UpdatedAt from ES: {searchResponse.DebugInformation}");
                return DateTime.MinValue;
            }

            // Lấy kết quả từ aggregation
            if (searchResponse.Aggregations == null)
            {
                _logger.LogWarning("No aggregations returned from ES");
                return DateTime.MinValue;
            }
            
            var maxAgg = searchResponse.Aggregations.GetMax("max_updated_at");
            if (maxAgg == null || maxAgg.Value == null)
            {
                _logger.LogWarning("No max aggregation results found for UpdatedAt");
                return DateTime.MinValue;
            }

            double? maxUpdatedAtDouble = maxAgg.Value.GetValueOrDefault();
            if (maxUpdatedAtDouble <= 0)
            {
                _logger.LogWarning("Invalid max UpdatedAt value returned from aggregation");
                return DateTime.MinValue;
            }

            // Convert double value to DateTime (assuming it's a Unix timestamp in milliseconds)
            var lastSyncTime = DateTimeOffset.FromUnixTimeMilliseconds((long)maxUpdatedAtDouble).UtcDateTime;
            return lastSyncTime;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving last sync time from ES");
            return DateTime.MinValue;
        }
    }

    /// <summary>
    /// Lấy thời gian tạo mới nhất từ ElasticSearch cho các bản ghi mới (có UpdatedAt = null)
    /// </summary>
    public async Task<DateTime> GetLastCreateTimeFromEs(CancellationToken stoppingToken)
    {
        try
        {
            // Kiểm tra xem index có tồn tại hay không
            var indexExistsResponse = await _elasticClient.Indices.ExistsAsync(_indexName, stoppingToken);
            if (!indexExistsResponse.Exists)
            {
                // Nếu index chưa tồn tại, trả về thời gian xa trong quá khứ
                return DateTime.MinValue;
            }

            // Tìm giá trị CreatedAt lớn nhất cho các bản ghi mới (UpdatedAt = null)
            var searchResponse = await _elasticClient.SearchAsync<FavPlaceEs>(s => s
                .Index(_indexName)
                .Size(0)
                .Query(q => q
                    .Bool(b => b
                        .MustNot(mn => mn
                            .Exists(e => e
                                .Field(f => f.UpdatedAt)
                            )
                        )
                    )
                )
                .Aggregations(aggs => aggs
                    .Add("max_created_at", d =>
                    {
                        d.Max(x =>
                        {
                            x.Field(f => f.CreatedAt);
                        });
                    })
                ), stoppingToken);

            if (!searchResponse.IsValidResponse)
            {
                _logger.LogError($"Error retrieving max CreatedAt from ES: {searchResponse.DebugInformation}");
                return DateTime.MinValue;
            }

            // Lấy kết quả từ aggregation
            if (searchResponse.Aggregations == null)
            {
                _logger.LogWarning("No aggregations returned from ES for CreatedAt");
                return DateTime.MinValue;
            }
            
            var maxAgg = searchResponse.Aggregations.GetMax("max_created_at");
            if (maxAgg == null || maxAgg.Value == null)
            {
                _logger.LogWarning("No max aggregation results found for CreatedAt");
                return DateTime.MinValue;
            }

            double? maxCreatedAtDouble = maxAgg.Value.GetValueOrDefault();
            if (maxCreatedAtDouble <= 0)
            {
                _logger.LogWarning("Invalid max CreatedAt value returned from aggregation");
                return DateTime.MinValue;
            }

            // Convert double value to DateTime (assuming it's a Unix timestamp in milliseconds)
            var lastCreateTime = DateTimeOffset.FromUnixTimeMilliseconds((long)maxCreatedAtDouble).UtcDateTime;
            return lastCreateTime;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving last create time from ES");
            return DateTime.MinValue;
        }
    }
} 