using System.Diagnostics;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Context;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Entities.Context;
using NDS.ETL.Infrastructure.Common;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.ScanPlaces;

public class InsertPlacesScanResultTask : RecurringJobBase<PgPlaceContext>
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<InsertPlacesScanResultTask> _logger;
    private readonly IESPlacesService _iesPlacesServiceService;
    private readonly IScopedResolver<NpgPlaceContext> _dbContextResolver;

    public InsertPlacesScanResultTask(IConfiguration configuration,
        ILogger<InsertPlacesScanResultTask> logger,
        IScopedResolver<NpgPlaceContext> dbContextResolver,
        IESPlacesService iesPlacesServiceService)
        : base(configuration, logger, null)
    {
        _logger = logger;
        _dbContextResolver = dbContextResolver;
        _iesPlacesServiceService = iesPlacesServiceService;
    }

    public override string JobId => "InsertPlacesScanResult";

    public override string ConfigSection => "JobPgSyncPlace";

    public override async Task DoWorkAsync(CancellationToken stoppingToken)
    {
        var begin = Stopwatch.GetTimestamp();
        _logger.LogInformation("InsertPlacesScanResultTask DoWorkAsync started");

        try
        {
            await _dbContextResolver.ResolveAsync(async dbContext =>
            {
                dbContext.Database.SetCommandTimeout(900);

                var placesQueuesWithStatus3 = await dbContext.PlacesQueues
                    .Where(x => x.Status == 3)
                    .OrderBy(x => x.CreateDate)
                    .ToListAsync(stoppingToken)
                    .ConfigureAwait(false);

                if (!placesQueuesWithStatus3.Any())
                {
                    _logger.LogInformation("No place queues found with Status = 3 (scored)");
                    return true;
                }

                _logger.LogInformation("Found {Count} districts with status = 3 to process", placesQueuesWithStatus3.Count);

                int processedCount = 0;
                int successCount = 0;

                foreach (var placeQueue in placesQueuesWithStatus3)
                {
                    if (stoppingToken.IsCancellationRequested)
                    {
                        _logger.LogInformation("Cancellation requested, stopping processing");
                        break;
                    }

                    var districtStopwatch = Stopwatch.StartNew();
                    var districtId = (int)placeQueue.Districtid;

                    try
                    {
                        _logger.LogInformation("Processing district {DistrictId} (Queue ID: {QueueId})", 
                            districtId, placeQueue.Id);

                        var insertResult = await _iesPlacesServiceService.InsertPlacesScanResult(districtId, stoppingToken);

                        if (insertResult)
                        {
                            var freshPlaceQueue = await dbContext.PlacesQueues.FindAsync(placeQueue.Id, stoppingToken);
                            if (freshPlaceQueue != null)
                            {
                                freshPlaceQueue.Status = 4;
                                freshPlaceQueue.UpdateDate = DateTime.Now;
                                await dbContext.SaveChangesAsync(stoppingToken);
                                
                                successCount++;
                                _logger.LogInformation(
                                    "Successfully processed district {DistrictId} and updated status to 4 (synthesized) in {ElapsedSeconds:F2} seconds",
                                    districtId, districtStopwatch.Elapsed.TotalSeconds);
                            }
                            else
                            {
                                _logger.LogWarning("Could not find PlaceQueue with ID {QueueId} to update status", placeQueue.Id);
                            }
                        }
                        else
                        {
                            _logger.LogError("Failed to insert places scan result for district {DistrictId}", districtId);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing district {DistrictId}", districtId);
                    }
                    finally
                    {
                        processedCount++;
                        districtStopwatch.Stop();
                    }
                }

                var end = Stopwatch.GetTimestamp();
                _logger.LogInformation(
                    "Completed InsertPlacesScanResultTask: Processed {ProcessedCount}/{TotalCount} districts, " +
                    "Successfully synthesized {SuccessCount} districts in {TotalTime:F2} seconds",
                    processedCount, placesQueuesWithStatus3.Count, successCount,
                    TimeSpan.FromTicks(end - begin).TotalSeconds);

                return true;
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred in InsertPlacesScanResultTask");
            throw;
        }
    }

    public override Task IndexMany<T>(List<T> source, CancellationToken cancellationToken, int take = 1000)
    {
        return Task.CompletedTask;
    }

    public override Task IndexMany<T>(List<T> source, CancellationToken cancellationToken, string esIndex, int take = 1000)
    {
        return Task.CompletedTask;
    }
} 