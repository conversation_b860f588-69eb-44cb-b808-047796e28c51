using System;

namespace NDS.ETL.BackgroundJobs.Model.ElasticSearch.PlacePairSync
{
    /// <summary>
    /// Thông tin thống kê từ cơ sở dữ liệu
    /// </summary>
    public class DatabaseStats
    {
        /// <summary>
        /// ID lớn nhất trong cơ sở dữ liệu
        /// </summary>
        public long MaxId { get; set; }
        
        /// <summary>
        /// Thời gian cập nhật mới nhất trong cơ sở dữ liệu
        /// </summary>
        public DateTime MaxUpdateAt { get; set; }
        
        /// <summary>
        /// Thời gian tạo mới nhất trong cơ sở dữ liệu (cho các bản ghi mới có UpdateAt = null)
        /// </summary>
        public DateTime MaxCreateAt { get; set; }
        
        /// <summary>
        /// Tổng số bản ghi trong cơ sở dữ liệu
        /// </summary>
        public int TotalRecords { get; set; }
        
        /// <summary>
        /// <PERSON><PERSON> bản ghi mới (có CreatedAt nhưng UpdateAt là null)
        /// </summary>
        public int NewRecordsCount { get; set; }
    }

    /// <summary>
    /// Thông tin thống kê từ Elasticsearch
    /// </summary>
    public class ElasticsearchStats
    {
        /// <summary>
        /// Index có tồn tại hay không
        /// </summary>
        public bool IndexExists { get; set; }
        
        /// <summary>
        /// Thời gian cập nhật mới nhất trong Elasticsearch
        /// </summary>
        public DateTime MaxUpdateAt { get; set; }
        
        /// <summary>
        /// Thời gian tạo mới nhất trong Elasticsearch (cho các bản ghi có UpdateAt = null)
        /// </summary>
        public DateTime MaxCreateAt { get; set; }
        
        /// <summary>
        /// Thông tin về index
        /// </summary>
        public IndexState? IndexInfo { get; set; }
    }

    /// <summary>
    /// Chiến lược đồng bộ dữ liệu
    /// </summary>
    public class SyncStrategy
    {
        /// <summary>
        /// Có thực hiện full sync hay không
        /// </summary>
        public bool IsFullSync { get; set; }
        
        /// <summary>
        /// Số lượng bản ghi cần đồng bộ
        /// </summary>
        public int RecordsToSync { get; set; }
        
        /// <summary>
        /// Lý do cho chiến lược đồng bộ
        /// </summary>
        public string Reason { get; set; }
    }

    /// <summary>
    /// Thống kê quá trình đồng bộ
    /// </summary>
    public class SyncStats
    {
        /// <summary>
        /// Tổng số bản ghi cần xử lý
        /// </summary>
        public int TotalCount { get; set; }
        
        /// <summary>
        /// Số bản ghi đã xử lý thành công
        /// </summary>
        public int TotalProcessed { get; set; }
        
        /// <summary>
        /// Số batch đã xử lý thành công
        /// </summary>
        public int BatchSuccessCount { get; set; }
        
        /// <summary>
        /// Số batch xử lý thất bại
        /// </summary>
        public int BatchFailureCount { get; set; }
        
        /// <summary>
        /// Số lần retry
        /// </summary>
        public int RetryCount { get; set; }
    }

    /// <summary>
    /// Thông tin về index Elasticsearch
    /// </summary>
    public class IndexState
    {
        /// <summary>
        /// Thời gian tạo index
        /// </summary>
        public DateTime CreationTime { get; set; }
        
        /// <summary>
        /// Số bản ghi trong index
        /// </summary>
        public long DocumentCount { get; set; }
    }
} 