using Elastic.Clients.Elasticsearch;
using KnowledgeBase.Core.Extensions;
using NDS.ETL.BackgroundJobs.Extensions;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.Infrastructure.Utils;
using System.Collections.Concurrent;
using Elastic.Clients.Elasticsearch.QueryDsl;

namespace NDS.ETL.BackgroundJobs.Services.ES
{
    /// <summary>
    /// Dịch vụ xử lý cặp địa điểm trên Elasticsearch
    /// </summary>
    public class ESPlacePairsService : IESPlacePairs
    {
        private readonly ElasticsearchClient _elasticClient;
        private readonly string _indexName;
        private readonly ILogger<ESPlacePairsService> _logger;
        
        // <PERSON><PERSON><PERSON> hình song song tối ưu cho xử lý đa luồng
        private readonly ParallelOptions _parallelOptions;

        public ESPlacePairsService(
            ElasticsearchClient elasticClient,
            IConfiguration configuration,
            ILogger<ESPlacePairsService> logger)
        {
            _elasticClient = elasticClient;
            _logger = logger;

            var indexNames = configuration.Bind<ElasticSearchConfig>(nameof(ElasticSearchConfig));
            _indexName = indexNames.Index?.FirstOrDefault(x => x.StartsWith("place-pair")) ?? "place-pair";

            // Thiết lập cấu hình song song với số luồng tối đa bằng số lõi CPU vật lý
            // Nhưng giới hạn ở mức tối đa 8 luồng để tránh sử dụng quá nhiều tài nguyên
            _parallelOptions = new ParallelOptions
            {
                MaxDegreeOfParallelism = Math.Min(Environment.ProcessorCount, 8)
            };
            
            _logger.LogInformation("Initialized ESPlacePairsService with {Threads} parallel threads", 
                _parallelOptions.MaxDegreeOfParallelism);
        }

        /// <summary>
        /// Lấy danh sách các lộ trình gần đây của người dùng bao gồm cả thông tin cặp địa điểm
        /// </summary>
        /// <param name="userId">ID người dùng</param>
        /// <param name="phoneNumber">Số điện thoại</param>
        /// <param name="limit">Số lượng kết quả tối đa</param>
        /// <param name="page">Số trang (bắt đầu từ 0)</param>
        /// <param name="currentLocation">Vị trí hiện tại</param>
        /// <param name="minDistanceMeters">Khoảng cách tối thiểu tính bằng mét</param>
        /// <param name="cancellationToken">Token hủy</param>
        /// <returns>Danh sách các cặp địa điểm đầy đủ</returns>
        public async Task<List<PlacePairRoute>> GetRecentRoutesWithPairedPlaces(
            string? userId,
            string? phoneNumber,
            int limit = 5,
            int page = 1,
            LatLonGeoLocation? currentLocation = null,
            double? minDistanceMeters = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                // Kiểm tra thông tin người dùng
                if (string.IsNullOrEmpty(userId) && string.IsNullOrEmpty(phoneNumber))
                {
                    throw new ArgumentException("Either userId or phoneNumber must be provided");
                }

                // Tạo truy vấn lấy lộ trình gần đây
                // Truy vấn này sẽ lấy các lộ trình của người dùng trong 15 ngày gần đây
                var queryDescriptor = _elasticClient.CreateRecentRoutesQuery(
                    userId,
                    phoneNumber,
                    DateTime.UtcNow.AddDays(-15), // Lấy lộ trình trong 15 ngày gần đây
                    limit, // Lấy nhiều hơn để sau khi lọc vẫn đủ
                    page, // Elasticsearch sử dụng page bắt đầu từ 1
                    _indexName,
                    currentLocation,
                    null,
                    minDistanceMeters ?? 1000 // Sử dụng giá trị được truyền vào hoặc mặc định 1km
                );

                // Thực hiện truy vấn
                var searchResponse = await _elasticClient.SearchAsync(queryDescriptor, cancellationToken);

                if (!searchResponse.IsValidResponse || searchResponse.Documents.Count == 0)
                {
                    _logger.LogWarning("No recent routes found for user {UserId} or phone {Phone} on page {Page}", userId, phoneNumber, page);
                    return new List<PlacePairRoute>();
                }

                _logger.LogInformation("Found {Count} recent place records on page {Page}", searchResponse.Documents.Count, page);

                // Trích xuất danh sách BookingPlacePairId từ kết quả
                // BookingPlacePairId là ID duy nhất cho một cặp điểm đi-đến
                var bookingPlacePairIds = searchResponse.Documents.ExtractBookingPlacePairIds();

                // Tạo truy vấn lấy các cặp địa điểm
                // Truy vấn này lấy đầy đủ thông tin cho mỗi cặp địa điểm (gồm điểm đi và điểm đến)
                var pairsQueryDescriptor = _elasticClient.GetByPlacePairIdsQuery(
                    bookingPlacePairIds,
                    _indexName,
                    bookingPlacePairIds.Count * 2 // Mỗi ID có tối đa 2 điểm (đi và đến)
                );

                // Thực hiện truy vấn lấy các cặp địa điểm
                var pairsSearchResponse = await _elasticClient.SearchAsync(pairsQueryDescriptor, cancellationToken);

                if (!pairsSearchResponse.IsValidResponse)
                {
                    _logger.LogError("Failed to retrieve paired places: {Error}", pairsSearchResponse.ElasticsearchServerError?.Error?.ToString());
                    // Nếu không lấy được cặp, vẫn trả về kết quả gốc
                    var singlePlaces = OrganizeSinglePlacesParallel(searchResponse.Documents, currentLocation);
                    return singlePlaces.Take(limit).ToList();
                }

                // Tổ chức các cặp địa điểm
                // Gộp các địa điểm riêng lẻ thành các cặp hoàn chỉnh
                var placePairs = pairsSearchResponse.Documents.OrganizePlacePairs();
                var routes = placePairs.ToPlacePairRoutes();

                // Điều chỉnh hướng đi về dựa vào vị trí hiện tại
                if (currentLocation != null)
                {
                    routes = AdjustRouteDirections(routes, currentLocation);
                }

                // Sắp xếp kết quả theo thứ tự ưu tiên và trả về số lượng theo yêu cầu
                return SortRoutes(routes, currentLocation).Take(limit).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving recent routes with paired places for page {Page}", page);
                return new List<PlacePairRoute>();
            }
        }

        /// <summary>
        /// Điều chỉnh hướng đi về của các cặp địa điểm dựa vào vị trí hiện tại
        /// </summary>
        /// <param name="routes">Danh sách lộ trình cần điều chỉnh</param>
        /// <param name="currentLocation">Vị trí hiện tại của người dùng</param>
        /// <returns>Danh sách lộ trình đã điều chỉnh</returns>
        private List<PlacePairRoute> AdjustRouteDirections(List<PlacePairRoute> routes, LatLonGeoLocation currentLocation)
        {
            _logger.LogInformation("Adjusting route directions based on current location: {Lat}, {Lon}", 
                currentLocation.Lat, currentLocation.Lon);

            // Tạo một list mới để lưu kết quả từ song song
            var adjustedRoutes = new ConcurrentBag<PlacePairRoute>(routes);

            Parallel.ForEach(routes, _parallelOptions, route =>
            {
                // Chỉ xử lý các lộ trình có cả điểm đi và điểm đến
                if (route is { Pickup.Location: not null, Arrival.Location: not null })
                {
                    // Tính khoảng cách từ vị trí hiện tại đến điểm đón hiện tại
                    var distanceToPickup = H3Helper.CalculateDistance(
                        currentLocation.Lat,
                        currentLocation.Lon,
                        route.Pickup.Location.Lat,
                        route.Pickup.Location.Lon);

                    // Tính khoảng cách từ vị trí hiện tại đến điểm đến hiện tại
                    var distanceToArrival = H3Helper.CalculateDistance(
                        currentLocation.Lat,
                        currentLocation.Lon,
                        route.Arrival.Location.Lat,
                        route.Arrival.Location.Lon);

                    // Lưu khoảng cách đến điểm đón vào route để sắp xếp theo sau này
                    route.DistanceToPickup = distanceToPickup;

                    // Nếu điểm đến gần với vị trí hiện tại hơn điểm đón
                    // thì đảo ngược thứ tự để người dùng đi từ vị trí gần nhất
                    if (distanceToArrival < distanceToPickup)
                    {
                        _logger.LogDebug("Swapping pickup and arrival for route {RouteId} based on current location", 
                            route.BookingPlacePairId);

                        // Hoán đổi điểm đón và điểm đến sử dụng tuple assignment
                        (route.Pickup, route.Arrival) = (route.Arrival, route.Pickup);

                        // Cập nhật khoảng cách mới đến điểm đón (sau khi đã đổi)
                        route.DistanceToPickup = distanceToArrival;
                    }
                }
                else if (route is { Pickup: not null } && route.Pickup.Location != null)
                {
                    // Nếu chỉ có điểm đón, tính khoảng cách để sắp xếp sau này
                    route.DistanceToPickup = H3Helper.CalculateDistance(
                        currentLocation.Lat,
                        currentLocation.Lon,
                        route.Pickup.Location.Lat,
                        route.Pickup.Location.Lon);
                }
            });

            return adjustedRoutes.ToList();
        }

        /// <summary>
        /// Tổ chức các địa điểm đơn lẻ thành PlacePairRoute khi không có thông tin cặp (bản song song)
        /// </summary>
        private List<PlacePairRoute> OrganizeSinglePlacesParallel(IReadOnlyCollection<PlacePairEs> documents, LatLonGeoLocation? currentLocation)
        {
            var result = new ConcurrentBag<PlacePairRoute>();

            // Nhóm các document theo BookingPlacePairId để xác định cặp pickup-arrival
            var groupedDocs = documents.GroupBy(d => d.BookingPlacePairId).ToList();

            Parallel.ForEach(groupedDocs, _parallelOptions, group =>
            {
                var firstDoc = group.FirstOrDefault();
                if (firstDoc == null) return;

                // Tạo đối tượng PlacePairRoute mới với thông tin cơ bản từ document
                var route = new PlacePairRoute
                {
                    BookingPlacePairId = firstDoc.BookingPlacePairId,
                    CustomerId = firstDoc.CustomerId,
                    CustomerPhone = firstDoc.CustomerPhone,
                    BookingCount = firstDoc.BookingCount,
                    LastBookingAt = firstDoc.LastBookingAt,
                    LastSearchAt = firstDoc.LastSearchAt,
                    LastEstimateAmount = firstDoc.LastEstimateAmount,
                    LastNote = firstDoc.LastNote
                };

                // Xác định pickup và arrival từ nhóm documents
                foreach (var doc in group)
                {
                    // Gán thông tin dựa vào loại (đi/đến)
                    if (doc.Type == PlacePairType.Pickup) // Điểm đi
                    {
                        route.Pickup = new PlacePairPoint
                        {
                            PlaceId = doc.PlaceId,
                            FullAddress = doc.FullAddress,
                            Location = doc.Location,
                        };
                    }
                    else // Điểm đến
                    {
                        route.Arrival = new PlacePairPoint
                        {
                            PlaceId = doc.PlaceId,
                            FullAddress = doc.FullAddress,
                            Location = doc.Location,
                        };
                    }
                }

                // Tính khoảng cách và điều chỉnh hướng nếu cần thiết
                if (currentLocation != null)
                {
                    // Nếu có cả pickup và arrival
                    if (route is { Pickup.Location: not null, Arrival.Location: not null })
                    {
                        // Tính khoảng cách đến pickup và arrival
                        var distanceToPickup = H3Helper.CalculateDistance(
                            currentLocation.Lat,
                            currentLocation.Lon,
                            route.Pickup.Location.Lat,
                            route.Pickup.Location.Lon);
                            
                        var distanceToArrival = H3Helper.CalculateDistance(
                            currentLocation.Lat,
                            currentLocation.Lon,
                            route.Arrival.Location.Lat,
                            route.Arrival.Location.Lon);
                            
                        // Nếu arrival gần với vị trí hiện tại hơn pickup
                        // thì đảo ngược thứ tự để route đi từ gần đến xa
                        if (distanceToArrival < distanceToPickup)
                        {
                            (route.Pickup, route.Arrival) = (route.Arrival, route.Pickup);
                            route.DistanceToPickup = distanceToArrival;
                        }
                        else
                        {
                            route.DistanceToPickup = distanceToPickup;
                        }
                    }
                    // Nếu chỉ có pickup
                    else if (route is { Pickup.Location: not null })
                    {
                        route.DistanceToPickup = H3Helper.CalculateDistance(
                            currentLocation.Lat,
                            currentLocation.Lon,
                            route.Pickup.Location.Lat,
                            route.Pickup.Location.Lon);
                    }
                }

                result.Add(route);
            });

            return result.ToList();
        }

        /// <summary>
        /// Tổ chức các địa điểm đơn lẻ thành PlacePairRoute khi không có thông tin cặp
        /// </summary>
        private List<PlacePairRoute> OrganizeSinglePlaces(IReadOnlyCollection<PlacePairEs> documents, LatLonGeoLocation? currentLocation)
        {
            var result = new List<PlacePairRoute>();

            // Nhóm các document theo BookingPlacePairId để xác định cặp pickup-arrival
            var groupedDocs = documents.GroupBy(d => d.BookingPlacePairId);

            foreach (var group in groupedDocs)
            {
                var firstDoc = group.FirstOrDefault();
                if (firstDoc == null) continue;

                // Tạo đối tượng PlacePairRoute mới với thông tin cơ bản từ document
                var route = new PlacePairRoute
                {
                    BookingPlacePairId = firstDoc.BookingPlacePairId,
                    CustomerId = firstDoc.CustomerId,
                    CustomerPhone = firstDoc.CustomerPhone,
                    BookingCount = firstDoc.BookingCount,
                    LastBookingAt = firstDoc.LastBookingAt,
                    LastSearchAt = firstDoc.LastSearchAt,
                    LastEstimateAmount = firstDoc.LastEstimateAmount,
                    LastNote = firstDoc.LastNote
                };

                // Xác định pickup và arrival từ nhóm documents
                foreach (var doc in group)
                {
                    // Gán thông tin dựa vào loại (đi/đến)
                    if (doc.Type == PlacePairType.Pickup) // Điểm đi
                    {
                        route.Pickup = new PlacePairPoint
                        {
                            PlaceId = doc.PlaceId,
                            FullAddress = doc.FullAddress,
                            Location = doc.Location,
                        };
                    }
                    else // Điểm đến
                    {
                        route.Arrival = new PlacePairPoint
                        {
                            PlaceId = doc.PlaceId,
                            FullAddress = doc.FullAddress,
                            Location = doc.Location,
                        };
                    }
                }

                // Tính khoảng cách và điều chỉnh hướng nếu cần thiết
                if (currentLocation != null)
                {
                    // Nếu có cả pickup và arrival
                    if (route is { Pickup.Location: not null, Arrival.Location: not null })
                    {
                        // Tính khoảng cách đến pickup và arrival
                        var distanceToPickup = H3Helper.CalculateDistance(
                            currentLocation.Lat,
                            currentLocation.Lon,
                            route.Pickup.Location.Lat,
                            route.Pickup.Location.Lon);

                        var distanceToArrival = H3Helper.CalculateDistance(
                            currentLocation.Lat,
                            currentLocation.Lon,
                            route.Arrival.Location.Lat,
                            route.Arrival.Location.Lon);

                        // Nếu arrival gần với vị trí hiện tại hơn pickup
                        // thì đảo ngược thứ tự để route đi từ gần đến xa
                        if (distanceToArrival < distanceToPickup)
                        {
                            (route.Pickup, route.Arrival) = (route.Arrival, route.Pickup);
                            route.DistanceToPickup = distanceToArrival;
                        }
                        else
                        {
                            route.DistanceToPickup = distanceToPickup;
                        }
                    }
                    // Nếu chỉ có pickup
                    else if (route is { Pickup.Location: not null })
                    {
                        route.DistanceToPickup = H3Helper.CalculateDistance(
                            currentLocation.Lat,
                            currentLocation.Lon,
                            route.Pickup.Location.Lat,
                            route.Pickup.Location.Lon);
                    }
                }

                result.Add(route);
            }

            return result;
        }

        /// <summary>
        /// Sắp xếp các cặp địa điểm theo thứ tự ưu tiên
        /// </summary>
        private List<PlacePairRoute> SortRoutes(List<PlacePairRoute> routes, LatLonGeoLocation? currentLocation)
        {
            if (routes.Count <= 1)
            {
                return routes; // Không cần sắp xếp nếu chỉ có 0 hoặc 1 item
            }

            // Ưu tiên sắp xếp theo:
            // 1. Lộ trình có cả điểm đi và điểm đến
            // 2. Lộ trình có thời gian đặt xe gần đây nhất
            // 3. Lộ trình có số lần đặt xe nhiều nhất
            // 4. Lộ trình có điểm đón gần nhất (nếu có thông tin vị trí hiện tại)

            // Tạo bản sao để xử lý song song an toàn
            var sortedRoutes = new List<PlacePairRoute>(routes);

            // Sắp xếp song song sử dụng PLINQ khi có nhiều phần tử
            if (routes.Count > 10)
            {
                _logger.LogDebug("Using parallel sorting for {Count} routes", routes.Count);
                
                // Áp dụng song song cho quá trình sắp xếp khi có nhiều lộ trình
                return routes.AsParallel()
                    .WithDegreeOfParallelism(Math.Min(routes.Count / 2, _parallelOptions.MaxDegreeOfParallelism))
                    .OrderByDescending(r => r is { Pickup: not null, Arrival: not null }) // Ưu tiên lộ trình đầy đủ
                    .ThenByDescending(r => r.LastBookingAt) // Sau đó ưu tiên theo thời gian đặt gần nhất
                    .ThenByDescending(r => r.BookingCount) // Sau đó ưu tiên theo số lần đặt nhiều nhất
                    .ThenBy(r => currentLocation != null ? r.DistanceToPickup : null) // Sau đó ưu tiên theo khoảng cách gần nhất
                    .ToList();
            }
            else
            {
                // Sử dụng sắp xếp tuần tự cho danh sách nhỏ
                return routes
                    .OrderByDescending(r => r is { Pickup: not null, Arrival: not null }) // Ưu tiên lộ trình đầy đủ
                    .ThenByDescending(r => r.LastBookingAt) // Sau đó ưu tiên theo thời gian đặt gần nhất
                    .ThenByDescending(r => r.BookingCount) // Sau đó ưu tiên theo số lần đặt nhiều nhất
                    .ThenBy(r => currentLocation != null ? r.DistanceToPickup : null) // Sau đó ưu tiên theo khoảng cách gần nhất
                    .ToList();
            }
        }

        /// <summary>
        /// Lấy danh sách lộ trình phổ biến theo giờ và ngày trong tuần
        /// </summary>
        public async Task<List<PlacePairRoute>> GetFrequentRoutesByTimeAndDay(
            string? userId,
            string? phoneNumber,
            int hourOfDay,
            int dayOfWeek,
            int limit = 5,
            LatLonGeoLocation? currentLocation = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Getting frequent routes for user {UserId} on hour {Hour} and day {Day}",
                    userId, hourOfDay, dayOfWeek);

                var queryDescriptor = new SearchRequestDescriptor<PlacePairEs>()
                    .Index(_indexName)
                    .Size(limit)
                    .Query(q => q
                        .Bool(b => b
                            .Must(m =>
                                // User filter
                                m.Bool(bs => bs
                                    .Should(s =>
                                    {
                                        // Tìm kiếm theo userId hoặc phoneNumber, tùy thuộc vào thông tin nào được cung cấp
                                        if (!string.IsNullOrEmpty(userId))
                                            s.Term(t => t.Field(f => f.CustomerId!.Suffix("keyword")).Value(userId));
                                        else
                                            s.Term(t => t.Field(f => f.CustomerPhone!.Suffix("keyword")).Value(phoneNumber));
                                    })
                                ),
                              // Date range filter - last month
                              m => m.Range(r => r
                                  .DateRange(dr => dr
                                      .Field(f => f.LastBookingAt)
                                      // Chỉ lấy các lộ trình từ 1 tháng trước đến hiện tại
                                      .Gte(DateMath.Anchored(DateTime.UtcNow.AddMonths(-1)))
                                  )
                              ),
                              // Hour of day filter using script
                              m => m.Script(s => s
                                  .Script(sc => sc
                                      // Lọc theo giờ trong ngày của lần đặt xe cuối cùng
                                      .Source("doc['lastBookingAt'].value != null && doc['lastBookingAt'].value.getHour() == params.hour")
                                      .Params(p => p.Add("hour", hourOfDay))
                                  )
                              ),
                               // Day of week filter using script
                               m => m.Script(s => s
                                  .Script(sc => sc
                                      // Lọc theo ngày trong tuần của lần đặt xe cuối cùng
                                      // Elasticsearch sử dụng Java's DayOfWeek enum (1=Monday, 7=Sunday)
                                      .Source("doc['lastBookingAt'].value != null && doc['lastBookingAt'].value.getDayOfWeekEnum().getValue() == params.day")
                                      .Params(p => p.Add("day", dayOfWeek))
                                  )
                              )
                            )
                        )
                    )
                    .Sort(s => s
                        .Field(f => f.BookingCount, o => o.Order(SortOrder.Desc))
                    );

                // Thực hiện truy vấn
                var searchResponse = await _elasticClient.SearchAsync(queryDescriptor, cancellationToken);

                if (!searchResponse.IsValidResponse || searchResponse.Documents.Count == 0)
                {
                    _logger.LogWarning("No frequent routes found for hour {Hour} and day {Day}", hourOfDay, dayOfWeek);
                    return new List<PlacePairRoute>();
                }

                _logger.LogInformation("Found {Count} frequent place records for hour {Hour} and day {Day}",
                    searchResponse.Documents.Count, hourOfDay, dayOfWeek);

                // Tạo danh sách lộ trình từ kết quả (sử dụng phiên bản song song)
                var routes = OrganizePlacePairRoutesParallel(searchResponse.Documents, currentLocation);

                return routes.Take(limit).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting frequent routes by time and day: {Message}", ex.Message);
                return new List<PlacePairRoute>();
            }
        }

        /// <summary>
        /// Lấy danh sách lộ trình phổ biến trong khoảng giờ và ngày trong tuần
        /// </summary>
        public async Task<List<PlacePairRoute>> GetFrequentRoutesByTimeRange(
            string? userId,
            string? phoneNumber,
            int startHour,
            int endHour,
            int dayOfWeek,
            int limit = 5,
            LatLonGeoLocation? currentLocation = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                // Chuẩn hóa khoảng giờ
                startHour = Math.Max(0, Math.Min(23, startHour));
                endHour = Math.Max(0, Math.Min(23, endHour));

                // Đảm bảo startHour <= endHour
                if (startHour > endHour)
                {
                    (startHour, endHour) = (endHour, startHour);
                }

                _logger.LogInformation("Getting frequent routes for user {UserId} on hour range {StartHour}-{EndHour} and day {Day}",
                    userId, startHour, endHour, dayOfWeek);

                var queryDescriptor = new SearchRequestDescriptor<PlacePairEs>()
                    .Index(_indexName)
                    .Size(limit)
                    .Query(q => q
                        .Bool(b => b
                            .Must(m =>
                                // User filter
                                m.Bool(bs => bs
                                    .Should(s =>
                                    {
                                        // Tìm kiếm theo userId hoặc phoneNumber, tùy thuộc vào thông tin nào được cung cấp
                                        if (!string.IsNullOrEmpty(userId))
                                            s.Term(t => t.Field(f => f.CustomerId!.Suffix("keyword")).Value(userId));
                                        else
                                            s.Term(t => t.Field(f => f.CustomerPhone!.Suffix("keyword")).Value(phoneNumber));
                                    })
                                ),
                               // Date range filter - last month
                               m => m.Range(r => r
                                   .DateRange(dr => dr
                                       .Field(f => f.LastBookingAt)
                                       // Chỉ lấy các lộ trình từ 1 tháng trước đến hiện tại
                                       .Gte(DateMath.Anchored(DateTime.UtcNow.AddMonths(-1)))
                                   )
                               ),
                               // Hour range filter using script
                               m => m.Script(s => s
                                  .Script(sc => sc
                                      // Lọc theo khoảng giờ (từ startHour đến endHour) của lần đặt xe cuối cùng
                                      // Ví dụ: 7-9 sẽ lấy các chuyến đi vào giờ 7, 8, và 9
                                      .Source("doc['LastBookingAt'].value != null && doc['LastBookingAt'].value.getHour() >= params.startHour && doc['LastBookingAt'].value.getHour() <= params.endHour")
                                      .Params(p => p
                                          .Add("startHour", startHour)
                                          .Add("endHour", endHour)
                                      )
                                  )
                              ),
                                // Day of week filter using script
                                m => m.Script(s => s
                                  .Script(sc => sc
                                      // Lọc theo ngày trong tuần của lần đặt xe cuối cùng
                                      // Elasticsearch sử dụng Java's DayOfWeek enum (1=Monday, 7=Sunday)
                                      .Source("doc['LastBookingAt'].value != null && doc['LastBookingAt'].value.getDayOfWeekEnum().getValue() == params.day")
                                      .Params(p => p.Add("day", dayOfWeek))
                                  )
                              )
                            )
                        )
                    )
                    // Sắp xếp kết quả theo số lần đặt xe nhiều nhất
                    .Sort(s => s
                        .Field(f => f.BookingCount, o => o.Order(SortOrder.Desc))
                    );

                // Thực hiện truy vấn
                var searchResponse = await _elasticClient.SearchAsync(queryDescriptor, cancellationToken);

                if (!searchResponse.IsValidResponse || searchResponse.Documents.Count == 0)
                {
                    _logger.LogWarning("No frequent routes found for hour range {StartHour}-{EndHour} and day {Day}",
                        startHour, endHour, dayOfWeek);
                    return new List<PlacePairRoute>();
                }

                _logger.LogInformation("Found {Count} frequent place records for hour range {StartHour}-{EndHour} and day {Day}",
                    searchResponse.Documents.Count, startHour, endHour, dayOfWeek);

                // Tạo danh sách lộ trình từ kết quả (sử dụng phiên bản song song)
                var routes = OrganizePlacePairRoutesParallel(searchResponse.Documents, currentLocation);

                return routes.Take(limit).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting frequent routes by time range: {Message}", ex.Message);
                return new List<PlacePairRoute>();
            }
        }

        /// <summary>
        /// Lấy danh sách lộ trình đã sử dụng trong ngày hôm nay
        /// </summary>
        public async Task<List<PlacePairRoute>> GetTodayRoutes(
            string? userId,
            string? phoneNumber,
            DateTime date,
            int limit = 10,
            LatLonGeoLocation? currentLocation = null,
            int? minDistanceMeters = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var startTime = date.Date;
                var endTime = date.Date.AddDays(1).AddSeconds(-1);

                _logger.LogInformation("Getting today's routes for user {UserId} on date {Date:yyyy-MM-dd HH:mm:ss.fff}",
                    userId, date);

                // Xây dựng danh sách các filter điều kiện - tất cả đều bắt buộc
                var filters = new List<Action<QueryDescriptor<PlacePairEs>>>();

                // User filter - bắt buộc phải có userId hoặc phoneNumber
                if (!string.IsNullOrEmpty(userId))
                {
                    filters.Add(m => m.Term(t => t.Field(f => f.CustomerId!.Suffix("keyword")).Value(userId)));
                }
                else
                {
                    filters.Add(m => m.Term(t => t.Field(f => f.CustomerPhone!.Suffix("keyword")).Value(phoneNumber)));
                }

                // Time filter - bắt buộc phải thỏa mãn ít nhất một trong hai điều kiện thời gian
                filters.Add(m => m.Range(r => r.DateRange(dr => dr
                    .Field(f => f.LastBookingAt)
                    .Gte(DateMath.Anchored(startTime))
                    .Lte(DateMath.Anchored(endTime)))));

                // Xây dựng danh sách các filter MustNot (loại trừ)
                var mustNotFilters = new List<Action<QueryDescriptor<PlacePairEs>>>();

                // Thêm filter khoảng cách tối thiểu chỉ khi > 0
                if (currentLocation != null && minDistanceMeters is > 0)
                {
                    _logger.LogInformation("Applying minimum distance filter: {MinDistance}m", minDistanceMeters.Value);
                    
                    // Loại bỏ các địa điểm có khoảng cách < minDistanceMeters
                    mustNotFilters.Add(mn => mn.Bool(mb => mb
                        .Must(
                            // Phải có thông tin địa lý
                            m => m.Exists(e => e.Field(x => x.Location)),
                            // Và khoảng cách < minDistanceMeters
                            m => m.GeoDistance(gd => gd
                                .Field(f => f.Location)
                                .Location(GeoLocation.LatitudeLongitude(new LatLonGeoLocation
                                {
                                    Lat = currentLocation.Lat,
                                    Lon = currentLocation.Lon
                                }))
                                .Distance($"{minDistanceMeters.Value}m")
                                .DistanceType(GeoDistanceType.Arc)
                            )
                        )
                    ));
                }

                var queryDescriptor = new SearchRequestDescriptor<PlacePairEs>()
                    .Index(_indexName)
                    .Size(limit) // Lấy gấp đôi để có thể lọc và tổ chức cặp địa điểm
                    .Query(q => q
                        .Bool(b => b
                            .Must(filters.ToArray())
                            .MustNot(mustNotFilters.ToArray())
                        )
                    )
                    // Sắp xếp kết quả theo thời gian đặt xe gần đây nhất
                    .Sort(s => s
                        .Field(f => f.LastBookingAt, o => o.Order(SortOrder.Desc))
                    );

                // Thực hiện truy vấn
                var searchResponse = await _elasticClient.SearchAsync(queryDescriptor, cancellationToken);

                if (!searchResponse.IsValidResponse || searchResponse.Documents.Count == 0)
                {
                    _logger.LogWarning("No routes found for today {Date}", date.ToString("yyyy-MM-dd"));
                    return new List<PlacePairRoute>();
                }

                _logger.LogInformation("Found {Count} place records for today {Date:yyyy-MM-dd HH:mm:ss.fff}",
                    searchResponse.Documents.Count, date);

                // Trích xuất danh sách BookingPlacePairId từ kết quả
                var bookingPlacePairIds = searchResponse.Documents.ExtractBookingPlacePairIds();

                // Tạo truy vấn lấy các cặp địa điểm
                var pairsQueryDescriptor = _elasticClient.GetByPlacePairIdsQuery(
                    bookingPlacePairIds,
                    _indexName,
                    bookingPlacePairIds.Count * 2 // Mỗi ID có tối đa 2 điểm (đi và đến)
                );

                // Thực hiện truy vấn lấy các cặp địa điểm
                var pairsSearchResponse = await _elasticClient.SearchAsync(pairsQueryDescriptor, cancellationToken);

                if (!pairsSearchResponse.IsValidResponse)
                {
                    _logger.LogError("Failed to retrieve paired places: {Error}",
                        pairsSearchResponse.ElasticsearchServerError?.Error?.ToString());
                    return new List<PlacePairRoute>();
                }

                // Tổ chức các cặp địa điểm (sử dụng phiên bản song song)
                var placePairs = pairsSearchResponse.Documents.OrganizePlacePairs();
                var routes = placePairs.ToPlacePairRoutes();
                
                // Điều chỉnh hướng đi về dựa vào vị trí hiện tại
                if (currentLocation != null)
                {
                    routes = AdjustRouteDirections(routes, currentLocation);
                }

                // Log thông tin về filter khoảng cách nếu được áp dụng
                if (minDistanceMeters is > 0 && currentLocation != null)
                {
                    _logger.LogInformation("Applied minimum distance filter {MinDistance}m in query, result count: {Count}",
                        minDistanceMeters.Value, routes.Count);
                }

                // Giới hạn số lượng kết quả trả về
                return routes.Take(limit).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting today's routes: {Message}", ex.Message);
                return new List<PlacePairRoute>();
            }
        }

        /// <summary>
        /// Tổ chức các PlacePairEs thành danh sách PlacePairRoute (bản song song)
        /// </summary>
        private List<PlacePairRoute> OrganizePlacePairRoutesParallel(IReadOnlyCollection<PlacePairEs> documents, LatLonGeoLocation? currentLocation = null)
        {
            // Nhóm các document theo BookingPlacePairId
            // Mỗi BookingPlacePairId có thể có 1-2 records (điểm đi và/hoặc điểm đến)
            var groupedDocs = documents.GroupBy(d => d.BookingPlacePairId).ToList();
            var result = new ConcurrentBag<PlacePairRoute>();

            Parallel.ForEach(groupedDocs, _parallelOptions, group =>
            {
                // Lấy thông tin từ document đầu tiên trong nhóm
                // Các thông tin như CustomerId, BookingCount... là giống nhau cho tất cả các records trong cùng một nhóm
                var firstDoc = group.First();

                var route = new PlacePairRoute
                {
                    BookingPlacePairId = firstDoc.BookingPlacePairId,
                    CustomerId = firstDoc.CustomerId,
                    CustomerPhone = firstDoc.CustomerPhone,
                    BookingCount = firstDoc.BookingCount,
                    LastBookingAt = firstDoc.LastBookingAt,
                    LastSearchAt = firstDoc.LastSearchAt,
                    LastEstimateAmount = firstDoc.LastEstimateAmount,
                    LastNote = firstDoc.LastNote
                };

                // Xác định điểm đón và điểm đến
                // Duyệt qua từng document trong nhóm và phân loại thành điểm đi hoặc điểm đến
                foreach (var doc in group)
                {
                    if (doc.Type == PlacePairType.Pickup)
                    {
                        route.Pickup = new PlacePairPoint
                        {
                            PlaceId = doc.PlaceId,
                            FullAddress = doc.FullAddress,
                            Location = doc.Location
                        };
                    }
                    else if (doc.Type == PlacePairType.Arrival)
                    {
                        route.Arrival = new PlacePairPoint
                        {
                            PlaceId = doc.PlaceId,
                            FullAddress = doc.FullAddress,
                            Location = doc.Location
                        };
                    }
                }

                // Tính khoảng cách và điều chỉnh hướng nếu cần thiết
                if (currentLocation != null)
                {
                    // Nếu có cả pickup và arrival
                    if (route is { Pickup.Location: not null, Arrival.Location: not null })
                    {
                        // Tính khoảng cách đến pickup và arrival
                        var distanceToPickup = H3Helper.CalculateDistance(
                            currentLocation.Lat,
                            currentLocation.Lon,
                            route.Pickup.Location.Lat,
                            route.Pickup.Location.Lon);
                            
                        var distanceToArrival = H3Helper.CalculateDistance(
                            currentLocation.Lat,
                            currentLocation.Lon,
                            route.Arrival.Location.Lat,
                            route.Arrival.Location.Lon);
                            
                        // Nếu arrival gần với vị trí hiện tại hơn pickup
                        // thì đảo ngược thứ tự để route đi từ gần đến xa
                        if (distanceToArrival < distanceToPickup)
                        {
                            (route.Pickup, route.Arrival) = (route.Arrival, route.Pickup);
                            route.DistanceToPickup = distanceToArrival;
                        }
                        else
                        {
                            route.DistanceToPickup = distanceToPickup;
                        }
                    }
                    // Nếu chỉ có pickup
                    else if (route is { Pickup.Location: not null })
                    {
                        route.DistanceToPickup = H3Helper.CalculateDistance(
                            currentLocation.Lat,
                            currentLocation.Lon,
                            route.Pickup.Location.Lat,
                            route.Pickup.Location.Lon);
                    }
                }

                result.Add(route);
            });

            return result.ToList();
        }
    }
}