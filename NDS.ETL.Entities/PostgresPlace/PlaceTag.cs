using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("place_tags")]
public partial class PlaceTag
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("code")]
    public string? Code { get; set; }

    [Column("vi")]
    public string? Vi { get; set; }

    [Column("en")]
    public string? En { get; set; }

    [Column("description")]
    public string? Description { get; set; }

    [Column("createdAt", TypeName = "timestamp without time zone")]
    public DateTime CreatedAt { get; set; }

    [Column("creator")]
    public string? Creator { get; set; }

    [Column("updatedAt", TypeName = "timestamp without time zone")]
    public DateTime UpdatedAt { get; set; }

    [Column("updateUser")]
    public string? UpdateUser { get; set; }
}
