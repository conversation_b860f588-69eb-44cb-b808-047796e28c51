<?xml version="1.0" encoding="utf-8" ?>
<log4net>
	<appender name="Console" type="log4net.Appender.ConsoleAppender">
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date %-5level: %message%newline" />
		</layout>
		<filter type="log4net.Filter.LoggerMatchFilter">
			<!-- allows this sub-namespace to be logged... -->
			<loggerToMatch value="NDS.ETL" />
		</filter>
		<filter type="log4net.Filter.LoggerMatchFilter">
			<!-- ...but not the rest of it -->
			<loggerToMatch value="Microsoft.EntityFrameworkCore" />
			<acceptOnMatch value="false" />
		</filter>
	</appender>
	<appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender" >
		<file value="App_Data/Logs/Logs.txt" />
		<appendToFile value="true" />
		<rollingStyle value="Size" />
		<maxSizeRollBackups value="10" />
		<maximumFileSize value="10000KB" />
		<staticLogFileName value="true" />
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%-5level %date [%-5.5thread] %-40.40logger - %message%newline" />
		</layout>
		<filter type="log4net.Filter.LoggerMatchFilter">
			<!-- allows this sub-namespace to be logged... -->
			<loggerToMatch value="NDS.ETL" />
		</filter>
		<filter type="log4net.Filter.LoggerMatchFilter">
			<!-- ...but not the rest of it -->
			<loggerToMatch value="Microsoft.EntityFrameworkCore" />
			<acceptOnMatch value="false" />
		</filter>
	</appender>

	<root>
		<appender-ref ref="Console" />
		<appender-ref ref="RollingFileAppender" />
		<level value="ALL" />
	</root>
</log4net>