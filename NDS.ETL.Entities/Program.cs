// See https://aka.ms/new-console-template for more information
//Scaffold-DbContext "User Id=lifestyle_be;Password=lifestyle`$dd123;Data Source=************:1521/NCPTTEST;" Oracle.EntityFrameworkCore -Schemas LIFESTYLE_BE -OutputDir Oracle -ContextDir Context -Context OracleContext -DataAnnotations -f
//Scaffold-DbContext "Host=************;Port=5435;Database=lifestylecms2;Username=lifestylecms;Password=lifestylecms" Npgsql.EntityFrameworkCore.PostgreSQL -OutputDir Postgres -ContextDir Context -Context NpgsqlContext -DataAnnotations -f
//Scaffold-DbContext "Host=************;Port=5444;Database=******;Username=******;Password=******" Npgsql.EntityFrameworkCore.PostgreSQL -OutputDir PostgresPlace -ContextDir Context -Context NpgPlaceContext -DataAnnotations -f -Schemas public
//Scaffold-DbContext "Host=***********;Port=9432;Database=******;Username=******;Password=******" Npgsql.EntityFrameworkCore.PostgreSQL -OutputDir PostgresPlaceDev -ContextDir Context -Context NpgPlaceDevContext -DataAnnotations -f -Schemas public