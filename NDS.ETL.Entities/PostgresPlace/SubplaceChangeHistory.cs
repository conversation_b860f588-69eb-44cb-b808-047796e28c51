using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("subplace_change_histories")]
public partial class SubplaceChangeHistory
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("dataJson")]
    public string? DataJson { get; set; }

    [Column("lastUpdatedUser")]
    public string? LastUpdatedUser { get; set; }

    [Column("createdAt", TypeName = "timestamp without time zone")]
    public DateTime CreatedAt { get; set; }

    [Column("updatedAt", TypeName = "timestamp without time zone")]
    public DateTime UpdatedAt { get; set; }

    [Column("subPlaceId")]
    public long SubPlaceId { get; set; }

    [Column("creator")]
    public string? Creator { get; set; }
}
