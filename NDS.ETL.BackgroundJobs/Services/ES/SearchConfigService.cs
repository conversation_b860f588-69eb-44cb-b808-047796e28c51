namespace NDS.ETL.BackgroundJobs.Services.ES;

public class SearchConfigService : ISearchConfigService
{
    //public QueryDescriptor BuildQuery(QueryDescriptor<PlacesEs> selector, string term, IEnumerable<Func<QueryDescriptor<PlacesEs>, QueryDescriptor>> queries = null)
    //{
    //    term = term?.Trim();
    //    //IEnumerable<Func<QueryDescriptor<PlacesEs>, QueryDescriptor>> XMust()
    //    //{
    //    //    if (queries != null)
    //    //    {
    //    //        foreach (var query in queries)
    //    //        {
    //    //            yield return query;
    //    //        }
    //    //    }

    //    //    if (!string.IsNullOrEmpty(term))
    //    //    {
    //    //        var placeNameFields = PlaceNameFields();
    //    //        // Ưu tiên về độ match từ
    //    //        yield return m => m.Bool(b => b.Should(
    //    //            s => s.MultiMatch(mm => mm
    //    //                .Query(term)
    //    //                .Operator(Operator.And)
    //    //                .Type(TextQueryType.Phrase)
    //    //                .Boost(1.5f)
    //    //                .Fields(placeNameFields)
    //    //            ),
    //    //            s => s.MultiMatch(mm => mm
    //    //                .Query(term)
    //    //                .Operator(Operator.And)
    //    //                .Type(TextQueryType.BestFields)
    //    //                .Boost(1.1f)
    //    //                .Fields(placeNameFields)
    //    //            ),
    //    //            s => s.MultiMatch(mm => mm
    //    //                .Query(term)
    //    //                .Operator(Operator.Or)
    //    //                .Type(TextQueryType.BestFields)
    //    //                .Boost(0.1f)
    //    //                .Fields(placeNameFields)
    //    //            )));
    //    //    }
    //    //}
    //    //return selector.Bool(b => b.Must(XMust()));
    //    return selector.
    //}

    //public IEnumerable<Func<QueryDescriptor<PlacesEs>, QueryDescriptor>> GetFilter(params Func<QueryDescriptor<PlacesEs>, QueryDescriptor>[] filters)
    //{
    //    yield return f => f.Term(t => t.Active, true);
    //    yield return f => f.Term(t => t.Removed, false);
    //    if (filters?.Any() != true) yield break;
    //    foreach (var filter in filters)
    //    {
    //        yield return filter;
    //    }
    //}

    //public Fields PlaceNameFields()
    //{
    //    return _().ToArray(); static IEnumerable<Field> _()
    //    {
    //        yield return Infer.Field<PlacesEs>(f => f.Name, 2);
    //        yield return Infer.Field<PlacesEs>(f => f.FullAddress, 1.5);
    //        yield return Infer.Field<PlacesEs>(f => f.Keywords, 1.1);
    //    }
    //}

    //public QueryDescriptor ScoreQuery(QueryDescriptor<PlacesEs> selector, string term, GeoLocation location,
    //    IEnumerable<Func<QueryDescriptor<PlacesEs>, QueryDescriptor>> queries = null, IEnumerable<IScoreFunction> functions = null)
    //{
    //    term = term?.Trim();

    //    IEnumerable<IScoreFunction> InnerFunctions()
    //    {
    //        if (!string.IsNullOrWhiteSpace(term))
    //        {
    //            //Nếu nhập đúng name , hoặc 1 trong các trường này thì ưu tiên cao nhất
    //            yield return new WeightFunctionDescriptor<PlacesEs>()
    //                .Weight(100)
    //                .Filter(f => f.Bool(b => b.Should(
    //                    s => s.Term(tt => tt.Name.Suffix("keyword"), term),
    //                    s => s.Term(tt => tt.FullAddress.Suffix("keyword"), term)
    //                )));
    //        }
    //        yield return new WeightFunctionDescriptor<PlacesEs>()
    //            .Filter(f => f.GeoDistance(b => b
    //                    .Distance(Distance.Kilometers(45))
    //                    .Location(location)
    //                )
    //            )
    //            .Weight(90);
    //        // Nếu match với Namethì ưu tiên thứ 1
    //        yield return new WeightFunctionDescriptor<PlacesEs>()
    //            .Filter(f => f.Bool(b => b
    //                    .Should(
    //                        bs => bs.MatchPhrase(m => m
    //                            .Field(fi => fi.Name)
    //                            .Query(term)
    //                        )
    //                    )
    //                )
    //            )
    //            .Weight(60);
    //        // Nếu match với fullAddress thì ưu tiên thứ 2
    //        yield return new WeightFunctionDescriptor<PlacesEs>()
    //            .Filter(f => f.Bool(b => b
    //                    .Should(
    //                        bs => bs.MatchPhrase(m => m
    //                            .Field(fi => fi.FullAddress)
    //                            .Query(term)
    //                        )
    //                    )
    //                )
    //            )
    //            .Weight(50);
    //        // Nếu match với Keyword thì ưu tiên thứ 3
    //        yield return new WeightFunctionDescriptor<PlacesEs>()
    //            .Filter(f => f.Bool(b => b
    //                    .Should(
    //                        bs => bs.MatchPhrase(m => m
    //                            .Field(fi => fi.Keywords)
    //                            .Query(term)
    //                        )
    //                    )
    //                )
    //            )
    //            .Weight(50);
    //        if (functions?.Any() == true)
    //        {
    //            foreach (var func in functions)
    //                yield return func;
    //        }
    //    }

    //    return selector.Bool(b => b
    //        .Filter(GetFilter())
    //        .Must(
    //            m => m.FunctionScore(fs => fs
    //                .ScoreMode(FunctionScoreMode.Sum)
    //                .BoostMode(FunctionBoostMode.Sum)
    //                // .Query(q => BuildQuery(q, term, queries))
    //                .Functions(InnerFunctions().ToList())
    //            )
    //        )
    //    );
    //}
}