CREATE OR REPLACE FUNCTION contains_pluscode(text_to_check TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check for standard or short Plus Code format
  RETURN text_to_check ~* '([23456789cfghjmpqrvwx]{8}\+[23456789cfghjmpqrvwx]{2,3}|[23456789cfghjmpqrvwx]{4}\+[23456789cfghjmpqrvwx]{2,3})';
END;
$$ LANGUAGE plpgsql;


SELECT * FROM places
WHERE contains_pluscode("address") LIMIT 100;


DELETE FROM sub_places
WHERE "parentId" IN (
  SELECT "id" FROM places
  WHERE contains_pluscode("address")
);

DELETE FROM places
WHERE contains_pluscode("address");


docker network create elastic

docker rm -f es01 || true && docker run --name es01 \
  --restart=always \
  --net elastic \
  -d -p 9300:9200 \
  -e "discovery.type=single-node" \
  -e "ES_JAVA_OPTS=-Xms1g -Xmx8g" \
  -m 16g \
  -it \
  docker.elastic.co/elasticsearch/elasticsearch:8.17.2
  

docker exec -it es01 bin/elasticsearch-reset-password -u elastic

curl -u elastic:yourpassword http://localhost:9200

elastic/PT-4Q6_qA7I7OSva5F=O

docker rm -f kib01 || true &&  docker run --name kib01 \
--restart=always \
--net elastic \
-d -p 5601:5601 \
-e "ELASTICSEARCH_HOSTS=http://es01:9200" \
docker.elastic.co/kibana/kibana:8.17.2


bin/elasticsearch-plugin install analysis-icu
bin/elasticsearch-plugin install analysis-phonetic
 
 or manual 

./bin/elasticsearch-plugin install --batch --download-only analysis-icu
elasticsearch/plugins/.downloads


bin/elasticsearch-plugin install file:///opt/lib/ingest-h3-8.17.2.zip


//No pass
docker rm -f es01 || true && docker run --name es01 \
  --restart=always \
  --net elastic \
  -d -p 9300:9200 \
  -e "discovery.type=single-node" \
  -e "xpack.security.enabled=true" \
  -e "xpack.security.http.ssl.enabled=false" \
  -e "xpack.security.transport.ssl.enabled=false" \
  -e "ELASTIC_PASSWORD=nopass" \
  -e "ES_JAVA_OPTS=-Xms1g -Xmx12g" \
  -m 24g \
  -it docker.elastic.co/elasticsearch/elasticsearch:8.17.2

docker exec -it es01 bin/elasticsearch-reset-password -u kibana_system -i


curl -u elastic:nopass -X POST "http://localhost:9300/_security/user/kibana_system" \
-H "Content-Type: application/json" \
-d '{"password":"nopass", "roles":["kibana_system"]}'


docker rm -f kib01 || true && docker run --name kib01 \
--restart=always \
--net elastic \
-d -p 5601:5601 \
-e "ELASTICSEARCH_HOSTS=http://es01:9200" \
-e "ELASTICSEARCH_USERNAME=kibana_system" \
-e "ELASTICSEARCH_PASSWORD=nopass" \
docker.elastic.co/kibana/kibana:8.17.2


PUT _ingest/pipeline/location-to-h3
{
  "description": "A pipeline to create H3 geo indexes",
  "processors": [
        {
            "h3": {
                "field": "location",
                "target_field": "h3",
                "from": 0,
                "to": 15
            }
        }
    ]
}

PUT _ingest/pipeline/location-to-h3
{
  "description": "A pipeline to create H3 geo indexes",
  "processors": [
        {
            "h3": {
                "field": "location",
                "target_field": "h3",
                "from": 0,
                "to": 15
            }
        },
        {
          "h3": {
            "field": "location1",
            "target_field": "h3_from",
            "from": 0,
            "to": 15
          }
        }
        {
          "h3": {
            "field": "location2",
            "target_field": "h3_to",
            "from": 0,
            "to": 15
          }
        }
    ]
}

PUT _ingest/pipeline/auto_suggest_pipeline
{
  "description": "Generate suggest input from masterAddress",
  "processors": [
    {
      "script": {
        "lang": "painless",
        "source": """
          if (ctx.masterAddress != null) {
            def inputList = new ArrayList();
            def parts = ctx.masterAddress.toLowerCase().splitOnToken(",");
            for (item in parts) {
              inputList.add(item.trim());
            }
            ctx.masterAddress = [
              'value': ctx.masterAddress,
              'suggest': ['input': inputList]
            ];
          }
        """
      }
    }
  ]
}

POST _reindex
{
  "source": {
    "index": "place-dev"
  },
  "dest": {
    "index": "place-dev-reindexed",
    "pipeline": "auto_suggest_pipeline"
  }
}

//DELETE place-dev

POST _aliases
{
  "actions": [
    {
      "add": {
        "index": "place-dev-reindexed",
        "alias": "place-dev"
      }
    }
  ]
}


PUT /place-dev3/_settings
{
    "refresh_interval": "5s",
    "number_of_replicas": 0
}

PUT /place-uat2/_settings
{
    "refresh_interval": "10s",
    "number_of_replicas": 1
}

GET _cat/indices?v

GET /_cat/shards/place-dev?v

GET place-dev-reindexed/_search
{
  "_source": ["masterAddress", "masterAddress.suggest"],
  "query": {
    "match_all": {}
  },
  "size": 5
}


POST /place-dev3/_search
{
  "size": 10,
  "query": {
    "bool": {
      "must": [
        {
          "bool": {
            "should": [
              {
                "match_phrase": {
                  "addressPermuted": {
                    "query": "thpt",
                    "boost": 5,
                    "slop": 1
                  }
                }
              },
              {
                "multi_match": {
                  "query": "thpt",
                  "fields": [
                    "addressPermuted^3",
                    "addressPermuted.suggest^2"
                  ],
                  "type": "most_fields",
                  "operator": "and",
                  "minimum_should_match": "75%",
                  "tie_breaker": 0.3
                }
              }
            ],
            "minimum_should_match": 1
          }
        }
      ],
      "filter": [
        {
          "geo_distance": {
            "distance": "50km",
            "location": {
              "lon": 105.803715,
              "lat": 21.021673
            }
          }
        }
      ]
    }
  },
  "sort": [
    "_score",
    {
      "_geo_distance": {
        "location": {
          "lon": 105.803715,
          "lat": 21.021673
        },
        "order": "asc",
        "unit": "km"
      }
    }
  ]
}


POST /places/_search
{
  "size": 5,
  "query": {
    "multi_match": {
      "query": "220 ng",
      "type": "bool_prefix",
      "fields": [
        "address",
        "address._2gram",
        "address._3gram"
      ]
    }
  }
}


SELECT 
    p.id AS place_id,
    p."name" AS place_name,
    p."provinceId", 
    pr."name" AS province_name, 
    p."districtId", 
    d."name" AS district_name,
    p."wardId", 
    w."name" AS ward_name,
    p."fullAddress",
    p."city", 
    p."district", 
    p."ward",
    p."updatedAt"
FROM 
    places p
LEFT JOIN 
    provinces pr ON p."provinceId" = pr.id
LEFT JOIN 
    districts d ON p."districtId" = d.id
LEFT JOIN 
    wards w ON p."wardId" = w.id
WHERE 
    p."updatedAt" >= '2025-05-14'
ORDER BY 
    p."updatedAt" DESC
LIMIT 100;


## Tạo Index

PUT /place-sit2
{
	"settings": {
		"index": {
			"mapping": {
				"nested_fields": {
					"limit": "30"
				},
				"total_fields": {
					"limit": "500"
				}
			},
			"refresh_interval": "10s",
			"translog": {
				"flush_threshold_size": "1gb",
				"durability": "async"
			},
			"max_inner_result_window": "20000",
			"analysis": {
				"filter": {
					"whitespace_cleanup": {
						"pattern": "\\s+",
						"type": "pattern_replace",
						"replacement": " "
					},
					"address_shingle": {
						"max_shingle_size": "4",
						"min_shingle_size": "2",
						"output_unigrams": "true",
						"type": "shingle"
					}
				},
				"analyzer": {
					"address_suggest": {
						"filter": [
							"lowercase",
							"whitespace_cleanup"
						],
						"type": "custom",
						"tokenizer": "standard"
					},
					"address_search": {
						"filter": [
							"lowercase",
							"whitespace_cleanup",
							"address_shingle"
						],
						"type": "custom",
						"tokenizer": "standard"
					},
					"icu": {
						"filter": [
							"lowercase",
							"asciifolding",
							"whitespace_cleanup"
						],
						"type": "custom",
						"tokenizer": "standard"
					}
				}
			},
			"number_of_replicas": "0",
			"codec": "best_compression",
			"number_of_shards": "3"
		}
	},
    "mappings": {
    "properties": {
      "active": {
        "type": "boolean"
      },
      "address": {
        "type": "text",
        "analyzer": "icu"
      },
      "addressPermuted": {
        "type": "text",
        "fields": {
          "completion": {
            "type": "completion",
            "analyzer": "address_suggest",
            "preserve_separators": true,
            "preserve_position_increments": true,
            "max_input_length": 100,
            "contexts": [
              {
                "name": "location_50km",
                "type": "CATEGORY",
                "path": "h3.4"
              }
            ]
          },
          "suggest": {
            "type": "text",
            "analyzer": "address_suggest",
            "search_analyzer": "address_search"
          }
        },
        "term_vector": "with_positions_offsets",
        "norms": false,
        "analyzer": "address_search"
      },
      "bookingCount": {
        "type": "long"
      },
      "city": {
        "type": "keyword",
        "index": false
      },
      "createdAt": {
        "type": "date"
      },
      "creatorId": {
        "type": "keyword"
      },
      "dataFrom": {
        "type": "text",
        "fields": {
          "keyword": {
            "type": "keyword",
            "ignore_above": 256
          }
        }
      },
      "determineAddressLevel": {
        "type": "long"
      },
      "distance": {
        "type": "double",
        "index": false
      },
      "district": {
        "type": "text",
        "fields": {
          "keyword": {
            "type": "keyword",
            "ignore_above": 256
          }
        }
      },
      "districtId": {
        "type": "integer"
      },
      "fullAddress": {
        "type": "text",
        "fields": {
          "keyword": {
            "type": "keyword",
            "ignore_above": 256
          }
        }
      },
      "h3": {
        "properties": {
          "0": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          },
          "1": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          },
          "2": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          },
          "3": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          },
          "4": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          },
          "5": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          },
          "6": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          },
          "7": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          },
          "8": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          },
          "9": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          },
          "10": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          },
          "11": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          },
          "12": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          },
          "13": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          },
          "14": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          },
          "15": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          }
        }
      },
      "hasChild": {
        "type": "boolean"
      },
      "hidden": {
        "type": "boolean"
      },
      "id": {
        "type": "long"
      },
      "jobScanId": {
        "type": "keyword"
      },
      "keywords": {
        "type": "text",
        "fields": {
          "icu": {
            "type": "text",
            "analyzer": "icu"
          },
          "raw": {
            "type": "keyword",
            "ignore_above": 256
          }
        },
        "norms": false,
        "analyzer": "address_search"
      },
      "lastUpdateUser": {
        "type": "keyword"
      },
      "location": {
        "type": "geo_point"
      },
      "masterAddress": {
        "type": "keyword",
        "doc_values": false,
        "ignore_above": 256
      },
      "name": {
        "type": "text",
        "analyzer": "icu"
      },
      "note": {
        "type": "keyword"
      },
      "pending": {
        "type": "boolean"
      },
      "phoneNumber": {
        "type": "text",
        "fields": {
          "keyword": {
            "type": "keyword",
            "ignore_above": 256
          }
        }
      },
      "placeDetailId": {
        "type": "text",
        "fields": {
          "keyword": {
            "type": "keyword",
            "ignore_above": 256
          }
        }
      },
      "placeId": {
        "type": "keyword"
      },
      "popular": {
        "type": "boolean"
      },
      "processed": {
        "type": "boolean"
      },
      "provinceId": {
        "type": "integer"
      },
      "quoteCount": {
        "type": "long"
      },
      "reason": {
        "type": "keyword"
      },
      "reasonApprove": {
        "type": "keyword"
      },
      "removed": {
        "type": "boolean"
      },
      "route": {
        "type": "text",
        "fields": {
          "keyword": {
            "type": "keyword",
            "ignore_above": 256
          }
        }
      },
      "scanProcessed": {
        "type": "long"
      },
      "scanStatus": {
        "type": "keyword"
      },
      "score": {
        "type": "double",
        "index": false
      },
      "sourcePlaceId": {
        "type": "keyword"
      },
      "streetNumber": {
        "type": "text",
        "fields": {
          "keyword": {
            "type": "keyword",
            "ignore_above": 256
          }
        }
      },
      "subPlaces": {
        "type": "nested",
        "properties": {
          "active": {
            "type": "keyword"
          },
          "address": {
            "type": "text",
            "analyzer": "icu"
          },
          "addressPermuted": {
            "type": "text",
            "fields": {
              "completion": {
                "type": "completion",
                "analyzer": "address_suggest",
                "preserve_separators": true,
                "preserve_position_increments": true,
                "max_input_length": 100
              },
              "suggest": {
                "type": "text",
                "analyzer": "address_suggest",
                "search_analyzer": "address_search"
              }
            },
            "term_vector": "with_positions_offsets",
            "norms": false,
            "analyzer": "address_search"
          },
          "bookingCount": {
            "type": "long"
          },
          "city": {
            "type": "text",
            "analyzer": "icu"
          },
          "createdAt": {
            "type": "date"
          },
          "creatorId": {
            "type": "keyword"
          },
          "dataFrom": {
            "type": "keyword"
          },
          "distance": {
            "type": "keyword",
            "index": false
          },
          "district": {
            "type": "text",
            "analyzer": "icu"
          },
          "districtId": {
            "type": "integer"
          },
          "fullAddress": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          },
          "hidden": {
            "type": "keyword"
          },
          "id": {
            "type": "long"
          },
          "keywords": {
            "type": "text",
            "fields": {
              "keyword": {
                "type": "text",
                "analyzer": "icu"
              }
            },
            "norms": false,
            "analyzer": "address_search"
          },
          "lastUpdateUser": {
            "type": "keyword"
          },
          "location": {
            "type": "geo_point"
          },
          "masterAddress": {
            "type": "keyword",
            "store": true,
            "eager_global_ordinals": true,
            "ignore_above": 256
          },
          "name": {
            "type": "text",
            "analyzer": "icu"
          },
          "parentId": {
            "type": "long"
          },
          "parentPlaceId": {
            "type": "keyword"
          },
          "pending": {
            "type": "boolean"
          },
          "phoneNumber": {
            "type": "keyword"
          },
          "placeDetailId": {
            "type": "keyword"
          },
          "placeId": {
            "type": "keyword"
          },
          "popular": {
            "type": "boolean"
          },
          "processed": {
            "type": "boolean"
          },
          "provinceId": {
            "type": "integer"
          },
          "quoteCount": {
            "type": "long"
          },
          "removed": {
            "type": "keyword"
          },
          "route": {
            "type": "text",
            "analyzer": "icu"
          },
          "score": {
            "type": "keyword",
            "index": false
          },
          "streetNumber": {
            "type": "text",
            "analyzer": "icu"
          },
          "tagInput": {
            "type": "text",
            "analyzer": "icu"
          },
          "tags": {
            "type": "keyword"
          },
          "type": {
            "type": "keyword"
          },
          "updatedAt": {
            "type": "date"
          },
          "ward": {
            "type": "text",
            "analyzer": "icu"
          },
          "wardId": {
            "type": "integer"
          }
        }
      },
      "tagInput": {
        "type": "text",
        "fields": {
          "keyword": {
            "type": "keyword",
            "ignore_above": 256
          }
        }
      },
      "tags": {
        "type": "text",
        "fields": {
          "keyword": {
            "type": "keyword",
            "ignore_above": 256
          }
        }
      },
      "type": {
        "type": "long"
      },
      "updatedAt": {
        "type": "date"
      },
      "ward": {
        "type": "text",
        "fields": {
          "keyword": {
            "type": "keyword",
            "ignore_above": 256
          }
        }
      },
      "wardId": {
        "type": "integer"
      }
    }
  }
}

2. Reindex dữ liệu từ place-sit sang place-sit2

POST /_reindex?wait_for_completion=false
{
  "source": {
    "index": "place-sit2"
  },
  "dest": {
    "index": "place-sit"
  }
}

// Kiểm tra trạng thái task reindex
GET _tasks/-A1tPSFrSz6F-qABUL2XuQ:********?pretty


Chạy lệnh này để xem các task đang chạy, tìm task reindex:
GET /_tasks?actions=*reindex&detailed=true&pretty

2. Dừng task reindex bằng task id
POST /_tasks/12345/_cancel


# Tạm dừng shard allocation để chờ node quay lại:
PUT _cluster/settings
{
  "transient": {
    "cluster.routing.allocation.enable": "none"
  }
}

//Khi node đã quay lại, bật lại:
PUT _cluster/settings
{
  "transient": {
    "cluster.routing.allocation.enable": "all"
  }
}


GET _cat/nodes?v
GET _cluster/health?pretty

# Hoặc dùng câu truy vấn tìm _id lớn nhất (nếu _id có dạng số hoặc sortable)
GET place-sit/_search
{
  "size": 1,
  "sort": [{ "_id": "desc" }]
}

PUT _cluster/settings
{
  "persistent": {
    "indices.id_field_data.enabled": true
  }
}

POST _reindex
{
  "source": {
    "index": "place-sit2",
    "query": {
      "range": {
        "created_at": {
          "gt": "2024-01-15T12:00:00"
        }
      }
    }
  },
  "dest": {
    "index": "place-sit"
  }
}
### Tính năng BM25Scoring

Hệ thống sử dụng thuật toán BM25 đơn giản hóa trong `Extensions/BM25Scoring.cs` để tính toán điểm khớp cho kết quả tìm kiếm địa điểm taxi. Thuật toán này kết hợp:

1. **Term Frequency (TF)**: Tính tần suất xuất hiện của từ khóa tìm kiếm trong địa chỉ
2. **Inverse Document Frequency (IDF)**: Mô phỏng độ quan trọng của từ khóa
3. **Document Length Normalization**: Điều chỉnh điểm theo độ dài của địa chỉ
4. **Boost Factor**: Tăng điểm cho các trường hợp khớp chính xác tên, địa chỉ, v.v.

Sử dụng thuật toán này giúp kết quả tìm kiếm chính xác hơn, ưu tiên hiển thị kết quả phù hợp nhất trước.

### Cách sử dụng:

```csharp
// Chuẩn hóa query
var normalizedQuery = VietnameseAddressPermutator.NormalizeVietnameseAddress(originalQuery);

// Áp dụng BM25 scoring
BM25Scoring.CalculateBM25Scores(listPlaces, normalizedQuery, logger);

// Sắp xếp kết quả
var results = listPlaces.OrderByDescending(p => p.Score).ToList();
```
