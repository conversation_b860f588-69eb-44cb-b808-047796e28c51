using System.Collections.Generic;

namespace NDS.ETL.BackgroundJobs.Model.API;

/// <summary>
/// Response model for paginated ward data
/// </summary>
public class PagedWardResponse
{
    /// <summary>
    /// Danh s�ch ph??ng/x� trong trang hi?n t?i
    /// </summary>
    public List<WardResponse> Items { get; set; } = new List<WardResponse>();
    
    /// <summary>
    /// T?ng s? ph??ng/x�
    /// </summary>
    public int TotalCount { get; set; }
    
    /// <summary>
    /// Trang hi?n t?i
    /// </summary>
    public int CurrentPage { get; set; }
    
    /// <summary>
    /// K�ch th??c trang
    /// </summary>
    public int PageSize { get; set; }
    
    /// <summary>
    /// T?ng s? trang
    /// </summary>
    public int TotalPages { get; set; }
    
    /// <summary>
    /// C� trang tr??c kh�ng
    /// </summary>
    public bool HasPreviousPage { get; set; }
    
    /// <summary>
    /// C� trang sau kh�ng
    /// </summary>
    public bool HasNextPage { get; set; }
}