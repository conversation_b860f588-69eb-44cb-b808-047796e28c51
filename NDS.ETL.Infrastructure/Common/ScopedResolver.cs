using Microsoft.Extensions.DependencyInjection;

namespace NDS.ETL.Infrastructure.Common;

public class ScopedResolver<T> : IScopedResolver<T> where T : class
{
    private readonly IServiceProvider _provider;

    public ScopedResolver(IServiceProvider provider)
    {
        _provider = provider;
    }

    public TResult Resolve<TResult>(Func<T, TResult> dataFactory)
    {
        using IServiceScope scope = _provider.CreateScope();
        var service = scope.ServiceProvider.GetRequiredService<T>();
        return dataFactory(service);
    }

    public async Task<TResult> ResolveAsync<TResult>(Func<T, Task<TResult>> dataFactory)
    {
        using var scope = _provider.CreateScope();
        var service = scope.ServiceProvider.GetRequiredService<T>();
        return await dataFactory(service);
    }
}