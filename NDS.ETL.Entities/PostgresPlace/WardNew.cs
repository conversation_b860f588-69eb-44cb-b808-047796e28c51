using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("ward_new")]
[Index("Code", Name = "ward_new_code_idx")]
[Index("Name", Name = "ward_new_name_idx")]
[Index("Code", Name = "ward_new_unique", IsUnique = true)]
public partial class WardNew
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("code")]
    [StringLength(50)]
    public string Code { get; set; } = null!;

    [Column("provincecode")]
    [StringLength(50)]
    public string? Provincecode { get; set; }

    [Column("name")]
    [StringLength(1000)]
    public string? Name { get; set; }

    [Column("type")]
    [StringLength(50)]
    public string? Type { get; set; }

    [Column("oldcode")]
    [StringLength(1000)]
    public string? Oldcode { get; set; }

    [Column("boundary")]
    public string? Boundary { get; set; }

    [Column("no_space_name")]
    [StringLength(50)]
    public string? NoSpaceName { get; set; }

    [Column("center_location")]
    [StringLength(255)]
    public string? CenterLocation { get; set; }

    [Column("processed")]
    public bool? Processed { get; set; }

    [Column("boundary_geom", TypeName = "geometry(Polygon,4326)")]
    public Polygon? BoundaryGeom { get; set; }

    [InverseProperty("WardsNewCodeNavigation")]
    public virtual ICollection<WardsBridge> WardsBridges { get; set; } = new List<WardsBridge>();
}
