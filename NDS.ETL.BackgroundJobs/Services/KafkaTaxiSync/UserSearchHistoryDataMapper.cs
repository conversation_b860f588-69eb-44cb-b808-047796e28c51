using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.Infrastructure.Utils;
using Newtonsoft.Json.Linq;

namespace NDS.ETL.BackgroundJobs.Services.KafkaTaxiSync;

public class UserSearchHistoryDataMapper
{
    public static UserSearchHistoryEs MapToUserSearchHistoryEs(JToken data)
    {
        // Chuyển đổi dữ liệu từ Kafka sang định dạng ES
        return new UserSearchHistoryEs
        {
            Id = data["placeId"]?.ToString(),
            CustomerMobileNo = data["customerMobileNo"]?.ToString(),
            Keyword = data["keyword"]?.ToString(),
            PlaceId = data["placeId"]?.ToString(),
            Name = data["name"]?.ToString(),
            Address = data["address"]?.ToString(),
            FullAddress = data["fullAddress"]?.ToString(),
            Location = data["location"] != null ? new Location
            {
                Lat = data["location"]["lat"]?.Value<double>() ?? 0.0,
                Lon = data["location"]["lng"]?.Value<double>() ?? 0.0
            } : null,
            // Sử dụng DateTimeHelper để chuyển đổi timestamp sang DateTime local
            CreatedAt = DateTimeHelper.FromUnixTimeMillisecondsToLocal(
                data["createdAt"]?.Value<long>(), 
                DateTime.MinValue)
        };
    }
} 