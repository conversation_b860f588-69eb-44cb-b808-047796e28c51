using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;
using NpgsqlTypes;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Keyless]
[Table("places_org")]
public partial class PlacesOrg
{
    [Column("id")]
    public long Id { get; set; }

    [Column("placeDetailId", TypeName = "character varying")]
    public string? PlaceDetailId { get; set; }

    [Column("phoneNumber", TypeName = "character varying")]
    public string? PhoneNumber { get; set; }

    [Column("placeId", TypeName = "character varying")]
    public string PlaceId { get; set; } = null!;

    [Column("location", TypeName = "geometry(Point,4326)")]
    public Point? Location { get; set; }

    [Column("name", TypeName = "character varying")]
    public string? Name { get; set; }

    [Column("address", TypeName = "character varying")]
    public string? Address { get; set; }

    [Column("keywords", TypeName = "character varying")]
    public string? Keywords { get; set; }

    [Column("fullAddress", TypeName = "character varying")]
    public string FullAddress { get; set; } = null!;

    [Column("streetNumber", TypeName = "character varying")]
    public string? StreetNumber { get; set; }

    [Column("route", TypeName = "character varying")]
    public string? Route { get; set; }

    [Column("ward", TypeName = "character varying")]
    public string? Ward { get; set; }

    [Column("district", TypeName = "character varying")]
    public string? District { get; set; }

    [Column("city", TypeName = "character varying")]
    public string? City { get; set; }

    [Column("type")]
    public int Type { get; set; }

    [Column("active")]
    public bool Active { get; set; }

    [Column("tags")]
    public List<string>? Tags { get; set; }

    [Column("tagInput")]
    public string? TagInput { get; set; }

    [Column("popular")]
    public bool Popular { get; set; }

    [Column("processed")]
    public bool Processed { get; set; }

    [Column("bookingCount")]
    public int BookingCount { get; set; }

    [Column("removed")]
    public bool Removed { get; set; }

    [Column("hidden")]
    public bool Hidden { get; set; }

    [Column("createdAt", TypeName = "timestamp without time zone")]
    public DateTime CreatedAt { get; set; }

    [Column("updatedAt", TypeName = "timestamp without time zone")]
    public DateTime UpdatedAt { get; set; }

    [Column("creatorId", TypeName = "character varying")]
    public string? CreatorId { get; set; }

    [Column("lastUpdateUser", TypeName = "character varying")]
    public string? LastUpdateUser { get; set; }

    [Column("dataFrom", TypeName = "character varying")]
    public string? DataFrom { get; set; }

    [Column("ts")]
    public NpgsqlTsVector? Ts { get; set; }

    [Column("locationHash", TypeName = "character varying")]
    public string? LocationHash { get; set; }

    [Column("locationHash2", TypeName = "character varying")]
    public string? LocationHash2 { get; set; }

    [Column("locationHash3", TypeName = "character varying")]
    public string? LocationHash3 { get; set; }

    [Column("hasChild")]
    public bool HasChild { get; set; }

    [Column("fullAddressHash", TypeName = "character varying")]
    public string? FullAddressHash { get; set; }

    [Column("ts2")]
    public NpgsqlTsVector? Ts2 { get; set; }

    [Column("districtId")]
    public int? DistrictId { get; set; }

    [Column("provinceId")]
    public int? ProvinceId { get; set; }

    [Column("wardId")]
    public int? WardId { get; set; }

    [Column("pending")]
    public bool Pending { get; set; }

    [Column("quoteCount")]
    public int QuoteCount { get; set; }

    [Column("source")]
    [StringLength(255)]
    public string? Source { get; set; }

    [Column("isVietnam")]
    public bool? IsVietnam { get; set; }

    [Column("status")]
    [StringLength(255)]
    public string? Status { get; set; }
}
