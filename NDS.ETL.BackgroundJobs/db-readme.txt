dotnet tool update --global dotnet-ef
dotnet-ef migrations add Add_ApiKeys --context ESDbContext -v
dotnet-ef database update --context ESDbContext -v

dotnet-ef migrations add CreateBookingCountIdxMigration --context ESDbContext
dotnet-ef database update --context ESDbContext

dotnet-ef database update SqlFunctionMigration --context ESDbContext

dotnet-ef migrations remove --context ESDbContext

Scaffold-DbContext "Host=***********;Port=5432;Database=******;Username=******;Password=******" Npgsql.EntityFrameworkCore.PostgreSQL -OutputDir Postgres -ContextDir Context -Context ESDbContext -DataAnnotations -f

dotnet-ef migrations script --context ESDbContext -v -o Migrations/Scripts/ESDbContext.sql
dotnet-ef migrations script IndexLastUserUpdate Add_Place_Details

dotnet-ef migrations script Add_Place_Details Fix_PlaceDetails_Index -o Migrations/Scripts/Fix_PlaceDetails_Index.sql


SELECT * FROM information_schema.sequences;

-- Sequence names with tables
SELECT t.oid::regclass AS table_name,
       a.attname AS column_name,
       s.relname AS sequence_name
FROM pg_class AS t
         JOIN pg_attribute AS a
              ON a.attrelid = t.oid
         JOIN pg_depend AS d
              ON d.refobjid = t.oid
                  AND d.refobjsubid = a.attnum
         JOIN pg_class AS s
              ON s.oid = d.objid
WHERE d.classid = 'pg_catalog.pg_class'::regclass
  AND d.refclassid = 'pg_catalog.pg_class'::regclass
  AND d.deptype IN ('i', 'a')
  AND t.relkind IN ('r', 'P')
  AND s.relkind = 'S';


https://websymphony.net/blog/how-to-fix-postgres-sequences/

ALTER TABLE "places" ALTER COLUMN "Id" RESTART WITH 35;

--- Fix all sequences
SELECT 'SELECT SETVAL(' ||
  quote_literal(quote_ident(PGT.schemaname) || '.' || quote_ident(S.relname)) ||
  ', COALESCE(MAX(' ||quote_ident(C.attname)|| '), 1) ) FROM ' ||
  quote_ident(PGT.schemaname)|| '.'||quote_ident(T.relname)|| ';'
FROM pg_class AS S,
     pg_depend AS D,
     pg_class AS T,
     pg_attribute AS C,
     pg_tables AS PGT
WHERE S.relkind = 'S'
    AND S.oid = D.objid
    AND D.refobjid = T.oid
    AND D.refobjid = C.attrelid
    AND D.refobjsubid = C.attnum
    AND T.relname = PGT.tablename
ORDER BY S.relname;


pg_dump -h localhost -p 5432 -d namedb -U postgres -t '*_id_seq' > dump-seq.sql

pg_dump -h ********** -p 5444 -d ****** -U ****** -n public -t '*_id_seq' > dump-seq.sql

pg_dump -h ********** -p 5444 -d ****** -U ****** -n public -Z 6 -f C:\Users\<USER>\Desktop\******.sql

psql -h *********** -p 9432 -U ****** -d ****** -f D:\******.sql

gzip -d ./******.gz
zcat ******.gz | psql -d test_db -U postgres -h localhost -p port

#Restore your databases
PGPASSWORD=****** zcat ******.zip | docker exec -i pgsql-postgresql-1 psql -U ****** -d ******

zcat ******.zip | docker exec -i pgsql-postgresql-1  /bin/bash -c "PGPASSWORD=****** psql -U ****** -d ******"

cat ******.sql | docker exec -i pgsql-postgresql-1  /bin/bash -c "PGPASSWORD=****** psql -U ****** -d ******"

pg_dump -h host1 dbname | psql -h host2 dbname

For example to dump tables which start with "test", you can use
pg_dump -D -a -t zones_seq -t interway -t "^test*" -f /tmp/zones_seq.sql <DBNAME>


---- Only table ----
pg_dump -h *********** -p 9432 -t places -d ****** -U ****** -n public -Z 9 -f L:\places.gz

--> unzip L:\places.gz

psql -h ************ -p 5444 -U ****** -d ****** -f L:\places

zcat /var/data/places.gz | docker exec -i pgsql-postgresql-1  /bin/bash -c "PGPASSWORD=****** psql -h ************ -p 5444 -U ****** -d ******"


CREATE DATABASE ******_bak
WITH TEMPLATE ******
OWNER ******;


# List databases
sudo su postgres
psql
\list

# Create a compressed backup
sudo su postgres
pg_dump -Fc <database_name> > <file>

# Example
pg_dump -Fc geartranslations_development > /tmp/backup.dump


# Restore a plain-text backup
# TABLES THAT ALREADY EXIST WILL NOT BE MODIFIED.
sudo su postgres
pg_restore -Fc -d <database_name> <backup_file>

# Example
pg_restore -Fc -d geartranslations_development /tmp/backup.dump


SELECT 
    pg_terminate_backend(pid) 
FROM 
    pg_stat_activity 
WHERE 
    -- don't kill my own connection!
    pid <> pg_backend_pid()
    -- don't kill the connections to other databases
    AND datname = '******'
    ;
    