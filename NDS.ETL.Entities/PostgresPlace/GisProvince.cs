using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Keyless]
[Table("gis_province")]
public partial class GisProvince
{
    [Column("_id")]
    public int? Id { get; set; }

    [Column("_class")]
    [StringLength(50)]
    public string? Class { get; set; }

    [Column("created_at")]
    [StringLength(50)]
    public string? CreatedAt { get; set; }

    [Column("location")]
    [StringLength(32767)]
    public string? Location { get; set; }

    [Column("modified_at")]
    [StringLength(50)]
    public string? ModifiedAt { get; set; }

    [Column("modified_by")]
    [StringLength(50)]
    public string? ModifiedBy { get; set; }

    [Column("name")]
    [StringLength(50)]
    public string? Name { get; set; }

    [Column("postCode")]
    [StringLength(50)]
    public string? PostCode { get; set; }

    [Column("status")]
    public int? Status { get; set; }
}
