using H3.Extensions;
using NDS.ETL.Infrastructure.Extensions;

namespace TestProject
{
    public class MainUnitTest
    {
        [SetUp]
        public void Setup()
        {
            // C<PERSON><PERSON> thiết lập riêng cho từng test
        }

        [Test]
        public void Test1()
        {
            var addresses = new[]
            {
                "22 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>",
                "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> 10 (<PERSON><PERSON><PERSON><PERSON> 10), <PERSON><PERSON><PERSON><PERSON> 10, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, Vi<PERSON>t Nam",
                "Green House Coffee, 420 Đ. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 5, <PERSON><PERSON>, <PERSON><PERSON> 700000, <PERSON><PERSON><PERSON><PERSON>",
                "640/22 <PERSON><PERSON>, <PERSON><PERSON> 52, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>",
                "97/22, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>",
                "138 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>",
                "43/3 <PERSON><PERSON><PERSON><PERSON> 2, <PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON> 7, <PERSON> p<PERSON> <PERSON>, <PERSON><PERSON><PERSON>",
                "113/4 <PERSON><PERSON>, <PERSON> <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> p<PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>",
                "22 <PERSON> <PERSON>, <PERSON> <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>",
                "9 <PERSON> 21 hẻm 85 Nguyễn Thái Sơn, Gò Vấp, Hồ Chí Minh, Việt Nam",
                "566 /48 Đ. Nguyễn Thái Sơn, Phường 5, Gò Vấp, Hồ Chí Minh, Việt Nam",
                "420 Đ. Nguyễn Thái Sơn, Phường 5, Gò Vấp, Hồ Chí Minh 700000, Việt Nam",
                "156/7e/9, Đ. Tô Hiến Thành, Phường 15, Quận 10, Hồ Chí Minh 70000, Việt Nam",
                "Số 1 P. Huỳnh Tấn Phát, Khu công nghiệp Sài, Long Biên, Hà Nội 100000, Việt Nam",
                "207 Đ. Giải Phóng, Đồng Tâm, Hai Bà Trưng, Hà Nội, Việt Nam",
            };
            foreach (var address in addresses)
            {
                var permutations = AddressPermutator.GenerateOptimizedPermutations(address);
                foreach (var permutation in permutations)
                {
                    Console.WriteLine(permutation);
                }

                Console.WriteLine("\n=========================================================\n");
            }

            Assert.Pass();
        }


        [Test]
        public void TestVietnamese()
        {
            var addresses = new[]
            {
                "22 Đường Láng, Láng Thượng, Đống Đa, Hà Nội, Việt Nam",
                "Quán Ăn Bắc Lợi, Ba Tháng Hai, Phường 10 (Quận 10), Quận 10, Thành phố Hồ Chí Minh, Việt Nam",
                "Green House Coffee, 420 Đ. Nguyễn Thái Sơn, Phường 5, Gò Vấp, Hồ Chí Minh 700000, Việt Nam",
                "640/22 Đ. Láng, Tổ 52, Đống Đa, Hà Nội, Việt Nam",
                "97/22, Nguyễn Chí Thanh, Phường Láng Hạ, Quận Đống Đa, Thành Phố Hà Nội, Láng Hạ, Đống Đa, Hà Nội, Việt Nam",
                "138 Phố Hoàng Văn Thái, Khương Mai, Thanh Xuân, Hà Nội, Việt Nam",
                "43/3 Khu Phố Hưng Phước 2, Tân Phong, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
                "113/4 Đường Phú Thọ Hòa, Phú Thọ Hoà, Tân Phú, Thành phố Hồ Chí Minh, Việt Nam",
                "22 Láng Hạ, Láng Hạ, Đống Đa, Hà Nội, Vietnam",
                "9 ngách 21 hẻm 85 Nguyễn Thái Sơn, Gò Vấp, Hồ Chí Minh, Việt Nam",
                "566 /48 Đ. Nguyễn Thái Sơn, Phường 5, Gò Vấp, Hồ Chí Minh, Việt Nam",
                "420 Đ. Nguyễn Thái Sơn, Phường 5, Gò Vấp, Hồ Chí Minh 700000, Việt Nam",
                "156/7e/9, Đ. Tô Hiến Thành, Phường 15, Quận 10, Hồ Chí Minh 70000, Việt Nam",
                "Số 1 P. Huỳnh Tấn Phát, Khu công nghiệp Sài, Long Biên, Hà Nội 100000, Việt Nam",
                "207 Đ. Giải Phóng, Đồng Tâm, Hai Bà Trưng, Hà Nội, Việt Nam",
            };
            foreach (var address in addresses)
            {
                var permutations = VietnameseAddressPermutator.GenerateOptimizedPermutations(address, 2);
                foreach (var permutation in permutations)
                {
                    Console.WriteLine(permutation);
                }

                Console.WriteLine("\n=========================================================\n");
            }

            Assert.Pass();
        }


        [Test]
        public void TestH3()
        {

            var coordinate = new NetTopologySuite.Geometries.Coordinate(105.8666812, 20.9743233);
            var h3Index = coordinate.ToH3Index(15).ToString();
            Console.WriteLine($"8f415cb6e67049b == {h3Index}");
            if ("8f415cb6e67049b" == h3Index) Assert.Pass();
            else Assert.Fail();
        }


        [Test]
        public void TestH32()
        {

            var coordinate = new NetTopologySuite.Geometries.Coordinate(105.8531022, 21.0244094);
            var h3Index = coordinate.ToH3Index(10).ToString();
            Console.WriteLine($"8a415cb4ecc7fff == {h3Index}");
            if ("8a415cb4ecc7fff" == h3Index) Assert.Pass();
            else Assert.Fail();
        }
    }
}
