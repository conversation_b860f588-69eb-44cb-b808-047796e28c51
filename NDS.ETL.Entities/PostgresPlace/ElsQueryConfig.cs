using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("els_query_configs")]
public partial class ElsQueryConfig
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("name_index")]
    [StringLength(255)]
    public string NameIndex { get; set; } = null!;

    [Column("query_vi")]
    [StringLength(10000)]
    public string QueryVi { get; set; } = null!;

    [Column("query_en")]
    [StringLength(10000)]
    public string QueryEn { get; set; } = null!;
}
