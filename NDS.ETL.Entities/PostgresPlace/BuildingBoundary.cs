using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;
using NodaTime;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("building_boundary")]
public partial class BuildingBoundary
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("latitude")]
    public double Latitude { get; set; }

    [Column("longitude")]
    public double Longitude { get; set; }

    [Column("processStatus")]
    public int ProcessStatus { get; set; }

    [Column("boundary", TypeName = "jsonb")]
    public string? Boundary { get; set; }

    [Column("name")]
    [StringLength(256)]
    public string? Name { get; set; }

    [Column("origin")]
    public int? Origin { get; set; }

    [Column("geom", TypeName = "geometry(Polygon,4326)")]
    public Polygon? Geom { get; set; }

    [Column("code")]
    [StringLength(50)]
    public string? Code { get; set; }

    [Column("note")]
    [StringLength(500)]
    public string? Note { get; set; }

    [Column("status")]
    public int? Status { get; set; }

    [Column("placeFkId")]
    public long PlaceFkId { get; set; }

    [Column("creatorId")]
    [StringLength(255)]
    public string? CreatorId { get; set; }

    [Column("lastUpdateUser")]
    [StringLength(255)]
    public string? LastUpdateUser { get; set; }

    [Column("lastUpdateUserId")]
    [StringLength(255)]
    public string? LastUpdateUserId { get; set; }

    [Column("placeMasterAddress")]
    public string? PlaceMasterAddress { get; set; }

    [InverseProperty("BuildingBoundary")]
    public virtual ICollection<BuildingBoundaryPlace> BuildingBoundaryPlaces { get; set; } = new List<BuildingBoundaryPlace>();
}
