using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch.PlacePairSync;
using NDS.ETL.Entities.Context;
using NDS.ETL.Infrastructure.Common;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.PlacePairSync;

/// <summary>
/// Dịch vụ xác định chiến lược đồng bộ
/// CHÍNH SÁCH MỚI: Chỉ thực hiện INCREMENTAL SYNC, không bao giờ FULL SYNC
/// (trừ khi index ES không tồn tại)
/// </summary>
public class SyncStrategyService
{
    private readonly ILogger _logger;
    private readonly IScopedResolver<NpgPlaceContext> _dbContextResolver;
    
    // NOTE: Các hằng số này được giữ lại để reference, nhưng không sử dụng
    private const int FULL_SYNC_DAYS_THRESHOLD = 7; // Không sử dụng
    private const double INCREMENTAL_SYNC_PERCENTAGE_THRESHOLD = 50.0; // Không sử dụng

    public SyncStrategyService(
        ILogger logger,
        IScopedResolver<NpgPlaceContext> dbContextResolver)
    {
        _logger = logger;
        _dbContextResolver = dbContextResolver;
    }

    /// <summary>
    /// Quyết định chiến lược đồng bộ
    /// </summary>
    public SyncStrategy DetermineSyncStrategy(DatabaseStats dbStats, ElasticsearchStats esStats, DateTime sixMonthsAgo, CancellationToken stoppingToken)
    {
        // Nếu index chưa tồn tại hoặc trống, thực hiện full sync
        if (!esStats.IndexExists)
        {
            return new SyncStrategy
            {
                IsFullSync = true,
                RecordsToSync = dbStats.TotalRecords,
                Reason = "Index doesn't exist or is empty"
            };
        }
        
        // Kiểm tra nếu không có dữ liệu mới để đồng bộ dựa trên thời gian UpdateAt và CreatedAt
        bool hasUpdates = esStats.MaxUpdateAt < dbStats.MaxUpdateAt;
        bool hasNewRecords = dbStats.NewRecordsCount > 0 && 
            (esStats.MaxCreateAt == DateTime.MinValue || esStats.MaxCreateAt < dbStats.MaxCreateAt);
        
        // Log chi tiết để debug
        _logger.LogInformation($"🔍 Sync analysis: " +
            $"ES_MaxUpdateAt: {esStats.MaxUpdateAt:HH:mm:ss.fff}, DB_MaxUpdateAt: {dbStats.MaxUpdateAt:HH:mm:ss.fff}, " +
            $"ES_MaxCreateAt: {esStats.MaxCreateAt:HH:mm:ss.fff}, DB_MaxCreateAt: {dbStats.MaxCreateAt:HH:mm:ss.fff}");
        
        if (!hasUpdates && !hasNewRecords)
        {
            _logger.LogInformation("No records to sync: " + 
                $"ES max UpdateAt: {esStats.MaxUpdateAt}, DB max UpdateAt: {dbStats.MaxUpdateAt}, " +
                $"ES max CreateAt: {esStats.MaxCreateAt}, DB max CreateAt: {dbStats.MaxCreateAt}, " + 
                $"New records count: {dbStats.NewRecordsCount}");
                
            return new SyncStrategy
            {
                IsFullSync = false,
                RecordsToSync = 0,
                Reason = "No new updates or new records"
            };
        }

        // Tính số lượng bản ghi cần thêm hoặc cập nhật với logic tối ưu
        var (updatedRecordsCount, actualNewRecordsCount) = _dbContextResolver.Resolve(dbContext =>
        {
            // Đếm số lượng bản ghi cập nhật thực sự (UpdateAt > CreatedAt và UpdateAt > esMaxUpdateAt)
            var updatedCount = dbContext.BookingPlacePairs
                .AsNoTracking()
                .Count(x => x.UpdateAt != null && x.CreatedAt != null && 
                           x.UpdateAt > x.CreatedAt && x.UpdateAt > esStats.MaxUpdateAt);
                
            // Đếm số lượng bản ghi mới thực sự cần sync (dựa trên CreatedAt)
            var newCount = 0;
            if (hasNewRecords)
            {
                newCount = dbContext.BookingPlacePairs
                    .AsNoTracking()
                    .Count(x => ((x.UpdateAt == null && x.CreatedAt != null) || 
                               (x.UpdateAt != null && x.CreatedAt != null && x.UpdateAt == x.CreatedAt)) &&
                               x.CreatedAt > esStats.MaxCreateAt);
            }
            
            return (updatedCount, newCount);
        });
        
        var recordsToSync = updatedRecordsCount + actualNewRecordsCount;
        var recordsToSyncPercentage = dbStats.TotalRecords > 0 
            ? (double)recordsToSync / dbStats.TotalRecords * 100 
            : 0;

        // CHÍNH SÁCH MỚI: Luôn thực hiện incremental sync, không bao giờ full sync
        // (trừ khi index không tồn tại)
        bool isFullSync = false; // Luôn là false
        
        _logger.LogInformation($"📊 Sync strategy: " +
            $"Records to sync: {recordsToSync:N0} ({recordsToSyncPercentage:F2}%) = " +
            $"NEW: {actualNewRecordsCount:N0} + UPDATED: {updatedRecordsCount:N0}, " +
            $"Strategy: INCREMENTAL ONLY");
            
        return new SyncStrategy
        {
            IsFullSync = isFullSync,
            RecordsToSync = recordsToSync,
            Reason = recordsToSync > 0 ? "Incremental sync for all changes" : "No changes to sync"
        };
    }
} 