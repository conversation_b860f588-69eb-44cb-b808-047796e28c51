using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Table("wards")]
public partial class Ward
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("code", TypeName = "character varying")]
    public string? Code { get; set; }

    [Column("districtCode", TypeName = "character varying")]
    public string? DistrictCode { get; set; }

    [Column("name", TypeName = "character varying")]
    public string? Name { get; set; }

    [Column("type")]
    public int? Type { get; set; }

    [Column("boundary")]
    public string? Boundary { get; set; }

    [Column("no_space_name", TypeName = "character varying")]
    public string? NoSpaceName { get; set; }

    [Column("province_code", TypeName = "character varying")]
    public string? ProvinceCode { get; set; }

    [Column("center_location", TypeName = "character varying")]
    public string? CenterLocation { get; set; }

    [Column("processed")]
    public bool? Processed { get; set; }

    [Column("boundary_geom")]
    public string? BoundaryGeom { get; set; }

    [Column("district_code", TypeName = "character varying")]
    public string? DistrictCode1 { get; set; }

    [Column("same_street_name")]
    public bool? SameStreetName { get; set; }
}
