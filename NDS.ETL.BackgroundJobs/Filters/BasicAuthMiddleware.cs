using System.Text;

namespace NDS.ETL.BackgroundJobs.Filters
{
    public class BasicAuthMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IConfiguration _config;

        public BasicAuthMiddleware(RequestDelegate next, IConfiguration config)
        {
            _next = next;
            _config = config;
        }

        public async Task Invoke(HttpContext context)
        {
            if (!context.Request.Headers.TryGetValue("Authorization", out var authHeader))
            {
                context.Response.StatusCode = 401;
                await context.Response.WriteAsync("Unauthorized");
                return;
            }

            var authHeaderVal = authHeader.ToString();
            if (!authHeaderVal.StartsWith("Basic "))
            {
                context.Response.StatusCode = 401;
                await context.Response.WriteAsync("Invalid Authorization Header");
                return;
            }

            var encodedCredentials = authHeaderVal.Substring("Basic ".Length).Trim();
            var decodedCredentials = Encoding.UTF8.GetString(Convert.FromBase64String(encodedCredentials));
            var credentials = decodedCredentials.Split(':');

            var username = credentials[0];
            var password = credentials[1];

            if (username != _config["BasicAuth:UserName"] || password != _config["BasicAuth:Password"])
            {
                context.Response.StatusCode = 401;
                await context.Response.WriteAsync("Invalid Credentials");
                return;
            }

            await _next(context);
        }
    }


}
