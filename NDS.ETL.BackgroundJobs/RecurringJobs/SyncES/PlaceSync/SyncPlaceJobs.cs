using System.Diagnostics;
using System.Text.RegularExpressions;
using Elastic.Clients.Elasticsearch;
using KnowledgeBase.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.Entities.Context;
using NDS.ETL.Infrastructure.Common;
using NDS.ETL.Infrastructure.Utils;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.PlaceSync;

/// <summary>
/// Job đồng bộ dữ liệu Place từ PostgreSQL sang ElasticSearch
/// </summary>
public class SyncPlaceJobs : RecurringJobBaseElastic
{
    private readonly ILogger<RecurringJobBaseElastic> _logger;
    private readonly ElasticsearchClient _elasticClient;
    private readonly IServiceProvider _serviceProvider;
    private readonly string _indexName;
    private readonly IDistributedLock _distributedLock;
    
    // Các hằng số cấu hình
    private const int BATCH_SIZE = 10_000; // <PERSON><PERSON><PERSON> thước batch để xử lý
    private const int COMMAND_TIMEOUT = 300; // Timeout cho database command (giây)
    private const int SYNC_INTERVAL_MINUTES = 30; // Thời gian đồng bộ mặc định (phút)
    private const string LOCK_KEY = "sync_places_lock"; // Key cho distributed lock
    private const int LOCK_TIMEOUT_SECONDS = 3600; // 1 giờ

    /// <summary>
    /// Khởi tạo job đồng bộ Place
    /// </summary>
    public SyncPlaceJobs(
        IConfiguration configuration, 
        ILogger<RecurringJobBaseElastic> logger,
        ElasticsearchClient elasticClient, 
        IServiceProvider serviceProvider,
        IDistributedLock distributedLock) : base(configuration, logger, elasticClient, null)
    {
        var indexConfig = configuration.Bind<ElasticSearchConfig>(nameof(ElasticSearchConfig));
        _indexName = indexConfig.Index[0]; // Lấy index đầu tiên trong danh sách
        _logger = logger;
        _elasticClient = elasticClient;
        _serviceProvider = serviceProvider;
        _distributedLock = distributedLock;
    }

    public override string JobId => "SyncPlaces";
    public override string ConfigSection => "JobPgSyncPlace";

    /// <summary>
    /// Lấy thời điểm đồng bộ cuối cùng từ ElasticSearch
    /// </summary>
    private async Task<DateTime> GetLastSyncTimeFromEsAsync(CancellationToken stoppingToken)
    {
        try
        {
            // Kiểm tra xem index có tồn tại hay không
            var indexExistsResponse = await _elasticClient.Indices.ExistsAsync(_indexName, stoppingToken);
            if (!indexExistsResponse.Exists)
            {
                _logger.LogWarning($"ES index '{_indexName}' doesn't exist");
                return DateTime.Now.AddMinutes(-SYNC_INTERVAL_MINUTES);
            }

            // Truy vấn UpdatedAt lớn nhất từ Elasticsearch
            var response = await _elasticClient.SearchAsync<PlacesEs>(s => s
                .Index(_indexName)
                .Size(0)
                .Aggregations(aggs => aggs
                    .Add("max_updated_at", d =>
                    {
                        d.Max(x =>
                        {
                            x.Field(f => f.UpdatedAt);
                        });
                    })
                ), stoppingToken);

            // Lấy kết quả từ aggregation
            if (response.Aggregations == null)
            {
                _logger.LogWarning("No aggregations returned from ES");
                return DateTime.Now.AddMinutes(-SYNC_INTERVAL_MINUTES);
            }
        
            var maxAgg = response.Aggregations.GetMax("max_updated_at");
            if (maxAgg == null || maxAgg.Value == null)
            {
                _logger.LogWarning("No max aggregation results found for UpdatedAt");
                return DateTime.Now.AddMinutes(-SYNC_INTERVAL_MINUTES);
            }

            // Chuyển đổi giá trị từ double sang DateTime
            if (maxAgg.Value.HasValue && maxAgg.Value.Value > 0)
            {
                // Giả sử giá trị là Unix timestamp tính bằng mili giây
                var lastSyncTime = DateTimeOffset.FromUnixTimeMilliseconds((long)maxAgg.Value.Value).UtcDateTime;
                return lastSyncTime;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving max UpdatedAt from Elasticsearch");
        }
        
        // Nếu không lấy được giá trị, trả về thời điểm mặc định
        return DateTime.Now.AddMinutes(-SYNC_INTERVAL_MINUTES);
    }

    /// <summary>
    /// Lấy thời điểm tạo mới cuối cùng từ ElasticSearch cho các bản ghi mới
    /// </summary>
    private async Task<DateTime> GetLastCreatedTimeFromEsAsync(CancellationToken stoppingToken)
    {
        try
        {
            // Kiểm tra xem index có tồn tại hay không
            var indexExistsResponse = await _elasticClient.Indices.ExistsAsync(_indexName, stoppingToken);
            if (!indexExistsResponse.Exists)
            {
                _logger.LogWarning($"ES index '{_indexName}' doesn't exist");
                return DateTime.Now.AddMinutes(-SYNC_INTERVAL_MINUTES);
            }

            // Truy vấn CreatedAt lớn nhất từ Elasticsearch
            var response = await _elasticClient.SearchAsync<PlacesEs>(s => s
                .Index(_indexName)
                .Size(0)
                .Aggregations(aggs => aggs
                    .Add("max_created_at", d =>
                    {
                        d.Max(x =>
                        {
                            x.Field(f => f.CreatedAt);
                        });
                    })
                ), stoppingToken);

            // Lấy kết quả từ aggregation
            if (response.Aggregations == null)
            {
                _logger.LogWarning("No aggregations returned from ES for CreatedAt");
                return DateTime.Now.AddMinutes(-SYNC_INTERVAL_MINUTES);
            }
        
            var maxAgg = response.Aggregations.GetMax("max_created_at");
            if (maxAgg == null || maxAgg.Value == null)
            {
                _logger.LogWarning("No max aggregation results found for CreatedAt");
                return DateTime.Now.AddMinutes(-SYNC_INTERVAL_MINUTES);
            }

            // Chuyển đổi giá trị từ double sang DateTime
            if (maxAgg.Value.HasValue && maxAgg.Value.Value > 0)
            {
                // Giả sử giá trị là Unix timestamp tính bằng mili giây
                var lastCreatedTime = DateTimeOffset.FromUnixTimeMilliseconds((long)maxAgg.Value.Value).UtcDateTime;
                return lastCreatedTime;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving max CreatedAt from Elasticsearch");
        }
        
        // Nếu không lấy được giá trị, trả về thời điểm mặc định
        return DateTime.Now.AddMinutes(-SYNC_INTERVAL_MINUTES);
    }

    /// <summary>
    /// Xử lý các bản ghi Place trước khi đồng bộ lên ES
    /// </summary>
    private List<PlacesEs> ProcessPlaceRecords(List<PlacesEs> items)
    {
        // Regex: Tìm PlusCode dạng '4-6 ký tự' + 'dấu +' + '2-3 ký tự'
        const string pattern = @"(?i)\b[23456789CFGHJMPQRVWX]{4,6}\s*\+\s*[23456789CFGHJMPQRVWX]{2,3}\b";

        // Xử lý từng bản ghi: sinh keyword + permuted address + lọc bỏ địa chỉ chứa PlusCode
        return items.Where(x =>
        {
            x.ProcessKeywords(); // Xử lý sinh keyword tìm kiếm
            x.CalculateMasterAddressAndPermuted(); // Sinh địa chỉ chuẩn hóa + hoán vị
            var hasPlusCode = Regex.Match(x.FullAddress, pattern).Success;
            if (hasPlusCode)
            {
                _logger.LogInformation($"Filtered out address with PlusCode: {x.FullAddress}");
            }
            return !hasPlusCode; // Loại bỏ những địa chỉ có PlusCode
        }).ToList();
    }

    /// <summary>
    /// Thực hiện đồng bộ dữ liệu Place từ PostgreSQL sang ElasticSearch
    /// </summary>
    public override async Task DoWorkAsync(CancellationToken stoppingToken)
    {
        // Kiểm tra xem job có đang chạy không bằng cách sử dụng distributed lock
        var lockAcquired = false;
        try
        {
            _logger.LogInformation("Attempting to acquire lock for Place sync");

            // Cố gắng lấy khóa với timeout
            lockAcquired = await _distributedLock.TryAcquireLockAsync(
                LOCK_KEY,
                TimeSpan.FromSeconds(LOCK_TIMEOUT_SECONDS));

            if (!lockAcquired)
            {
                _logger.LogWarning("Could not acquire lock - another sync job is already running");
                return;
            }

            _logger.LogInformation("Lock acquired. Starting Place sync");

            // Bắt đầu đo tổng thời gian toàn bộ process
            var globalStopwatch = Stopwatch.StartNew();
            _logger.LogInformation("Starting to sync Place data to ElasticSearch");

            try
            {
                using var scope = _serviceProvider.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<NpgPlaceContext>();
                dbContext.Database.SetCommandTimeout(COMMAND_TIMEOUT); // Set timeout cho các query DB

                // Quan trọng: Đồng bộ theo thứ tự để tránh xung đột
                // 1. Đồng bộ các bản ghi mới trước (CreatedAt mới và UpdatedAt = null)
                var newlyProcessedIds = await SyncNewPlaceRecordsAsync(dbContext, stoppingToken);
                
                // 2. Sau đó mới đồng bộ các bản ghi cập nhật (UpdatedAt > lastSyncTime)
                // Truyền danh sách ID đã xử lý ở bước 1 để tránh xử lý trùng lặp
                await SyncUpdatedPlaceRecordsAsync(dbContext, newlyProcessedIds, stoppingToken);

                globalStopwatch.Stop();
                _logger.LogInformation(
                    $"Completed syncing Place records to ElasticSearch, total time: {globalStopwatch.Elapsed.TotalSeconds:F2} seconds");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while syncing Place data to ElasticSearch");
                throw; // Rethrow để hệ thống quản lý error tracking
            }
        }
        catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
        {
            _logger.LogInformation("Place sync job canceled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Place sync job");
            throw;
        }
        finally
        {
            // Giải phóng khóa nếu đã lấy được
            if (lockAcquired)
            {
                await _distributedLock.ReleaseLockAsync(LOCK_KEY);
                _logger.LogInformation("Released lock for Place sync");
            }
        }
    }

    /// <summary>
    /// Đồng bộ các bản ghi mới (CreatedAt mới và UpdatedAt = null)
    /// </summary>
    /// <returns>Danh sách ID của các bản ghi đã được đồng bộ</returns>
    private async Task<HashSet<long>> SyncNewPlaceRecordsAsync(NpgPlaceContext dbContext, CancellationToken stoppingToken)
    {
        _logger.LogInformation("Starting to sync new Place records (CreatedAt with UpdatedAt = null)");
        
        // Set để lưu ID của các bản ghi đã được xử lý
        var processedIds = new HashSet<long>();
        
        // Lấy thời gian tạo mới cuối cùng đã được đồng bộ
        var lastSyncCreatedTime = await GetLastCreatedTimeFromEsAsync(stoppingToken);
        
        // Log với định dạng chuẩn
        _logger.LogInformation($"ES last sync CreatedAt: {lastSyncCreatedTime:yyyy-MM-dd HH:mm:ss.fff}");
        
        var count = 0; // Đếm tổng số bản ghi đã xử lý
        var batchSize = BATCH_SIZE; // Kích thước batch
        var lastSyncedCreatedAt = lastSyncCreatedTime; // Thời điểm tạo mới gần nhất đã sync
        
        while (true)
        {
            var itemStopwatch = Stopwatch.StartNew(); // Đo thời gian từng batch
            
            // Lấy batch dữ liệu mới (CreatedAt > lastSyncedCreatedAt và UpdatedAt = null)
            var items = await dbContext.Places
                .AsNoTracking()
                .Where(x => x.CreatedAt > lastSyncedCreatedAt && x.UpdatedAt == null)
                .OrderBy(x => x.CreatedAt) // Phải order theo CreatedAt tăng dần để phân trang chính xác
                .Take(batchSize)
                .Select(PlacesEs.Selector) // Chỉ select những field cần thiết để nhẹ payload
                .ToListAsync(stoppingToken);
            
            if (items.Count == 0)
                break; // Nếu hết data thì thoát vòng lặp
            
            // Xử lý các bản ghi trước khi đồng bộ
            var processedItems = ProcessPlaceRecords(items);
            
            // Lưu ID của các bản ghi đã xử lý
            foreach (var item in processedItems)
            {
                if (item.Id > 0)
                {
                    processedIds.Add(item.Id);
                }
            }
            
            // Gửi batch này lên ElasticSearch
            await base.IndexMany(processedItems, stoppingToken, _indexName, BATCH_SIZE);
            
            count += processedItems.Count; // Cập nhật tổng số đã sync
            
            if (items.Count > 0)
            {
                // Cập nhật thời điểm CreatedAt của item cuối cùng trong batch này
                lastSyncedCreatedAt = items.Max(x => x.CreatedAt);
            }
            
            itemStopwatch.Stop();
            _logger.LogInformation(
                $"Synced batch of {processedItems.Count} new Place records, total: {count}, time: {itemStopwatch.Elapsed.TotalSeconds:F2} seconds");
        }
        
        _logger.LogInformation($"Completed syncing {count} new Place records to ElasticSearch");
        return processedIds;
    }

    /// <summary>
    /// Đồng bộ các bản ghi đã cập nhật (UpdatedAt > lastSyncTime)
    /// </summary>
    /// <param name="dbContext">Database context</param>
    /// <param name="excludeIds">Danh sách ID bản ghi đã được xử lý ở bước đồng bộ dữ liệu mới</param>
    /// <param name="stoppingToken">Token hủy</param>
    private async Task SyncUpdatedPlaceRecordsAsync(NpgPlaceContext dbContext, HashSet<long> excludeIds, CancellationToken stoppingToken)
    {
        _logger.LogInformation("Starting to sync updated Place records");
        
        // Lấy thời gian cập nhật (UpdatedAt) mới nhất trong bảng Places
        var maxUpdatedAt = await dbContext.Places
            .AsNoTracking()
            .MaxAsync(i => i.UpdatedAt, stoppingToken);
        
        // Lấy thông tin lần đồng bộ cuối cùng từ ElasticSearch
        var lastSyncTime = await GetLastSyncTimeFromEsAsync(stoppingToken);
        
        // Log thông tin với định dạng chuẩn
        _logger.LogInformation($"DB max UpdatedAt: {maxUpdatedAt:yyyy-MM-dd HH:mm:ss.fff}, ES last sync time: {lastSyncTime:yyyy-MM-dd HH:mm:ss.fff}");
        
        // Kiểm tra nếu có dữ liệu mới hơn lần đồng bộ trước thì tiến hành đồng bộ
        if (maxUpdatedAt > lastSyncTime)
        {
            var count = 0; // Đếm tổng số bản ghi đã xử lý
            var batchSize = BATCH_SIZE; // Kích thước batch
            var lastSyncedUpdatedAt = lastSyncTime; // Thời điểm cập nhật gần nhất đã sync
            
            while (true)
            {
                var itemStopwatch = Stopwatch.StartNew(); // Đo thời gian từng batch
                
                // Lấy batch dữ liệu tiếp theo lớn hơn lastSyncedUpdatedAt
                // Loại trừ những bản ghi đã được xử lý trong quá trình đồng bộ dữ liệu mới
                var query = dbContext.Places
                    .AsNoTracking()
                    .Where(x => x.UpdatedAt > lastSyncedUpdatedAt);
                
                // Nếu có bản ghi cần loại trừ, thêm điều kiện để loại trừ
                if (excludeIds.Count > 0)
                {
                    query = query.Where(x => !excludeIds.Contains(x.Id));
                }
                
                var items = await query
                    .OrderBy(x => x.UpdatedAt) // Phải order theo UpdatedAt tăng dần để phân trang chính xác
                    .Take(batchSize)
                    .Select(PlacesEs.Selector) // Chỉ select những field cần thiết để nhẹ payload
                    .ToListAsync(stoppingToken);
                
                if (items.Count == 0)
                    break; // Nếu hết data thì thoát vòng lặp
                
                // Xử lý các bản ghi trước khi đồng bộ
                var processedItems = ProcessPlaceRecords(items);
                
                // Gửi batch này lên ElasticSearch
                await base.IndexMany(processedItems, stoppingToken, _indexName, BATCH_SIZE);
                
                count += processedItems.Count; // Cập nhật tổng số đã sync
                
                if (items.Count > 0)
                {
                    // Cập nhật thời điểm UpdatedAt của item cuối cùng trong batch này
                    lastSyncedUpdatedAt = items.Max(x => x.UpdatedAt);
                }
                
                itemStopwatch.Stop();
                _logger.LogInformation(
                    $"Synced batch of {processedItems.Count} updated Place records, total: {count}, time: {itemStopwatch.Elapsed.TotalSeconds:F2} seconds");
            }
            
            _logger.LogInformation($"Completed syncing {count} updated Place records to ElasticSearch");
        }
        else
        {
            _logger.LogInformation("No new updated records to sync");
        }
    }
}