using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using System.Diagnostics;
using System.Text.RegularExpressions;

namespace NDS.ETL.BackgroundJobs.Services.ES
{
    /// <summary>
    /// Dịch vụ lấy chi tiết địa điểm từ Elasticsearch
    /// </summary>
    public class ESPlaceDetailsService : IESPlaceDetailsService
    {
        #region Private Fields

        private readonly IESPlacesService _esPlacesService;
        private readonly ILogger<ESPlaceDetailsService> _logger;
        private static readonly Regex _subPlaceRegex = new Regex("_S_\\d{3,10}", RegexOptions.Compiled);

        #endregion

        #region Constructor

        /// <summary>
        /// Khởi tạo dịch vụ lấy chi tiết địa điểm
        /// </summary>
        public ESPlaceDetailsService(
            ESPlacesService esPlacesService,
            ILogger<ESPlaceDetailsService> logger)
        {
            _esPlacesService = esPlacesService;
            _logger = logger;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Lấy thông tin chi tiết của địa điểm dựa trên các ID địa điểm
        /// </summary>
        /// <param name="placeIds">Danh sách ID địa điểm</param>
        /// <param name="isRetryCall">Cờ đánh dấu có phải là lời gọi retry không để tránh đệ quy vô hạn</param>
        /// <returns>Tuple chứa địa điểm đầu tiên và danh sách tất cả các địa điểm theo đúng thứ tự</returns>
        /// <exception cref="Exception">Ném ra nếu không tìm thấy địa điểm nào</exception>
        public async Task<Tuple<PlacesEs, List<PlacesEs>>> GetPlaceDetailsAsync(params string[] placeIds)
        {
            return await GetPlaceDetailsInternalAsync(placeIds, false);
        }

        /// <summary>
        /// Lấy thông tin chi tiết của nhiều địa điểm dựa trên danh sách ID
        /// </summary>
        /// <param name="placeIds">Danh sách ID địa điểm</param>
        /// <returns>Danh sách các địa điểm</returns>
        public async Task<List<PlacesEs>> GetPlacesDetailsByIdsAsync(List<string> placeIds)
        {
            if (placeIds == null || !placeIds.Any())
            {
                return new List<PlacesEs>();
            }

            var stopwatch = Stopwatch.StartNew();
            try
            {
                _logger.LogInformation("Getting details for {Count} places from Elasticsearch", placeIds.Count);

                // Lấy thông tin địa điểm
                var result = await GetPlaceDetailsAsync(placeIds.Distinct().ToArray());

                // Trả về danh sách địa điểm
                stopwatch.Stop();
                _logger.LogInformation("Retrieved {Count}/{TotalRequested} place details in {ElapsedMs}ms",
                    result.Item2.Count, placeIds.Count, stopwatch.ElapsedMilliseconds);
                
                return result.Item2;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Error getting place details: {Message}", ex.Message);
                return new List<PlacesEs>();
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Phương thức nội bộ để lấy thông tin chi tiết địa điểm với khả năng retry
        /// </summary>
        private async Task<Tuple<PlacesEs, List<PlacesEs>>> GetPlaceDetailsInternalAsync(string[] placeIds, bool isRetryCall)
        {
            var stopwatch = Stopwatch.StartNew();
            
            // Phân loại địa điểm thành hai nhóm dựa trên regex pattern
            var mainPlaceIds = new List<string>();
            var subPlaceIds = new List<string>();
            
            // Phân loại nhanh các địa điểm
            foreach (var id in placeIds)
            {
                if (_subPlaceRegex.IsMatch(id))
                    subPlaceIds.Add(id);
                else
                    mainPlaceIds.Add(id);
            }

            // Khởi tạo các biến để lưu kết quả từ các task
            var mainPlacesResult = new List<PlacesEs>();
            var subPlacesResult = new List<SubPlacesEs>();
            var tasks = new List<Task>();

            // Tạo và thực hiện song song các task tìm kiếm
            if (mainPlaceIds.Any())
            {
                var mainTask = _esPlacesService.GetPlacesByPlaceIdsAsync(mainPlaceIds)
                    .ContinueWith(t => {
                        if (t.IsCompletedSuccessfully && t.Result.Any())
                            mainPlacesResult.AddRange(t.Result);
                    }, TaskContinuationOptions.OnlyOnRanToCompletion);
                tasks.Add(mainTask);
            }

            if (subPlaceIds.Any())
            {
                var subTask = _esPlacesService.GetPlacesBySubPlaceIdsAsync(subPlaceIds)
                    .ContinueWith(t => {
                        if (t.IsCompletedSuccessfully && t.Result.Any())
                            subPlacesResult.AddRange(t.Result);
                    }, TaskContinuationOptions.OnlyOnRanToCompletion);
                tasks.Add(subTask);
            }

            // Chờ tất cả task hoàn thành
            if (tasks.Any())
                await Task.WhenAll(tasks);

            // Tạo dictionary để map kết quả theo PlaceId
            var placesDict = new Dictionary<string, PlacesEs>();
            
            // Thêm main places vào dictionary
            foreach (var item in mainPlacesResult)
            {
                item.AddressPermuted?.Clear();
                placesDict[item.PlaceId] = item;
            }
            
            // Thêm sub places vào dictionary
            foreach (var item in subPlacesResult)
            {
                item.AddressPermuted?.Clear();
                var placesEs = new PlacesEs(item);
                placesDict[placesEs.PlaceId] = placesEs;
            }

            // Sắp xếp kết quả theo thứ tự của placeIds đầu vào
            var orderedPlacesList = new List<PlacesEs>();
            var missingPlaceIds = new List<string>(); // Danh sách các placeId không tìm thấy
            
            foreach (var placeId in placeIds)
            {
                if (placesDict.TryGetValue(placeId, out var place))
                {
                    orderedPlacesList.Add(place);
                }
                else
                {
                    // Thêm vào danh sách missing để xử lý sau
                    missingPlaceIds.Add(placeId);
                }
            }

            // Xử lý các placeId không tìm thấy bằng cách tách phần trước _S_ (chỉ khi không phải retry call)
            if (missingPlaceIds.Any() && !isRetryCall)
            {
                _logger.LogWarning("Could not find {Count} place(s): {PlaceIds}", 
                    missingPlaceIds.Count, string.Join(", ", missingPlaceIds));

                var retryPlaceIds = new List<string>();
                var retryPlaceIdMapping = new Dictionary<string, string>(); // Key: original placeId, Value: prefix placeId

                foreach (var missingPlaceId in missingPlaceIds)
                {
                    // Tách phần trước _S_ nếu placeId chứa pattern này
                    var indexOfS = missingPlaceId.IndexOf("_S_");
                    if (indexOfS > 0)
                    {
                        var prefixPlaceId = missingPlaceId.Substring(0, indexOfS);
                        retryPlaceIds.Add(prefixPlaceId);
                        retryPlaceIdMapping[missingPlaceId] = prefixPlaceId;
                        
                        _logger.LogInformation("Retrying with prefix placeId: {OriginalPlaceId} -> {PrefixPlaceId}", 
                            missingPlaceId, prefixPlaceId);
                    }
                }

                // Thực hiện retry với các prefix placeId
                if (retryPlaceIds.Any())
                {
                    try
                    {
                        var retryResult = await GetPlaceDetailsInternalAsync(retryPlaceIds.Distinct().ToArray(), true);
                        if (retryResult.Item2.Any())
                        {
                            var retryPlacesDict = retryResult.Item2.ToDictionary(p => p.PlaceId);
                            
                            // Tạo lại ordered list với các địa điểm tìm được từ retry
                            var finalOrderedList = new List<PlacesEs>();
                            
                            foreach (var placeId in placeIds)
                            {
                                // Thử tìm place từ kết quả ban đầu
                                if (placesDict.TryGetValue(placeId, out var originalPlace))
                                {
                                    finalOrderedList.Add(originalPlace);
                                }
                                // Nếu không tìm thấy, thử tìm từ retry result bằng prefix
                                else if (retryPlaceIdMapping.TryGetValue(placeId, out var prefixPlaceId) &&
                                        retryPlacesDict.TryGetValue(prefixPlaceId, out var retryPlace))
                                {
                                    finalOrderedList.Add(retryPlace);
                                    
                                    _logger.LogInformation("Found place with prefix placeId: {OriginalPlaceId} -> {PrefixPlaceId}", 
                                        placeId, prefixPlaceId);
                                }
                            }
                            
                            orderedPlacesList = finalOrderedList;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error during retry with prefix placeIds: {RetryPlaceIds}", 
                            string.Join(", ", retryPlaceIds));
                    }
                }
            }

            stopwatch.Stop();
            _logger.LogInformation("Retrieved {Count} place details in {ElapsedMs}ms", 
                orderedPlacesList.Count, stopwatch.ElapsedMilliseconds);

            // Kiểm tra và trả về kết quả
            if (!orderedPlacesList.Any())
                throw new Exception($"Place not found in ES: {string.Join(',', placeIds)}");

            // Trả về địa điểm đầu tiên và danh sách đã sắp xếp theo thứ tự
            var firstPlace = orderedPlacesList.First();
            return new Tuple<PlacesEs, List<PlacesEs>>(firstPlace, orderedPlacesList);
        }

        #endregion
    }
}