using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Table("favorite_places")]
[Index("PhoneNumber", Name = "index_phonenumber")]
[Index("Type", Name = "index_type")]
[Index("UserId", Name = "index_userid")]
public partial class FavoritePlace
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("active")]
    public bool Active { get; set; }

    [Column("address")]
    [StringLength(1000)]
    public string? Address { get; set; }

    [Column("bookingCount")]
    public long BookingCount { get; set; }

    [Column("city")]
    [StringLength(255)]
    public string? City { get; set; }

    [Column("dataFrom")]
    [StringLength(255)]
    public string? DataFrom { get; set; }

    [Column("district")]
    [StringLength(255)]
    public string? District { get; set; }

    [Column("fullAddress")]
    [StringLength(1000)]
    public string? FullAddress { get; set; }

    [Column("keywords")]
    [StringLength(255)]
    public string? Keywords { get; set; }

    [Column("location", TypeName = "geometry(Geometry,4326)")]
    public Point? Location { get; set; }

    [Column("name")]
    [StringLength(255)]
    public string? Name { get; set; }

    [Column("parentAddress")]
    [StringLength(1000)]
    public string? ParentAddress { get; set; }

    [Column("parentId")]
    public string? ParentId { get; set; }

    [Column("phoneNumber")]
    [StringLength(255)]
    public string? PhoneNumber { get; set; }

    [Column("placeDetailId")]
    public string? PlaceDetailId { get; set; }

    [Column("placeId")]
    [StringLength(255)]
    public string? PlaceId { get; set; }

    [Column("removed")]
    public bool Removed { get; set; }

    [Column("route")]
    [StringLength(255)]
    public string? Route { get; set; }

    [Column("streetNumber")]
    [StringLength(255)]
    public string? StreetNumber { get; set; }

    [Column("tagInput")]
    [StringLength(255)]
    public string? TagInput { get; set; }

    [Column("type")]
    public int Type { get; set; }

    [Column("ward")]
    [StringLength(255)]
    public string? Ward { get; set; }

    [Column("userId")]
    [StringLength(255)]
    public string? UserId { get; set; }

    [Column("createdAt", TypeName = "timestamp(6) without time zone")]
    public DateTime CreatedAt { get; set; }

    [Column("updatedAt", TypeName = "timestamp(6) without time zone")]
    public DateTime UpdatedAt { get; set; }
}
