using System.Diagnostics;
using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Core.Bulk;
using KnowledgeBase.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Entities.Context;
using NDS.ETL.Entities.PostgresPlace;
using NDS.ETL.Infrastructure.Common;
using NDS.ETL.Infrastructure.Utils;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.ESTools;

public class SyncPlaceScanResultJobs : RecurringJobBaseElastic
{
    private readonly ElasticsearchClient _elasticClient;
    private readonly IESPlacesService _iesPlacesService;
    private readonly IScopedResolver<NpgPlaceContext> _dbContextResolver;
    private readonly ElasticSearchConfig _indexName;
    private readonly SemaphoreSlim _semaphore;
    private readonly ILogger<SyncPlaceScanResultJobs> _logger;

    private const int MaxConcurrentBatches = 1;
    private const int BatchSize = 5_000;
    private const int CommandTimeout = 300;


    public SyncPlaceScanResultJobs(IConfiguration configuration, ILogger<SyncPlaceScanResultJobs> logger,
        ElasticsearchClient elasticClient, IESPlacesService iesPlacesService, IScopedResolver<NpgPlaceContext> dbContextResolver)
        : base(configuration, logger, elasticClient, null)
    {
        _indexName = configuration.Bind<ElasticSearchConfig>(nameof(ElasticSearchConfig));
        _elasticClient = elasticClient;
        _iesPlacesService = iesPlacesService;
        _dbContextResolver = dbContextResolver;
        _semaphore = new SemaphoreSlim(MaxConcurrentBatches);
        _logger = logger;
    }

    public override string JobId => "SyncPlaceScanResult";
    public override string ConfigSection => "JobPgSyncPlace";

    public override async Task DoWorkAsync(CancellationToken stoppingToken)
    {
        var begin = Stopwatch.GetTimestamp();
        _logger.LogInformation($"{JobId} DoWorkAsync started");

        try
        {
            _lastId = 0;
            int totalCount = 0;
            var tasks = new List<Task>();
            bool hasMoreData = true;

            while (hasMoreData && !stoppingToken.IsCancellationRequested)
            {
                var batch = await GetNextBatchAsync(stoppingToken);
                if (batch.Count == 0)
                {
                    hasMoreData = false;
                    _logger.LogInformation("No more data to process. Exiting batch retrieval.");
                    break;
                }


                await _semaphore.WaitAsync(stoppingToken);
                tasks.Add(ProcessBatchAsync(batch, stoppingToken)
                    .ContinueWith(_ => _semaphore.Release(), stoppingToken));

                totalCount += batch.Count;
            }

            await Task.WhenAll(tasks);

            var end = Stopwatch.GetTimestamp();
            _logger.LogInformation($"Time to index total: {totalCount} Collection PlaceResult to es: {TimeSpan.FromTicks(end - begin).TotalSeconds} seconds");
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error occurred while processing SyncPlaceScanResultJobs");
            throw;
        }
    }

    private long _lastId = 0;

    private async Task<List<PlacesScanResult>> GetNextBatchAsync(CancellationToken stoppingToken)
    {
        return await _dbContextResolver.ResolveAsync(async dbContext =>
        {
            dbContext.Database.SetCommandTimeout(CommandTimeout);

            var query = dbContext.PlacesScanResults.AsQueryable();
            query = query.Where(x => x.SyncStatus == 0); //chỉ lấy các bản ghi chưa dc đồng bộ

            if (_lastId == 0)
            {
                var total = await query.CountAsync(stoppingToken);
                _logger.LogInformation("Starting sync: {Count} unsynced records found.", total);
            }
            else
            {
                // Thêm log thông báo số lượng bản ghi còn lại
                var remainingCount = await query.Where(x => x.Id > _lastId).CountAsync(stoppingToken);
                _logger.LogInformation("Resuming sync. {RemainingCount} unsynced records remain.", remainingCount);
            }

            var batch = await query
                .Where(x => x.Id > _lastId)
                .OrderBy(x => x.Id)
                .Take(BatchSize)
                .ToListAsync(stoppingToken);

            if (batch.Count > 0)
            {
                _lastId = batch.Max(x => x.Id);
            }
            else
            {
                _logger.LogInformation("No more data to process. Exiting batch retrieval.");
            }

            return batch;
        });
    }


    private async Task ProcessBatchAsync(List<PlacesScanResult> batch, CancellationToken stoppingToken)
    {
        var t1 = Stopwatch.GetTimestamp();

        try
        {
            var placeIds = batch.Select(x => x.Placeid).Distinct().ToList();
            _logger.LogInformation($"Processing batch with {placeIds.Count} unique placeIds");

            var placesResp = await _iesPlacesService.GetPlacesByPlaceIdsAsync(placeIds, stoppingToken, BatchSize);
            if (placesResp.Count == 0 || placesResp.Count != placeIds.Count)
            {
                var missingIds = placeIds.Except(placesResp.Select(p => p.PlaceId)).ToList();
                _logger.LogWarning("Mismatch between requested placeIds and retrieved places from ES.");
                _logger.LogWarning("Requested: {RequestedCount}, Retrieved: {RetrievedCount}, Missing: {MissingCount}", placeIds.Count, placesResp.Count, missingIds.Count);
                if (missingIds.Count <= 20)
                {
                    _logger.LogWarning("Missing PlaceIds: {MissingIds}", string.Join(", ", missingIds));
                }
                if (missingIds.Count == 0) return;
            }

            _logger.LogInformation($"Retrieved {placesResp.Count} places from ES");

            var placeDict = placesResp.ToDictionary(x => x.PlaceId);
            var updateRequests = new List<IBulkOperation>();

            await Parallel.ForEachAsync(batch, new ParallelOptions
            {
                MaxDegreeOfParallelism = Environment.ProcessorCount,
                CancellationToken = stoppingToken
            }, async (item, ct) =>
            {
                try
                {
                    if (placeDict.TryGetValue(item.Placeid, out var placeResp))
                    {
                        var updatedDoc = UpdatePlaceData(placeResp, item);
                        updateRequests.Add(new BulkUpdateOperation<PlacesEs, PlacesEs>(updatedDoc.Id)
                        {
                            Doc = updatedDoc,
                            DocAsUpsert = true
                        });
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error preparing update for place {item.Placeid}");
                }
            });

            _logger.LogInformation($"Prepared {updateRequests.Count} update requests");

            if (updateRequests.Any())
            {
                var retryCount = 0;
                var maxRetries = 3;
                var success = false;

                while (!success && retryCount < maxRetries)
                {
                    try
                    {
                        var response = await _elasticClient.BulkAsync(b => b
                            .Index(_indexName.Index[0])
                            .IndexMany(updateRequests)
                            .Pipeline("location-to-h3"), stoppingToken);

                        if (response.IsValidResponse)
                        {
                            success = true;
                            _logger.LogInformation($"Successfully updated {response.Items.Count} documents");

                            await _dbContextResolver.ResolveAsync(async dbContext =>
                            {
                                dbContext.Database.SetCommandTimeout(CommandTimeout);

                                var ids = batch.Select(x => x.Id).ToList();
                                var entities = await dbContext.PlacesScanResults
                                    .Where(x => x.SyncStatus != 1 && ids.Contains(x.Id))
                                    .ToListAsync(stoppingToken);
                                if (entities.Count > 0)
                                {
                                    entities.ForEach(x => x.SyncStatus = 1);

                                    var affectedRows = await dbContext.SaveChangesAsync(stoppingToken);
                                    _logger.LogInformation("Updated SyncStatus=1 for {Count} records in PlacesScanResults", affectedRows);

                                }
                                return true;
                            });

                        }
                        else
                        {
                            _logger.LogError($"Bulk update failed: {response.DebugInformation}");
                            retryCount++;
                            if (retryCount < maxRetries)
                                await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, retryCount)), stoppingToken);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Error during bulk update attempt {retryCount + 1}");
                        retryCount++;
                        if (retryCount < maxRetries)
                            await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, retryCount)), stoppingToken);
                    }
                }

                if (!success)
                {
                    _logger.LogError($"Failed to update documents after {maxRetries} attempts");
                }
            }
            else
            {
                _logger.LogWarning("No update requests to send");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ProcessBatchAsync");
            throw;
        }

        var t2 = Stopwatch.GetTimestamp();
        _logger.LogInformation($"Processed batch of {batch.Count} items in {TimeSpan.FromTicks(t2 - t1).TotalSeconds} seconds");
    }

    private PlacesEs UpdatePlaceData(PlacesEs place, PlacesScanResult item)
    {
        place.CompareScoreMax = item.CompareScoreMax;
        place.TotalScans = item.TotalScans;
        place.SourcePlaceId = item.SourcePlaceid;
        place.ScanType = 1;
        place.JobScanId = item.HangfireJobId;
        place.ScanProcessed = item.ScanProcessed == null ? (int)PlacesScanResultProcessed.NotYet : (int)item.ScanProcessed;
        place.ReasonOperations = item.ReasonOperations?.Select(e => (int)e).ToList() ?? new List<int>();
        place.Reason = item.Reason;
        place.Note = item.Note;
        place.UpdatedAt = item.ScanProcessed == null ? DateTime.Now : item.UpdateAt;
        place.ScanStatus = 1;
        return place;
    }
}