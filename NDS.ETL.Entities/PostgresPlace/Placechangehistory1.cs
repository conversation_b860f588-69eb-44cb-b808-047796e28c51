using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Keyless]
[Table("placechangehistory")]
public partial class Placechangehistory1
{
    [Column("id")]
    public int? Id { get; set; }

    [Column("placeId")]
    public int? PlaceId { get; set; }

    [StringLength(2048)]
    public string? DataJson { get; set; }

    [Column("createdAt")]
    [StringLength(50)]
    public string? CreatedAt { get; set; }

    [Column("creator")]
    [StringLength(50)]
    public string? Creator { get; set; }

    [Column("updatedAt")]
    [StringLength(50)]
    public string? UpdatedAt { get; set; }

    [Column("lastUpdatedUser")]
    [StringLength(50)]
    public string? LastUpdatedUser { get; set; }
}
