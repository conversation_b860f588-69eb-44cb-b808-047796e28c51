using System.ComponentModel.DataAnnotations.Schema;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NDS.ETL.Entities.TaxiMongoDB;

[BsonIgnoreExtraElements]
[Table("user_search_histories")]
public class UserSearchHistory
{
    [BsonId]
    public ObjectId Id { get; set; }

    [BsonElement("customerMobileNo")]
    public string CustomerMobileNo { get; set; }

    [BsonElement("keyword")]
    public string Keyword { get; set; }

    [BsonElement("placeId")]
    public string PlaceId { get; set; }

    [BsonElement("name")]
    public string Name { get; set; }

    [BsonElement("address")]
    public string Address { get; set; }

    [BsonElement("fullAddress")]
    public string FullAddress { get; set; }

    [BsonElement("placeLocation")]
    public PlaceLocation PlaceLocation { get; set; }

    [BsonElement("createdAt")]
    [BsonDateTimeOptions(Kind = DateTimeKind.Local)] // Ensure MongoDB driver treats this as local time
    public DateTime CreatedAt { get; set; }
}

public class PlaceLocation
{
    public PlaceLocation()
    {
        Lat = 0;
        Lng = 0;
    }
    [BsonElement("lat")]
    public double Lat { get; set; }

    [BsonElement("lng")]
    public double Lng { get; set; }
}

