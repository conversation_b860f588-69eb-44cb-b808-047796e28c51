using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;
using NetTopologySuite.Geometries;
using NodaTime;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using NpgsqlTypes;

#nullable disable

namespace NDS.ETL.Entities.Migrations.NpgPlace
{
    /// <inheritdoc />
    public partial class AddFullAddressNewColumn : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterDatabase()
                .Annotation("Npgsql:Enum:sys.dss_freq_enum", "YEARLY,MONTHLY,WEEKLY,DAILY,HOURLY,MINUTELY,SECONDLY")
                .Annotation("Npgsql:Enum:sys.dss_program_type_enum", "PLSQL_BLOCK,STORED_PROCEDURE")
                .Annotation("Npgsql:PostgresExtension:btree_gist", ",,")
                .Annotation("Npgsql:PostgresExtension:pg_catalog.edb_dblink_libpq", ",,")
                .Annotation("Npgsql:PostgresExtension:pg_catalog.edb_dblink_oci", ",,")
                .Annotation("Npgsql:PostgresExtension:pg_catalog.edbspl", ",,")
                .Annotation("Npgsql:PostgresExtension:pg_stat_statements", ",,")
                .Annotation("Npgsql:PostgresExtension:pgstattuple", ",,")
                .Annotation("Npgsql:PostgresExtension:postgis", ",,")
                .Annotation("Npgsql:PostgresExtension:unaccent", ",,");

            migrationBuilder.CreateSequence(
                name: "countries_id_seq");

            migrationBuilder.CreateSequence(
                name: "hibernate_sequence");

            migrationBuilder.CreateSequence(
                name: "public.user_booking_histories_SEQ");

            migrationBuilder.CreateSequence(
                name: "user_booking_histories_seq");

            migrationBuilder.CreateSequence(
                name: "user_booking_histories_SEQ");

            migrationBuilder.CreateTable(
                name: "__EFMigrationsHistory_ESDbContext",
                columns: table => new
                {
                    MigrationId = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    ProductVersion = table.Column<string>(type: "character varying(32)", maxLength: 32, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK___EFMigrationsHistory_ESDbContext", x => x.MigrationId);
                });

            migrationBuilder.CreateTable(
                name: "ApiKeys",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Key = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Description = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    CreatedAt = table.Column<Instant>(type: "timestamp without time zone", nullable: false),
                    ExpirationDate = table.Column<Instant>(type: "timestamp without time zone", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ApiKeys", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "blocked_places",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    placeId = table.Column<string>(type: "text", nullable: true),
                    createdAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "booking_histories",
                columns: table => new
                {
                    id = table.Column<string>(type: "character varying", nullable: false),
                    customer_id = table.Column<string>(type: "character varying", nullable: true),
                    customer_phone = table.Column<string>(type: "character varying", nullable: true),
                    origin_place_id = table.Column<string>(type: "character varying", nullable: true),
                    origin_full_address = table.Column<string>(type: "character varying", nullable: true),
                    des_place_id = table.Column<string>(type: "character varying", nullable: true),
                    des_address = table.Column<string>(type: "character varying", nullable: true),
                    des_full_address = table.Column<string>(type: "character varying", nullable: true),
                    last_booking_at = table.Column<DateTime>(type: "timestamp(0) without time zone", nullable: true),
                    booking_count = table.Column<int>(type: "integer", nullable: true),
                    last_search_at = table.Column<DateTime>(type: "timestamp(0) without time zone", nullable: true),
                    search_count = table.Column<int>(type: "integer", nullable: true),
                    last_paid_at = table.Column<DateTime>(type: "timestamp(0) without time zone", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp(0) without time zone", nullable: true),
                    origin_address = table.Column<string>(type: "character varying", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("booking_histories_pkey", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "booking_histories_item",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    customer_phone = table.Column<string>(type: "character varying", nullable: true),
                    booking_history_id = table.Column<string>(type: "character varying", nullable: true),
                    booking_time = table.Column<DateTime>(type: "timestamp(0) without time zone", nullable: true),
                    search_time = table.Column<DateTime>(type: "timestamp(0) without time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("booking_histories_item_pkey", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "booking_place_pairs",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    arrivalPlaceId = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    bookingCount = table.Column<int>(type: "integer", nullable: false),
                    customerId = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    customerPhone = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    distance = table.Column<double>(type: "double precision", nullable: false),
                    fromAddress = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    fromCity = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    fromFullAddress = table.Column<string>(type: "character varying(10000)", maxLength: 10000, nullable: true),
                    hash1 = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    hash2 = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    location1 = table.Column<Point>(type: "geometry", nullable: true),
                    location2 = table.Column<Point>(type: "geometry", nullable: true),
                    paidCount = table.Column<int>(type: "integer", nullable: false),
                    pickupPlaceId = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    searchCount = table.Column<int>(type: "integer", nullable: false),
                    toAddress = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    toCity = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    toFullAddress = table.Column<string>(type: "character varying(10000)", maxLength: 10000, nullable: true),
                    createdAt = table.Column<DateTime>(type: "timestamp(6) without time zone", nullable: false),
                    updateAt = table.Column<DateTime>(type: "timestamp(6) without time zone", nullable: false),
                    ts = table.Column<NpgsqlTsVector>(type: "tsvector", nullable: true, computedColumnSql: "to_tsvector('english'::regconfig, (((((((COALESCE(\"fromAddress\", ''::character varying))::text || ' '::text) || (COALESCE(\"fromFullAddress\", ''::character varying))::text) || ' '::text) || (COALESCE(\"toAddress\", ''::character varying))::text) || ' '::text) || (COALESCE(\"toFullAddress\", ''::character varying))::text))", stored: true),
                    ts2 = table.Column<NpgsqlTsVector>(type: "tsvector", nullable: true, computedColumnSql: "to_tsvector('english'::regconfig, regexp_replace(((COALESCE(vn_unaccent((\"fromFullAddress\")::text), ''::text) || ' '::text) || COALESCE(vn_unaccent((\"toFullAddress\")::text), ''::text)), '[^\\w]+'::text, ' '::text, 'g'::text))", stored: true),
                    lastBookingAt = table.Column<DateTime>(type: "timestamp(6) without time zone", nullable: true),
                    lastSearchAt = table.Column<DateTime>(type: "timestamp(6) without time zone", nullable: true),
                    estimateAmount = table.Column<long>(type: "bigint", nullable: true),
                    lastEstimateAmount = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    lastNote = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_booking_place_pairs", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "building_boundary",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    latitude = table.Column<double>(type: "double precision", nullable: false),
                    longitude = table.Column<double>(type: "double precision", nullable: false),
                    processStatus = table.Column<int>(type: "integer", nullable: false),
                    boundary = table.Column<string>(type: "jsonb", nullable: true),
                    name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    origin = table.Column<int>(type: "integer", nullable: true),
                    geom = table.Column<Polygon>(type: "geometry(Polygon,4326)", nullable: true),
                    code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    note = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    status = table.Column<int>(type: "integer", nullable: true),
                    placeFkId = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                    creatorId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    lastUpdateUser = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    lastUpdateUserId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    placeMasterAddress = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_building_boundary", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "countries",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    boundary_geom = table.Column<string>(type: "text", nullable: true),
                    boundary = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("countries_pkey", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "customers",
                columns: table => new
                {
                    userId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    phoneNumber = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("customers_pkey", x => x.userId);
                });

            migrationBuilder.CreateTable(
                name: "danhmuc",
                columns: table => new
                {
                    Code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Name = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    ParentCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    ClosedCodeAt = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    OldCode = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    DistrictCodeDisable = table.Column<string>(name: "DistrictCode(Disable)", type: "character varying(1000)", maxLength: 1000, nullable: true),
                    ProvinceCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    AreaCode = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    WardCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ActiveAt = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CancelAt = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ModifiedAt = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CreatedAt = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    InssuanceAt = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Creator = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "default_columns",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    key = table.Column<string>(type: "text", nullable: true),
                    value = table.Column<string>(type: "text", nullable: true),
                    description = table.Column<string>(type: "text", nullable: true),
                    create_date = table.Column<LocalDateTime>(type: "timestamp without time zone", nullable: false),
                    update_date = table.Column<LocalDateTime>(type: "timestamp without time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_default_columns", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "district_boundaries",
                columns: table => new
                {
                    _id = table.Column<int>(type: "integer", nullable: false),
                    _class = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    created_at = table.Column<LocalDateTime>(type: "timestamp without time zone", nullable: true),
                    location = table.Column<string>(type: "character varying(10485760)", maxLength: 10485760, nullable: true),
                    modified_at = table.Column<LocalDateTime>(type: "timestamp without time zone", nullable: true),
                    modified_by = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    data = table.Column<string>(type: "character varying(10485760)", maxLength: 10485760, nullable: true),
                    provinces = table.Column<string>(type: "character varying(25)", maxLength: 25, nullable: true),
                    status = table.Column<int>(type: "integer", nullable: true),
                    postcode = table.Column<int>(type: "integer", nullable: true),
                    province = table.Column<int>(type: "integer", nullable: true),
                    iddistrict = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("district_boundaries_pk", x => x._id);
                });

            migrationBuilder.CreateTable(
                name: "districts",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    code = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: true),
                    name = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: true),
                    provinceCode = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: true),
                    type = table.Column<int>(type: "integer", nullable: true),
                    boundary = table.Column<string>(type: "text", nullable: true),
                    no_space_name = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: true),
                    center_location = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: true),
                    processed = table.Column<bool>(type: "boolean", nullable: true),
                    boundary_geom = table.Column<string>(type: "text", nullable: true),
                    province_code = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: true),
                    provinceId = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("districts_pkey", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "els_query_configs",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    name_index = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    query_vi = table.Column<string>(type: "character varying(10000)", maxLength: 10000, nullable: false),
                    query_en = table.Column<string>(type: "character varying(10000)", maxLength: 10000, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("els_query_config_copy1_pkey", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "es_queue",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    es_jobid = table.Column<string>(type: "character varying", nullable: true),
                    create_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: false, defaultValueSql: "current_timestamp"),
                    update_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: false, defaultValueSql: "current_timestamp"),
                    status = table.Column<int>(type: "integer", nullable: false),
                    description = table.Column<string>(type: "character varying", nullable: true),
                    hangfire_job_id = table.Column<string>(type: "character varying", nullable: true),
                    places_queue_id = table.Column<long>(type: "bigint", nullable: false),
                    scan_config_range = table.Column<int>(type: "integer", nullable: false),
                    scan_config_distance = table.Column<int>(type: "integer", nullable: true),
                    order_num = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_es_queue", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "favorite_places",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    active = table.Column<bool>(type: "boolean", nullable: false),
                    address = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    bookingCount = table.Column<long>(type: "bigint", nullable: false),
                    city = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    dataFrom = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    district = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    fullAddress = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    keywords = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    location = table.Column<Point>(type: "geometry(Geometry,4326)", nullable: true),
                    name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    parentAddress = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    parentId = table.Column<string>(type: "text", nullable: true),
                    phoneNumber = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    placeDetailId = table.Column<string>(type: "text", nullable: true),
                    placeId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    removed = table.Column<bool>(type: "boolean", nullable: false),
                    route = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    streetNumber = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    tagInput = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    type = table.Column<int>(type: "integer", nullable: false),
                    ward = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    userId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    createdAt = table.Column<DateTime>(type: "timestamp(6) without time zone", nullable: false),
                    updatedAt = table.Column<DateTime>(type: "timestamp(6) without time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_favorite_places", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "gis_province",
                columns: table => new
                {
                    _id = table.Column<int>(type: "integer", nullable: true),
                    _class = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    created_at = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    location = table.Column<string>(type: "character varying(32767)", maxLength: 32767, nullable: true),
                    modified_at = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    modified_by = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    postCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    status = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "hanoi_boundary",
                columns: table => new
                {
                    osm_id = table.Column<int>(type: "integer", nullable: true),
                    name = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    addr = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    amenity = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    boundary = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    building = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    @operator = table.Column<string>(name: "operator", type: "character varying(500)", maxLength: 500, nullable: true),
                    way_area = table.Column<float>(type: "real", nullable: true),
                    st_astext = table.Column<string>(type: "character varying", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "hidden_places",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    hasChild = table.Column<bool>(type: "boolean", nullable: true),
                    placeDetailId = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: true),
                    phoneNumber = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: true),
                    placeId = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    location = table.Column<Point>(type: "geometry(Point,4326)", nullable: true),
                    locationHash = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    locationHash2 = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    locationHash3 = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    name = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    address = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    keywords = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    fullAddress = table.Column<string>(type: "character varying(10000)", maxLength: 10000, nullable: false),
                    fullAddressHash = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    streetNumber = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    route = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    ward = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    district = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    city = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    type = table.Column<int>(type: "integer", nullable: false),
                    active = table.Column<bool>(type: "boolean", nullable: false),
                    tags = table.Column<List<string>>(type: "text[]", nullable: true),
                    tagInput = table.Column<string>(type: "text", nullable: true),
                    popular = table.Column<bool>(type: "boolean", nullable: false),
                    processed = table.Column<bool>(type: "boolean", nullable: false),
                    bookingCount = table.Column<int>(type: "integer", nullable: false),
                    removed = table.Column<bool>(type: "boolean", nullable: false),
                    hidden = table.Column<bool>(type: "boolean", nullable: false),
                    createdAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    updatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    creatorId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    lastUpdateUser = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    dataFrom = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ts = table.Column<NpgsqlTsVector>(type: "tsvector", nullable: true, computedColumnSql: "to_tsvector('english'::regconfig, (((((COALESCE(name, ''::character varying))::text || ' '::text) || (\"fullAddress\")::text) || ' '::text) || (COALESCE(keywords, ''::character varying))::text))", stored: true),
                    ts2 = table.Column<NpgsqlTsVector>(type: "tsvector", nullable: true, computedColumnSql: "to_tsvector('english'::regconfig, regexp_replace(((((COALESCE(vn_unaccent((name)::text), ''::text) || ' '::text) || COALESCE(vn_unaccent((\"fullAddress\")::text), ''::text)) || ' '::text) || COALESCE(vn_unaccent((keywords)::text), ''::text)), '[^\\w]+'::text, ' '::text, 'g'::text))", stored: true),
                    provinceId = table.Column<int>(type: "integer", nullable: true),
                    districtId = table.Column<int>(type: "integer", nullable: true),
                    wardId = table.Column<int>(type: "integer", nullable: true),
                    pending = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    quoteCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_hidden_places", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "job_config",
                columns: table => new
                {
                    id = table.Column<string>(type: "character varying(254)", maxLength: 254, nullable: true),
                    lastTime = table.Column<long>(type: "bigint", nullable: true),
                    interval = table.Column<long>(type: "bigint", nullable: true),
                    threadPoolSize = table.Column<int>(type: "integer", nullable: true),
                    enable = table.Column<int>(type: "integer", nullable: true),
                    batchSize = table.Column<double>(type: "double precision", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "job_config_place",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    jobId = table.Column<string>(type: "text", nullable: true),
                    keyConfig = table.Column<string>(type: "text", nullable: true),
                    valueConfig = table.Column<string>(type: "text", nullable: true),
                    description = table.Column<string>(type: "text", nullable: true),
                    createdAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    lastUpdateUser = table.Column<string>(type: "text", nullable: true),
                    creatorUser = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_job_config_place", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "job_last_update",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    lastUpdate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    origin = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_job_last_update", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "jwt_clients",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    clientId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    clientSecretKey = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("jwt_clients_pkey", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "migration_checkpoints",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    checkpoint_time = table.Column<LocalDateTime>(type: "timestamp without time zone", nullable: false),
                    error_message = table.Column<string>(type: "character varying(4000)", maxLength: 4000, nullable: true),
                    last_processed_id = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    migration_id = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    processed_count = table.Column<long>(type: "bigint", nullable: false),
                    status = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    thread_id = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    total_count = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("migration_checkpoints_pkey", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "osm2pgsql_properties",
                columns: table => new
                {
                    property = table.Column<string>(type: "text", nullable: false),
                    value = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("osm2pgsql_properties_pkey", x => x.property);
                });

            migrationBuilder.CreateTable(
                name: "place_approve",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    name = table.Column<string>(type: "text", nullable: true),
                    location = table.Column<Point>(type: "geometry(Point,4326)", nullable: true),
                    address = table.Column<string>(type: "text", nullable: true),
                    placeId = table.Column<string>(type: "text", nullable: true),
                    provinceId = table.Column<int>(type: "integer", nullable: true, defaultValue: 0),
                    districtId = table.Column<int>(type: "integer", nullable: true, defaultValue: 0),
                    wardId = table.Column<int>(type: "integer", nullable: true, defaultValue: 0),
                    scanProcessed = table.Column<int>(type: "integer", nullable: false),
                    reasonOperations = table.Column<List<int>>(type: "integer[]", nullable: true),
                    reason = table.Column<string>(type: "text", nullable: true),
                    note = table.Column<string>(type: "text", nullable: true),
                    statusApprove = table.Column<int>(type: "integer", nullable: false),
                    descriptionReasonApprove = table.Column<string>(type: "text", nullable: true),
                    reasonApprove = table.Column<List<int>>(type: "integer[]", nullable: true),
                    lastUpdateUser = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: true, defaultValueSql: "'-infinity'::timestamp without time zone"),
                    CreatorUserId = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    statusCalculate = table.Column<int>(type: "integer", nullable: false),
                    locationAddressDiffScore = table.Column<string>(type: "text", nullable: true),
                    determineAddressLevel = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_place_approve", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "place_change_history",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    placeId = table.Column<long>(type: "bigint", nullable: false),
                    lastUpdatedUser = table.Column<string>(type: "text", nullable: true),
                    createdAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    updatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    DataJson = table.Column<string>(type: "text", nullable: true),
                    creator = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_place_change_history", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "place_tags",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    code = table.Column<string>(type: "text", nullable: true),
                    vi = table.Column<string>(type: "text", nullable: true),
                    en = table.Column<string>(type: "text", nullable: true),
                    description = table.Column<string>(type: "text", nullable: true),
                    createdAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    creator = table.Column<string>(type: "text", nullable: true),
                    updatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    updateUser = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_place_tags", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "place_uat_up_live",
                columns: table => new
                {
                    placeid = table.Column<string>(type: "character varying", nullable: false),
                    type = table.Column<int>(type: "integer", nullable: false, defaultValue: 0)
                },
                constraints: table =>
                {
                    table.PrimaryKey("place_up_live_pk", x => x.placeid);
                });

            migrationBuilder.CreateTable(
                name: "placechangehistory",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: true),
                    placeId = table.Column<int>(type: "integer", nullable: true),
                    DataJson = table.Column<string>(type: "character varying(2048)", maxLength: 2048, nullable: true),
                    createdAt = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    creator = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    updatedAt = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    lastUpdatedUser = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "places",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    placeDetailId = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: true),
                    phoneNumber = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: true),
                    placeId = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    location = table.Column<Point>(type: "geometry(Point,4326)", nullable: true),
                    name = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    address = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    keywords = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    fullAddress = table.Column<string>(type: "character varying(10000)", maxLength: 10000, nullable: false),
                    fullAddressNew = table.Column<string>(type: "character varying(10000)", maxLength: 10000, nullable: true),
                    streetNumber = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    route = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    ward = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    district = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    city = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    type = table.Column<int>(type: "integer", nullable: false),
                    active = table.Column<bool>(type: "boolean", nullable: false),
                    tags = table.Column<List<string>>(type: "text[]", nullable: true),
                    tagInput = table.Column<string>(type: "text", nullable: true),
                    popular = table.Column<bool>(type: "boolean", nullable: false),
                    processed = table.Column<bool>(type: "boolean", nullable: false),
                    bookingCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    removed = table.Column<bool>(type: "boolean", nullable: false),
                    hidden = table.Column<bool>(type: "boolean", nullable: false),
                    createdAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false, defaultValueSql: "'01-JAN-01 00:00:00'::timestamp without time zone"),
                    updatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false, defaultValueSql: "'01-JAN-01 00:00:00'::timestamp without time zone"),
                    creatorId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    lastUpdateUser = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    dataFrom = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ts = table.Column<NpgsqlTsVector>(type: "tsvector", nullable: true, computedColumnSql: "to_tsvector('english'::regconfig, (((((COALESCE(name, ''::character varying))::text || ' '::text) || (\"fullAddress\")::text) || ' '::text) || (COALESCE(keywords, ''::character varying))::text))", stored: true),
                    locationHash = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    locationHash2 = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    locationHash3 = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    hasChild = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    fullAddressHash = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    ts2 = table.Column<NpgsqlTsVector>(type: "tsvector", nullable: true, computedColumnSql: "to_tsvector('english'::regconfig, regexp_replace(((((COALESCE(vn_unaccent((name)::text), ''::text) || ' '::text) || COALESCE(vn_unaccent((\"fullAddress\")::text), ''::text)) || ' '::text) || COALESCE(vn_unaccent((keywords)::text), ''::text)), '[^\\w]+'::text, ' '::text, 'g'::text))", stored: true),
                    districtId = table.Column<int>(type: "integer", nullable: true),
                    provinceId = table.Column<int>(type: "integer", nullable: true),
                    wardId = table.Column<int>(type: "integer", nullable: true),
                    pending = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    quoteCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_places", x => x.id);
                    table.UniqueConstraint("AK_places_placeId", x => x.placeId);
                });

            migrationBuilder.CreateTable(
                name: "places_details",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    placeFkId = table.Column<long>(type: "bigint", nullable: true),
                    placeId = table.Column<string>(type: "text", nullable: true),
                    imageUrl = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    rating = table.Column<double>(type: "double precision", nullable: true),
                    reviewCount = table.Column<int>(type: "integer", nullable: true),
                    types = table.Column<string>(type: "jsonb", nullable: true),
                    address_components = table.Column<string>(type: "jsonb", nullable: true),
                    createdAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    updatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "places_queue",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    districtid = table.Column<long>(type: "bigint", nullable: false),
                    provinceid = table.Column<long>(type: "bigint", nullable: false),
                    status = table.Column<int>(type: "integer", nullable: false),
                    create_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    update_date = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    place_type = table.Column<string>(type: "character varying", nullable: true),
                    creatorUserId = table.Column<string>(type: "text", nullable: true),
                    lastUpdateUser = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_places_queue", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "places_remove",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: true),
                    placeDetailId = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: true),
                    phoneNumber = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: true),
                    placeId = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    location = table.Column<Point>(type: "geometry(Point,4326)", nullable: true),
                    name = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    address = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    keywords = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    fullAddress = table.Column<string>(type: "character varying(10000)", maxLength: 10000, nullable: true),
                    streetNumber = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    route = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    ward = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    district = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    city = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    type = table.Column<int>(type: "integer", nullable: true),
                    active = table.Column<bool>(type: "boolean", nullable: true),
                    tags = table.Column<List<string>>(type: "text[]", nullable: true),
                    tagInput = table.Column<string>(type: "text", nullable: true),
                    popular = table.Column<bool>(type: "boolean", nullable: true),
                    processed = table.Column<bool>(type: "boolean", nullable: true),
                    bookingCount = table.Column<int>(type: "integer", nullable: true),
                    removed = table.Column<bool>(type: "boolean", nullable: true),
                    hidden = table.Column<bool>(type: "boolean", nullable: true),
                    createdAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    updatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    creatorId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    lastUpdateUser = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    dataFrom = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ts = table.Column<NpgsqlTsVector>(type: "tsvector", nullable: true),
                    locationHash = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    locationHash2 = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    locationHash3 = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    hasChild = table.Column<bool>(type: "boolean", nullable: true),
                    fullAddressHash = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    ts2 = table.Column<NpgsqlTsVector>(type: "tsvector", nullable: true),
                    districtId = table.Column<int>(type: "integer", nullable: true),
                    provinceId = table.Column<int>(type: "integer", nullable: true),
                    wardId = table.Column<int>(type: "integer", nullable: true),
                    pending = table.Column<bool>(type: "boolean", nullable: true),
                    quoteCount = table.Column<int>(type: "integer", nullable: true),
                    reason = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "places_scan_history",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    placeid = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    fulladdress = table.Column<string>(type: "character varying(10000)", maxLength: 10000, nullable: true),
                    type = table.Column<int>(type: "integer", nullable: true),
                    compare_score = table.Column<decimal>(type: "numeric", nullable: true),
                    hangfire_job_id = table.Column<string>(type: "character varying", nullable: true),
                    createat = table.Column<DateTime>(type: "timestamp without time zone", nullable: false, defaultValueSql: "current_timestamp"),
                    updateat = table.Column<DateTime>(type: "timestamp without time zone", nullable: true, defaultValueSql: "current_timestamp"),
                    source_placeid = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    source_fulladdress = table.Column<string>(type: "character varying(10000)", maxLength: 10000, nullable: true),
                    districtid = table.Column<int>(type: "integer", nullable: false),
                    provinceid = table.Column<int>(type: "integer", nullable: false),
                    wardid = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_places_scan_history", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "places_scan_result",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    placeid = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    source_placeid = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    hangfire_job_id = table.Column<string>(type: "character varying", nullable: true),
                    compare_score_max = table.Column<decimal>(type: "numeric", nullable: false),
                    total_scans = table.Column<int>(type: "integer", nullable: false),
                    create_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: false, defaultValueSql: "current_timestamp"),
                    update_at = table.Column<DateTime>(type: "timestamp without time zone", nullable: false, defaultValueSql: "current_timestamp"),
                    scan_type = table.Column<string>(type: "character varying", nullable: true),
                    note = table.Column<string>(type: "text", nullable: true),
                    reason = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    reason_operations = table.Column<int[]>(type: "integer[]", nullable: true),
                    scan_processed = table.Column<int>(type: "integer", nullable: true),
                    sync_status = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    lastUpdateUser = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_places_scan_result", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "places_uat_bak",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: true),
                    placeDetailId = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: true),
                    phoneNumber = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: true),
                    placeId = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    location = table.Column<Point>(type: "geometry(Point,4326)", nullable: true),
                    name = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    address = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    keywords = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    fullAddress = table.Column<string>(type: "character varying(10000)", maxLength: 10000, nullable: true),
                    streetNumber = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    route = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    ward = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    district = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    city = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    type = table.Column<int>(type: "integer", nullable: true),
                    active = table.Column<bool>(type: "boolean", nullable: true),
                    tags = table.Column<List<string>>(type: "text[]", nullable: true),
                    tagInput = table.Column<string>(type: "text", nullable: true),
                    popular = table.Column<bool>(type: "boolean", nullable: true),
                    processed = table.Column<bool>(type: "boolean", nullable: true),
                    bookingCount = table.Column<int>(type: "integer", nullable: true),
                    removed = table.Column<bool>(type: "boolean", nullable: true),
                    hidden = table.Column<bool>(type: "boolean", nullable: true),
                    createdAt = table.Column<LocalDateTime>(type: "timestamp without time zone", nullable: true),
                    updatedAt = table.Column<LocalDateTime>(type: "timestamp without time zone", nullable: true),
                    creatorId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    lastUpdateUser = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    dataFrom = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ts = table.Column<NpgsqlTsVector>(type: "tsvector", nullable: true),
                    locationHash = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    locationHash2 = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    locationHash3 = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    hasChild = table.Column<bool>(type: "boolean", nullable: true),
                    fullAddressHash = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    ts2 = table.Column<NpgsqlTsVector>(type: "tsvector", nullable: true),
                    districtId = table.Column<int>(type: "integer", nullable: true),
                    provinceId = table.Column<int>(type: "integer", nullable: true),
                    wardId = table.Column<int>(type: "integer", nullable: true),
                    pending = table.Column<bool>(type: "boolean", nullable: true),
                    quoteCount = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "placesdataes",
                columns: table => new
                {
                    PlaceId = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: true),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    Address = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: true),
                    Province = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    District = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    Ward = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    ScanProcessed = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    UpdatedAt = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    LastUpdateUser = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    CompareScore = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "planet_osm_line",
                columns: table => new
                {
                    osm_id = table.Column<long>(type: "bigint", nullable: true),
                    access = table.Column<string>(type: "text", nullable: true),
                    addrhousename = table.Column<string>(name: "addr:housename", type: "text", nullable: true),
                    addrhousenumber = table.Column<string>(name: "addr:housenumber", type: "text", nullable: true),
                    addrinterpolation = table.Column<string>(name: "addr:interpolation", type: "text", nullable: true),
                    admin_level = table.Column<string>(type: "text", nullable: true),
                    aerialway = table.Column<string>(type: "text", nullable: true),
                    aeroway = table.Column<string>(type: "text", nullable: true),
                    amenity = table.Column<string>(type: "text", nullable: true),
                    area = table.Column<string>(type: "text", nullable: true),
                    barrier = table.Column<string>(type: "text", nullable: true),
                    bicycle = table.Column<string>(type: "text", nullable: true),
                    brand = table.Column<string>(type: "text", nullable: true),
                    bridge = table.Column<string>(type: "text", nullable: true),
                    boundary = table.Column<string>(type: "text", nullable: true),
                    building = table.Column<string>(type: "text", nullable: true),
                    construction = table.Column<string>(type: "text", nullable: true),
                    covered = table.Column<string>(type: "text", nullable: true),
                    culvert = table.Column<string>(type: "text", nullable: true),
                    cutting = table.Column<string>(type: "text", nullable: true),
                    denomination = table.Column<string>(type: "text", nullable: true),
                    disused = table.Column<string>(type: "text", nullable: true),
                    embankment = table.Column<string>(type: "text", nullable: true),
                    foot = table.Column<string>(type: "text", nullable: true),
                    generatorsource = table.Column<string>(name: "generator:source", type: "text", nullable: true),
                    harbour = table.Column<string>(type: "text", nullable: true),
                    highway = table.Column<string>(type: "text", nullable: true),
                    historic = table.Column<string>(type: "text", nullable: true),
                    horse = table.Column<string>(type: "text", nullable: true),
                    intermittent = table.Column<string>(type: "text", nullable: true),
                    junction = table.Column<string>(type: "text", nullable: true),
                    landuse = table.Column<string>(type: "text", nullable: true),
                    layer = table.Column<string>(type: "text", nullable: true),
                    leisure = table.Column<string>(type: "text", nullable: true),
                    @lock = table.Column<string>(name: "lock", type: "text", nullable: true),
                    man_made = table.Column<string>(type: "text", nullable: true),
                    military = table.Column<string>(type: "text", nullable: true),
                    motorcar = table.Column<string>(type: "text", nullable: true),
                    name = table.Column<string>(type: "text", nullable: true),
                    natural = table.Column<string>(type: "text", nullable: true),
                    office = table.Column<string>(type: "text", nullable: true),
                    oneway = table.Column<string>(type: "text", nullable: true),
                    @operator = table.Column<string>(name: "operator", type: "text", nullable: true),
                    place = table.Column<string>(type: "text", nullable: true),
                    population = table.Column<string>(type: "text", nullable: true),
                    power = table.Column<string>(type: "text", nullable: true),
                    power_source = table.Column<string>(type: "text", nullable: true),
                    public_transport = table.Column<string>(type: "text", nullable: true),
                    railway = table.Column<string>(type: "text", nullable: true),
                    @ref = table.Column<string>(name: "ref", type: "text", nullable: true),
                    religion = table.Column<string>(type: "text", nullable: true),
                    route = table.Column<string>(type: "text", nullable: true),
                    service = table.Column<string>(type: "text", nullable: true),
                    shop = table.Column<string>(type: "text", nullable: true),
                    sport = table.Column<string>(type: "text", nullable: true),
                    surface = table.Column<string>(type: "text", nullable: true),
                    toll = table.Column<string>(type: "text", nullable: true),
                    tourism = table.Column<string>(type: "text", nullable: true),
                    towertype = table.Column<string>(name: "tower:type", type: "text", nullable: true),
                    tracktype = table.Column<string>(type: "text", nullable: true),
                    tunnel = table.Column<string>(type: "text", nullable: true),
                    water = table.Column<string>(type: "text", nullable: true),
                    waterway = table.Column<string>(type: "text", nullable: true),
                    wetland = table.Column<string>(type: "text", nullable: true),
                    width = table.Column<string>(type: "text", nullable: true),
                    wood = table.Column<string>(type: "text", nullable: true),
                    z_order = table.Column<int>(type: "integer", nullable: true),
                    way_area = table.Column<float>(type: "real", nullable: true),
                    way = table.Column<LineString>(type: "geometry(LineString,3857)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "planet_osm_nodes",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    lat = table.Column<int>(type: "integer", nullable: false),
                    lon = table.Column<int>(type: "integer", nullable: false),
                    tags = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("planet_osm_nodes_pkey", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "planet_osm_point",
                columns: table => new
                {
                    osm_id = table.Column<long>(type: "bigint", nullable: true),
                    access = table.Column<string>(type: "text", nullable: true),
                    addrhousename = table.Column<string>(name: "addr:housename", type: "text", nullable: true),
                    addrhousenumber = table.Column<string>(name: "addr:housenumber", type: "text", nullable: true),
                    addrinterpolation = table.Column<string>(name: "addr:interpolation", type: "text", nullable: true),
                    admin_level = table.Column<string>(type: "text", nullable: true),
                    aerialway = table.Column<string>(type: "text", nullable: true),
                    aeroway = table.Column<string>(type: "text", nullable: true),
                    amenity = table.Column<string>(type: "text", nullable: true),
                    area = table.Column<string>(type: "text", nullable: true),
                    barrier = table.Column<string>(type: "text", nullable: true),
                    bicycle = table.Column<string>(type: "text", nullable: true),
                    brand = table.Column<string>(type: "text", nullable: true),
                    bridge = table.Column<string>(type: "text", nullable: true),
                    boundary = table.Column<string>(type: "text", nullable: true),
                    building = table.Column<string>(type: "text", nullable: true),
                    capital = table.Column<string>(type: "text", nullable: true),
                    construction = table.Column<string>(type: "text", nullable: true),
                    covered = table.Column<string>(type: "text", nullable: true),
                    culvert = table.Column<string>(type: "text", nullable: true),
                    cutting = table.Column<string>(type: "text", nullable: true),
                    denomination = table.Column<string>(type: "text", nullable: true),
                    disused = table.Column<string>(type: "text", nullable: true),
                    ele = table.Column<string>(type: "text", nullable: true),
                    embankment = table.Column<string>(type: "text", nullable: true),
                    foot = table.Column<string>(type: "text", nullable: true),
                    generatorsource = table.Column<string>(name: "generator:source", type: "text", nullable: true),
                    harbour = table.Column<string>(type: "text", nullable: true),
                    highway = table.Column<string>(type: "text", nullable: true),
                    historic = table.Column<string>(type: "text", nullable: true),
                    horse = table.Column<string>(type: "text", nullable: true),
                    intermittent = table.Column<string>(type: "text", nullable: true),
                    junction = table.Column<string>(type: "text", nullable: true),
                    landuse = table.Column<string>(type: "text", nullable: true),
                    layer = table.Column<string>(type: "text", nullable: true),
                    leisure = table.Column<string>(type: "text", nullable: true),
                    @lock = table.Column<string>(name: "lock", type: "text", nullable: true),
                    man_made = table.Column<string>(type: "text", nullable: true),
                    military = table.Column<string>(type: "text", nullable: true),
                    motorcar = table.Column<string>(type: "text", nullable: true),
                    name = table.Column<string>(type: "text", nullable: true),
                    natural = table.Column<string>(type: "text", nullable: true),
                    office = table.Column<string>(type: "text", nullable: true),
                    oneway = table.Column<string>(type: "text", nullable: true),
                    @operator = table.Column<string>(name: "operator", type: "text", nullable: true),
                    place = table.Column<string>(type: "text", nullable: true),
                    population = table.Column<string>(type: "text", nullable: true),
                    power = table.Column<string>(type: "text", nullable: true),
                    power_source = table.Column<string>(type: "text", nullable: true),
                    public_transport = table.Column<string>(type: "text", nullable: true),
                    railway = table.Column<string>(type: "text", nullable: true),
                    @ref = table.Column<string>(name: "ref", type: "text", nullable: true),
                    religion = table.Column<string>(type: "text", nullable: true),
                    route = table.Column<string>(type: "text", nullable: true),
                    service = table.Column<string>(type: "text", nullable: true),
                    shop = table.Column<string>(type: "text", nullable: true),
                    sport = table.Column<string>(type: "text", nullable: true),
                    surface = table.Column<string>(type: "text", nullable: true),
                    toll = table.Column<string>(type: "text", nullable: true),
                    tourism = table.Column<string>(type: "text", nullable: true),
                    towertype = table.Column<string>(name: "tower:type", type: "text", nullable: true),
                    tunnel = table.Column<string>(type: "text", nullable: true),
                    water = table.Column<string>(type: "text", nullable: true),
                    waterway = table.Column<string>(type: "text", nullable: true),
                    wetland = table.Column<string>(type: "text", nullable: true),
                    width = table.Column<string>(type: "text", nullable: true),
                    wood = table.Column<string>(type: "text", nullable: true),
                    z_order = table.Column<int>(type: "integer", nullable: true),
                    way = table.Column<Point>(type: "geometry(Point,3857)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "planet_osm_polygon",
                columns: table => new
                {
                    osm_id = table.Column<long>(type: "bigint", nullable: true),
                    access = table.Column<string>(type: "text", nullable: true),
                    addrhousename = table.Column<string>(name: "addr:housename", type: "text", nullable: true),
                    addrhousenumber = table.Column<string>(name: "addr:housenumber", type: "text", nullable: true),
                    addrinterpolation = table.Column<string>(name: "addr:interpolation", type: "text", nullable: true),
                    admin_level = table.Column<string>(type: "text", nullable: true),
                    aerialway = table.Column<string>(type: "text", nullable: true),
                    aeroway = table.Column<string>(type: "text", nullable: true),
                    amenity = table.Column<string>(type: "text", nullable: true),
                    area = table.Column<string>(type: "text", nullable: true),
                    barrier = table.Column<string>(type: "text", nullable: true),
                    bicycle = table.Column<string>(type: "text", nullable: true),
                    brand = table.Column<string>(type: "text", nullable: true),
                    bridge = table.Column<string>(type: "text", nullable: true),
                    boundary = table.Column<string>(type: "text", nullable: true),
                    building = table.Column<string>(type: "text", nullable: true),
                    construction = table.Column<string>(type: "text", nullable: true),
                    covered = table.Column<string>(type: "text", nullable: true),
                    culvert = table.Column<string>(type: "text", nullable: true),
                    cutting = table.Column<string>(type: "text", nullable: true),
                    denomination = table.Column<string>(type: "text", nullable: true),
                    disused = table.Column<string>(type: "text", nullable: true),
                    embankment = table.Column<string>(type: "text", nullable: true),
                    foot = table.Column<string>(type: "text", nullable: true),
                    generatorsource = table.Column<string>(name: "generator:source", type: "text", nullable: true),
                    harbour = table.Column<string>(type: "text", nullable: true),
                    highway = table.Column<string>(type: "text", nullable: true),
                    historic = table.Column<string>(type: "text", nullable: true),
                    horse = table.Column<string>(type: "text", nullable: true),
                    intermittent = table.Column<string>(type: "text", nullable: true),
                    junction = table.Column<string>(type: "text", nullable: true),
                    landuse = table.Column<string>(type: "text", nullable: true),
                    layer = table.Column<string>(type: "text", nullable: true),
                    leisure = table.Column<string>(type: "text", nullable: true),
                    @lock = table.Column<string>(name: "lock", type: "text", nullable: true),
                    man_made = table.Column<string>(type: "text", nullable: true),
                    military = table.Column<string>(type: "text", nullable: true),
                    motorcar = table.Column<string>(type: "text", nullable: true),
                    name = table.Column<string>(type: "text", nullable: true),
                    natural = table.Column<string>(type: "text", nullable: true),
                    office = table.Column<string>(type: "text", nullable: true),
                    oneway = table.Column<string>(type: "text", nullable: true),
                    @operator = table.Column<string>(name: "operator", type: "text", nullable: true),
                    place = table.Column<string>(type: "text", nullable: true),
                    population = table.Column<string>(type: "text", nullable: true),
                    power = table.Column<string>(type: "text", nullable: true),
                    power_source = table.Column<string>(type: "text", nullable: true),
                    public_transport = table.Column<string>(type: "text", nullable: true),
                    railway = table.Column<string>(type: "text", nullable: true),
                    @ref = table.Column<string>(name: "ref", type: "text", nullable: true),
                    religion = table.Column<string>(type: "text", nullable: true),
                    route = table.Column<string>(type: "text", nullable: true),
                    service = table.Column<string>(type: "text", nullable: true),
                    shop = table.Column<string>(type: "text", nullable: true),
                    sport = table.Column<string>(type: "text", nullable: true),
                    surface = table.Column<string>(type: "text", nullable: true),
                    toll = table.Column<string>(type: "text", nullable: true),
                    tourism = table.Column<string>(type: "text", nullable: true),
                    towertype = table.Column<string>(name: "tower:type", type: "text", nullable: true),
                    tracktype = table.Column<string>(type: "text", nullable: true),
                    tunnel = table.Column<string>(type: "text", nullable: true),
                    water = table.Column<string>(type: "text", nullable: true),
                    waterway = table.Column<string>(type: "text", nullable: true),
                    wetland = table.Column<string>(type: "text", nullable: true),
                    width = table.Column<string>(type: "text", nullable: true),
                    wood = table.Column<string>(type: "text", nullable: true),
                    z_order = table.Column<int>(type: "integer", nullable: true),
                    way_area = table.Column<float>(type: "real", nullable: true),
                    way = table.Column<Geometry>(type: "geometry(Geometry,3857)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "planet_osm_rels",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    members = table.Column<string>(type: "jsonb", nullable: false),
                    tags = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("planet_osm_rels_pkey", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "planet_osm_roads",
                columns: table => new
                {
                    osm_id = table.Column<long>(type: "bigint", nullable: true),
                    access = table.Column<string>(type: "text", nullable: true),
                    addrhousename = table.Column<string>(name: "addr:housename", type: "text", nullable: true),
                    addrhousenumber = table.Column<string>(name: "addr:housenumber", type: "text", nullable: true),
                    addrinterpolation = table.Column<string>(name: "addr:interpolation", type: "text", nullable: true),
                    admin_level = table.Column<string>(type: "text", nullable: true),
                    aerialway = table.Column<string>(type: "text", nullable: true),
                    aeroway = table.Column<string>(type: "text", nullable: true),
                    amenity = table.Column<string>(type: "text", nullable: true),
                    area = table.Column<string>(type: "text", nullable: true),
                    barrier = table.Column<string>(type: "text", nullable: true),
                    bicycle = table.Column<string>(type: "text", nullable: true),
                    brand = table.Column<string>(type: "text", nullable: true),
                    bridge = table.Column<string>(type: "text", nullable: true),
                    boundary = table.Column<string>(type: "text", nullable: true),
                    building = table.Column<string>(type: "text", nullable: true),
                    construction = table.Column<string>(type: "text", nullable: true),
                    covered = table.Column<string>(type: "text", nullable: true),
                    culvert = table.Column<string>(type: "text", nullable: true),
                    cutting = table.Column<string>(type: "text", nullable: true),
                    denomination = table.Column<string>(type: "text", nullable: true),
                    disused = table.Column<string>(type: "text", nullable: true),
                    embankment = table.Column<string>(type: "text", nullable: true),
                    foot = table.Column<string>(type: "text", nullable: true),
                    generatorsource = table.Column<string>(name: "generator:source", type: "text", nullable: true),
                    harbour = table.Column<string>(type: "text", nullable: true),
                    highway = table.Column<string>(type: "text", nullable: true),
                    historic = table.Column<string>(type: "text", nullable: true),
                    horse = table.Column<string>(type: "text", nullable: true),
                    intermittent = table.Column<string>(type: "text", nullable: true),
                    junction = table.Column<string>(type: "text", nullable: true),
                    landuse = table.Column<string>(type: "text", nullable: true),
                    layer = table.Column<string>(type: "text", nullable: true),
                    leisure = table.Column<string>(type: "text", nullable: true),
                    @lock = table.Column<string>(name: "lock", type: "text", nullable: true),
                    man_made = table.Column<string>(type: "text", nullable: true),
                    military = table.Column<string>(type: "text", nullable: true),
                    motorcar = table.Column<string>(type: "text", nullable: true),
                    name = table.Column<string>(type: "text", nullable: true),
                    natural = table.Column<string>(type: "text", nullable: true),
                    office = table.Column<string>(type: "text", nullable: true),
                    oneway = table.Column<string>(type: "text", nullable: true),
                    @operator = table.Column<string>(name: "operator", type: "text", nullable: true),
                    place = table.Column<string>(type: "text", nullable: true),
                    population = table.Column<string>(type: "text", nullable: true),
                    power = table.Column<string>(type: "text", nullable: true),
                    power_source = table.Column<string>(type: "text", nullable: true),
                    public_transport = table.Column<string>(type: "text", nullable: true),
                    railway = table.Column<string>(type: "text", nullable: true),
                    @ref = table.Column<string>(name: "ref", type: "text", nullable: true),
                    religion = table.Column<string>(type: "text", nullable: true),
                    route = table.Column<string>(type: "text", nullable: true),
                    service = table.Column<string>(type: "text", nullable: true),
                    shop = table.Column<string>(type: "text", nullable: true),
                    sport = table.Column<string>(type: "text", nullable: true),
                    surface = table.Column<string>(type: "text", nullable: true),
                    toll = table.Column<string>(type: "text", nullable: true),
                    tourism = table.Column<string>(type: "text", nullable: true),
                    towertype = table.Column<string>(name: "tower:type", type: "text", nullable: true),
                    tracktype = table.Column<string>(type: "text", nullable: true),
                    tunnel = table.Column<string>(type: "text", nullable: true),
                    water = table.Column<string>(type: "text", nullable: true),
                    waterway = table.Column<string>(type: "text", nullable: true),
                    wetland = table.Column<string>(type: "text", nullable: true),
                    width = table.Column<string>(type: "text", nullable: true),
                    wood = table.Column<string>(type: "text", nullable: true),
                    z_order = table.Column<int>(type: "integer", nullable: true),
                    way_area = table.Column<float>(type: "real", nullable: true),
                    way = table.Column<LineString>(type: "geometry(LineString,3857)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "planet_osm_ways",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    nodes = table.Column<List<long>>(type: "bigint[]", nullable: false),
                    tags = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("planet_osm_ways_pkey", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "province_boundaries",
                columns: table => new
                {
                    _id = table.Column<int>(type: "integer", nullable: true),
                    _class = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    created_at = table.Column<LocalDateTime>(type: "timestamp without time zone", nullable: true),
                    location = table.Column<string>(type: "character varying(10485760)", maxLength: 10485760, nullable: true),
                    modified_at = table.Column<LocalDateTime>(type: "timestamp without time zone", nullable: true),
                    modified_by = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    postcode = table.Column<int>(type: "integer", nullable: true),
                    status = table.Column<int>(type: "integer", nullable: true),
                    idprovince = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "provinces",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false),
                    code = table.Column<string>(type: "character varying", nullable: false),
                    name = table.Column<string>(type: "character varying", nullable: true),
                    type = table.Column<int>(type: "integer", nullable: true),
                    boundary = table.Column<string>(type: "text", nullable: true),
                    center_location = table.Column<string>(type: "character varying", nullable: true),
                    processed = table.Column<bool>(type: "boolean", nullable: true),
                    boundary_geom = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_provinces", x => x.id);
                    table.UniqueConstraint("AK_provinces_code", x => x.code);
                });

            migrationBuilder.CreateTable(
                name: "provinces_new",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false, defaultValueSql: "nextval('provinces__id_seq'::regclass)"),
                    code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    name = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    oldcode = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    boundary = table.Column<string>(type: "text", nullable: true),
                    center_location = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    processed = table.Column<bool>(type: "boolean", nullable: true),
                    boundary_geom = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("provinces__pkey", x => x.id);
                    table.UniqueConstraint("AK_provinces_new_code", x => x.code);
                });

            migrationBuilder.CreateTable(
                name: "sheet2",
                columns: table => new
                {
                    Column1id = table.Column<int>(name: "Column1.id", type: "integer", nullable: true),
                    Column10 = table.Column<int>(name: "Column1.0", type: "integer", nullable: true),
                    Column1matinh = table.Column<string>(name: "Column1.matinh", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column11 = table.Column<int>(name: "Column1.1", type: "integer", nullable: true),
                    Column1ma = table.Column<string>(name: "Column1.ma", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column12 = table.Column<string>(name: "Column1.2", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column1tentinh = table.Column<string>(name: "Column1.tentinh", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column13 = table.Column<string>(name: "Column1.3", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column1loai = table.Column<string>(name: "Column1.loai", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column14 = table.Column<string>(name: "Column1.4", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column1tenhc = table.Column<string>(name: "Column1.tenhc", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column15 = table.Column<string>(name: "Column1.5", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column1cay = table.Column<string>(name: "Column1.cay", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column16 = table.Column<string>(name: "Column1.6", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column1dientichkm2 = table.Column<int>(name: "Column1.dientichkm2", type: "integer", nullable: true),
                    Column17 = table.Column<int>(name: "Column1.7", type: "integer", nullable: true),
                    Column1dansonguoi = table.Column<string>(name: "Column1.dansonguoi", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column18 = table.Column<string>(name: "Column1.8", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column1trungtamhc = table.Column<string>(name: "Column1.trungtamhc", type: "character varying(64)", maxLength: 64, nullable: true),
                    Column19 = table.Column<string>(name: "Column1.9", type: "character varying(64)", maxLength: 64, nullable: true),
                    Column1kinhdo = table.Column<int>(name: "Column1.kinhdo", type: "integer", nullable: true),
                    Column110 = table.Column<int>(name: "Column1.10", type: "integer", nullable: true),
                    Column1vido = table.Column<int>(name: "Column1.vido", type: "integer", nullable: true),
                    Column111 = table.Column<int>(name: "Column1.11", type: "integer", nullable: true),
                    Column1truocsapnhap = table.Column<string>(name: "Column1.truocsapnhap", type: "character varying(1024)", maxLength: 1024, nullable: true),
                    Column112 = table.Column<string>(name: "Column1.12", type: "character varying(1024)", maxLength: 1024, nullable: true),
                    Column1maxa = table.Column<int>(name: "Column1.maxa", type: "integer", nullable: true),
                    Column113 = table.Column<int>(name: "Column1.13", type: "integer", nullable: true),
                    Column1mahc = table.Column<int>(name: "Column1.mahc", type: "integer", nullable: true),
                    Column1con = table.Column<string>(name: "Column1.con", type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "sheet3",
                columns: table => new
                {
                    Column1id = table.Column<int>(name: "Column1.id", type: "integer", nullable: false),
                    Column10 = table.Column<int>(name: "Column1.0", type: "integer", nullable: true),
                    Column1mahc = table.Column<string>(name: "Column1.mahc", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column11 = table.Column<int>(name: "Column1.1", type: "integer", nullable: true),
                    Column1tentinh = table.Column<string>(name: "Column1.tentinh", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column12 = table.Column<string>(name: "Column1.2", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column1dientichkm2 = table.Column<string>(name: "Column1.dientichkm2", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column13 = table.Column<string>(name: "Column1.3", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column1dansonguoi = table.Column<string>(name: "Column1.dansonguoi", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column14 = table.Column<string>(name: "Column1.4", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column1trungtamhc = table.Column<string>(name: "Column1.trungtamhc", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column15 = table.Column<string>(name: "Column1.5", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column1kinhdo = table.Column<int>(name: "Column1.kinhdo", type: "integer", nullable: true),
                    Column16 = table.Column<int>(name: "Column1.6", type: "integer", nullable: true),
                    Column1vido = table.Column<int>(name: "Column1.vido", type: "integer", nullable: true),
                    Column17 = table.Column<int>(name: "Column1.7", type: "integer", nullable: true),
                    Column1truocsapnhap = table.Column<string>(name: "Column1.truocsapnhap", type: "character varying(64)", maxLength: 64, nullable: true),
                    Column18 = table.Column<string>(name: "Column1.8", type: "character varying(64)", maxLength: 64, nullable: true),
                    Column1con = table.Column<string>(name: "Column1.con", type: "character varying(50)", maxLength: 50, nullable: true),
                    Column19 = table.Column<string>(name: "Column1.9", type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("sheet3_pk", x => x.Column1id);
                });

            migrationBuilder.CreateTable(
                name: "subplace_change_histories",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    dataJson = table.Column<string>(type: "text", nullable: true),
                    lastUpdatedUser = table.Column<string>(type: "text", nullable: true),
                    createdAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    updatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    subPlaceId = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                    creator = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_subplace_change_histories", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "sys_no",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    typeCode = table.Column<int>(type: "integer", nullable: false),
                    value = table.Column<long>(type: "bigint", nullable: false),
                    tableName = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sys_no", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "trusted_places",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    hasChild = table.Column<bool>(type: "boolean", nullable: true),
                    dataSource = table.Column<string>(type: "text", nullable: true),
                    placeDetailId = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: true),
                    phoneNumber = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: true),
                    placeId = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    location = table.Column<Point>(type: "geometry(Point,4326)", nullable: true),
                    locationHash = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    locationHash2 = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    locationHash3 = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    name = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    address = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    keywords = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    fullAddress = table.Column<string>(type: "character varying(10000)", maxLength: 10000, nullable: false),
                    fullAddressHash = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    streetNumber = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    route = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    ward = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    district = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    city = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    type = table.Column<int>(type: "integer", nullable: false),
                    active = table.Column<bool>(type: "boolean", nullable: false),
                    tags = table.Column<List<string>>(type: "text[]", nullable: true),
                    tagInput = table.Column<string>(type: "text", nullable: true),
                    popular = table.Column<bool>(type: "boolean", nullable: false),
                    processed = table.Column<bool>(type: "boolean", nullable: false),
                    bookingCount = table.Column<int>(type: "integer", nullable: false),
                    quoteCount = table.Column<int>(type: "integer", nullable: false),
                    removed = table.Column<bool>(type: "boolean", nullable: false),
                    hidden = table.Column<bool>(type: "boolean", nullable: false),
                    createdAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    updatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    creatorId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    lastUpdateUser = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    dataFrom = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ts = table.Column<NpgsqlTsVector>(type: "tsvector", nullable: true),
                    ts2 = table.Column<NpgsqlTsVector>(type: "tsvector", nullable: true),
                    pending = table.Column<bool>(type: "boolean", nullable: false),
                    provinceId = table.Column<int>(type: "integer", nullable: true),
                    districtId = table.Column<int>(type: "integer", nullable: true),
                    wardId = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "uat_place_migrate",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    placeId = table.Column<string>(type: "character varying(5000)", maxLength: 5000, nullable: false),
                    name = table.Column<string>(type: "character varying", nullable: true),
                    address_old = table.Column<string>(type: "character varying", nullable: true),
                    address = table.Column<string>(type: "character varying", nullable: true),
                    name_old = table.Column<string>(type: "character varying", nullable: true),
                    location_old = table.Column<Point>(type: "geometry(Point,4326)", nullable: true),
                    location = table.Column<Point>(type: "geometry(Point,4326)", nullable: true),
                    update_user = table.Column<string>(type: "character varying", nullable: true),
                    status = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    districtid = table.Column<int>(type: "integer", nullable: true),
                    provinceid = table.Column<int>(type: "integer", nullable: true),
                    wardid = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("uat_place_migrate_pk", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "uat_place_migrate2",
                columns: table => new
                {
                    placeId = table.Column<string>(type: "text", nullable: true),
                    name = table.Column<string>(type: "text", nullable: true),
                    name_old = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    address = table.Column<string>(type: "text", nullable: true),
                    address_old = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    location = table.Column<Point>(type: "geometry(Point,4326)", nullable: true),
                    location_old = table.Column<Point>(type: "geometry(Point,4326)", nullable: true),
                    update_user = table.Column<string>(type: "text", nullable: true),
                    districtId = table.Column<int>(type: "integer", nullable: true),
                    provinceId = table.Column<int>(type: "integer", nullable: true),
                    wardId = table.Column<int>(type: "integer", nullable: true),
                    update_at = table.Column<LocalDateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "user_booking_histories",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    customerPhone = table.Column<string>(type: "character varying", nullable: true),
                    originPlaceId = table.Column<string>(type: "character varying", nullable: true),
                    originName = table.Column<string>(type: "character varying", nullable: false),
                    originLocation = table.Column<Point>(type: "geometry(Point,4326)", nullable: true),
                    originFullAddress = table.Column<string>(type: "character varying", nullable: true),
                    originAddress = table.Column<string>(type: "character varying", nullable: true),
                    desPlaceId = table.Column<string>(type: "character varying", nullable: true),
                    desName = table.Column<string>(type: "character varying", nullable: false),
                    desLocation = table.Column<Point>(type: "geometry(Point,4326)", nullable: true),
                    desFullAddress = table.Column<string>(type: "character varying", nullable: true),
                    desAddress = table.Column<string>(type: "character varying", nullable: true),
                    createdAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "ward_boundaries",
                columns: table => new
                {
                    _id = table.Column<int>(type: "integer", nullable: false),
                    _class = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    created_at = table.Column<LocalDateTime>(type: "timestamp without time zone", nullable: true),
                    location = table.Column<string>(type: "character varying(10485760)", maxLength: 10485760, nullable: true),
                    modified_at = table.Column<LocalDateTime>(type: "timestamp without time zone", nullable: true),
                    modified_by = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    provinces = table.Column<string>(type: "character varying(25)", maxLength: 25, nullable: true),
                    district = table.Column<int>(type: "integer", nullable: true),
                    ward_type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    status = table.Column<int>(type: "integer", nullable: true),
                    postcode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    province = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("ward_boundaries_pk", x => x._id);
                });

            migrationBuilder.CreateTable(
                name: "ward_new",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false, defaultValueSql: "nextval('ward__id_seq'::regclass)"),
                    code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    provincecode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    name = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    oldcode = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    boundary = table.Column<string>(type: "text", nullable: true),
                    no_space_name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    center_location = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    processed = table.Column<bool>(type: "boolean", nullable: true),
                    boundary_geom = table.Column<Polygon>(type: "geometry(Polygon,4326)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("ward__pkey", x => x.id);
                    table.UniqueConstraint("AK_ward_new_code", x => x.code);
                });

            migrationBuilder.CreateTable(
                name: "wards",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    code = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    districtCode = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    type = table.Column<int>(type: "integer", nullable: true),
                    boundary = table.Column<string>(type: "text", nullable: true),
                    no_space_name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    province_code = table.Column<string>(type: "character varying(8)", maxLength: 8, nullable: true),
                    center_location = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    processed = table.Column<bool>(type: "boolean", nullable: true, defaultValue: false),
                    boundary_geom = table.Column<string>(type: "text", nullable: true),
                    district_code = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    districtId = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("wards_pkey", x => x.id);
                    table.UniqueConstraint("AK_wards_code", x => x.code);
                });

            migrationBuilder.CreateTable(
                name: "ApiKeyPermissions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PermissionName = table.Column<int>(type: "integer", nullable: false),
                    ApiKeyId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ApiKeyPermissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ApiKeyPermissions_ApiKeys_ApiKeyId",
                        column: x => x.ApiKeyId,
                        principalTable: "ApiKeys",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "building_boundary_place",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    buildingBoundaryId = table.Column<long>(type: "bigint", nullable: false),
                    placeFkId = table.Column<long>(type: "bigint", nullable: false),
                    placeId = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_building_boundary_place", x => x.id);
                    table.ForeignKey(
                        name: "FK_building_boundary_place_building_boundary_buildingBoundaryId",
                        column: x => x.buildingBoundaryId,
                        principalTable: "building_boundary",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_building_boundary_place_places_placeFkId",
                        column: x => x.placeFkId,
                        principalTable: "places",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "provinces_bridge",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    provinces_new_code = table.Column<string>(type: "character varying", nullable: false, comment: "Mã tỉnh mới sâu 1/7/2025"),
                    provinces_old_code = table.Column<string>(type: "character varying", nullable: false, comment: "Mã tỉnh cũ trước 1/7/2025")
                },
                constraints: table =>
                {
                    table.PrimaryKey("provinces_pk", x => x.id);
                    table.ForeignKey(
                        name: "provinces_bridge_provinces_fk",
                        column: x => x.provinces_old_code,
                        principalTable: "provinces",
                        principalColumn: "code");
                    table.ForeignKey(
                        name: "provinces_bridge_provinces_new_fk",
                        column: x => x.provinces_new_code,
                        principalTable: "provinces_new",
                        principalColumn: "code");
                });

            migrationBuilder.CreateTable(
                name: "sub_places",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    parentId = table.Column<long>(type: "bigint", nullable: true),
                    placeDetailId = table.Column<string>(type: "character varying", nullable: true),
                    phoneNumber = table.Column<string>(type: "character varying", nullable: true),
                    placeId = table.Column<string>(type: "character varying", nullable: false),
                    location = table.Column<Point>(type: "geometry(Point,4326)", nullable: true),
                    name = table.Column<string>(type: "character varying", nullable: true),
                    address = table.Column<string>(type: "character varying", nullable: true),
                    keywords = table.Column<string>(type: "character varying", nullable: true),
                    fullAddress = table.Column<string>(type: "character varying", nullable: false),
                    streetNumber = table.Column<string>(type: "character varying", nullable: true),
                    route = table.Column<string>(type: "character varying", nullable: true),
                    ward = table.Column<string>(type: "character varying", nullable: true),
                    district = table.Column<string>(type: "character varying", nullable: true),
                    city = table.Column<string>(type: "character varying", nullable: true),
                    type = table.Column<int>(type: "integer", nullable: false),
                    active = table.Column<bool>(type: "boolean", nullable: false),
                    tags = table.Column<List<string>>(type: "text[]", nullable: true),
                    tagInput = table.Column<string>(type: "text", nullable: true),
                    popular = table.Column<bool>(type: "boolean", nullable: false),
                    processed = table.Column<bool>(type: "boolean", nullable: false),
                    bookingCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    removed = table.Column<bool>(type: "boolean", nullable: false),
                    hidden = table.Column<bool>(type: "boolean", nullable: false),
                    createdAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false, defaultValueSql: "'01-JAN-01 00:00:00'::timestamp without time zone"),
                    updatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: false, defaultValueSql: "'01-JAN-01 00:00:00'::timestamp without time zone"),
                    creatorId = table.Column<string>(type: "character varying", nullable: true),
                    lastUpdateUser = table.Column<string>(type: "character varying", nullable: true),
                    dataFrom = table.Column<string>(type: "character varying", nullable: true),
                    ts = table.Column<NpgsqlTsVector>(type: "tsvector", nullable: true, computedColumnSql: "to_tsvector('english'::regconfig, (((((COALESCE(name, ''::character varying))::text || ' '::text) || (\"fullAddress\")::text) || ' '::text) || (COALESCE(keywords, ''::character varying))::text))", stored: true),
                    locationHash = table.Column<string>(type: "character varying", nullable: true),
                    locationHash2 = table.Column<string>(type: "character varying", nullable: true),
                    locationHash3 = table.Column<string>(type: "character varying", nullable: true),
                    parentPlaceId = table.Column<string>(type: "character varying", nullable: true),
                    fullAddressHash = table.Column<string>(type: "character varying", nullable: true),
                    ts2 = table.Column<NpgsqlTsVector>(type: "tsvector", nullable: true, computedColumnSql: "to_tsvector('english'::regconfig, regexp_replace(((((COALESCE(vn_unaccent((name)::text), ''::text) || ' '::text) || COALESCE(vn_unaccent((\"fullAddress\")::text), ''::text)) || ' '::text) || COALESCE(vn_unaccent((keywords)::text), ''::text)), '[^\\w]+'::text, ' '::text, 'g'::text))", stored: true),
                    districtId = table.Column<int>(type: "integer", nullable: true),
                    provinceId = table.Column<int>(type: "integer", nullable: true),
                    wardId = table.Column<int>(type: "integer", nullable: true),
                    pending = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    quoteCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    ts3 = table.Column<NpgsqlTsVector>(type: "tsvector", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sub_places", x => x.id);
                    table.ForeignKey(
                        name: "FK_sub_places_wards_wardId",
                        column: x => x.wardId,
                        principalTable: "wards",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_sub_places_places_parentplaceid",
                        column: x => x.parentPlaceId,
                        principalTable: "places",
                        principalColumn: "placeId");
                });

            migrationBuilder.CreateTable(
                name: "wards_bridge",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    wards_old_code = table.Column<string>(type: "character varying", nullable: false, comment: "Ward Code cũ trước 1/7/2025"),
                    wards_new_code = table.Column<string>(type: "character varying", nullable: false, comment: "wards code mới từ ngày 1/7/2025 từ bảng ward_new")
                },
                constraints: table =>
                {
                    table.PrimaryKey("wards_bridge_pk", x => x.id);
                    table.ForeignKey(
                        name: "wards_bridge_ward_new_fk",
                        column: x => x.wards_new_code,
                        principalTable: "ward_new",
                        principalColumn: "code");
                    table.ForeignKey(
                        name: "wards_bridge_wards_fk",
                        column: x => x.wards_old_code,
                        principalTable: "wards",
                        principalColumn: "code");
                });

            migrationBuilder.CreateIndex(
                name: "IX_ApiKeyPermissions_ApiKeyId",
                table: "ApiKeyPermissions",
                column: "ApiKeyId");

            migrationBuilder.CreateIndex(
                name: "IX_ApiKeyPermissions_PermissionName",
                table: "ApiKeyPermissions",
                column: "PermissionName");

            migrationBuilder.CreateIndex(
                name: "IX_ApiKeys_IsActive",
                table: "ApiKeys",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_ApiKeys_Key",
                table: "ApiKeys",
                column: "Key",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "booking_place_pairs_id_idx",
                table: "booking_place_pairs",
                columns: new[] { "id", "createdAt" });

            migrationBuilder.CreateIndex(
                name: "created_at",
                table: "booking_place_pairs",
                column: "createdAt");

            migrationBuilder.CreateIndex(
                name: "IX_booking_place_pairs_arrivalPlaceId",
                table: "booking_place_pairs",
                column: "arrivalPlaceId");

            migrationBuilder.CreateIndex(
                name: "IX_booking_place_pairs_bookingCount",
                table: "booking_place_pairs",
                column: "bookingCount");

            migrationBuilder.CreateIndex(
                name: "IX_booking_place_pairs_customerId",
                table: "booking_place_pairs",
                column: "customerId");

            migrationBuilder.CreateIndex(
                name: "IX_booking_place_pairs_customerPhone",
                table: "booking_place_pairs",
                column: "customerPhone");

            migrationBuilder.CreateIndex(
                name: "IX_booking_place_pairs_hash1",
                table: "booking_place_pairs",
                column: "hash1");

            migrationBuilder.CreateIndex(
                name: "IX_booking_place_pairs_hash2",
                table: "booking_place_pairs",
                column: "hash2");

            migrationBuilder.CreateIndex(
                name: "IX_booking_place_pairs_paidCount",
                table: "booking_place_pairs",
                column: "paidCount");

            migrationBuilder.CreateIndex(
                name: "IX_booking_place_pairs_pickupPlaceId",
                table: "booking_place_pairs",
                column: "pickupPlaceId");

            migrationBuilder.CreateIndex(
                name: "IX_booking_place_pairs_searchCount",
                table: "booking_place_pairs",
                column: "searchCount");

            migrationBuilder.CreateIndex(
                name: "IX_booking_place_pairs_ts",
                table: "booking_place_pairs",
                column: "ts")
                .Annotation("Npgsql:IndexMethod", "gin");

            migrationBuilder.CreateIndex(
                name: "IX_booking_place_pairs_ts2",
                table: "booking_place_pairs",
                column: "ts2")
                .Annotation("Npgsql:IndexMethod", "gin");

            migrationBuilder.CreateIndex(
                name: "IX_building_boundary_place_buildingBoundaryId",
                table: "building_boundary_place",
                column: "buildingBoundaryId");

            migrationBuilder.CreateIndex(
                name: "IX_building_boundary_place_placeFkId",
                table: "building_boundary_place",
                column: "placeFkId");

            migrationBuilder.CreateIndex(
                name: "phoneNumber",
                table: "customers",
                column: "phoneNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "userId",
                table: "customers",
                column: "userId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "index_phonenumber",
                table: "favorite_places",
                column: "phoneNumber");

            migrationBuilder.CreateIndex(
                name: "index_type",
                table: "favorite_places",
                column: "type");

            migrationBuilder.CreateIndex(
                name: "index_userid",
                table: "favorite_places",
                column: "userId");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_bookingCount",
                table: "hidden_places",
                column: "bookingCount");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_bookingCount_quoteCount",
                table: "hidden_places",
                columns: new[] { "bookingCount", "quoteCount" });

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_city",
                table: "hidden_places",
                column: "city");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_createdAt",
                table: "hidden_places",
                column: "createdAt");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_dataFrom",
                table: "hidden_places",
                column: "dataFrom");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_districtId",
                table: "hidden_places",
                column: "districtId");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_fullAddressHash",
                table: "hidden_places",
                column: "fullAddressHash");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_hidden",
                table: "hidden_places",
                column: "hidden");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_location",
                table: "hidden_places",
                column: "location")
                .Annotation("Npgsql:IndexMethod", "gist");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_locationHash",
                table: "hidden_places",
                column: "locationHash");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_locationHash2",
                table: "hidden_places",
                column: "locationHash2");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_locationHash3",
                table: "hidden_places",
                column: "locationHash3");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_pending",
                table: "hidden_places",
                column: "pending");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_placeDetailId",
                table: "hidden_places",
                column: "placeDetailId");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_popular",
                table: "hidden_places",
                column: "popular");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_popular_processed_removed_hidden",
                table: "hidden_places",
                columns: new[] { "popular", "processed", "removed", "hidden" });

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_processed",
                table: "hidden_places",
                column: "processed");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_processed_pending",
                table: "hidden_places",
                columns: new[] { "processed", "pending" });

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_provinceId",
                table: "hidden_places",
                column: "provinceId");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_quoteCount",
                table: "hidden_places",
                column: "quoteCount");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_removed",
                table: "hidden_places",
                column: "removed");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_removed_processed_pending",
                table: "hidden_places",
                columns: new[] { "removed", "processed", "pending" });

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_removed_processed_pending_bookingCount_quoteC~",
                table: "hidden_places",
                columns: new[] { "removed", "processed", "pending", "bookingCount", "quoteCount" });

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_ts",
                table: "hidden_places",
                column: "ts")
                .Annotation("Npgsql:IndexMethod", "gin");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_ts2",
                table: "hidden_places",
                column: "ts2")
                .Annotation("Npgsql:IndexMethod", "gin");

            migrationBuilder.CreateIndex(
                name: "IX_hidden_places_wardId",
                table: "hidden_places",
                column: "wardId");

            migrationBuilder.CreateIndex(
                name: "job_last_update_origin_idx",
                table: "job_last_update",
                column: "origin");

            migrationBuilder.CreateIndex(
                name: "place_approve_placeid_idx",
                table: "place_approve",
                column: "placeId");

            migrationBuilder.CreateIndex(
                name: "place_uat_up_live_type_idx",
                table: "place_uat_up_live",
                column: "type");

            migrationBuilder.CreateIndex(
                name: "IX_districtId",
                table: "places",
                column: "districtId");

            migrationBuilder.CreateIndex(
                name: "IX_places_bookingCount",
                table: "places",
                column: "bookingCount");

            migrationBuilder.CreateIndex(
                name: "IX_places_bookingCount_quoteCount",
                table: "places",
                columns: new[] { "bookingCount", "quoteCount" });

            migrationBuilder.CreateIndex(
                name: "IX_places_city",
                table: "places",
                column: "city");

            migrationBuilder.CreateIndex(
                name: "ix_places_createdat",
                table: "places",
                column: "createdAt");

            migrationBuilder.CreateIndex(
                name: "IX_places_dataFrom",
                table: "places",
                column: "dataFrom");

            migrationBuilder.CreateIndex(
                name: "IX_places_fullAddressHash",
                table: "places",
                column: "fullAddressHash");

            migrationBuilder.CreateIndex(
                name: "IX_places_hidden",
                table: "places",
                column: "hidden");

            migrationBuilder.CreateIndex(
                name: "IX_places_lastUpdateUser",
                table: "places",
                column: "lastUpdateUser");

            migrationBuilder.CreateIndex(
                name: "IX_places_location",
                table: "places",
                column: "location")
                .Annotation("Npgsql:IndexMethod", "gist");

            migrationBuilder.CreateIndex(
                name: "IX_places_locationHash",
                table: "places",
                column: "locationHash");

            migrationBuilder.CreateIndex(
                name: "IX_places_locationHash2",
                table: "places",
                column: "locationHash2");

            migrationBuilder.CreateIndex(
                name: "IX_places_locationHash3",
                table: "places",
                column: "locationHash3");

            migrationBuilder.CreateIndex(
                name: "IX_places_pending",
                table: "places",
                column: "pending");

            migrationBuilder.CreateIndex(
                name: "IX_places_placeDetailId",
                table: "places",
                column: "placeDetailId");

            migrationBuilder.CreateIndex(
                name: "IX_places_placeId",
                table: "places",
                column: "placeId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_places_popular",
                table: "places",
                column: "popular");

            migrationBuilder.CreateIndex(
                name: "IX_places_processed",
                table: "places",
                column: "processed");

            migrationBuilder.CreateIndex(
                name: "IX_places_processed_pending",
                table: "places",
                columns: new[] { "processed", "pending" });

            migrationBuilder.CreateIndex(
                name: "IX_places_quoteCount",
                table: "places",
                column: "quoteCount");

            migrationBuilder.CreateIndex(
                name: "IX_places_removed",
                table: "places",
                column: "removed");

            migrationBuilder.CreateIndex(
                name: "IX_places_removed_processed_pending",
                table: "places",
                columns: new[] { "removed", "processed", "pending" });

            migrationBuilder.CreateIndex(
                name: "IX_places_removed_processed_pending_bookingCount_quoteCount",
                table: "places",
                columns: new[] { "removed", "processed", "pending", "bookingCount", "quoteCount" });

            migrationBuilder.CreateIndex(
                name: "IX_places_ts",
                table: "places",
                column: "ts")
                .Annotation("Npgsql:IndexMethod", "gin");

            migrationBuilder.CreateIndex(
                name: "IX_places_ts2",
                table: "places",
                column: "ts2")
                .Annotation("Npgsql:IndexMethod", "gin");

            migrationBuilder.CreateIndex(
                name: "IX_places_updatedAt",
                table: "places",
                column: "updatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_provinceId",
                table: "places",
                column: "provinceId");

            migrationBuilder.CreateIndex(
                name: "IX_wardId",
                table: "places",
                column: "wardId");

            migrationBuilder.CreateIndex(
                name: "IX_places_details_address_components",
                table: "places_details",
                column: "address_components")
                .Annotation("Npgsql:IndexMethod", "gin");

            migrationBuilder.CreateIndex(
                name: "IX_places_details_placeFkId",
                table: "places_details",
                column: "placeFkId");

            migrationBuilder.CreateIndex(
                name: "IX_places_details_rating",
                table: "places_details",
                column: "rating");

            migrationBuilder.CreateIndex(
                name: "IX_places_details_reviewCount",
                table: "places_details",
                column: "reviewCount");

            migrationBuilder.CreateIndex(
                name: "IX_places_details_types",
                table: "places_details",
                column: "types")
                .Annotation("Npgsql:IndexMethod", "gin");

            migrationBuilder.CreateIndex(
                name: "idx_places_scan_history_placeid",
                table: "places_scan_history",
                column: "placeid");

            migrationBuilder.CreateIndex(
                name: "idx_places_scan_history_source_hangfire_job_id",
                table: "places_scan_history",
                column: "hangfire_job_id");

            migrationBuilder.CreateIndex(
                name: "idx_places_scan_history_source_placeid",
                table: "places_scan_history",
                column: "source_placeid");

            migrationBuilder.CreateIndex(
                name: "places_scan_result_hangfire_job_id_idx",
                table: "places_scan_result",
                column: "hangfire_job_id");

            migrationBuilder.CreateIndex(
                name: "places_scan_result_placeid_idx",
                table: "places_scan_result",
                column: "placeid");

            migrationBuilder.CreateIndex(
                name: "places_scan_result_scan_processed_idx",
                table: "places_scan_result",
                column: "scan_processed");

            migrationBuilder.CreateIndex(
                name: "places_scan_result_sync_status_idx",
                table: "places_scan_result",
                column: "sync_status");

            migrationBuilder.CreateIndex(
                name: "places_scan_result_unique",
                table: "places_scan_result",
                column: "placeid",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "planet_osm_line_way_idx",
                table: "planet_osm_line",
                column: "way")
                .Annotation("Npgsql:IndexMethod", "gist")
                .Annotation("Npgsql:StorageParameter:fillfactor", "100");

            migrationBuilder.CreateIndex(
                name: "planet_osm_point_way_idx",
                table: "planet_osm_point",
                column: "way")
                .Annotation("Npgsql:IndexMethod", "gist")
                .Annotation("Npgsql:StorageParameter:fillfactor", "100");

            migrationBuilder.CreateIndex(
                name: "planet_osm_polygon_way_idx",
                table: "planet_osm_polygon",
                column: "way")
                .Annotation("Npgsql:IndexMethod", "gist")
                .Annotation("Npgsql:StorageParameter:fillfactor", "100");

            migrationBuilder.CreateIndex(
                name: "planet_osm_roads_way_idx",
                table: "planet_osm_roads",
                column: "way")
                .Annotation("Npgsql:IndexMethod", "gist")
                .Annotation("Npgsql:StorageParameter:fillfactor", "100");

            migrationBuilder.CreateIndex(
                name: "provinces_code_unique",
                table: "provinces",
                column: "code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "provinces_provinces_new_code_idx",
                table: "provinces_bridge",
                column: "provinces_new_code");

            migrationBuilder.CreateIndex(
                name: "provinces_provinces_old_code_idx",
                table: "provinces_bridge",
                column: "provinces_old_code");

            migrationBuilder.CreateIndex(
                name: "provinces_new_code_unique",
                table: "provinces_new",
                column: "code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_bookingCount",
                table: "sub_places",
                column: "bookingCount");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_bookingCount_quoteCount",
                table: "sub_places",
                columns: new[] { "bookingCount", "quoteCount" });

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_city",
                table: "sub_places",
                column: "city");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_districtId",
                table: "sub_places",
                column: "districtId");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_fullAddressHash",
                table: "sub_places",
                column: "fullAddressHash");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_hidden",
                table: "sub_places",
                column: "hidden");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_location",
                table: "sub_places",
                column: "location")
                .Annotation("Npgsql:IndexMethod", "gist");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_locationHash",
                table: "sub_places",
                column: "locationHash");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_locationHash2",
                table: "sub_places",
                column: "locationHash2");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_locationHash3",
                table: "sub_places",
                column: "locationHash3");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_parentId",
                table: "sub_places",
                column: "parentId");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_parentPlaceId",
                table: "sub_places",
                column: "parentPlaceId");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_pending",
                table: "sub_places",
                column: "pending");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_placeDetailId",
                table: "sub_places",
                column: "placeDetailId");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_placeId",
                table: "sub_places",
                column: "placeId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_popular",
                table: "sub_places",
                column: "popular");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_popular_processed_removed_hidden",
                table: "sub_places",
                columns: new[] { "popular", "processed", "removed", "hidden" });

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_processed",
                table: "sub_places",
                column: "processed");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_processed_pending",
                table: "sub_places",
                columns: new[] { "processed", "pending" });

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_provinceId",
                table: "sub_places",
                column: "provinceId");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_quoteCount",
                table: "sub_places",
                column: "quoteCount");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_removed",
                table: "sub_places",
                column: "removed");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_removed_processed_pending",
                table: "sub_places",
                columns: new[] { "removed", "processed", "pending" });

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_removed_processed_pending_bookingCount_quoteCount",
                table: "sub_places",
                columns: new[] { "removed", "processed", "pending", "bookingCount", "quoteCount" });

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_ts",
                table: "sub_places",
                column: "ts")
                .Annotation("Npgsql:IndexMethod", "gin");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_ts2",
                table: "sub_places",
                column: "ts2")
                .Annotation("Npgsql:IndexMethod", "gin");

            migrationBuilder.CreateIndex(
                name: "IX_sub_places_wardId",
                table: "sub_places",
                column: "wardId");

            migrationBuilder.CreateIndex(
                name: "idx_uat_place_migrate_placeid",
                table: "uat_place_migrate",
                column: "placeId");

            migrationBuilder.CreateIndex(
                name: "uat_place_migrate2_placeid_idx",
                table: "uat_place_migrate2",
                column: "placeId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "uat_place_migrate2_unique",
                table: "uat_place_migrate2",
                column: "placeId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ward_new_code_idx",
                table: "ward_new",
                column: "code");

            migrationBuilder.CreateIndex(
                name: "ward_new_name_idx",
                table: "ward_new",
                column: "name");

            migrationBuilder.CreateIndex(
                name: "ward_new_unique",
                table: "ward_new",
                column: "code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "wards_code_unique",
                table: "wards",
                column: "code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "wards_bridge_wards_new_code_idx",
                table: "wards_bridge",
                column: "wards_new_code");

            migrationBuilder.CreateIndex(
                name: "wards_bridge_wards_old_code_idx",
                table: "wards_bridge",
                column: "wards_old_code");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "__EFMigrationsHistory_ESDbContext");

            migrationBuilder.DropTable(
                name: "ApiKeyPermissions");

            migrationBuilder.DropTable(
                name: "blocked_places");

            migrationBuilder.DropTable(
                name: "booking_histories");

            migrationBuilder.DropTable(
                name: "booking_histories_item");

            migrationBuilder.DropTable(
                name: "booking_place_pairs");

            migrationBuilder.DropTable(
                name: "building_boundary_place");

            migrationBuilder.DropTable(
                name: "countries");

            migrationBuilder.DropTable(
                name: "customers");

            migrationBuilder.DropTable(
                name: "danhmuc");

            migrationBuilder.DropTable(
                name: "default_columns");

            migrationBuilder.DropTable(
                name: "district_boundaries");

            migrationBuilder.DropTable(
                name: "districts");

            migrationBuilder.DropTable(
                name: "els_query_configs");

            migrationBuilder.DropTable(
                name: "es_queue");

            migrationBuilder.DropTable(
                name: "favorite_places");

            migrationBuilder.DropTable(
                name: "gis_province");

            migrationBuilder.DropTable(
                name: "hanoi_boundary");

            migrationBuilder.DropTable(
                name: "hidden_places");

            migrationBuilder.DropTable(
                name: "job_config");

            migrationBuilder.DropTable(
                name: "job_config_place");

            migrationBuilder.DropTable(
                name: "job_last_update");

            migrationBuilder.DropTable(
                name: "jwt_clients");

            migrationBuilder.DropTable(
                name: "migration_checkpoints");

            migrationBuilder.DropTable(
                name: "osm2pgsql_properties");

            migrationBuilder.DropTable(
                name: "place_approve");

            migrationBuilder.DropTable(
                name: "place_change_history");

            migrationBuilder.DropTable(
                name: "place_tags");

            migrationBuilder.DropTable(
                name: "place_uat_up_live");

            migrationBuilder.DropTable(
                name: "placechangehistory");

            migrationBuilder.DropTable(
                name: "places_details");

            migrationBuilder.DropTable(
                name: "places_queue");

            migrationBuilder.DropTable(
                name: "places_remove");

            migrationBuilder.DropTable(
                name: "places_scan_history");

            migrationBuilder.DropTable(
                name: "places_scan_result");

            migrationBuilder.DropTable(
                name: "places_uat_bak");

            migrationBuilder.DropTable(
                name: "placesdataes");

            migrationBuilder.DropTable(
                name: "planet_osm_line");

            migrationBuilder.DropTable(
                name: "planet_osm_nodes");

            migrationBuilder.DropTable(
                name: "planet_osm_point");

            migrationBuilder.DropTable(
                name: "planet_osm_polygon");

            migrationBuilder.DropTable(
                name: "planet_osm_rels");

            migrationBuilder.DropTable(
                name: "planet_osm_roads");

            migrationBuilder.DropTable(
                name: "planet_osm_ways");

            migrationBuilder.DropTable(
                name: "province_boundaries");

            migrationBuilder.DropTable(
                name: "provinces_bridge");

            migrationBuilder.DropTable(
                name: "sheet2");

            migrationBuilder.DropTable(
                name: "sheet3");

            migrationBuilder.DropTable(
                name: "sub_places");

            migrationBuilder.DropTable(
                name: "subplace_change_histories");

            migrationBuilder.DropTable(
                name: "sys_no");

            migrationBuilder.DropTable(
                name: "trusted_places");

            migrationBuilder.DropTable(
                name: "uat_place_migrate");

            migrationBuilder.DropTable(
                name: "uat_place_migrate2");

            migrationBuilder.DropTable(
                name: "user_booking_histories");

            migrationBuilder.DropTable(
                name: "ward_boundaries");

            migrationBuilder.DropTable(
                name: "wards_bridge");

            migrationBuilder.DropTable(
                name: "ApiKeys");

            migrationBuilder.DropTable(
                name: "building_boundary");

            migrationBuilder.DropTable(
                name: "provinces");

            migrationBuilder.DropTable(
                name: "provinces_new");

            migrationBuilder.DropTable(
                name: "places");

            migrationBuilder.DropTable(
                name: "ward_new");

            migrationBuilder.DropTable(
                name: "wards");

            migrationBuilder.DropSequence(
                name: "countries_id_seq");

            migrationBuilder.DropSequence(
                name: "hibernate_sequence");

            migrationBuilder.DropSequence(
                name: "public.user_booking_histories_SEQ");

            migrationBuilder.DropSequence(
                name: "user_booking_histories_seq");

            migrationBuilder.DropSequence(
                name: "user_booking_histories_SEQ");
        }
    }
}
