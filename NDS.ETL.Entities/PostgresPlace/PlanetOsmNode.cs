using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("planet_osm_nodes")]
public partial class PlanetOsmNode
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("lat")]
    public int Lat { get; set; }

    [Column("lon")]
    public int Lon { get; set; }

    [Column("tags", TypeName = "jsonb")]
    public string? Tags { get; set; }
}
