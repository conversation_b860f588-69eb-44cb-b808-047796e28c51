using System.ComponentModel.DataAnnotations;

namespace NDS.ETL.BackgroundJobs.Configuration;

/// <summary>
/// Configuration settings for address migration process
/// </summary>
public class AddressMigrationConfig
{
    /// <summary>
    /// Configuration section name in appsettings.json
    /// </summary>
    public const string SectionName = "AddressMigration";

    /// <summary>
    /// Number of records to process in each batch
    /// Default: 500, Range: 100-1000
    /// </summary>
    [Range(100, 1000)]
    public int BatchSize { get; set; } = 500;

    /// <summary>
    /// Maximum number of retry attempts for failed operations
    /// Default: 3, Range: 1-10
    /// </summary>
    [Range(1, 10)]
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Base delay in milliseconds for exponential backoff retry strategy
    /// Default: 1000ms (1 second)
    /// </summary>
    [Range(100, 10000)]
    public int BaseRetryDelayMs { get; set; } = 1000;

    /// <summary>
    /// Maximum delay in milliseconds for exponential backoff
    /// Default: 30000ms (30 seconds)
    /// </summary>
    [Range(1000, 300000)]
    public int MaxRetryDelayMs { get; set; } = 30000;

    /// <summary>
    /// Delay in milliseconds between processing batches to prevent API overload
    /// Default: 100ms
    /// </summary>
    [Range(0, 5000)]
    public int BatchDelayMs { get; set; } = 100;

    /// <summary>
    /// Maximum number of concurrent batch processing tasks
    /// Default: 2, Range: 1-10
    /// </summary>
    [Range(1, 10)]
    public int MaxConcurrency { get; set; } = 2;

    /// <summary>
    /// Command timeout in seconds for database operations
    /// Default: 300 seconds (5 minutes)
    /// </summary>
    [Range(30, 3600)]
    public int CommandTimeoutSeconds { get; set; } = 300;

    /// <summary>
    /// Whether to enable detailed logging for migration process
    /// Default: true
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = true;

    /// <summary>
    /// Whether to validate migrated data after processing
    /// Default: true
    /// </summary>
    public bool EnableDataValidation { get; set; } = true;

    /// <summary>
    /// Checkpoint frequency - save progress every N batches
    /// Default: 10
    /// </summary>
    [Range(1, 100)]
    public int CheckpointFrequency { get; set; } = 10;

    /// <summary>
    /// Whether to resume from last checkpoint on restart
    /// Default: true
    /// </summary>
    public bool ResumeFromCheckpoint { get; set; } = true;

    /// <summary>
    /// Migration job identifier for tracking and resumption
    /// </summary>
    public string MigrationJobId { get; set; } = "AddressMigration_v1";

    /// <summary>
    /// Whether the migration job is enabled
    /// Default: false (must be explicitly enabled)
    /// </summary>
    public bool Enabled { get; set; } = false;

    /// <summary>
    /// Cron expression for scheduled migration execution
    /// Default: Run once at 2 AM daily
    /// </summary>
    public string CronExpression { get; set; } = "0 2 * * *";

    /// <summary>
    /// Whether to run the migration immediately when the application starts
    /// Default: false
    /// </summary>
    public bool RunImmediately { get; set; } = false;
}
