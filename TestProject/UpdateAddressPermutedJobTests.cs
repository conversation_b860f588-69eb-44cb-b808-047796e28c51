using Elastic.Clients.Elasticsearch;
using Elastic.Transport;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.PlaceSync;
using System.Diagnostics;

namespace TestProject;

/// <summary>
/// Class test để debug job UpdateAddressPermutedJob
/// </summary>
[TestFixture]
public class UpdateAddressPermutedJobTests
{
    private ElasticsearchClient _elasticClient;
    private Mock<ILogger<UpdateAddressPermutedJob>> _loggerMock;
    private IConfiguration _configuration;
    private UpdateAddressPermutedJob _job;
    private string _testIndex = "place-uat2";

    // Thông tin xác thực ElasticSearch (cấu hình theo môi trường cụ thể)
    // UAT-SIT (thay đổi theo môi trường cần test)
    private readonly string _elasticSearchUrl = "http://localhost:8200";
    private readonly string _elasticSearchUsername = "elastic";
    private readonly string _elasticSearchPassword = "";

    /// <summary>
    /// Thiết lập một lần trước khi chạy các test case
    /// </summary>
    [OneTimeSetUp]
    public void OneTimeSetup()
    {
        // Thiết lập kết nối ElasticSearch dùng cho test với Basic Authentication
        var settings = new ElasticsearchClientSettings(new Uri(_elasticSearchUrl))
            .DefaultIndex(_testIndex)
            // Thêm Basic Authentication
            .Authentication(new BasicAuthentication(_elasticSearchUsername, _elasticSearchPassword))
            // Bỏ qua xác thực SSL nếu sử dụng chứng chỉ tự ký
            .ServerCertificateValidationCallback((sender, cert, chain, errors) => true)
            // Cấu hình debug mode cho việc kiểm tra lỗi
            .EnableDebugMode()
            .DisableDirectStreaming()
            .RequestTimeout(TimeSpan.FromMinutes(2));

        _elasticClient = new ElasticsearchClient(settings);
        
        // Tạo mock logger
        _loggerMock = new Mock<ILogger<UpdateAddressPermutedJob>>();
        
        // Tạo cấu hình từ appsettings.json và thêm cấu hình cho job
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json")
            .Build();
            
        // Thêm cấu hình cho ElasticSearchConfig
        var inMemorySettings = new Dictionary<string, string> {
            {$"ElasticSearchConfig:Index:0", _testIndex},
            {$"JobUpdateAddressPermuted:Status", "Active"},
            {$"JobUpdateAddressPermuted:Cron", "*/5 * * * *"},
            {$"JobUpdateAddressPermuted:BatchSize", "1000"},
            {$"JobUpdateAddressPermuted:Retry", "3"}
        };

        _configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: true)
            .AddInMemoryCollection(inMemorySettings)
            .Build();
            
        // Khởi tạo job
        _job = new UpdateAddressPermutedJob(_configuration, _loggerMock.Object, _elasticClient);
    }

    /// <summary>
    /// Thiết lập trước mỗi test case
    /// </summary>
    [SetUp]
    public async Task Setup()
    {
        // Kiểm tra kết nối elasticsearch trước mỗi test
        var pingResponse = await _elasticClient.PingAsync();
        if (!pingResponse.IsValidResponse)
        {
            Assert.Fail("Không thể kết nối đến Elasticsearch. Kiểm tra lại cấu hình kết nối.");
        }
    }
    
    /// <summary>
    /// Test job cập nhật AddressPermuted
    /// </summary>
    [Test]
    public async Task TestUpdateAddressPermutedJob()
    {
        // Chạy job và kiểm tra kết quả
        var stopwatch = Stopwatch.StartNew();
        
        // Chạy job với CancellationToken
        using var cts = new CancellationTokenSource(TimeSpan.FromMinutes(5)); // Timeout sau 5 phút
        await _job.DoWorkAsync(cts.Token);
        
        stopwatch.Stop();

        long documentId = 17913188;
        // Kiểm tra các documents đã được cập nhật AddressPermuted
        var response = await _elasticClient.SearchAsync<PlacesEs>(s => s
            .Index(_testIndex)
            .Size(100)
            .Query(q => q
                .Bool(b => b
                    .Must(m => m
                        //.Exists(e => e
                        //    .Field(f => f.AddressPermuted)
                        //)
                        .Term(t => t
                            .Field(f => f.Id)
                            .Value(documentId)
                        )
                    )
                )
            ), cts.Token);
        
        Console.WriteLine($"Job chạy trong {stopwatch.ElapsedMilliseconds}ms, tìm thấy {response.Hits.Count} documents có AddressPermuted");
        
        Assert.That(response.IsValidResponse, Is.True, $"Search không thành công: {response.DebugInformation}");
        Assert.That(response.Hits.Count, Is.GreaterThan(0), "Không tìm thấy documents nào có AddressPermuted");
        
        // In ra một số documents để kiểm tra
        foreach (var hit in response.Hits.Take(5))
        {
            Console.WriteLine($"ID: {hit.Source.Id}, Name: {hit.Source.Name}");
            Console.WriteLine($"MasterAddress: {hit.Source.MasterAddress}");
            Console.WriteLine($"AddressPermuted (length: {hit.Source.AddressPermuted?.Count ?? 0}): {hit.Source.AddressPermuted?.FirstOrDefault()}");
            Console.WriteLine("-------------------------------------");
        }
    }
    
    /// <summary>
    /// Xóa index test sau khi hoàn thành tất cả các test
    /// </summary>
    [OneTimeTearDown]
    public async Task TearDown()
    {
        // Uncomment dòng dưới đây nếu muốn xóa index test sau khi hoàn thành
        // await _elasticClient.Indices.DeleteAsync(_testIndex);
    }
    /// <summary>
    /// Test tìm kiếm document theo ID cụ thể
    /// </summary>
    [Test]
    public async Task TestSearchDocumentById()
    {
        // ID cụ thể cần tìm kiếm
        long documentId = 17913188;

        // Tạo query tìm kiếm theo ID
        var response = await _elasticClient.SearchAsync<PlacesEs>(s => s
            .Index(_testIndex)
            .Query(q => q
                .Bool(b => b
                    .Must(m => m
                        .Term(t => t
                            .Field(f => f.Id)
                            .Value(documentId)
                        )
                    )
                )
            ), CancellationToken.None);

        // Kiểm tra kết quả và hiển thị thông tin
        Console.WriteLine($"Search status: {(response.IsValidResponse ? "Success" : "Failed")}");
        if (!response.IsValidResponse)
        {
            Console.WriteLine($"Error: {response.DebugInformation}");
            Assert.Fail($"Search không thành công: {response.DebugInformation}");
            return;
        }

        Console.WriteLine($"Tìm thấy {response.Hits.Count} kết quả cho ID: {documentId}");

        if (response.Hits.Count > 0)
        {
            var document = response.Hits.First().Source;
            Console.WriteLine("===== DOCUMENT DETAILS =====");
            Console.WriteLine($"ID: {document.Id}");
            Console.WriteLine($"Name: {document.Name}");
            Console.WriteLine($"FullAddress: {document.FullAddress}");
            Console.WriteLine($"MasterAddress: {document.MasterAddress}");
            Console.WriteLine($"AddressPermuted count: {document.AddressPermuted?.Count ?? 0}");

            if (document.AddressPermuted?.Any() == true)
            {
                Console.WriteLine("Sample AddressPermuted entries:");
                foreach (var addr in document.AddressPermuted)
                {
                    Console.WriteLine($"  - {addr}");
                }
                //if (document.AddressPermuted.Count > 3)
                //{
                //    Console.WriteLine($"  ... and {document.AddressPermuted.Count - 3} more");
                //}
            }

            Console.WriteLine($"Location: Lat={document.Location?.Lat}, Lon={document.Location?.Lon}");
            Console.WriteLine($"Keywords: {document.Keywords}");
            Console.WriteLine($"SubPlaces count: {document.SubPlaces?.Count ?? 0}");

            Assert.That(document.Id, Is.EqualTo(documentId), "Document có ID không khớp với ID cần tìm");
        }
        else
        {
            Console.WriteLine($"Không tìm thấy document với ID: {documentId}");
        }
    }
    
} 