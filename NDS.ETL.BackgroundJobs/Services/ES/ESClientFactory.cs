using Elastic.Clients.Elasticsearch;
using Elastic.Transport;
using NDS.ETL.Infrastructure.Utils;

namespace NDS.ETL.BackgroundJobs.Services.ES
{
    /// <summary>
    /// Factory để tạo và cấu hình ElasticsearchClient
    /// </summary>
    public class ESClientFactory : IESClientFactory
    {
        private readonly ElasticsearchClient _elasticClient;
        private readonly string _placeIdxName;
        private readonly string _placePairIdxName;
        private readonly string _placeFavIdxName;
        private readonly ILogger<ESClientFactory> _logger;

        /// <summary>
        /// Khởi tạo factory với cấu hình từ DI
        /// </summary>
        public ESClientFactory(
            ElasticSearchConfig elasticSearchConfig,
            ILogger<ESClientFactory> logger)
        {
            _logger = logger;

            // Lấy thông tin index từ config
            _placeIdxName = elasticSearchConfig.Index.FirstOrDefault(x => x.StartsWith("place")) ?? "place";
            _placePairIdxName = elasticSearchConfig.Index.FirstOrDefault(x => x.StartsWith("place-pair")) ?? "place-pair";
            _placeFavIdxName = elasticSearchConfig.Index.FirstOrDefault(x => x.StartsWith("fav-place")) ?? "fav-place";

            var url = elasticSearchConfig.Url.FirstOrDefault() ?? "http://localhost:9200";
            var username = elasticSearchConfig.UserName ?? "elastic";
            var password = elasticSearchConfig.Password ?? string.Empty;

            _logger.LogInformation("Khởi tạo ElasticsearchClient với URL: {Url}, Index: {Index}", url, _placeIdxName);

            try
            {
                // Khởi tạo Elasticsearch client
                var settings = new ElasticsearchClientSettings(new Uri(url))
                    .DefaultIndex(_placeIdxName)
                    .Authentication(new BasicAuthentication(username, password))
                    .ServerCertificateValidationCallback((sender, cert, chain, errors) => true)
                    .EnableDebugMode()
                    .DisableDirectStreaming()
                    .RequestTimeout(TimeSpan.FromSeconds(30));

                _elasticClient = new ElasticsearchClient(settings);
                _logger.LogInformation("Khởi tạo ElasticsearchClient thành công");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi khởi tạo ElasticsearchClient");
                throw;
            }
        }

        /// <summary>
        /// Trả về ElasticsearchClient đã được cấu hình
        /// </summary>
        public ElasticsearchClient GetClient() => _elasticClient;

        /// <summary>
        /// Trả về tên index locations
        /// </summary>
        public string GetPlaceIndexName() => _placeIdxName;

        /// <summary>
        /// Trả về tên index place-pairs
        /// </summary>
        public string GetPlacePairIndexName() => _placePairIdxName;

        /// <summary>
        /// Trả về tên index favorite places
        /// </summary>
        public string GetPlaceFavoriteIndexName() => _placeFavIdxName;
    }
}
