{"PathBase": "/backgroundjob", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore.Hosting.Diagnostics": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft": "Warning", "Hangfire": "Warning", "Microsoft.Hosting.Lifetime": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Warning", "Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker": "Warning"}}, "ConnectionStrings": {"Redis": "*************:6379,*************:6379,*************:6379,password=redisvnpay,abortConnect=false,connectRetry=3,connectTimeout=5000,syncTimeout=5000,keepAlive=180", "PgPlace": "Host=***********;Port=9432;Database=******;Username=******;Password=******;Timeout=600;Command Timeout=10000", "HangfireSchema": "es_backend_log"}, "ElasticSearchConfig": {"Index": ["place", "fav-place", "user-place", "place-pair"], "LogIndex": "place-log-1", "Url": ["http://***********:9200/", "http://***********:9200/", "http://***********:9200/"], "UserName": "elastic", "Password": ""}, "KafkaConfig": {"Disable": "false", "Connection": "***********:9092,***********:9092,***********:9092", "GroupSyncPlaces": "sync_es_places_gr", "TopicSyncPlaces": "sync_es_places"}, "JobPgSyncPlace": [{"CronExpression": "* * 31 2 *", "JobId": "InitPlace", "Disable": false, "Immediately": false}, {"CronExpression": "* * 31 2 *", "JobId": "SyncPlaceScanResult", "Disable": false, "Immediately": false}, {"CronExpression": "* * 31 2 *", "JobId": "SyncPlaceScanResult2", "Disable": false, "Immediately": false}, {"CronExpression": "* * 31 2 *", "JobId": "<PERSON>an<PERSON><PERSON><PERSON>", "Disable": false, "Immediately": false}, {"CronExpression": "* * 31 2 *", "JobId": "SyncDataScannedToEs", "Disable": false, "Immediately": false}, {"CronExpression": "*/5 * * * *", "JobId": "SyncFavPlaces", "Disable": false, "Immediately": false}, {"CronExpression": "*/30 * * * *", "JobId": "SyncPlaces", "Disable": false, "Immediately": false}, {"CronExpression": "*/30 * * * *", "JobId": "SyncFavPlace", "Disable": false, "Immediately": false}, {"CronExpression": "*/30 * * * *", "JobId": "SyncPlacePairs", "Disable": false, "Immediately": false}], "AllowedHosts": "*"}