using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Core.Bulk;
using Elastic.Clients.Elasticsearch.Core.Search;
using KnowledgeBase.Core.Extensions;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.Infrastructure.Utils;
using System.Collections.Concurrent;
using System.Diagnostics;
using SourceFilter = Elastic.Clients.Elasticsearch.Core.Search.SourceFilter;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.PlaceSync;

/// <summary>
/// Job để cập nhật trường AddressPermuted trong PlacesEs theo cách bulk 
/// </summary>
public class UpdateAddressPermutedJob : RecurringJobBaseElastic
{
    // <PERSON>ác phụ thuộc
    private readonly ElasticsearchClient _elasticClient;
    private readonly ElasticSearchConfig _indexName;
    private readonly ILogger<UpdateAddressPermutedJob> _logger;

    // Semaphore để kiểm soát đồng thời
    private readonly SemaphoreSlim _semaphore;

    // Kh<PERSON>a để bảo vệ biến _lastId và các biến khác khỏi truy cập đồng thời
    private readonly object _syncLock = new();

    // Cấu hình xử lý song song và batch
    private const int MaxConcurrentBatches = 8; // Giảm từ 12 xuống 8 để giảm áp lực lên ES
    private const int BatchSize = 10_000;
    private const int BulkSize = 3000; // Giảm từ 5000 xuống 3000 để giảm áp lực lên ES
    private const int MaxConcurrentUpdates = 6; // Giảm từ 12 xuống 6 
    private const int MaxRetries = 5;
    private const int CheckpointInterval = 5; // Lưu checkpoint sau mỗi 5 batch

    // Thống kê hiệu suất
    private long _totalProcessed;
    private long _uniqueProcessed;
    private int _batchesProcessed; // Thêm biến đếm số batch đã xử lý
    private readonly Stopwatch _jobTimer = new();
    private DateTime _lastCheckpointTime = DateTime.MinValue; // Thời điểm checkpoint cuối cùng

    // ID cuối cùng đã xử lý
    private long _lastId = 0;

    // Cấu hình checkpoint
    private const string CheckpointIndex = "job-checkpoints";
    private string CheckpointDocumentId => $"{JobId}-checkpoint";

    // Tránh lặp vô hạn
    private int _consecutiveEmptyBatches = 0;
    private const int MaxConsecutiveEmptyBatches = 3;
    private long _totalRecords = 0;

    // Biến theo dõi trạng thái
    private string _jobStatus = "Initializing";
    private readonly ConcurrentDictionary<long, (DateTime StartTime, int Count)> _processingBatches = new();

    public UpdateAddressPermutedJob(
        IConfiguration configuration,
        ILogger<UpdateAddressPermutedJob> logger,
        ElasticsearchClient elasticClient)
        : base(configuration, logger, elasticClient, null)
    {
        _indexName = configuration.Bind<ElasticSearchConfig>(nameof(ElasticSearchConfig));
        _elasticClient = elasticClient;
        _semaphore = new SemaphoreSlim(MaxConcurrentBatches);
        _logger = logger;
    }

    // Job identifier
    public override string JobId => "UpdateAddressPermuted";

    // Configuration section
    public override string ConfigSection => "JobPgSyncPlace";

    /// <summary>
    /// Phương thức chính xử lý công việc của job
    /// </summary>
    public override async Task DoWorkAsync(CancellationToken stoppingToken)
    {
        _jobTimer.Start();
        _logger.LogInformation($"{JobId} has started");
        _jobStatus = "Running";

        try
        {
            // Chuẩn bị index checkpoint và lấy trạng thái từ lần chạy trước
            await EnsureCheckpointIndexExistsAsync();
            _lastId = await GetLastProcessedIdAsync();
            _logger.LogInformation($"Starting job from last ID: {_lastId}");

            // Lấy tổng số bản ghi để tính toán tiến độ
            await GetTotalRecordCountAsync();

            // Bắt đầu xử lý tuần tự các batch
            ScrollId? scrollId = null;
            bool hasMoreData = true;

            // Vòng lặp chính để lấy và xử lý các batch
            while (!stoppingToken.IsCancellationRequested && hasMoreData)
            {
                try
                {
                    // Lấy batch tiếp theo từ Elasticsearch
                    var batch = await FetchNextBatchAsync(scrollId, stoppingToken);

                    // Xử lý trường hợp batch rỗng
                    if (batch.Count == 0)
                    {
                        _logger.LogInformation("Reached the end of data or scroll expired. Reinitializing scroll from last ID.");
                        // Reset scrollId để bắt đầu lại từ vị trí cuối cùng
                        scrollId = null;

                        // Tránh vòng lặp vô hạn nếu liên tục không có dữ liệu
                        _consecutiveEmptyBatches++;
                        if (_consecutiveEmptyBatches >= MaxConsecutiveEmptyBatches)
                        {
                            _logger.LogWarning($"Received {MaxConsecutiveEmptyBatches} empty batches in a row. Stopping job.");
                            hasMoreData = false;
                            break;
                        }

                        await Task.Delay(1000, stoppingToken); // Chờ 1 giây trước khi thử lại
                        continue;
                    }

                    // Reset biến đếm batch rỗng liên tiếp
                    _consecutiveEmptyBatches = 0;

                    var maxId = batch.Max(x => x.Id);

                    // Cập nhật số lượng bản ghi đã xử lý
                    Interlocked.Add(ref _uniqueProcessed, batch.Count);
                    Interlocked.Add(ref _totalProcessed, batch.Count);

                    // Log tiến độ
                    LogProgress();

                    // Xử lý batch
                    await ProcessBatchAsync(batch, stoppingToken);

                    // Không cần cập nhật scrollId ở đây vì đã được cập nhật trong FetchNextBatchAsync
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in main processing loop. Will try to continue.");
                    scrollId = null; // Reset scrollId để bắt đầu lại
                    await Task.Delay(5000, stoppingToken); // Chờ 5 giây trước khi thử lại
                }
            }

            _jobTimer.Stop();
            _jobStatus = "Completed";

            // Báo cáo kết quả cuối cùng
            LogFinalResults();

            // Lưu checkpoint hoàn thành
            await SaveCheckpointAsync("Completed");
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Job was cancelled by user");
            _jobStatus = "Cancelled";
            await SaveCheckpointAsync("Cancelled");
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error during job processing");
            _jobStatus = "Error";
            await SaveCheckpointAsync("Error");
            throw;
        }
    }

    /// <summary>
    /// Lấy tổng số bản ghi trong index
    /// </summary>
    private async Task GetTotalRecordCountAsync()
    {
        try
        {
            // Đếm bản ghi còn lại cần xử lý (id > _lastId)
            var countResponse = await _elasticClient.CountAsync<PlacesEs>(c => c
                .Indices(_indexName.Index[0])
                .Query(q => q
                    .Range(r => r
                        .NumberRange(nr => nr
                            .Field(f => f.Id)
                            .Gt(_lastId)
                        )
                    )
                ));

            if (countResponse.IsValidResponse)
            {
                _totalRecords = countResponse.Count;
                _logger.LogInformation($"Total records remaining to process (ID > {_lastId}): {_totalRecords:N0}");

                // Nếu không còn bản ghi nào, có thể đã hoàn thành
                if (_totalRecords == 0)
                {
                    // Kiểm tra tổng số bản ghi trong index
                    var totalCountResponse = await _elasticClient.CountAsync<PlacesEs>(c => c.Indices(_indexName.Index[0]));
                    if (totalCountResponse.IsValidResponse)
                    {
                        var totalCount = totalCountResponse.Count;
                        _logger.LogInformation($"Total records in index: {totalCount:N0}");

                        // Nếu có bản ghi trong index nhưng không còn bản ghi cần xử lý, đặt lại _lastId = 0
                        if (totalCount > 0)
                        {
                            _logger.LogInformation("All records processed. Resetting lastId to 0 for next run.");
                            _lastId = 0;
                            _totalRecords = totalCount; // Cập nhật lại tổng số bản ghi
                        }
                    }
                }
            }
            else
            {
                _logger.LogWarning($"Cannot retrieve total records: {countResponse.DebugInformation}");
                _totalRecords = 0;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving total records");
            _totalRecords = 0;
        }
    }

    /// <summary>
    /// Lấy batch tiếp theo từ Elasticsearch
    /// </summary>
    private async Task<List<PlacesEs>> FetchNextBatchAsync(ScrollId? scrollId, CancellationToken stoppingToken)
    {
        var batchTimer = Stopwatch.StartNew();

        try
        {
            List<PlacesEs> batch;

            if (string.IsNullOrEmpty(scrollId?.Id))
            {
                // Khởi tạo scroll
                var response = await _elasticClient.SearchAsync<PlacesEs>(s => s
                     .Index(_indexName.Index[0])
                     .Size(BatchSize)
                     .Query(q => q
                         .Bool(b => b
                             .Must(m => m
                                 .Range(r => r
                                     .NumberRange(f => f.Field(x => x.Id).Gt(_lastId))
                                 )
                                 
                                 //// Thêm điều kiện nested
                                 //,m => m.Nested(n => n
                                 //    .Path(p => p.SubPlaces)
                                 //    .Query(nq => nq
                                 //        .Exists(e => e
                                 //                .Field("subPlaces.name")
                                 //        )
                                 //    )
                                 //)
                             )
                         )
                     )
                     .Sort(s => s
                         .Field(f => f.Id, new FieldSort { Order = SortOrder.Asc })
                     )
                     .Source(new SourceConfig(new SourceFilter
                     {
                         Includes = new[] { "id", "name", "fullAddress", "masterAddress", "keywords", "subPlaces" }
                     }))
                     .Scroll("2m"), // Thời gian giữ scroll
                     stoppingToken);

                if (!response.IsValidResponse)
                {
                    _logger.LogError($"Error fetch batch: {response.DebugInformation}");
                    return new List<PlacesEs>();
                }

                batch = response.Documents.ToList();
                scrollId = response.ScrollId; // Lưu scrollId mới

                _logger.LogInformation($"Initial scroll created. Scroll ID: {response.ScrollId}");
            }
            else
            {
                try
                {
                    // Lấy batch tiếp theo từ scroll
                    var response = await _elasticClient.ScrollAsync<PlacesEs>(new ScrollRequest { ScrollId = scrollId, Scroll = "2m" }, stoppingToken);

                    if (!response.IsValidResponse)
                    {
                        _logger.LogError($"Error fetch batch: {response.DebugInformation}");
                        // Nếu lỗi với scroll ID, thử lại từ đầu
                        _logger.LogWarning("Invalid scroll ID, will restart scrolling from last processed ID");
                        return new List<PlacesEs>();
                    }

                    batch = response.Documents.ToList();
                    scrollId = response.ScrollId; // Cập nhật scrollId mới

                    if (batch.Count > 0)
                    {
                        _logger.LogInformation($"Fetched {batch.Count} records in {batchTimer.ElapsedMilliseconds}ms. Scroll ID: {response.ScrollId}");
                    }
                    else
                    {
                        _logger.LogInformation("No more records found in scroll. Scroll completed.");
                    }
                }
                catch (Exception ex) when (ex.Message.Contains("Cannot parse scroll id"))
                {
                    _logger.LogWarning($"Invalid scroll ID format. Restarting scroll from last processed ID: {_lastId}");
                    return new List<PlacesEs>();
                }
            }

            batchTimer.Stop();

            return batch;
        }
        catch (Exception ex)
        {
            batchTimer.Stop();
            _logger.LogError(ex, $"Error fetching batch with scroll ID {scrollId}: {ex.Message}. Time: {batchTimer.ElapsedMilliseconds}ms");

            // Đợi 2 giây trước khi trả về để tránh vòng lặp nhanh nếu lỗi xảy ra liên tục
            await Task.Delay(2000, stoppingToken);
            return new List<PlacesEs>();
        }
    }

    /// <summary>
    /// Xử lý một batch các địa điểm
    /// </summary>
    private async Task ProcessBatchAsync(List<PlacesEs> batch, CancellationToken stoppingToken)
    {
        var batchTimer = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation($"Processing batch with {batch.Count} records");
            var batchMaxId = batch.Max(x => x.Id);

            // Tính toán AddressPermuted song song với hiệu suất cao
            var processedItems = await CalculateAddressPermutedAsync(batch, stoppingToken);

            if (processedItems.Count == 0)
            {
                _logger.LogWarning("No data to update after processing");

                // Vẫn cập nhật lastId để tránh lặp vô hạn
                UpdateLastId(batchMaxId);
                return;
            }

            // Chia thành các nhóm bulk nhỏ hơn để xử lý song song
            var itemsList = processedItems.ToList();
            var bulks = new List<List<UpdateDoc>>();

            for (int i = 0; i < itemsList.Count; i += BulkSize)
            {
                bulks.Add(itemsList.Skip(i).Take(BulkSize).ToList());
            }

            _logger.LogInformation($"Divided into {bulks.Count} bulks, each with approximately {BulkSize} records");

            // Xử lý các bulk updates song song với số lượng concurrent được kiểm soát
            if (bulks.Count > 0)
            {
                var updateSuccess = await SendBulkUpdatesAsync(bulks, stoppingToken);

                if (!updateSuccess)
                {
                    _logger.LogWarning("Errors occurred while updating some bulks. Check logs for details.");
                }
            }

            // Chỉ cập nhật lastId sau khi xử lý thành công
            UpdateLastId(batchMaxId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi xử lý batch");
        }
        finally
        {
            batchTimer.Stop();
            _logger.LogInformation($"Completed batch in {batchTimer.Elapsed.TotalSeconds:F1} seconds");
        }
    }

    /// <summary>
    /// Tính toán AddressPermuted cho tất cả địa điểm trong batch
    /// </summary>
    private async Task<List<UpdateDoc>> CalculateAddressPermutedAsync(List<PlacesEs> batch, CancellationToken stoppingToken)
    {
        var processedItems = new ConcurrentBag<UpdateDoc>();

        try
        {
            // Tính toán song song với số thread tối ưu
            await Parallel.ForEachAsync(batch,
                new ParallelOptions
                {
                    MaxDegreeOfParallelism = Math.Min(Environment.ProcessorCount, 16),
                    CancellationToken = stoppingToken
                },
                async (place, ct) =>
                {
                    try
                    {
                        var updateData = CalculateAddressPermutedData(place);
                        processedItems.Add(updateData);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Error calculating AddressPermuted for ID {place.Id}");
                    }
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating AddressPermuted in parallel");
        }

        return processedItems.ToList();
    }

    /// <summary>
    /// Gửi tất cả các bulk update với xử lý song song
    /// </summary>
    private async Task<bool> SendBulkUpdatesAsync(List<List<UpdateDoc>> bulks, CancellationToken stoppingToken)
    {
        try
        {
            using var updateSemaphore = new SemaphoreSlim(MaxConcurrentUpdates);
            var updateTasks = new List<Task<bool>>();

            foreach (var bulkItems in bulks)
            {
                await updateSemaphore.WaitAsync(stoppingToken);

                updateTasks.Add(Task.Run(async () =>
                {
                    try
                    {
                        return await SendBulkUpdateAsync(bulkItems, stoppingToken);
                    }
                    finally
                    {
                        updateSemaphore.Release();
                    }
                }, stoppingToken));
            }

            var results = await Task.WhenAll(updateTasks);
            return results.All(r => r);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error managing bulk updates");
            return false;
        }
    }

    /// <summary>
    /// Cập nhật lastId an toàn và lưu checkpoint
    /// </summary>
    private void UpdateLastId(long newLastId)
    {
        lock (_syncLock)
        {
            if (newLastId > _lastId)
            {
                _lastId = newLastId;
                _logger.LogInformation($"Updated lastId to {_lastId}");
            }
        }
    }

    /// <summary>
    /// Gửi bulk update request đến Elasticsearch
    /// </summary>
    private async Task<bool> SendBulkUpdateAsync(List<UpdateDoc> updates, CancellationToken stoppingToken)
    {
        var updateTimer = Stopwatch.StartNew();
        var retryCount = 0;
        var successfulIds = new HashSet<long>();
        bool fullySuccessful = false;

        _logger.LogInformation($"Starting bulk update for {updates.Count} records");

        while (retryCount < MaxRetries && !stoppingToken.IsCancellationRequested)
        {
            try
            {
                // Loại bỏ các documents đã xử lý thành công
                var pendingUpdates = updates
                    .Where(u => !successfulIds.Contains(u.Id))
                    .ToList();

                if (pendingUpdates.Count == 0)
                {
                    _logger.LogInformation("All records have been successfully updated");
                    fullySuccessful = true;
                    break;
                }

                if (retryCount > 0)
                {
                    _logger.LogInformation($"Retrying {pendingUpdates.Count}/{updates.Count} remaining records (attempt {retryCount + 1}/{MaxRetries})");
                }

                // Tạo bulk request với cấu hình tối ưu
                var bulkRequest = new BulkRequest(_indexName.Index[0])
                {
                    Operations = new List<IBulkOperation>(),
                    Refresh = Refresh.False, // Không cần refresh ngay lập tức
                    RequireAlias = false,
                    WaitForActiveShards = "1", // Chỉ cần 1 shard để xác nhận
                    Timeout = "60s" // Thêm timeout để tránh treo
                };

                // Thêm các operations cần xử lý
                foreach (var update in pendingUpdates)
                {
                    bulkRequest.Operations.Add(new BulkUpdateOperation<PlacesEs, object>(update.Id)
                    {
                        Doc = new
                        {
                            addressPermuted = update.AddressPermuted,
                        },
                        DocAsUpsert = false,
                        RetryOnConflict = 3
                    });
                }

                // Gửi bulk request
                var response = await _elasticClient.BulkAsync(bulkRequest, stoppingToken);

                updateTimer.Stop();

                if (response.IsValidResponse)
                {
                    var successCount = response.Items.Count(i => i.IsValid);
                    var failedCount = response.Items.Count - successCount;

                    // Đánh dấu các documents đã xử lý thành công
                    foreach (var item in response.Items.Where(i => i.IsValid))
                    {
                        if (long.TryParse(item.Id, out long docId))
                        {
                            successfulIds.Add(docId);
                        }
                    }

                    _logger.LogInformation($"Updated {successCount}/{response.Items.Count} records in {updateTimer.ElapsedMilliseconds}ms");

                    // Nếu tất cả đều thành công, thoát vòng lặp
                    if (failedCount == 0)
                    {
                        fullySuccessful = true;
                        break;
                    }

                    // Log chi tiết lỗi nếu có
                    if (failedCount > 0)
                    {
                        foreach (var item in response.Items.Where(i => !i.IsValid))
                        {
                            _logger.LogWarning($"Error updating ID {item.Id}: {item.Error?.Reason}");
                        }

                        // Thử lại nếu còn cơ hội
                        if (retryCount < MaxRetries - 1)
                        {
                            retryCount++;
                            // Tăng thời gian chờ theo cấp số mũ
                            var delay = TimeSpan.FromSeconds(Math.Pow(2, retryCount));
                            _logger.LogInformation($"Waiting {delay.TotalSeconds} seconds before retrying");
                            await Task.Delay(delay, stoppingToken);
                            continue;
                        }
                    }
                }
                else
                {
                    _logger.LogError($"Bulk update failed: {response.DebugInformation}");

                    // Chi tiết hơn về lỗi
                    if (response.ApiCallDetails.OriginalException != null)
                    {
                        _logger.LogError($"Error cause: {response.ApiCallDetails.OriginalException.Message}");
                    }

                    retryCount++;

                    if (retryCount < MaxRetries)
                    {
                        // Tăng thời gian chờ theo cấp số mũ
                        var delay = TimeSpan.FromSeconds(Math.Pow(2, retryCount));
                        _logger.LogInformation($"Waiting {delay.TotalSeconds} seconds before retrying");
                        await Task.Delay(delay, stoppingToken);
                    }
                }
            }
            catch (Exception ex)
            {
                updateTimer.Stop();
                _logger.LogError(ex, $"Error performing bulk update (attempt {retryCount + 1})");
                retryCount++;

                if (retryCount < MaxRetries && !stoppingToken.IsCancellationRequested)
                {
                    // Tăng thời gian chờ theo cấp số mũ
                    var delay = TimeSpan.FromSeconds(Math.Pow(2, retryCount));
                    await Task.Delay(delay, stoppingToken);
                }
            }
        }

        var finalSuccess = successfulIds.Count == updates.Count;
        _logger.LogInformation($"Completed bulk update: {successfulIds.Count}/{updates.Count} records successful. Time: {updateTimer.Elapsed.TotalMilliseconds}ms");

        return finalSuccess;
    }

    // Class lưu trữ thông tin cập nhật
    private class UpdateDoc
    {
        public long Id { get; set; }
        public List<string> AddressPermuted { get; set; }
        public string MasterAddress { get; set; }
        public List<SubPlacesEs> SubPlaces { get; set; }
    }

    /// <summary>
    /// Tính toán AddressPermuted cho một địa điểm
    /// </summary>
    private UpdateDoc CalculateAddressPermutedData(PlacesEs place)
    {
        try
        {
            place.ProcessKeywords();
            place.CalculateMasterAddressAndPermuted();

            return new UpdateDoc
            {
                Id = place.Id,
                AddressPermuted = place.AddressPermuted,
                MasterAddress = place.MasterAddress,
                SubPlaces = place.SubPlaces
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating AddressPermuted for ID {place.Id}");

            // Trả về đối tượng cơ bản khi có lỗi
            return new UpdateDoc
            {
                Id = place.Id,
                AddressPermuted = new List<string>(),
                MasterAddress = place.FullAddress,
                SubPlaces = place.SubPlaces
            };
        }
    }

    /// <summary>
    /// Đảm bảo index để lưu checkpoint đã được tạo
    /// </summary>
    private async Task EnsureCheckpointIndexExistsAsync()
    {
        try
        {
            var indexExists = await _elasticClient.Indices.ExistsAsync(CheckpointIndex);
            if (!indexExists.Exists)
            {
                _logger.LogInformation($"Created checkpoint index {CheckpointIndex}");

                var createResponse = await _elasticClient.Indices.CreateAsync(CheckpointIndex, c => c
                    .Settings(s => s
                        .NumberOfShards(1)
                        .NumberOfReplicas(1)
                        .RefreshInterval("5s")
                    )
                    .Mappings(m => m
                        .Properties<JobCheckpointEs>(p => p
                            .Keyword(k => k.Id)
                            .LongNumber(l => l.LastProcessedId)
                            .Date(d => d.UpdatedAt)
                            .LongNumber(l => l.TotalProcessed)
                            .LongNumber(l => l.UniqueProcessed)
                            .LongNumber(l => l.TotalRecordsInIndex)
                            .Keyword(k => k.Status)
                            .Text(t => t.AdditionalInfo)
                            .Text(t => t.LastError)
                            .DoubleNumber(n => n.ProcessingSpeed)
                            .DoubleNumber(n => n.ProgressPercentage)
                            .Date(d => d.EstimatedCompletionTime)
                            .IntegerNumber(n => n.BatchesProcessed)
                        )
                    )
                );

                if (!createResponse.IsValidResponse)
                {
                    _logger.LogError($"Cannot create checkpoint index: {createResponse.DebugInformation}");
                }
                else
                {
                    _logger.LogInformation("Checkpoint index created successfully");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating checkpoint index");
        }
    }

    /// <summary>
    /// Lấy ID cuối cùng đã xử lý từ checkpoint
    /// </summary>
    private async Task<long> GetLastProcessedIdAsync()
    {
        try
        {
            var response = await _elasticClient.GetAsync<JobCheckpointEs>(CheckpointDocumentId, g => g.Index(CheckpointIndex));

            if (response.IsValidResponse && response.Found)
            {
                var checkpoint = response.Source;
                _logger.LogInformation($"Checkpoint found: LastId={checkpoint.LastProcessedId}, Status={checkpoint.Status}, Records={checkpoint.UniqueProcessed:N0}, UpdatedAt={checkpoint.UpdatedAt:yyyy-MM-dd HH:mm:ss.fff}");

                // Khôi phục các biến từ checkpoint nếu job đang chạy hoặc gặp lỗi
                if (checkpoint.Status == "Running" || checkpoint.Status == "Error" || checkpoint.Status == "Cancelled")
                {
                    _uniqueProcessed = checkpoint.UniqueProcessed;
                    _totalProcessed = checkpoint.TotalProcessed;
                    _totalRecords = checkpoint.TotalRecordsInIndex > 0 ? checkpoint.TotalRecordsInIndex : _totalRecords;
                    _batchesProcessed = checkpoint.BatchesProcessed;
                    _lastCheckpointTime = checkpoint.UpdatedAt;

                    // Nếu checkpoint quá cũ (hơn 1 ngày), bắt đầu lại từ đầu
                    if ((DateTime.UtcNow - checkpoint.UpdatedAt).TotalDays > 1)
                    {
                        _logger.LogWarning($"Checkpoint too old ({(DateTime.UtcNow - checkpoint.UpdatedAt).TotalHours:F1} hours, last updated at {checkpoint.UpdatedAt:yyyy-MM-dd HH:mm:ss.fff}). Starting over");
                        ResetStats();
                        return 0;
                    }

                    return checkpoint.LastProcessedId;
                }

                // Bắt đầu lại từ đầu nếu job đã hoàn thành
                if (checkpoint.Status == "Completed")
                {
                    _logger.LogInformation("Previous job completed. Starting over (ID=0)");
                    ResetStats();
                    return 0;
                }
            }

            _logger.LogInformation("No checkpoint found, starting from ID=0");
            ResetStats();
            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving checkpoint, starting from ID=0");
            ResetStats();
            return 0;
        }
    }

    /// <summary>
    /// Reset tất cả các biến thống kê
    /// </summary>
    private void ResetStats()
    {
        _totalProcessed = 0;
        _uniqueProcessed = 0;
        _batchesProcessed = 0;
        _consecutiveEmptyBatches = 0;
        _lastCheckpointTime = DateTime.UtcNow;
        _processingBatches.Clear();
        _jobTimer.Reset(); // Reset timer khi bắt đầu lại
    }

    /// <summary>
    /// Lưu checkpoint hiện tại
    /// </summary>
    private async Task SaveCheckpointAsync(string status)
    {
        try
        {
            long currentLastId;
            lock (_syncLock)
            {
                currentLastId = _lastId;
            }

            double processingSpeed = _jobTimer.Elapsed.TotalSeconds > 0
                ? _uniqueProcessed / _jobTimer.Elapsed.TotalSeconds
                : 0;

            double progressPercentage = _totalRecords > 0
                ? (double)_uniqueProcessed / _totalRecords * 100
                : 0;

            DateTime estimatedCompletionTime = DateTime.UtcNow;
            if (_totalRecords > 0 && processingSpeed > 0)
            {
                double recordsRemaining = _totalRecords - _uniqueProcessed;
                if (recordsRemaining > 0)
                {
                    double secondsRemaining = recordsRemaining / processingSpeed;
                    estimatedCompletionTime = DateTime.UtcNow.AddSeconds(secondsRemaining);
                }
            }

            var checkpoint = new JobCheckpointEs
            {
                Id = CheckpointDocumentId,
                LastProcessedId = currentLastId,
                UpdatedAt = DateTime.UtcNow,
                TotalProcessed = _totalProcessed,
                UniqueProcessed = _uniqueProcessed,
                TotalRecordsInIndex = _totalRecords,
                ProcessingSpeed = processingSpeed,
                ProgressPercentage = progressPercentage,
                EstimatedCompletionTime = estimatedCompletionTime,
                Status = status,
                AdditionalInfo = $"Total: {_totalProcessed:N0}, " +
                               $"Unique: {_uniqueProcessed:N0}, " +
                               $"Progress: {progressPercentage:F2}%, " +
                               $"Speed: {processingSpeed:F1} records/sec"
            };

            if (status == "Error")
            {
                checkpoint.LastError = "Job gặp lỗi. Xem logs để biết chi tiết.";
            }

            // Lưu checkpoint với retry
            for (int retry = 0; retry < 3; retry++)
            {
                try
                {
                    var response = await _elasticClient.IndexAsync(checkpoint, i => i
                        .Index(CheckpointIndex)
                        .Id(CheckpointDocumentId)
                        .Refresh(Refresh.WaitFor));

                    if (!response.IsValidResponse && retry < 2)
                    {
                        await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, retry)));
                        continue;
                    }

                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error saving checkpoint (attempt {retry + 1}/3)");

                    if (retry < 2)
                    {
                        await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, retry)));
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving checkpoint");
        }
    }

    /// <summary>
    /// Ghi log tiến độ hiện tại
    /// </summary>
    private void LogProgress()
    {
        double elapsedMinutes = _jobTimer.Elapsed.TotalMinutes;
        double recordsPerSecond = _jobTimer.Elapsed.TotalSeconds > 0
            ? _totalProcessed / _jobTimer.Elapsed.TotalSeconds
            : 0;

        if (_totalRecords > 0)
        {
            double percentage = (double)_uniqueProcessed / _totalRecords * 100;
            _logger.LogInformation($"Progress: {percentage:F2}% ({_uniqueProcessed:N0}/{_totalRecords:N0}) in {elapsedMinutes:F2} minutes ({recordsPerSecond:F1} records/second)");
        }
        else
        {
            _logger.LogInformation($"Processed {_totalProcessed:N0} records ({_uniqueProcessed:N0} unique) in {elapsedMinutes:F2} minutes ({recordsPerSecond:F1} records/second)");
        }
    }

    /// <summary>
    /// Ghi log kết quả cuối cùng
    /// </summary>
    private void LogFinalResults()
    {
        double elapsedMinutes = _jobTimer.Elapsed.TotalMinutes;
        double recordsPerSecond = _jobTimer.Elapsed.TotalSeconds > 0
            ? _totalProcessed / _jobTimer.Elapsed.TotalSeconds
            : 0;

        _logger.LogInformation($"Job completed: Processed {_totalProcessed:N0} records ({_uniqueProcessed:N0} unique) in {elapsedMinutes:F2} minutes ({recordsPerSecond:F1} records/second)");

        if (_totalRecords > 0)
        {
            double percentage = (double)_uniqueProcessed / _totalRecords * 100;
            _logger.LogInformation($"Completion rate: {percentage:F2}% ({_uniqueProcessed:N0}/{_totalRecords:N0})");
        }
    }

}