using Microsoft.AspNetCore.Mvc;
using NDS.ETL.BackgroundJobs.Authentication;
using NDS.ETL.BackgroundJobs.Model.API;
using NDS.ETL.BackgroundJobs.Services;
using Swashbuckle.AspNetCore.Annotations;

namespace NDS.ETL.BackgroundJobs.Controllers;

/// <summary>
/// Controller providing administrative endpoints for wards data
/// Protected by ApiKey authentication
/// </summary>
[ApiController]
[Route("api/admin/wards")]
[ApiKey] // B?o v? b?ng xac thuc ApiKey
public class AdminWardsController : ESControllerBase
{
    private readonly IWardsService _wardsService;

    public AdminWardsController(
        IWardsService wardsService,
        IRequestInfoLogger requestInfoLogger,
        ILogger<AdminWardsController> logger)
        : base(requestInfoLogger, logger)
    {
        _wardsService = wardsService;
    }

    /// <summary>
    /// Gets all wards with pagination
    /// </summary>
    /// <param name="pageNumber">S? trang (b?t ??u t? 1)</param>
    /// <param name="pageSize">K�ch th??c trang</param>
    [HttpGet]
    [SwaggerOperation(
        Summary = "Get all wards with pagination",
        Description = "Returns a paginated list of all wards",
        OperationId = "GetAllWards"
    )]
    [SwaggerResponse(200, "Returns a paginated list of wards")]
    [SwaggerResponse(401, "Unauthorized - Invalid API Key")]
    [SwaggerResponse(500, "Internal server error")]
    public async Task<ActionResult<PagedWardResponse>> GetAllWards(
        [FromQuery] [SwaggerParameter(Description = "Page number (starting from 1)")] 
        int pageNumber = 1,
        [FromQuery] [SwaggerParameter(Description = "Page size (number of items per page)")] 
        int pageSize = 10)
    {
        var requestInfo = ProcessRequestInfo($"getAllWards:page={pageNumber},size={pageSize}");

        return await ExecuteWithErrorHandling(
            async () => {
                var pagedWards = await _wardsService.GetAllWardsAsync(pageNumber, pageSize);
                
                // �nh x? k?t qu? sang PagedWardResponse
                var response = new PagedWardResponse
                {
                    Items = pagedWards.Items.Select(MapToWardResponse).ToList(),
                    TotalCount = pagedWards.TotalCount,
                    CurrentPage = pagedWards.CurrentPage,
                    PageSize = pagedWards.PageSize,
                    TotalPages = pagedWards.TotalPages,
                    HasPreviousPage = pagedWards.HasPreviousPage,
                    HasNextPage = pagedWards.HasNextPage
                };
                
                return response;
            },
            "get all wards with pagination");
    }

    /// <summary>
    /// Gets a ward by its code
    /// </summary>
    /// <param name="code">Ward code</param>
    [HttpGet("code/{code}")]
    [SwaggerOperation(
        Summary = "Get ward by code",
        Description = "Returns ward details for the specified code",
        OperationId = "GetWardByCode"
    )]
    [SwaggerResponse(200, "Returns ward details")]
    [SwaggerResponse(404, "Ward not found")]
    [SwaggerResponse(401, "Unauthorized - Invalid API Key")]
    [SwaggerResponse(500, "Internal server error")]
    public async Task<ActionResult<WardResponse>> GetWardByCode(string code)
    {
        var requestInfo = ProcessRequestInfo($"getWardByCode:{code}");

        try
        {
            var ward = await _wardsService.GetWardByCodeAsync(code);
            if (ward == null)
            {
                return NotFound($"Ward with code {code} not found");
            }
            
            return MapToWardResponse(ward);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error getting ward by code {Code}: {Message}", code, ex.Message);
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    /// <summary>
    /// Searches for wards by name with pagination
    /// </summary>
    /// <param name="request">Search request containing name and pagination parameters</param>
    [HttpPost("search/name")]
    [SwaggerOperation(
        Summary = "Search wards by name with pagination",
        Description = "Returns paginated wards matching the specified name search term, including matches on old ward names",
        OperationId = "SearchWardsByName"
    )]
    [SwaggerResponse(200, "Returns a paginated list of matching wards")]
    [SwaggerResponse(401, "Unauthorized - Invalid API Key")]
    [SwaggerResponse(500, "Internal server error")]
    public async Task<ActionResult<PagedWardResponse>> SearchWardsByName(
        [FromBody] [SwaggerParameter(Description = "Search request containing name and pagination parameters")]
        SearchByNamePaginatedRequest request)
    {
        // Kiểm tra dữ liệu đầu vào
        if (request == null || string.IsNullOrEmpty(request.Name))
        {
            return BadRequest("Name parameter is required");
        }

        // Đảm bảo các tham số phân trang hợp lệ
        int pageNumber = request.PageNumber < 1 ? 1 : request.PageNumber;
        int pageSize = request.PageSize < 1 ? 10 : request.PageSize > 100 ? 100 : request.PageSize;

        var requestInfo = ProcessRequestInfo($"searchWardsByName:{request.Name},page={pageNumber},size={pageSize}");

        return await ExecuteWithErrorHandling(
            async () => {
                var pagedWards = await _wardsService.GetWardsByNameAsync(request.Name, pageNumber, pageSize);
                
                // Ánh xạ kết quả sang PagedWardResponse
                var response = new PagedWardResponse
                {
                    Items = pagedWards.Items.Select(MapToWardResponse).ToList(),
                    TotalCount = pagedWards.TotalCount,
                    CurrentPage = pagedWards.CurrentPage,
                    PageSize = pagedWards.PageSize,
                    TotalPages = pagedWards.TotalPages,
                    HasPreviousPage = pagedWards.HasPreviousPage,
                    HasNextPage = pagedWards.HasNextPage
                };
                
                return response;
            },
            "search wards by name with pagination");
    }

    /// <summary>
    /// Filters wards by type with pagination
    /// </summary>
    /// <param name="type">Ward type</param>
    /// <param name="pageNumber">S? trang (b?t ??u t? 1)</param>
    /// <param name="pageSize">K�ch th??c trang</param>
    [HttpGet("filter/type")]
    [SwaggerOperation(
        Summary = "Filter wards by type with pagination",
        Description = "Returns paginated wards matching the specified type",
        OperationId = "FilterWardsByType"
    )]
    [SwaggerResponse(200, "Returns a paginated list of matching wards")]
    [SwaggerResponse(401, "Unauthorized - Invalid API Key")]
    [SwaggerResponse(500, "Internal server error")]
    public async Task<ActionResult<PagedWardResponse>> FilterWardsByType(
        [FromQuery] [SwaggerParameter(Description = "Ward type")]
        string type,
        [FromQuery] [SwaggerParameter(Description = "Page number (starting from 1)")] 
        int pageNumber = 1,
        [FromQuery] [SwaggerParameter(Description = "Page size (number of items per page)")] 
        int pageSize = 10)
    {
        var requestInfo = ProcessRequestInfo($"filterWardsByType:{type},page={pageNumber},size={pageSize}");

        return await ExecuteWithErrorHandling(
            async () => {
                var pagedWards = await _wardsService.GetWardsByTypeAsync(type, pageNumber, pageSize);
                
                // �nh x? k?t qu? sang PagedWardResponse
                var response = new PagedWardResponse
                {
                    Items = pagedWards.Items.Select(MapToWardResponse).ToList(),
                    TotalCount = pagedWards.TotalCount,
                    CurrentPage = pagedWards.CurrentPage,
                    PageSize = pagedWards.PageSize,
                    TotalPages = pagedWards.TotalPages,
                    HasPreviousPage = pagedWards.HasPreviousPage,
                    HasNextPage = pagedWards.HasNextPage
                };
                
                return response;
            },
            "filter wards by type with pagination");
    }

    /// <summary>
    /// Maps a WardNew entity to a WardResponse model
    /// </summary>
    private static WardResponse MapToWardResponse(Entities.PostgresPlace.WardNew ward)
    {
        // T?o ??i t??ng response v?i c�c thu?c t�nh c? b?n
        var response = new WardResponse
        {
            Id = ward.Id,
            Code = ward.Code,
            ProvinceCode = ward.Provincecode,
            Name = ward.Name,
            Type = ward.Type,
            OldCode = ward.Oldcode,
            CenterLocation = ward.CenterLocation,
            Processed = ward.Processed
        };
        
        // Th�m th�ng tin c�c ph??ng/x� c? t? WardsBridge
        if (ward.WardsBridges != null && ward.WardsBridges.Any())
        {
            foreach (var bridge in ward.WardsBridges)
            {
                // Ki?m tra xem WardsOldCodeNavigation c� d? li?u kh�ng
                if (bridge.WardsOldCodeNavigation != null)
                {
                    // �nh x? th�ng tin ph??ng/x� c? v�o OldWardInfo
                    var oldWardInfo = new OldWardInfo
                    {
                        Id = bridge.WardsOldCodeNavigation.Id,
                        Code = bridge.WardsOldCodeNavigation.Code,
                        Name = bridge.WardsOldCodeNavigation.Name,
                        Type = bridge.WardsOldCodeNavigation.Type,
                        CenterLocation = bridge.WardsOldCodeNavigation.CenterLocation,
                        Processed = bridge.WardsOldCodeNavigation.Processed
                    };
                    
                    // Th�m v�o danh s�ch ph??ng/x� c?
                    response.OldWards.Add(oldWardInfo);
                }
            }
        }
        
        return response;
    }

    /// <summary>
    /// Clear all wards cache
    /// </summary>
    [HttpPost("cache/clear")]
    [SwaggerOperation(
        Summary = "Clear wards cache",
        Description = "Clears all cached ward data to force fresh data to be fetched from database",
        OperationId = "ClearWardsCache"
    )]
    [SwaggerResponse(200, "Cache cleared successfully")]
    [SwaggerResponse(401, "Unauthorized - Invalid API Key")]
    [SwaggerResponse(500, "Internal server error")]
    public ActionResult ClearWardsCache()
    {
        var requestInfo = ProcessRequestInfo("clearWardsCache");

        try
        {
            // Xóa toàn bộ cache của phường/xã
            _wardsService.InvalidateAllWardCache();
            
            Logger.LogInformation("Wards cache cleared successfully");
            return Ok(new { message = "Wards cache cleared successfully" });
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error clearing wards cache: {Message}", ex.Message);
            return StatusCode(500, "An error occurred while clearing wards cache");
        }
    }

    /// <summary>
    /// Gets new address by location coordinates
    /// </summary>
    /// <param name="request">Request containing location coordinates and optional current address</param>
    [HttpPost("search/getnewaddressbylocation")]
    [SwaggerOperation(
        Summary = "Get new address by location coordinates",
        Description = "Returns ward that contains the specified coordinates and an updated address with the new ward information",
        OperationId = "GetNewAddressByLocation"
    )]
    [SwaggerResponse(200, "Returns ward details and updated address")]
    [SwaggerResponse(400, "Bad request - Invalid coordinates or missing data")]
    [SwaggerResponse(404, "Ward not found for the specified location")]
    [SwaggerResponse(401, "Unauthorized - Invalid API Key")]
    [SwaggerResponse(500, "Internal server error")]
    public async Task<ActionResult<GetNewAddressByLocationResponse>> GetNewAddressByLocation(
        [FromBody] [SwaggerParameter(Description = "Request containing location coordinates and optional current address")]
        GetWardByLocationRequest request)
    {
        // Kiểm tra dữ liệu đầu vào
        if (request == null || request.Location == null)
        {
            return BadRequest("Location coordinates are required");
        }

        // Validate coordinates
        if (request.Location.Latitude < -90 || request.Location.Latitude > 90)
        {
            return BadRequest("Latitude must be between -90 and 90");
        }
        
        if (request.Location.Longitude < -180 || request.Location.Longitude > 180)
        {
            return BadRequest("Longitude must be between -180 and 180");
        }

        var requestInfo = ProcessRequestInfo($"getNewAddressByLocation:lat={request.Location.Latitude},lng={request.Location.Longitude}");

        try
        {
            var (ward, updatedAddress) = await _wardsService.GetNewAddressByLocationAsync(
                request.Location.Latitude, 
                request.Location.Longitude, 
                request.CurrentAddress);
            
            if (ward == null)
            {
                return NotFound($"No ward found for location ({request.Location.Latitude}, {request.Location.Longitude})");
            }
            
            // Tạo đối tượng response mở rộng từ WardResponse và bổ sung thêm CurrentAddress
            var wardResponse = MapToWardResponse(ward);
            var response = new GetNewAddressByLocationResponse
            {
                Id = wardResponse.Id,
                Code = wardResponse.Code,
                ProvinceCode = wardResponse.ProvinceCode,
                Name = wardResponse.Name,
                Type = wardResponse.Type,
                OldCode = wardResponse.OldCode,
                CenterLocation = wardResponse.CenterLocation,
                Processed = wardResponse.Processed,
                OldWards = wardResponse.OldWards,
                NewAddress = updatedAddress,
                CurrentAddress = request.CurrentAddress
            };
            
            return response;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error getting new address by location ({Latitude}, {Longitude}): {Message}", 
                request.Location.Latitude, request.Location.Longitude, ex.Message);
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    [HttpPost("search/location")]
    [SwaggerOperation(
        Summary = "Get ward by location coordinates",
        Description = "Returns ward that contains the specified coordinates",
        OperationId = "GetWardByLocation"
    )]
    [SwaggerResponse(200, "Returns ward details")]
    [SwaggerResponse(400, "Bad request - Invalid coordinates or missing data")]
    [SwaggerResponse(404, "Ward not found for the specified location")]
    [SwaggerResponse(401, "Unauthorized - Invalid API Key")]
    [SwaggerResponse(500, "Internal server error")]
    public async Task<ActionResult<WardResponse>> GetWardByLocation(
        [FromBody] [SwaggerParameter(Description = "Request containing location coordinates and optional current address")]
        GetWardByLocationRequest request)
    {
        // Kiểm tra dữ liệu đầu vào
        if (request == null || request.Location == null)
        {
            return BadRequest("Location coordinates are required");
        }

        // Validate coordinates
        if (request.Location.Latitude < -90 || request.Location.Latitude > 90)
        {
            return BadRequest("Latitude must be between -90 and 90");
        }

        if (request.Location.Longitude < -180 || request.Location.Longitude > 180)
        {
            return BadRequest("Longitude must be between -180 and 180");
        }

        var requestInfo = ProcessRequestInfo($"getWardByLocation:lat={request.Location.Latitude},lng={request.Location.Longitude}");

        try
        {
            var ward = await _wardsService.GetWardByLocationAsync(
                request.Location.Latitude,
                request.Location.Longitude,
                request.CurrentAddress);

            if (ward == null)
            {
                return NotFound($"No ward found for location ({request.Location.Latitude}, {request.Location.Longitude})");
            }

            // Ánh xạ kết quả sang WardResponse
            return MapToWardResponse(ward);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error getting ward by location ({Latitude}, {Longitude}): {Message}",
                request.Location.Latitude, request.Location.Longitude, ex.Message);
            return StatusCode(500, "An error occurred while processing your request");
        }
    }
}