using System.Text.RegularExpressions;

namespace NDS.ETL.Infrastructure.Extensions;

public class AddressProcessor
{
    // Từ khóa đánh dấu các loại đường phố
    private static readonly HashSet<string> StreetPrefixes = new HashSet<string>(
        new[] { "p.", "p ", "phố", "ph.", "đ.", "đ ", "đường", "ph " },
        StringComparer.OrdinalIgnoreCase
    );

    // Từ khóa đánh dấu ngõ, hẻm, ngách
    private static readonly HashSet<string> AlleyKeywords = new HashSet<string>(
        new[] { "ng.", "ngõ", "ngách", "ngh.", "hẻm" },
        StringComparer.OrdinalIgnoreCase
    );

    /// <summary>
    /// Xác định level của địa chỉ
    /// </summary>
    /// <param name="address">Địa chỉ cần phân loại</param>
    /// <returns>Level của địa chỉ (1, 2 hoặc 3)</returns>
    public static int DetermineAddressLevel(string address)
    {
        if (string.IsNullOrWhiteSpace(address))
            return 0;

        // Chuẩn hóa địa chỉ
        string normalizedAddress = NormalizeAddress(address);
        
        // Tách phần đầu tiên của địa chỉ (trước dấu phẩy đầu tiên)
        string firstPart = ExtractFirstPart(normalizedAddress);
        
        // Kiểm tra nếu địa chỉ thỏa mãn điều kiện Level 1
        if (IsLevel1Structure(firstPart, normalizedAddress))
            return 1;
            
        // Kiểm tra nếu địa chỉ thỏa mãn điều kiện Level 2
        if (IsLevel2Structure(firstPart))
            return 2;
        
        // Mặc định trả về level 3
        return 3;
    }

    /// <summary>
    /// Chuẩn hóa địa chỉ
    /// </summary>
    /// <param name="address">Địa chỉ cần chuẩn hóa</param>
    /// <returns>Địa chỉ đã chuẩn hóa</returns>
    private static string NormalizeAddress(string address)
    {
        if (string.IsNullOrEmpty(address))
            return string.Empty;
            
        // Loại bỏ khoảng trắng dư thừa
        address = Regex.Replace(address, @"\s+", " ").Trim();
        
        // Chuyển đổi thành chữ thường
        address = address.ToLower();
        
        return address;
    }

    /// <summary>
    /// Tách phần đầu tiên của địa chỉ (trước dấu phẩy đầu tiên)
    /// </summary>
    /// <param name="address">Địa chỉ cần tách</param>
    /// <returns>Phần đầu tiên của địa chỉ</returns>
    private static string ExtractFirstPart(string address)
    {
        int commaIndex = address.IndexOf(',');
        if (commaIndex > 0)
            return address.Substring(0, commaIndex).Trim();
        return address.Trim();
    }

    /// <summary>
    /// Kiểm tra nếu địa chỉ có cấu trúc Level 1
    /// </summary>
    /// <param name="addressPart">Phần đầu của địa chỉ (trước dấu phẩy đầu tiên)</param>
    /// <param name="fullAddress">Toàn bộ địa chỉ</param>
    /// <returns>True nếu có cấu trúc Level 1, False nếu không</returns>
    private static bool IsLevel1Structure(string addressPart, string fullAddress)
    {
        // Điều kiện 1: Không chứa ký tự "/"
        if (fullAddress.Contains("/"))
            return false;
            
        // Điều kiện 2: Kiểm tra nếu có số đứng trước các từ ngõ/ngách
        if (HasNumberBeforeAlleyKeyword(addressPart))
            return false;
            
        // Điều kiện 3: Kiểm tra các mẫu địa chỉ Level 1
        return IsLevel1AddressPattern(addressPart) || IsLevel1AlleyPattern(addressPart);
    }

    /// <summary>
    /// Kiểm tra nếu có số đứng trước các từ ngõ/ngách
    /// </summary>
    private static bool HasNumberBeforeAlleyKeyword(string addressPart)
    {
        foreach (var keyword in AlleyKeywords)
        {
            // Pattern cập nhật để bắt cả số kèm chữ cái
            //string pattern = @"\b(\d+[a-zA-Z]?)\s+" + Regex.Escape(keyword);
            // Pattern cập nhật để bắt số k kèm chữ cái
            string pattern = @"\b(\d+)\s+" + Regex.Escape(keyword);

            if (Regex.IsMatch(addressPart, pattern, RegexOptions.IgnoreCase))
                return true;
        }
        return false;
    }

    /// <summary>
    /// Kiểm tra nếu địa chỉ khớp với các mẫu địa chỉ Level 1 (không phải ngõ/hẻm)
    /// </summary>
    private static bool IsLevel1AddressPattern(string addressPart)
    {
        // Pattern cho các mẫu số nhà + tên đường
        var patterns = new List<string>
        {
            // Số + "Số" + Tên Đường/Phố
            @"^(số\s+)?(\d+[a-zA-Z]?)\s+([^,]+)$",
            
            // Số + "Số" + P.Tên Đường/Phố hoặc P. Tên Đường/Phố
            @"^(số\s+)?(\d+[a-zA-Z]?)\s+p\.?\s+([^,]+)$",
            
            // Số + "Số" + Đ.Tên Đường/Phố hoặc Đ. Tên Đường/Phố
            @"^(số\s+)?(\d+[a-zA-Z]?)\s+đ\.?\s+([^,]+)$",
            
            // Số + "Số" + Phố + Tên Đường/Phố
            @"^(số\s+)?(\d+[a-zA-Z]?)\s+phố\s+([^,]+)$",
            
            // Số + "Số" + Đường + Tên Đường/Phố
            @"^(số\s+)?(\d+[a-zA-Z]?)\s+đường\s+([^,]+)$",
            
            // "Số" + Đ. + Tên Đường/Phố
            @"^(\d+[a-zA-Z]?)\s+đ\.?\s+([^,]+)$",
            
            // "Số" + Đường + Tên Đường/Phố
            @"^(\d+[a-zA-Z]?)\s+đường\s+([^,]+)$",
            
            // "Số" + P.Tên Đường/Phố hoặc P. Tên Đường/Phố
            @"^(\d+[a-zA-Z]?)\s+p\.?\s+([^,]+)$",
            
            // "Số" + Phố + Tên Đường/Phố
            @"^(\d+[a-zA-Z]?)\s+phố\s+([^,]+)$",
            
            // "Số" + Ph. + Tên Đường/Phố
            @"^(\d+[a-zA-Z]?)\s+ph\.?\s+([^,]+)$"
        };
        
        // Kiểm tra từng pattern
        foreach (var pattern in patterns)
        {
            if (Regex.IsMatch(addressPart, pattern, RegexOptions.IgnoreCase))
                return true;
        }
        
        return false;
    }

    /// <summary>
    /// Kiểm tra nếu địa chỉ khớp với các mẫu ngõ/hẻm Level 1
    /// </summary>
    private static bool IsLevel1AlleyPattern(string addressPart)
    {
        // Pattern cho các mẫu ngõ/hẻm Level 1
        var patterns = new List<string>
        {
            // Ng. + Tên ngõ dạng chữ/dạng số
            @"^ng\.\s+([^,]+)$",
            
            // Ng. + "Số ngõ" + Tên Ngõ/Đường/Phố
            @"^ng\.\s+(\d+)\s+([^,]+)$",
            @"^ng\.\s+(\d+)\s+p\.?\s+([^,]+)$",
            @"^ng\.\s+(\d+)\s+ph\.?\s+([^,]+)$",
            @"^ng\.\s+(\d+)\s+phố\s+([^,]+)$",
            @"^ng\.\s+(\d+)\s+đ\.?\s+([^,]+)$",
            @"^ng\.\s+(\d+)\s+đường\s+([^,]+)$",
            
            // Ngõ + Tên ngõ dạng chữ/dạng số
            @"^ngõ\s+([^,]+)$",
            
            // Ngõ + "Số ngõ" + Tên Ngõ/Đường/Phố
            @"^ngõ\s+(\d+)\s+([^,]+)$",
            @"^ngõ\s+(\d+)\s+p\.?\s+([^,]+)$",
            @"^ngõ\s+(\d+)\s+ph\.?\s+([^,]+)$",
            @"^ngõ\s+(\d+)\s+phố\s+([^,]+)$",
            @"^ngõ\s+(\d+)\s+đ\.?\s+([^,]+)$",
            @"^ngõ\s+(\d+)\s+đường\s+([^,]+)$",
            
            // Hẻm + Tên hẻm dạng chữ/dạng số
            @"^hẻm\s+([^,]+)$",
            
            // Hẻm + "Số" + Đ. + Tên hẻm/đường/phố
            @"^hẻm\s+(\d+)\s+([^,]+)$",
            @"^hẻm\s+(\d+)\s+đ\.?\s+([^,]+)$",
            
            // Hẻm + "Số" + Đường + Tên hẻm/đường/phố
            @"^hẻm\s+(\d+)\s+đường\s+([^,]+)$"
        };
        
        // Kiểm tra từng pattern
        foreach (var pattern in patterns)
        {
            if (Regex.IsMatch(addressPart, pattern, RegexOptions.IgnoreCase))
                return true;
        }
        
        return false;
    }

    /// <summary>
    /// Kiểm tra nếu địa chỉ có cấu trúc Level 2
    /// </summary>
    private static bool IsLevel2Structure(string addressPart)
    {
        // Địa chỉ Level 2 chứa nhiều cấp ngõ/hẻm hoặc có định dạng phức tạp hơn
        // Pattern cho cấu trúc nhiều cấp ngõ/hẻm
        var level2Patterns = new List<string>
        {
            // Số + ngõ/hẻm + số + đường (cấu trúc 2 cấp)
            @"^(\d+)\s+(ngõ|ngách|hẻm)\s+(\d+)\s+([^,]+)$",
            
            // Hẻm + số + ngõ + số + đường (cấu trúc 3 cấp)
            @"^(hẻm|ngách)\s+(\d+)\s+(ngõ|kiệt)\s+(\d+)\s+([^,]+)$",
            
            // Kiểm tra cấu trúc có dấu "/"
            @"ngõ\s+\d+\/\d+"
        };
        
        foreach (var pattern in level2Patterns)
        {
            if (Regex.IsMatch(addressPart, pattern, RegexOptions.IgnoreCase))
                return true;
        }
        
        return false;
    }

    /// <summary>
    /// Phân tích chi tiết địa chỉ Level 1
    /// </summary>
    /// <param name="address">Địa chỉ cần phân tích</param>
    /// <returns>Thông tin chi tiết về địa chỉ Level 1</returns>
    public static AddressDetails ParseLevel1Address(string address)
    {
        // Chuẩn hóa địa chỉ
        string normalizedAddress = NormalizeAddress(address);
        
        // Tách phần đầu tiên
        string firstPart = ExtractFirstPart(normalizedAddress);
        
        var result = new AddressDetails
        {
            Level = 1,
            OriginalAddress = address,
            NormalizedAddress = normalizedAddress
        };
        
        // Kiểm tra nếu là địa chỉ dạng số nhà + tên đường
        if (IsLevel1AddressPattern(firstPart))
        {
            ParseHouseNumberAndStreet(firstPart, result);
        }
        // Kiểm tra nếu là địa chỉ dạng ngõ/hẻm
        else if (IsLevel1AlleyPattern(firstPart))
        {
            ParseAlleyAddress(firstPart, result);
        }
        
        return result;
    }

    /// <summary>
    /// Phân tích địa chỉ dạng số nhà + tên đường
    /// </summary>
    private static void ParseHouseNumberAndStreet(string addressPart, AddressDetails details)
    {
        // Pattern để tách số nhà và tên đường
        string pattern = @"^(số\s+)?(\d+[a-zA-Z]?)\s+(.+)$";
        Match match = Regex.Match(addressPart, pattern, RegexOptions.IgnoreCase);
        
        if (match.Success)
        {
            details.HouseNumber = match.Groups[2].Value.Trim();
            
            string remainingPart = match.Groups[3].Value.Trim();
            
            // Tách tiền tố đường (nếu có) và tên đường
            foreach (var prefix in StreetPrefixes)
            {
                if (remainingPart.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
                {
                    details.StreetPrefix = prefix;
                    details.StreetName = remainingPart.Substring(prefix.Length).Trim();
                    return;
                }
            }
            
            // Nếu không có tiền tố đường, lấy toàn bộ phần còn lại là tên đường
            details.StreetName = remainingPart;
        }
    }

    /// <summary>
    /// Phân tích địa chỉ dạng ngõ/hẻm
    /// </summary>
    private static void ParseAlleyAddress(string addressPart, AddressDetails details)
    {
        // Pattern cho ngõ/hẻm + số + tên đường
        string pattern = @"^(ng\.|ngõ|hẻm)\s+(\d+)?\s*(.*)$";
        Match match = Regex.Match(addressPart, pattern, RegexOptions.IgnoreCase);
        
        if (match.Success)
        {
            details.AlleyType = match.Groups[1].Value.Trim();
            
            // Số ngõ/hẻm (có thể null)
            if (!string.IsNullOrEmpty(match.Groups[2].Value))
            {
                details.AlleyNumber = match.Groups[2].Value.Trim();
            }
            
            string remainingPart = match.Groups[3].Value.Trim();
            
            // Tách tiền tố đường (nếu có) và tên đường
            foreach (var prefix in StreetPrefixes)
            {
                if (remainingPart.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
                {
                    details.StreetPrefix = prefix;
                    details.StreetName = remainingPart.Substring(prefix.Length).Trim();
                    return;
                }
            }
            
            // Nếu không có tiền tố đường, lấy toàn bộ phần còn lại là tên đường
            details.StreetName = remainingPart;
        }
    }

    /// <summary>
    /// Phương thức để kiểm tra và hiển thị kết quả
    /// </summary>
    /// <param name="address">Địa chỉ cần xử lý</param>
    public static void ProcessAddress(string address)
    {
        int level = DetermineAddressLevel(address);
        Console.WriteLine($"Địa chỉ: {address}");
        Console.WriteLine($"Level: {level}");
        
        if (level == 1)
        {
            var details = ParseLevel1Address(address);
            Console.WriteLine($"Chi tiết: {details}");
        }
        
        Console.WriteLine();
    }

    /// <summary>
    /// Lớp để lưu trữ chi tiết về địa chỉ đã phân tích
    /// </summary>
    public class AddressDetails
    {
        public int Level { get; set; }
        public string OriginalAddress { get; set; }
        public string NormalizedAddress { get; set; }
        public string HouseNumber { get; set; }
        public string StreetPrefix { get; set; }
        public string StreetName { get; set; }
        public string AlleyType { get; set; }
        public string AlleyNumber { get; set; }
        
        public override string ToString()
        {
            if (!string.IsNullOrEmpty(HouseNumber))
            {
                return $"Số nhà: {HouseNumber}, " +
                       $"{(string.IsNullOrEmpty(StreetPrefix) ? "" : $"Tiền tố: {StreetPrefix}, ")}" +
                       $"Đường: {StreetName}";
            }
            else if (!string.IsNullOrEmpty(AlleyType))
            {
                return $"Loại ngõ/hẻm: {AlleyType}, " +
                       $"{(string.IsNullOrEmpty(AlleyNumber) ? "" : $"Số ngõ/hẻm: {AlleyNumber}, ")}" +
                       $"{(string.IsNullOrEmpty(StreetPrefix) ? "" : $"Tiền tố: {StreetPrefix}, ")}" +
                       $"Đường: {StreetName}";
            }
            
            return "Không thể phân tích chi tiết";
        }
    }
    public static int DetermineAddressLevelResp(string address)
    {
        int level = DetermineAddressLevel(address);
        Console.WriteLine($"Địa chỉ: {address}");
        Console.WriteLine($"Level: {level}");
        return level;
    }
}

// // Chương trình ví dụ để kiểm tra
// class Program
// {
//     static void Main()
//     {
//         // Danh sách các địa chỉ mẫu để kiểm tra
//         var addresses = new List<string>
//         {
//             // Level 1 - Số nhà + tên đường
//             "70 P. Gia Ngư, Hàng Đào, Hoàn Kiếm, Hà Nội",
//             "1A Trần Quang Khải, P. Tràng Tiền, Q. Hoàn Kiếm, TP. Hà Nội",
//             "Số 83 Yên Lãng, Thịnh Quang, Đống Đa, Hà Nội",
//             "Số 83 P. Yên Lãng, Thịnh Quang, Đống Đa, Hà Nội",
//             "22 P. Láng Hạ, Láng Hạ, Đống Đa, Hà Nội",
//             "72 Đường Trần Duy Hưng, Láng Thượng, Đống Đa, Hà Nội",
//             
//             // Level 1 - Ngõ/hẻm đơn cấp
//             "Ng. Hàng Thịt, Cửa Nam, Hoàn Kiếm, Hà Nội",
//             "Ng. 97 Láng Hạ, P. Láng Hạ, Q. Đống Đa, Hà Nội",
//             "Ngõ 97 P. Láng Hạ, P. Láng Hạ, Q. Đống Đa, Hà Nội",
//             "Hẻm 14 Trần Bình Trọng, Phường 1, Quận 5, Hồ Chí Minh",
//             
//             // Level 2 - Cấu trúc phức tạp
//             "số 34 ngõ 444 đội cấn, cống vị hà nội",
//             "hẻm 5 ngõ 34/444 đội cấn, cống vị hà nội",
//             "2 Ngõ 44 P. Trần Thái Tông, Dịch Vọng Hậu, Cầu Giấy, Hà Nội",
//             "2/2 Láng Hạ, Đống Đa, Hà Nội"
//         };
//
//         // Xử lý từng địa chỉ
//         foreach (var address in addresses)
//         {
//             AddressProcessor.ProcessAddress(address);
//         }
//     }
// }
