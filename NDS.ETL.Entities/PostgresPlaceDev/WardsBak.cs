using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Keyless]
[Table("wards_bak")]
public partial class WardsBak
{
    [Column("id")]
    public int? Id { get; set; }

    [Column("code")]
    [StringLength(255)]
    public string? Code { get; set; }

    [Column("districtCode")]
    [StringLength(255)]
    public string? DistrictCode { get; set; }

    [Column("name")]
    [StringLength(255)]
    public string? Name { get; set; }

    [Column("type")]
    public int? Type { get; set; }

    [Column("boundary")]
    public string? Boundary { get; set; }

    [Column("no_space_name")]
    [StringLength(50)]
    public string? NoSpaceName { get; set; }

    [Column("province_code")]
    [StringLength(50)]
    public string? ProvinceCode { get; set; }

    [Column("center_location")]
    [StringLength(50)]
    public string? CenterLocation { get; set; }

    [Column("processed")]
    public bool? Processed { get; set; }

    [Column("boundary_geom")]
    public string? BoundaryGeom { get; set; }
}
