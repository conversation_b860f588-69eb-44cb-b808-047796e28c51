using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Keyless]
[Table("jwt_clients_test")]
public partial class JwtClientsTest
{
    [Column("id")]
    public int Id { get; set; }

    [Column("client_id")]
    [StringLength(255)]
    public string? ClientId { get; set; }

    [Column("client_secret_key")]
    [StringLength(255)]
    public string? ClientSecretKey { get; set; }
}
