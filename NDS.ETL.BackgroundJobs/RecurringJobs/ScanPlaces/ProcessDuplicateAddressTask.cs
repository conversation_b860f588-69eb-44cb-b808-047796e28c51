using System.Diagnostics;
using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Context;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Entities.Context;
using NDS.ETL.Infrastructure.Common;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.ScanPlaces;

public class ProcessDuplicateAddressTask : RecurringJobBase<PgPlaceContext>
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ProcessDuplicateAddressTask> _logger;
    private readonly IESPlacesService _iesPlacesServiceService;
    private readonly IScopedResolver<NpgPlaceContext> _dbContextResolver;

    public ProcessDuplicateAddressTask(IConfiguration configuration,
        ILogger<ProcessDuplicateAddressTask> logger,
        IScopedResolver<NpgPlaceContext> dbContextResolver,
        IESPlacesService iesPlacesServiceService)
        : base(configuration, logger, null)
    {
        _logger = logger;
        _dbContextResolver = dbContextResolver;
        _iesPlacesServiceService = iesPlacesServiceService;
    }

    public override string JobId => "ProcessDuplicateAddress";

    public override string ConfigSection => "JobPgSyncPlace";

    public override async Task DoWorkAsync(CancellationToken stoppingToken)
    {
        var begin = Stopwatch.GetTimestamp();
        _logger.LogInformation("ProcessDuplicateAddressTask DoWorkAsync started");

        try
        {
            await _dbContextResolver.ResolveAsync(async dbContext =>
            {
                dbContext.Database.SetCommandTimeout(900);

                var placesQueuesWithStatus4 = await dbContext.PlacesQueues
                    .Where(x => x.Status == 4)
                    .OrderBy(x => x.CreateDate)
                    .ToListAsync(stoppingToken)
                    .ConfigureAwait(false);

                if (!placesQueuesWithStatus4.Any())
                {
                    _logger.LogInformation("No place queues found with Status = 4 (synthesized)");
                    return true;
                }

                _logger.LogInformation("Found {Count} districts with status = 4 to process", placesQueuesWithStatus4.Count);

                int processedCount = 0;
                int successCount = 0;

                foreach (var placeQueue in placesQueuesWithStatus4)
                {
                    if (stoppingToken.IsCancellationRequested)
                    {
                        _logger.LogInformation("Cancellation requested, stopping processing");
                        break;
                    }

                    var districtStopwatch = Stopwatch.StartNew();
                    var districtId = (int)placeQueue.Districtid;

                    try
                    {
                        _logger.LogInformation("Processing duplicate addresses for district {DistrictId} (Queue ID: {QueueId})", 
                            districtId, placeQueue.Id);

                        var duplicateAddresses = await _iesPlacesServiceService.GetDuplicateAddress(districtId, stoppingToken);

                        if (duplicateAddresses != null)
                        {
                            _logger.LogInformation("Found {DuplicateCount} duplicate addresses in district {DistrictId}", 
                                duplicateAddresses.Count, districtId);

                            var freshPlaceQueue = await dbContext.PlacesQueues.FindAsync(placeQueue.Id, stoppingToken);
                            if (freshPlaceQueue != null)
                            {
                                freshPlaceQueue.Status = 5;
                                freshPlaceQueue.UpdateDate = DateTime.Now;
                                await dbContext.SaveChangesAsync(stoppingToken);
                                
                                successCount++;
                                _logger.LogInformation(
                                    "Successfully processed district {DistrictId} and updated status to 5 (duplicates filtered) in {ElapsedSeconds:F2} seconds. Processed {DuplicateCount} duplicate addresses.",
                                    districtId, districtStopwatch.Elapsed.TotalSeconds, duplicateAddresses.Count);
                            }
                            else
                            {
                                _logger.LogWarning("Could not find PlaceQueue with ID {QueueId} to update status", placeQueue.Id);
                            }
                        }
                        else
                        {
                            _logger.LogWarning("No duplicate addresses found for district {DistrictId}", districtId);
                            
                            var freshPlaceQueue = await dbContext.PlacesQueues.FindAsync(placeQueue.Id, stoppingToken);
                            if (freshPlaceQueue != null)
                            {
                                freshPlaceQueue.Status = 5;
                                freshPlaceQueue.UpdateDate = DateTime.Now;
                                await dbContext.SaveChangesAsync(stoppingToken);
                                successCount++;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing duplicate addresses for district {DistrictId}", districtId);
                    }
                    finally
                    {
                        processedCount++;
                        districtStopwatch.Stop();
                    }
                }

                var end = Stopwatch.GetTimestamp();
                _logger.LogInformation(
                    "Completed ProcessDuplicateAddressTask: Processed {ProcessedCount}/{TotalCount} districts, " +
                    "Successfully filtered duplicates in {SuccessCount} districts in {TotalTime:F2} seconds",
                    processedCount, placesQueuesWithStatus4.Count, successCount,
                    TimeSpan.FromTicks(end - begin).TotalSeconds);

                return true;
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred in ProcessDuplicateAddressTask");
            throw;
        }
    }

    public override Task IndexMany<T>(List<T> source, CancellationToken cancellationToken, int take = 1000)
    {
        return Task.CompletedTask;
    }

    public override Task IndexMany<T>(List<T> source, CancellationToken cancellationToken, string esIndex, int take = 1000)
    {
        return Task.CompletedTask;
    }
} 