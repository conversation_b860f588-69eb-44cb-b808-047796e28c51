using Microsoft.Extensions.DependencyInjection;
using NDS.ETL.BackgroundJobs.Services.ES;

namespace NDS.ETL.BackgroundJobs.Extensions
{
    /// <summary>
    /// Extension methods cho đăng ký các dịch vụ Elasticsearch vào DI container
    /// </summary>
    public static class ElasticsearchServiceExtensions
    {
        /// <summary>
        /// Đăng ký các dịch vụ Elasticsearch vào DI container
        /// </summary>
        public static IServiceCollection AddElasticsearchServices(this IServiceCollection services)
        {
            // Đăng ký ElasticSearch client factory
            services.AddSingleton<IESClientFactory, ESClientFactory>();
            
            // Đăng ký các dịch vụ Elasticsearch khác
            services.AddSingleton<IESAutocompleteService, ESAutocompleteService>();
            services.AddSingleton<IESNearbyService, ESNearbyService>();
            services.AddSingleton<IESRecommendationService, ESRecommendationService>();
            
            // Đăng ký dịch vụ xử lý cặp địa điểm
            services.AddSingleton<IESPlacePairs, ESPlacePairsService>();
            
            return services;
        }
    }
}
