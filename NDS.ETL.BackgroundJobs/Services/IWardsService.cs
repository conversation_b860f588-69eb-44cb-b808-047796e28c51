using NDS.ETL.BackgroundJobs.Model.API;
using NDS.ETL.Entities.PostgresPlace;

namespace NDS.ETL.BackgroundJobs.Services;

/// <summary>
/// Service interface for handling operations related to wards
/// </summary>
public interface IWardsService
{
    /// <summary>
    /// Gets all wards with pagination
    /// </summary>
    /// <param name="pageNumber">S? trang (b?t ??u t? 1)</param>
    /// <param name="pageSize">K�ch th??c trang</param>
    /// <returns>Danh s�ch ph??ng/x� ???c ph�n trang</returns>
    Task<PagedResult<WardNew>> GetAllWardsAsync(int pageNumber = 1, int pageSize = 10);
    
    /// <summary>
    /// Gets a ward by its ID
    /// </summary>
    /// <param name="id">Ward ID</param>
    /// <returns>Ward entity</returns>
    Task<WardNew?> GetWardByIdAsync(int id);
    
    /// <summary>
    /// Gets a ward by its code
    /// </summary>
    /// <param name="code">Ward code</param>
    /// <returns>Ward entity</returns>
    Task<WardNew?> GetWardByCodeAsync(string code);
    
    /// <summary>
    /// Gets wards by name with pagination
    /// </summary>
    /// <param name="name">Name to search for</param>
    /// <param name="pageNumber">S? trang (b?t ??u t? 1)</param>
    /// <param name="pageSize">K�ch th??c trang</param>
    /// <returns>Danh s�ch ph??ng/x� ph� h?p ???c ph�n trang</returns>
    Task<PagedResult<WardNew>> GetWardsByNameAsync(string name, int pageNumber = 1, int pageSize = 10);
    
    /// <summary>
    /// Gets wards by type with pagination
    /// </summary>
    /// <param name="type">Ward type</param>
    /// <param name="pageNumber">S? trang (b?t ??u t? 1)</param>
    /// <param name="pageSize">K�ch th??c trang</param>
    /// <returns>Danh s�ch ph??ng/x� ph� h?p ???c ph�n trang</returns>
    Task<PagedResult<WardNew>> GetWardsByTypeAsync(string type, int pageNumber = 1, int pageSize = 10);
    
    /// <summary>
    /// Chuy?n ??i d? li?u boundary t? format c? (3 m?ng) sang format m?i (4 m?ng)
    /// </summary>
    /// <returns>Task th?c hi?n c�ng vi?c chuy?n ??i</returns>
    Task ConvertBoundaryFormatAsync();
    
    /// <summary>
    /// Invalidates all ward related caches
    /// </summary>
    void InvalidateAllWardCache();
    
    /// <summary>
    /// Gets ward by location coordinates using ST_Contains function
    /// </summary>
    /// <param name="latitude">V? ??</param>
    /// <param name="longitude">Kinh ??</param>
    /// <param name="currentAddress">??a ch? hi?n t?i (t�y ch?n)</param>
    /// <returns>Ph??ng/x� ph� h?p v?i v? tr�</returns>
    Task<WardNew?> GetWardByLocationAsync(double latitude, double longitude, string? currentAddress = null);
    
    /// <summary>
    /// L?y ph??ng/x� m?i v� ??a ch? c?p nh?t d?a tr�n t?a ??
    /// </summary>
    /// <param name="latitude">V? ??</param>
    /// <param name="longitude">Kinh ??</param>
    /// <param name="currentAddress">??a ch? hi?n t?i (t�y ch?n)</param>
    /// <returns>Ph??ng/x� m?i v� ??a ch? ?� ???c c?p nh?t</returns>
    Task<(WardNew? Ward, string? CurrentAddress)> GetNewAddressByLocationAsync(double latitude, double longitude, string? currentAddress = null);
}