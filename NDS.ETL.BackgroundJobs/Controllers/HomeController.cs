using Hangfire;
using KnowledgeBase.Core.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MongoDB.Driver;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch.Request;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Entities.TaxiMongoDB;
using NDS.ETL.Infrastructure.Extensions;


namespace NDS.ETL.BackgroundJobs.Controllers;


[Authorize]
public class HomeController(IMongoDatabase mongoDb, IESPlacesService iesPlacesService) : Controller
{
    [HttpGet]
    [Route("RecurringJob")]
    public Task<string> RecurringJobsAsync() {
            
        return Task.FromResult("Welcome user in Recurring Job Demo!");
    }
    
    [HttpGet]
    [Route("GetDuplicateAddress")]
    public async Task<List<string>?> DuplicateAddress(int districtId) {
            
        return await iesPlacesService.GetDuplicateAddress(districtId);
    }
    [HttpGet]
    [Route("InsertToScanResult")]
    public async Task<bool> InsertToScanResult(int districtId) {
        return await iesPlacesService.InsertPlacesScanResult(districtId);
    }
    
    [HttpGet]
    [Route("GetDistance")]
    public async Task<VerifyProcessedPlace> GetDistance(string placeIdSource, string placeIdDestination) {
        return await iesPlacesService.GetDistance(placeIdSource, placeIdDestination);
    }
    
    [HttpGet]
    [Route("CompareScore")]
    public Task<double> CompareScore(string source, string destination) {
        var score = source.ToLower().CalculateSimilarity(destination.ToLower());
        return Task.FromResult(score*100);
    }
    [HttpGet]
    [Route("CompareVerify")]
    public async Task<List<ResultVerifyProcessedPlace>> CompareVerify() {
        return await iesPlacesService.CompareVerify();
    }

    [HttpGet]
    [Route("Test")]
    public async Task<object> Test()
    {
       var userSearchQuery = mongoDb.GetCollection<UserSearchHistory>("user_search_histories");
       var check =  userSearchQuery.AsQueryable().Take(10).ToList();
        return check;
    }
    
    // Thêm route mới để test xử lý địa chỉ trùng lặp
    [HttpGet]
    [Route("TestAddressPermutation")]
    public Task<object> TestAddressPermutation(string address)
    {
        var result = new
        {
            Original = address,
            Normalized = AddressPermutator.NormalizeVietnamese(address.ToLower()),
            Permutations = AddressPermutator.GenerateOptimizedPermutations(address, 15)
        };
        
        return Task.FromResult<object>(result);
    }
    
    //create http post to search places
    [HttpPost]
    [Route("SearchPlaces")]
    public async Task<List<SearchResponseBase>> SearchPlaces([FromBody] PlaceEsSearchRq searchRequest,CancellationToken cancellationToken = default) {
        return await iesPlacesService.SearchPlaces(searchRequest,cancellationToken);
    }
    
    [HttpPost]
    [Route("CheckAddressLevel")]
    public async Task<object> CheckAddressLevel([FromBody] PlaceEsSearchRq searchRequest,CancellationToken cancellationToken = default) {
        var placelst =  await iesPlacesService.SearchPlaces(searchRequest,cancellationToken);
        var result = new List<KeyValuePair<string, int>>();
        foreach (var item in placelst)
        {
            int ketqua = AddressProcessor.DetermineAddressLevel(item.PlaceSearchResponse.Address);
            result.Add(new KeyValuePair<string, int>(item.PlaceSearchResponse.Address, ketqua));
        }
        return result;
    }
    
    [HttpPost]
    [Route("CheckAddressLevel2")]
    public async Task<object> CheckAddressLevel2(List<string> addresses ,CancellationToken cancellationToken = default) {
        var result = new List<KeyValuePair<string, int>>();
        foreach (var item in addresses)
        {
            int ketqua = AddressProcessor.DetermineAddressLevel(item);
            result.Add(new KeyValuePair<string, int>(item, ketqua));
        }
        return result;
    }
    [HttpPost]
    [Route("FixLocationPlaceApproved")]
    public async Task<bool> FixLocationPlaceApproved(string addresses ,CancellationToken cancellationToken = default) {
        
        return await iesPlacesService.UpdateLocationPlaceApproved(addresses, true,cancellationToken);
    }
    [HttpPost]
    [Route("SyncDataTrustedToEs")]
    public async Task<bool> SyncDataTrustedToEs(string placeId ,CancellationToken cancellationToken = default) {
        
        return await iesPlacesService.SyncDataTrustedToEs(placeId,cancellationToken);  
    }
    [HttpPost]
    [Route("TriggerScanPlacesOrchestrator")]
    public Task<string> TriggerScanPlacesOrchestrator() {
        
        RecurringJob.TriggerJob("ScanPlacesOrchestrator");
        return Task.FromResult("ScanPlacesOrchestrator job has been triggered");
    }
}