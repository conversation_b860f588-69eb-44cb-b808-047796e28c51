using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("planet_osm_rels")]
public partial class PlanetOsmRel
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("members", TypeName = "jsonb")]
    public string Members { get; set; } = null!;

    [Column("tags", TypeName = "jsonb")]
    public string? Tags { get; set; }
}
