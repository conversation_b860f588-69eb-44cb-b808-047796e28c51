using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations; // Add this namespace for annotations

namespace NDS.ETL.Entities.ESEntities
{
    public enum PermissionName
    {
        All, // Permission for the All API
        Autocomplete, // Permission for the Autocomplete API
        NearBy,       // Permission for the NearBy API
        Geocoding,    // Permission for the Geocoding API
        Recommendation, // Permission for the Recommendation APIs
        PlaceDetails // Permission for the PlaceDetails APIs
    }

    public class ApiKey
    {
        public Guid Id { get; set; } // Primary key

        [Required]
        [MaxLength(256)] // Enforce maximum length of 256 characters
        public string Key { get; set; } = string.Empty; // API key value

        [MaxLength(512)] // Enforce maximum length of 512 characters
        public string Description { get; set; } = string.Empty; // Description of the API key

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow; // Creation timestamp
        public DateTime? ExpirationDate { get; set; } // Expiration date (nullable)
        public bool IsActive { get; set; } = true; // Status of the API key
        public ICollection<ApiKeyPermission> Permissions { get; set; } = new List<ApiKeyPermission>(); // Associated permissions
    }

    public class ApiKeyPermission
    {
        public Guid Id { get; set; } // Primary key

        [Required]
        [MaxLength(128)] // Enforce maximum length of 128 characters
        public PermissionName PermissionName { get; set; } // Enum for permission name

        public Guid ApiKeyId { get; set; } // Foreign key to ApiKey
        public ApiKey ApiKey { get; set; } = null!; // Navigation property
    }
}
