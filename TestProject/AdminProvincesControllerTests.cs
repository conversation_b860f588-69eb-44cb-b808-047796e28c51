using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using NDS.ETL.BackgroundJobs.Controllers;
using NDS.ETL.BackgroundJobs.Model.API;
using NDS.ETL.BackgroundJobs.Services;
using NDS.ETL.Entities.PostgresPlace;
using NUnit.Framework;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace TestProject
{
    [TestFixture]
    public class AdminProvincesControllerTests
    {
        private Mock<IProvincesService> _mockProvincesService;
        private Mock<IRequestInfoLogger> _mockRequestInfoLogger;
        private Mock<ILogger<AdminProvincesController>> _mockLogger;
        private AdminProvincesController _controller;

        [SetUp]
        public void Setup()
        {
            // Khởi tạo các mock object
            _mockProvincesService = new Mock<IProvincesService>();
            _mockRequestInfoLogger = new Mock<IRequestInfoLogger>();
            _mockLogger = new Mock<ILogger<AdminProvincesController>>();

            // Khởi tạo controller với các mock dependency
            _controller = new AdminProvincesController(
                _mockProvincesService.Object,
                _mockRequestInfoLogger.Object,
                _mockLogger.Object);
        }

        [Test]
        public async Task GetAllProvinces_ReturnsListOfProvinces()
        {
            // Arrange
            var provinces = new List<ProvincesNew>
            {
                new ProvincesNew { Id = 1, Code = "01", Name = "Hà Nội", Type = "Thành phố" },
                new ProvincesNew { Id = 2, Code = "02", Name = "Hồ Chí Minh", Type = "Thành phố" }
            };
            
            _mockProvincesService.Setup(service => service.GetAllProvincesAsync())
                .ReturnsAsync(provinces);

            // Act
            var result = await _controller.GetAllProvinces();

            // Assert
            Assert.That(result.Result, Is.TypeOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult, Is.Not.Null);
            
            var provinceResponses = okResult.Value as List<ProvinceResponse>;
            Assert.That(provinceResponses, Is.Not.Null);
            Assert.That(provinceResponses.Count, Is.EqualTo(2));
            Assert.That(provinceResponses[0].Code, Is.EqualTo("01"));
            Assert.That(provinceResponses[0].Name, Is.EqualTo("Hà Nội"));
        }

        [Test]
        public async Task GetProvinceByCode_WithValidCode_ReturnsProvince()
        {
            // Arrange
            string code = "01";
            var province = new ProvincesNew
            {
                Id = 1,
                Code = code,
                Name = "Hà Nội",
                Type = "Thành phố"
            };
            
            _mockProvincesService.Setup(service => service.GetProvinceByCodeAsync(code))
                .ReturnsAsync(province);

            // Act
            var result = await _controller.GetProvinceByCode(code);

            // Assert
            Assert.That(result.Value, Is.Not.Null);
            Assert.That(result.Value.Code, Is.EqualTo("01"));
            Assert.That(result.Value.Name, Is.EqualTo("Hà Nội"));
        }

        [Test]
        public async Task GetProvinceByCode_WithInvalidCode_ReturnsNotFound()
        {
            // Arrange
            string code = "invalid";
            
            _mockProvincesService.Setup(service => service.GetProvinceByCodeAsync(code))
                .ReturnsAsync((ProvincesNew)null);

            // Act
            var result = await _controller.GetProvinceByCode(code);

            // Assert
            Assert.That(result.Result, Is.TypeOf<NotFoundObjectResult>());
        }

        [Test]
        public async Task SearchProvincesByName_WithValidRequest_ReturnsMatchingProvinces()
        {
            // Arrange
            string searchName = "Hà";
            var request = new SearchByNameRequest { Name = searchName };
            
            var provinces = new List<ProvincesNew>
            {
                new ProvincesNew { Id = 1, Code = "01", Name = "Hà Nội", Type = "Thành phố" },
                new ProvincesNew { Id = 3, Code = "15", Name = "Hà Nam", Type = "Tỉnh" }
            };
            
            _mockProvincesService.Setup(service => service.GetProvincesByNameAsync(searchName))
                .ReturnsAsync(provinces);

            // Act
            var result = await _controller.SearchProvincesByName(request);

            // Assert
            Assert.That(result.Result, Is.TypeOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult, Is.Not.Null);
            
            var provinceResponses = okResult.Value as List<ProvinceResponse>;
            Assert.That(provinceResponses, Is.Not.Null);
            Assert.That(provinceResponses.Count, Is.EqualTo(2));
            Assert.That(provinceResponses[0].Name, Is.EqualTo("Hà Nội"));
            Assert.That(provinceResponses[1].Name, Is.EqualTo("Hà Nam"));
        }

        [Test]
        public async Task SearchProvincesByName_WithNullRequest_ReturnsBadRequest()
        {
            // Arrange
            SearchByNameRequest request = null;

            // Act
            var result = await _controller.SearchProvincesByName(request);

            // Assert
            Assert.That(result.Result, Is.TypeOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task SearchProvincesByName_WithEmptyName_ReturnsBadRequest()
        {
            // Arrange
            var request = new SearchByNameRequest { Name = "" };

            // Act
            var result = await _controller.SearchProvincesByName(request);

            // Assert
            Assert.That(result.Result, Is.TypeOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task FilterProvincesByType_ReturnsMatchingProvinces()
        {
            // Arrange
            string type = "Thành phố";
            
            var provinces = new List<ProvincesNew>
            {
                new ProvincesNew { Id = 1, Code = "01", Name = "Hà Nội", Type = type },
                new ProvincesNew { Id = 2, Code = "02", Name = "Hồ Chí Minh", Type = type }
            };
            
            _mockProvincesService.Setup(service => service.GetProvincesByTypeAsync(type))
                .ReturnsAsync(provinces);

            // Act
            var result = await _controller.FilterProvincesByType(type);

            // Assert
            Assert.That(result.Result, Is.TypeOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult, Is.Not.Null);
            
            var provinceResponses = okResult.Value as List<ProvinceResponse>;
            Assert.That(provinceResponses, Is.Not.Null);
            Assert.That(provinceResponses.Count, Is.EqualTo(2));
            Assert.That(provinceResponses[0].Type, Is.EqualTo("Thành phố"));
            Assert.That(provinceResponses[1].Type, Is.EqualTo("Thành phố"));
        }
    }
}
