using Hangfire;
using Hangfire.Storage;
using Hangfire.Storage.Monitoring;

namespace NDS.ETL.BackgroundJobs.Services;

public class RecurringJobService(
    IRecurringJobManager recurringJobManager,
    IBackgroundJobClient backgroundJobClient,
    IEnumerable<IRecurringJob> jobs)
    : BackgroundService
{
    private readonly IRecurringJobManager _recurringJobManager = recurringJobManager;
    private readonly IBackgroundJobClient _backgroundJobClient = backgroundJobClient;
    private readonly IEnumerable<IRecurringJob> _jobs = jobs;

    [Obsolete]
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        using (var connection = JobStorage.Current.GetConnection())
        {
            foreach (var recurringJob in connection.GetRecurringJobs())
            {
                RecurringJob.RemoveIfExists(recurringJob.Id);
            }
        }

        PurgeJobs(JobStorage.Current.GetMonitoringApi());

        foreach (var job in _jobs)
        {
            if (job.Disable)
            {
                _recurringJobManager.RemoveIfExists(job.JobId);
            }
            else
            {
                if (job.Immediately)
                {
                    _backgroundJobClient.Enqueue(() => job.ExecuteAsync(stoppingToken));
                }
                var expression = string.IsNullOrEmpty(job.CronExpression) ? Cron.Never() : job.CronExpression;
                _recurringJobManager.AddOrUpdate(job.JobId, () => job.ExecuteAsync(stoppingToken), expression, TimeZoneInfo.Local);
            }
        }
        while (!stoppingToken.IsCancellationRequested)
        {
            await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
        }
    }
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        foreach (var job in _jobs)
        {
            _recurringJobManager.RemoveIfExists(job.JobId);
        }
        await base.StopAsync(cancellationToken);
    }

    private static void PurgeJobs(IMonitoringApi monitor)
    {
        var toDelete = new List<string>();

        foreach (QueueWithTopEnqueuedJobsDto queue in monitor.Queues())
        {
            for (var i = 0; i < Math.Ceiling(queue.Length / 1000d); i++)
            {
                monitor.EnqueuedJobs(queue.Name, 1000 * i, 1000)
                    .ForEach(x => toDelete.Add(x.Key));
            }
        }

        foreach (var jobId in toDelete)
        {
            BackgroundJob.Delete(jobId);
        }
    }
}