using System.ComponentModel.DataAnnotations;

namespace NDS.ETL.BackgroundJobs.Model.API;

/// <summary>
/// Request model for searching by name
/// </summary>
public class SearchByNameRequest
{
    /// <summary>
    /// Tên để tìm kiếm
    /// </summary>
    [Required]
    public string Name { get; set; } = null!;
}

/// <summary>
/// Request model for searching by name with pagination
/// </summary>
public class SearchByNamePaginatedRequest : SearchByNameRequest
{
    /// <summary>
    /// Số trang (bắt đầu từ 1)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Kích thước trang (số lượng kết quả trên mỗi trang)
    /// </summary>
    public int PageSize { get; set; } = 10;
}

/// <summary>
/// Request model for searching ward by location coordinates
/// </summary>
public class GetWardByLocationRequest
{
    /// <summary>
    /// Tọ<PERSON> độ vị trí để tìm kiếm ward
    /// </summary>
    [Required]
    public LocationCoordinates Location { get; set; } = null!;

    /// <summary>
    /// Địa chỉ hiện tại theo format cũ (tên, đường, phường, quận, thành phố)
    /// </summary>
    public string? CurrentAddress { get; set; }
}

/// <summary>
/// Model for location coordinates (latitude and longitude)
/// </summary>
public class LocationCoordinates
{
    /// <summary>
    /// Vĩ độ (latitude)
    /// </summary>
    [Required]
    [Range(-90, 90, ErrorMessage = "Latitude must be between -90 and 90")]
    public double Latitude { get; set; }

    /// <summary>
    /// Kinh độ (longitude)
    /// </summary>
    [Required]
    [Range(-180, 180, ErrorMessage = "Longitude must be between -180 and 180")]
    public double Longitude { get; set; }
}
