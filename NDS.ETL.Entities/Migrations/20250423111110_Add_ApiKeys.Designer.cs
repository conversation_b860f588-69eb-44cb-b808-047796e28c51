// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NDS.ETL.Entities.Context;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace NDS.ETL.Entities.Migrations
{
    [DbContext(typeof(ESDbContext))]
    [Migration("20250423111110_Add_ApiKeys")]
    partial class Add_ApiKeys
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 63)
                .HasAnnotation("Relational:MigrationHistoryTable", "EFMigrations_ESDbContext");

            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "postgis");
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("NDS.ETL.Entities.ESEntities.ApiKey", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<DateTime?>("ExpirationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("IsActive");

                    b.HasIndex("Key")
                        .IsUnique();

                    b.ToTable("ApiKeys");
                });

            modelBuilder.Entity("NDS.ETL.Entities.ESEntities.ApiKeyPermission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ApiKeyId")
                        .HasColumnType("uuid");

                    b.Property<int>("PermissionName")
                        .HasMaxLength(128)
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ApiKeyId");

                    b.HasIndex("PermissionName");

                    b.ToTable("ApiKeyPermissions");
                });

            modelBuilder.Entity("NDS.ETL.Entities.ESEntities.ApiKeyPermission", b =>
                {
                    b.HasOne("NDS.ETL.Entities.ESEntities.ApiKey", "ApiKey")
                        .WithMany("Permissions")
                        .HasForeignKey("ApiKeyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApiKey");
                });

            modelBuilder.Entity("NDS.ETL.Entities.ESEntities.ApiKey", b =>
                {
                    b.Navigation("Permissions");
                });
#pragma warning restore 612, 618
        }
    }
}
