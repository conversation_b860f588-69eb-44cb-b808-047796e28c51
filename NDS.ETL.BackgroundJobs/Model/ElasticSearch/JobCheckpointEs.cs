using System;

namespace NDS.ETL.BackgroundJobs.Model.ElasticSearch;

/// <summary>
/// Lớp này đại diện cho checkpoint của một job trong Elasticsearch,
/// lưu trữ trạng thái xử lý hiện tại để có thể recovery khi job bị lỗi
/// </summary>
public class JobCheckpointEs
{
    /// <summary>
    /// ID duy nhất của checkpoint, thường là tên của job
    /// </summary>
    public string Id { get; set; }
    
    /// <summary>
    /// ID cuối cùng đã xử lý của job
    /// </summary>
    public long LastProcessedId { get; set; }
    
    /// <summary>
    /// Thời điểm cập nhật checkpoint gần nhất
    /// </summary>
    public DateTime UpdatedAt { get; set; }
    
    /// <summary>
    /// Tổng số bản ghi đã xử lý trong lần chạy gần nhất
    /// </summary>
    public long TotalProcessed { get; set; }
    
    /// <summary>
    /// Trạng thái của job
    /// </summary>
    public string Status { get; set; }
    
    /// <summary>
    /// Thông tin bổ sung (có thể dùng để lưu metadata)
    /// </summary>
    public string AdditionalInfo { get; set; }
    
    /// <summary>
    /// Tốc độ xử lý hiện tại (records/giây)
    /// </summary>
    public double ProcessingSpeed { get; set; }
    
    /// <summary>
    /// Số bản ghi duy nhất đã được xử lý
    /// </summary>
    public long UniqueProcessed { get; set; }
    
    /// <summary>
    /// Tổng số bản ghi trong index cần xử lý
    /// </summary>
    public long TotalRecordsInIndex { get; set; }
    
    /// <summary>
    /// Phần trăm tiến độ hoàn thành
    /// </summary>
    public double ProgressPercentage { get; set; }
    
    /// <summary>
    /// Thời gian ước tính hoàn thành
    /// </summary>
    public DateTime EstimatedCompletionTime { get; set; }
    
    /// <summary>
    /// Số batch đã xử lý
    /// </summary>
    public int BatchesProcessed { get; set; }
    
    /// <summary>
    /// Lỗi gần nhất nếu có
    /// </summary>
    public string LastError { get; set; }
}