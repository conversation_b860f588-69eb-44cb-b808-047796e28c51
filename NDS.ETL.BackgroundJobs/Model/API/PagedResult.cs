using System.Collections.Generic;

namespace NDS.ETL.BackgroundJobs.Model.API;

/// <summary>
/// M� h�nh k?t qu? ph�n trang chung cho API
/// </summary>
/// <typeparam name="T">Lo?i d? li?u trong danh s�ch</typeparam>
public class PagedResult<T>
{
    /// <summary>
    /// Danh s�ch c�c m?c trong trang hi?n t?i
    /// </summary>
    public List<T> Items { get; set; } = new List<T>();
    
    /// <summary>
    /// T?ng s? m?c trong to�n b? d? li?u
    /// </summary>
    public int TotalCount { get; set; }
    
    /// <summary>
    /// S? trang hi?n t?i (b?t ??u t? 1)
    /// </summary>
    public int CurrentPage { get; set; }
    
    /// <summary>
    /// S? l??ng m?c tr�n m?i trang
    /// </summary>
    public int PageSize { get; set; }
    
    /// <summary>
    /// T?ng s? trang
    /// </summary>
    public int TotalPages { get; set; }
    
    /// <summary>
    /// C� trang tr??c kh�ng
    /// </summary>
    public bool HasPreviousPage => CurrentPage > 1;
    
    /// <summary>
    /// C� trang sau kh�ng
    /// </summary>
    public bool HasNextPage => CurrentPage < TotalPages;
}