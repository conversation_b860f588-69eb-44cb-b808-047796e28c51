using Elastic.Clients.Elasticsearch;
using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch.FavPlaceSync;
using NDS.ETL.Entities.Context;
using NDS.ETL.Infrastructure.Common;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.FavPlaceSync;

/// <summary>
/// Xử lý quá trình đồng bộ dữ liệu
/// </summary>
public class SyncProcessor
{
    private readonly ILogger _logger;
    private readonly ElasticsearchDataService _esService;
    private readonly DbDataService _dbService;
    private readonly ElasticsearchClient _elasticClient;
    private readonly string _indexName;
    private readonly IScopedResolver<NpgPlaceContext> _dbContextResolver;
    private readonly RecurringJobBaseElastic _recurringJobBase;
    private const int MAX_CONCURRENT_TASKS = 2;
    private const int BATCH_SIZE = 10_000;

    public SyncProcessor(
        ILogger logger,
        ElasticsearchDataService esService,
        DbDataService dbService,
        ElasticsearchClient elasticClient,
        string indexName,
        IScopedResolver<NpgPlaceContext> dbContextResolver = null,
        RecurringJobBaseElastic recurringJobBase = null)
    {
        _logger = logger;
        _esService = esService;
        _dbService = dbService;
        _elasticClient = elasticClient;
        _indexName = indexName;
        _dbContextResolver = dbContextResolver;
        _recurringJobBase = recurringJobBase;
    }

    /// <summary>
    /// Thực hiện đồng bộ toàn bộ dữ liệu
    /// </summary>
    public async Task PerformFullSync(
        NpgPlaceContext dbContext,
        long maxId,
        int totalCount,
        object syncLock,
        CancellationToken stoppingToken,
        SyncStats syncStats,
        int maxRetries,
        int pageSize)
    {
        var minId = await dbContext.FavoritePlaces.MinAsync(x => x.Id, stoppingToken);

        // Tính toán số lượng batch dựa trên khoảng ID thực tế
        var totalBatches = (int)Math.Ceiling((double)(maxId - minId + 1) / pageSize);

        _logger.LogInformation($"Full sync: {totalCount:N0} records (ID range: {minId}-{maxId}) in {totalBatches} batches");

        using var semaphore = new SemaphoreSlim(MAX_CONCURRENT_TASKS);
        var processingTasks = new List<Task>();
        var failedBatches = new ConcurrentBag<(long startId, long endId)>();

        for (var batchIndex = 0; batchIndex < totalBatches; batchIndex++)
        {
            if (stoppingToken.IsCancellationRequested)
            {
                _logger.LogWarning("Cancellation requested, stopping sync");
                break;
            }

            var startId = minId + (batchIndex * pageSize);
            var endId = Math.Min(startId + pageSize - 1, maxId);

            await semaphore.WaitAsync(stoppingToken);

            processingTasks.Add(Task.Run(async () =>
            {
                try
                {
                    var batchStopwatch = Stopwatch.StartNew();
                    var batchRetries = 0;
                    var success = false;

                    while (!success && batchRetries < maxRetries && !stoppingToken.IsCancellationRequested)
                    {
                        try
                        {
                            // Kiểm tra token trước khi thực hiện
                            stoppingToken.ThrowIfCancellationRequested();

                            // Sử dụng một context mới cho mỗi batch
                            await _dbContextResolver.ResolveAsync(async (batchDbContext) =>
                            {
                                batchDbContext.Database.SetCommandTimeout(3000);

                                var data = await _dbService.FetchBatchByIdRangeAsync(batchDbContext, startId, endId, stoppingToken);

                                if (data.Any())
                                {
                                    var esDocuments = _dbService.MapToEsDocuments(data);
                                    await IndexDocuments(esDocuments, stoppingToken);

                                    lock (syncLock)
                                    {
                                        syncStats.TotalProcessed += data.Count;
                                        syncStats.BatchSuccessCount++;

                                        var elapsedTime = batchStopwatch.Elapsed.TotalSeconds;
                                        LogBatchProgress(
                                            "ID range",
                                            $"{startId}-{endId}",
                                            data.Count,
                                            elapsedTime,
                                            syncStats.TotalProcessed,
                                            totalCount);
                                    }

                                    success = true;
                                }
                                else
                                {
                                    _logger.LogDebug($"No records found in batch ID range {startId}-{endId}");
                                    success = true;
                                }

                                return true;
                            });
                        }
                        catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
                        {
                            _logger.LogDebug("Task canceled");
                            throw;
                        }
                        catch (Exception ex)
                        {
                            if (ex is OperationCanceledException && stoppingToken.IsCancellationRequested)
                            {
                                throw;
                            }

                            batchRetries++;

                            if (batchRetries >= maxRetries)
                            {
                                lock (syncLock)
                                {
                                    syncStats.BatchFailureCount++;
                                    failedBatches.Add((startId, endId));
                                    _logger.LogError(ex, $"Failed to process batch ID range {startId}-{endId} after {maxRetries} attempts");
                                }
                            }
                            else
                            {
                                lock (syncLock)
                                {
                                    syncStats.RetryCount++;
                                    _logger.LogWarning($"Retrying batch ID={startId}-{endId} (Attempt {batchRetries}/{maxRetries})");
                                }

                                // Exponential backoff before retry
                                await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, batchRetries)), stoppingToken);
                            }
                        }
                    }

                    // Kiểm tra lại token sau khi kết thúc vòng lặp
                    stoppingToken.ThrowIfCancellationRequested();
                }
                catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
                {
                    _logger.LogDebug("Task canceled");
                }
                catch (Exception ex) when (!(ex is OperationCanceledException))
                {
                    _logger.LogError(ex, "Unhandled exception in processing task");
                }
                finally
                {
                    semaphore.Release();
                }
            }, stoppingToken));
        }

        // Chờ tất cả các task hoàn thành hoặc bị hủy
        try
        {
            await Task.WhenAll(processingTasks.Select(t => t.ContinueWith(task =>
            {
                if (task.IsFaulted && !(task.Exception?.InnerException is OperationCanceledException))
                {
                    _logger.LogError(task.Exception, "Task failed with unhandled exception");
                }
                else if (task.IsCanceled || (task.IsFaulted && task.Exception?.InnerException is OperationCanceledException))
                {
                    // Ghi nhận task bị hủy nhưng không coi là lỗi
                    _logger.LogDebug("A processing task was canceled");
                }
            }, TaskContinuationOptions.None)));
        }
        catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
        {
            _logger.LogInformation("Task processing was canceled");
            throw;
        }

        // Kiểm tra nếu mã thông báo hủy đã được kích hoạt và chỉ ghi log
        if (stoppingToken.IsCancellationRequested)
        {
            _logger.LogInformation("Sync canceled");
            throw new OperationCanceledException(stoppingToken);
        }
    }

    /// <summary>
    /// Thực hiện đồng bộ gia tăng (incremental) cho dữ liệu
    /// </summary>
    public async Task PerformIncrementalSync(
        NpgPlaceContext dbContext,
        DateTime lastUpdatedAt,
        DateTime currentUpdatedAt,
        long maxIdInEs,
        int totalToSync,
        object syncLock,
        CancellationToken stoppingToken,
        SyncStats syncStats,
        int maxRetries,
        int pageSize,
        bool syncNewRecordsOnly = false,
        DateTime? lastCreateAt = null,
        DateTime? currentCreateAt = null)
    {
        _logger.LogInformation(syncNewRecordsOnly 
            ? $"Starting incremental sync of NEW records (CreatedAt > {lastCreateAt:yyyy-MM-dd HH:mm:ss.fff})" 
            : $"Starting incremental sync of UPDATED records (UpdatedAt > {lastUpdatedAt:yyyy-MM-dd HH:mm:ss.fff})");
        
        // Phân tích các batch để xử lý
        using var semaphore = new SemaphoreSlim(MAX_CONCURRENT_TASKS);
        var processingTasks = new List<Task>();
        
        try
        {
            if (syncNewRecordsOnly && lastCreateAt.HasValue && currentCreateAt.HasValue)
            {
                // Xử lý các bản ghi mới (có CreatedAt nhưng UpdatedAt là null)
                var batchesWithData = await _dbContextResolver.ResolveAsync(async (ctx) =>
                {
                    // Đảm bảo context có timeout đủ lớn
                    ctx.Database.SetCommandTimeout(3000);
                    
                    var minId = await ctx.FavoritePlaces
                        .Where(x => x.UpdatedAt == null && x.CreatedAt != null && x.CreatedAt > lastCreateAt)
                        .MinAsync(m => (int?)m.Id, cancellationToken: stoppingToken) ?? 0;
                        
                    var maxId = await ctx.FavoritePlaces
                        .Where(x => x.UpdatedAt == null && x.CreatedAt != null && x.CreatedAt > lastCreateAt)
                        .MaxAsync(m => (int?)m.Id, cancellationToken: stoppingToken) ?? 0;
                        
                    if (minId == 0 && maxId == 0)
                    {
                        return new List<long>();
                    }
                    
                    _logger.LogInformation($"New records data range: ID from {minId} to {maxId}");
                    
                    // Tính toán số lượng batch cần kiểm tra
                    int startBatch = minId / pageSize;
                    int endBatch = maxId / pageSize;
                    int totalBatchesToCheck = endBatch - startBatch + 1;
                    
                    List<long> batchesWithData = new List<long>();
                    for (int i = startBatch; i <= endBatch; i++)
                    {
                        long batchStartId = i * pageSize;
                        long batchEndId = batchStartId + pageSize - 1;
                        
                        // Kiểm tra xem batch có dữ liệu phù hợp không
                        bool hasData = await ctx.FavoritePlaces
                            .Where(x => x.UpdatedAt == null && x.CreatedAt != null && x.CreatedAt > lastCreateAt)
                            .Where(x => x.Id >= batchStartId && x.Id <= batchEndId)
                            .AnyAsync(stoppingToken);
                        
                        if (hasData)
                        {
                            batchesWithData.Add(i);
                        }
                    }
                    
                    _logger.LogInformation($"Found {batchesWithData.Count} batches with new records");
                    return batchesWithData;
                });
                
                foreach (var batchIndex in batchesWithData)
                {
                    if (stoppingToken.IsCancellationRequested)
                    {
                        break;
                    }
                    
                    var startId = batchIndex * pageSize;
                    var endId = startId + pageSize - 1;
                    
                    await semaphore.WaitAsync(stoppingToken);
                    
                    // Xử lý các bản ghi mới trong batch
                    processingTasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            // Sử dụng scope mới để xử lý mỗi batch
                            await _dbContextResolver.ResolveAsync(async (batchCtx) =>
                            {
                                batchCtx.Database.SetCommandTimeout(3000);
                                
                                // Lấy các bản ghi mới
                                var data = await _dbService.FetchNewBatchByIdRangeAsync(batchCtx, startId, endId, stoppingToken);
                                
                                if (data.Any())
                                {
                                    var batchStopwatch = Stopwatch.StartNew();
                                    
                                    var esDocuments = _dbService.MapToEsDocuments(data);
                                    await IndexDocuments(esDocuments, stoppingToken);
                                    
                                    lock (syncLock)
                                    {
                                        syncStats.TotalProcessed += data.Count;
                                        syncStats.BatchSuccessCount++;
                                        
                                        var elapsedTime = batchStopwatch.Elapsed.TotalSeconds;
                                        LogBatchProgress(
                                            "New records",
                                            $"ID {startId}-{endId}",
                                            data.Count,
                                            elapsedTime,
                                            syncStats.TotalProcessed,
                                            totalToSync);
                                    }
                                }
                                else
                                {
                                    _logger.LogDebug($"No new records found in batch {startId}-{endId}");
                                }
                                
                                return true;
                            });
                        }
                        catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
                        {
                            // Bỏ qua khi bị hủy
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"Error processing new records batch {startId}-{endId}");
                            lock (syncLock)
                            {
                                syncStats.BatchFailureCount++;
                            }
                        }
                        finally
                        {
                            semaphore.Release();
                        }
                    }, stoppingToken));
                }
            }
            else
            {
                // Xử lý các bản ghi đã cập nhật
                var batchesWithData = await _dbContextResolver.ResolveAsync(async (ctx) =>
                {
                    // Đảm bảo context có timeout đủ lớn
                    ctx.Database.SetCommandTimeout(3000);
                    
                    var minId = await ctx.FavoritePlaces
                        .Where(x => x.UpdatedAt > lastUpdatedAt)
                        .MinAsync(m => (int?)m.Id, cancellationToken: stoppingToken) ?? 0;
                        
                    var maxId = await ctx.FavoritePlaces
                        .Where(x => x.UpdatedAt > lastUpdatedAt)
                        .MaxAsync(m => (int?)m.Id, cancellationToken: stoppingToken) ?? 0;
                        
                    if (minId == 0 && maxId == 0)
                    {
                        return new List<long>();
                    }
                    
                    _logger.LogInformation($"Updated records data range: ID from {minId} to {maxId}");
                    
                    // Tính toán số lượng batch cần kiểm tra
                    int startBatch = minId / pageSize;
                    int endBatch = maxId / pageSize;
                    int totalBatchesToCheck = endBatch - startBatch + 1;
                    
                    List<long> batchesWithData = new List<long>();
                    for (int i = startBatch; i <= endBatch; i++)
                    {
                        long batchStartId = i * pageSize;
                        long batchEndId = batchStartId + pageSize - 1;
                        
                        // Kiểm tra xem batch có dữ liệu phù hợp không
                        bool hasData = await ctx.FavoritePlaces
                            .Where(x => x.UpdatedAt > lastUpdatedAt)
                            .Where(x => x.Id >= batchStartId && x.Id <= batchEndId)
                            .AnyAsync(stoppingToken);
                        
                        if (hasData)
                        {
                            batchesWithData.Add(i);
                        }
                    }
                    
                    _logger.LogInformation($"Found {batchesWithData.Count} batches with updated records");
                    return batchesWithData;
                });
                
                foreach (var batchIndex in batchesWithData)
                {
                    if (stoppingToken.IsCancellationRequested)
                    {
                        break;
                    }
                    
                    var startId = batchIndex * pageSize;
                    var endId = startId + pageSize - 1;
                    
                    await semaphore.WaitAsync(stoppingToken);
                    
                    // Xử lý các bản ghi đã cập nhật trong batch
                    processingTasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            // Sử dụng scope mới để xử lý mỗi batch
                            await _dbContextResolver.ResolveAsync(async (batchCtx) =>
                            {
                                batchCtx.Database.SetCommandTimeout(3000);
                                
                                // Lấy các bản ghi đã cập nhật
                                var data = await _dbService.FetchUpdatedRecordsAsync(batchCtx, startId, endId, lastUpdatedAt, stoppingToken);
                                
                                if (data.Any())
                                {
                                    var batchStopwatch = Stopwatch.StartNew();
                                    
                                    var esDocuments = _dbService.MapToEsDocuments(data);
                                    await IndexDocuments(esDocuments, stoppingToken);
                                    
                                    lock (syncLock)
                                    {
                                        syncStats.TotalProcessed += data.Count;
                                        syncStats.BatchSuccessCount++;
                                        
                                        var elapsedTime = batchStopwatch.Elapsed.TotalSeconds;
                                        LogBatchProgress(
                                            "Updated records",
                                            $"ID {startId}-{endId}",
                                            data.Count,
                                            elapsedTime,
                                            syncStats.TotalProcessed,
                                            totalToSync);
                                    }
                                }
                                else
                                {
                                    _logger.LogDebug($"No updated records found in batch {startId}-{endId}");
                                }
                                
                                return true;
                            });
                        }
                        catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
                        {
                            // Bỏ qua khi bị hủy
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"Error processing updated records batch {startId}-{endId}");
                            lock (syncLock)
                            {
                                syncStats.BatchFailureCount++;
                            }
                        }
                        finally
                        {
                            semaphore.Release();
                        }
                    }, stoppingToken));
                }
            }
            
            // Chờ tất cả các task hoàn thành hoặc bị hủy
            try 
            {
                await Task.WhenAll(processingTasks.Select(t => t.ContinueWith(task =>
                {
                    if (task.IsFaulted && !(task.Exception?.InnerException is OperationCanceledException))
                    {
                        _logger.LogError(task.Exception, "Task failed with unhandled exception");
                    }
                })));
                
                _logger.LogInformation(syncNewRecordsOnly
                    ? $"Completed incremental sync of NEW records: {syncStats.TotalProcessed} records processed"
                    : $"Completed incremental sync of UPDATED records: {syncStats.TotalProcessed} records processed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error waiting for processing tasks to complete");
            }
        }
        catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
        {
            _logger.LogInformation("Incremental sync canceled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during incremental sync");
            throw;
        }
    }

    /// <summary>
    /// Đánh chỉ mục cho các documents trong Elasticsearch
    /// </summary>
    private async Task IndexDocuments(List<FavPlaceEs> documents, CancellationToken stoppingToken)
    {
        // Sử dụng cấu hình từ RecurringJobBaseElastic
        await _recurringJobBase.IndexMany(documents, stoppingToken, _indexName, BATCH_SIZE);
    }

    /// <summary>
    /// Ghi log tiến trình xử lý batch
    /// </summary>
    private void LogBatchProgress(
        string batchType,
        string batchIdentifier,
        int recordCount,
        double elapsedSeconds,
        int totalProcessed,
        int totalToProcess)
    {
        var recordsPerSecond = recordCount > 0 && elapsedSeconds > 0
            ? Math.Round(recordCount / elapsedSeconds, 0)
            : 0;
        var completionPercentage = totalToProcess > 0
            ? Math.Round((double)totalProcessed * 100 / totalToProcess, 2)
            : 0;

        _logger.LogInformation(
            $"Processed {batchType} batch {batchIdentifier} ({recordCount:N0} records) " +
            $"in {elapsedSeconds:F2}s ({recordsPerSecond:F0} records/sec). " +
            $"Progress: {totalProcessed:N0}/{totalToProcess:N0} ({completionPercentage:F2}%)");
    }
}