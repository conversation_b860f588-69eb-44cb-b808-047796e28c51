using System.Diagnostics;
using Elastic.Clients.Elasticsearch;
using KnowledgeBase.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Model.PlaceApproveMaster;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Entities.Context;
using NDS.ETL.Entities.PostgresPlace;
using NDS.ETL.Infrastructure.Common;
using NDS.ETL.Infrastructure.Utils;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.ESTools
{
    public class SyncPlaceAfterApproveJob : RecurringJobBaseElastic
    {
        private readonly ILogger<RecurringJobBaseElastic> _logger;
        private readonly IScopedResolver<NpgPlaceContext> _dbContext;
        private readonly ElasticSearchConfig _indexName;
        private readonly IESPlacesService _iesPlacesService;
        
        public SyncPlaceAfterApproveJob(IConfiguration configuration, IESPlacesService iesPlacesService, ElasticsearchClient elasticClient, ILogger<SyncPlaceAfterApproveJob> logger, IScopedResolver<NpgPlaceContext> dbContext) : base(configuration, logger, elasticClient, null)
        {
            _logger = logger;
            _dbContext = dbContext;
            _indexName = configuration.Bind<ElasticSearchConfig>(nameof(ElasticSearchConfig));
            _iesPlacesService = iesPlacesService;
        }

        public override string JobId => "SyncPlaceAfterApproveJob";

        public override string ConfigSection => "JobPgSyncPlace";

        public override async Task DoWorkAsync(CancellationToken stoppingToken)
        {
            try
            {
                await _dbContext.ResolveAsync(async (context) => {
                    Stopwatch stw = new Stopwatch();
                    stw.Start();
                    var skip = 0;
                    var take = 100;
                    Stopwatch getDataTime = new Stopwatch();
                    while (true)
                    {
                        getDataTime.Start();
                        var pathOfDataNew = context.PlaceApproves.Where(x => x.StatusApprove == 5 && x.ScanProcessed == 1 && x.IsDeleted == false)?.OrderBy(x => x.Id).Skip(skip).Take(take).ToList();
                        getDataTime.Stop();
                        _logger.LogInformation($"Total data get from PlaceApproves: {pathOfDataNew?.Count} ---------------: {getDataTime.ElapsedMilliseconds} miliseconds");
                        if (pathOfDataNew == null || pathOfDataNew.Count == 0) break;
                        getDataTime.Start();
                        var pathOfPlaceId = pathOfDataNew?.Select(x => x.PlaceId).ToList();
                        getDataTime.Stop();
                        _logger.LogInformation($" Time to get placeId from data: {pathOfPlaceId?.Count}/{pathOfDataNew?.Count()} ---------------: {getDataTime.ElapsedMilliseconds} miliseconds");
                        if (pathOfPlaceId == null || pathOfPlaceId.Count == 0) break;
                        getDataTime.Start();
                        var pathOfDataRawEs = await _iesPlacesService.GetPlacesByPlaceIdsAsync(pathOfPlaceId, stoppingToken);
                        getDataTime.Stop();
                        _logger.LogInformation($" Time to get data from es: {pathOfDataRawEs.Count()} ---------------: {getDataTime.ElapsedMilliseconds} miliseconds");
                        if (pathOfDataRawEs == null || pathOfDataRawEs.Count == 0) break;
                        getDataTime.Start();
                        var pathOfDataRawDb = await context.Places.Where(x => pathOfPlaceId.Contains(x.PlaceId)).ToListAsync(cancellationToken: stoppingToken);
                        getDataTime.Stop();
                        _logger.LogInformation($" Time to get data from Places: {pathOfDataRawDb.Count} ---------------: {getDataTime.ElapsedMilliseconds} miliseconds");
                        getDataTime.Start();
                        if (pathOfDataRawDb == null || pathOfDataRawDb.Count == 0 ) break;
                        var lstPlaceApproveMasterDto = new List<PlaceApproveMasterDto>();
                        lstPlaceApproveMasterDto = pathOfDataNew?.Select(x => new PlaceApproveMasterDto
                        {
                            PlaceApprove = x,
                            PlacesEs = pathOfDataRawEs.FirstOrDefault(y => y.PlaceId == x.PlaceId),
                            Place = pathOfDataRawDb.FirstOrDefault(y => y.PlaceId == x.PlaceId)
                        }).ToList();
                        if(lstPlaceApproveMasterDto == null || lstPlaceApproveMasterDto.Count == 0) break;
                        getDataTime.Stop();
                        _logger.LogInformation($" Time to map data into PlaceApproveMasterDto: {lstPlaceApproveMasterDto.Count()} / {pathOfDataNew?.Count} records ---------------: {getDataTime.ElapsedMilliseconds} miliseconds");    
                        getDataTime.Start();
                        var lstDataEsUpdate = new List<PlacesEs>();
                        var lstDataDbUpdate = new List<Place>();
                        var lstDataApprove = new List<PlaceApprove>();
                        foreach (var item in lstPlaceApproveMasterDto)
                        {
                            if (item.PlaceApprove.ScanProcessed == 1 && item.PlaceApprove.StatusApprove == 5)
                            {
                                if (item.PlaceApprove.Location == null)
                                {
                                    _logger.LogError($"Skip placeApprove {item.PlaceApprove.PlaceId} null location");
                                    continue;
                                }
                                var latNew = item.PlaceApprove.Location.Y;
                                var lonNew = item.PlaceApprove.Location.X;
                                if(item.Place != null)
                                {
                                    item.Place.Name = item.PlaceApprove.Name;
                                    item.Place.FullAddress = item.PlaceApprove.Address != null ? item.PlaceApprove.Address : "";
                                    item.Place.Address = item.PlaceApprove.Address != null ? item.PlaceApprove.Address : "";
                                    item.Place.Location = item.PlaceApprove.Location;
                                    item.Place.ProvinceId = item.PlaceApprove.ProvinceId;
                                    item.Place.DistrictId = item.PlaceApprove.DistrictId;
                                    item.Place.WardId = item.PlaceApprove.WardId;
                                    item.Place.LastUpdateUser = item.PlaceApprove.CreatorUserId;
                                    item.Place.Processed = true;
                                    item.Place.UpdatedAt = item.PlaceApprove.CreatedAt;
                                    lstDataDbUpdate.Add(item.Place);
                                }
                                if(item.PlacesEs != null)
                                {
                                    item.PlacesEs.Name = item.PlaceApprove.Name;
                                    item.PlacesEs.FullAddress = item.PlaceApprove.Address != null ? item.PlaceApprove.Address : "";
                                    item.PlacesEs.Address = item.PlaceApprove.Address != null ? item.PlaceApprove.Address : "";
                                    item.PlacesEs.Location = new Location { Lat = latNew, Lon = lonNew };
                                    item.PlacesEs.ProvinceId = item.PlaceApprove.ProvinceId;
                                    item.PlacesEs.DistrictId = item.PlaceApprove.DistrictId;
                                    item.PlacesEs.WardId = item.PlaceApprove.WardId;
                                    item.PlacesEs.LastUpdateUser = item.PlaceApprove.CreatorUserId;
                                    item.PlacesEs.ScanProcessed = item.PlaceApprove.ScanProcessed;
                                    item.PlacesEs.ReasonOperations = item.PlaceApprove.ReasonOperations; 
                                    item.PlacesEs.Reason = item.PlaceApprove.Reason != null ? item.PlaceApprove.Reason : "";
                                    item.PlacesEs.Note = item.PlaceApprove.Note;
                                    item.PlacesEs.StatusApprovePlace = 5;
                                    item.PlacesEs.UpdatedAt = item.PlaceApprove.CreatedAt;
                                    lstDataEsUpdate.Add(item.PlacesEs);
                                }
                                item.PlaceApprove.IsDeleted = true;
                                lstDataApprove.Add(item.PlaceApprove);
                            }
                        }
                        context.Places.UpdateRange(lstDataDbUpdate);
                        context.PlaceApproves.UpdateRange(lstDataApprove);
                        await context.SaveChangesAsync(stoppingToken);
                        await IndexMany(lstDataEsUpdate, stoppingToken, _indexName.Index[0], lstDataEsUpdate.Count);
                        getDataTime.Stop();
                        _logger.LogInformation($" Time to update data into db and es: {lstPlaceApproveMasterDto.Count()} / {take} records ---------------: {getDataTime.ElapsedMilliseconds} miliseconds");
                        skip += take;
                    }
                    stw.Stop();
                    return true;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while comparing score change places.");
                throw;
            }
        }
    }
}
