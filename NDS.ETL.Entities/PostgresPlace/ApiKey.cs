using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using NodaTime;

namespace NDS.ETL.Entities.PostgresPlace;

[Index("IsActive", Name = "IX_ApiKeys_IsActive")]
[Index("Key", Name = "IX_ApiKeys_Key", IsUnique = true)]
public partial class ApiKey
{
    [Key]
    public Guid Id { get; set; }

    [StringLength(256)]
    public string Key { get; set; } = null!;

    [StringLength(512)]
    public string Description { get; set; } = null!;

    public Instant CreatedAt { get; set; }

    public Instant? ExpirationDate { get; set; }

    public bool IsActive { get; set; }

    [InverseProperty("ApiKey")]
    public virtual ICollection<ApiKeyPermission> ApiKeyPermissions { get; set; } = new List<ApiKeyPermission>();
}
