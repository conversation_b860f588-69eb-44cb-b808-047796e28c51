using System.Data.Common;
using KnowledgeBase.Core.UnitOfWork;

namespace NDS.ETL.BackgroundJobs.Context;

public class PgPlaceContext : UnitOfWorkBase
{
    public PgPlaceContext(IServiceProvider serviceProvider, UnitOfWorkOptions<PgPlaceContext> options, DbTransaction transaction) : base(serviceProvider, options, transaction)
        
    {
    }

    protected void OnConfiguring(IConfiguration configuration, UnitOfWorkOptions options, DbTransaction transaction)
    {
        if (options.IsConfigured)
        {
            return;
        }

        options.ConnectionString = configuration.GetConnectionString("PgPlace");
        options.RetryCount = 5;
    }
}