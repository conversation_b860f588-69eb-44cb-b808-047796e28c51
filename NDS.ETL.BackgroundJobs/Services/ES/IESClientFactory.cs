using Elastic.Clients.Elasticsearch;

namespace NDS.ETL.BackgroundJobs.Services.ES
{
    /// <summary>
    /// Factory interface để cung cấp ElasticsearchClient
    /// </summary>
    public interface IESClientFactory
    {
        /// <summary>
        /// Lấy Elasticsearch client đã được cấu hình
        /// </summary>
        /// <returns>ElasticsearchClient instance</returns>
        ElasticsearchClient GetClient();
        
        /// <summary>
        /// Trả về tên index locations
        /// </summary>
        string GetPlaceIndexName();
        
        /// <summary>
        /// Trả về tên index place-pairs
        /// </summary>
        string GetPlacePairIndexName();
        
        /// <summary>
        /// Trả về tên index favorite places
        /// </summary>
        string GetPlaceFavoriteIndexName();
    }
}
