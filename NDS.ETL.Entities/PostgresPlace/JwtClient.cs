using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("jwt_clients")]
public partial class JwtClient
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("clientId")]
    [StringLength(255)]
    public string? ClientId { get; set; }

    [Column("clientSecretKey")]
    [StringLength(255)]
    public string? ClientSecretKey { get; set; }
}
