namespace NDS.ETL.BackgroundJobs.Model.KafkaSyncModel;

public class KafkaHistoryPlaceModel
{
    public long Id { get; set; }
    public string CustomerId { get; set; }
    public string CustomerPhone { get; set; }
    public string PickupPlaceId { get; set; }
    public string ArrivalPlaceId { get; set; }
    public double[] Location1 { get; set; }
    public double[] Location2 { get; set; }
    public string FromAddress { get; set; }
    public string ToAddress { get; set; }
    public string FromFullAddress { get; set; }
    public string ToFullAddress { get; set; }
    public string FromCity { get; set; }
    public string ToCity { get; set; }
    public double Distance { get; set; }
    public int BookingCount { get; set; }
    public int SearchCount { get; set; }
    public int PaidCount { get; set; }
    public string Hash1 { get; set; }
    public string Hash2 { get; set; }
    public long CreatedAt { get; set; }
    public long UpdateAt { get; set; }
    public long? LastBookingAt { get; set; }
    public long? LastSearchAt { get; set; }
    public string EstimateAmount { get; set; }
    public string LastEstimateAmount { get; set; }
    public string LastNote { get; set; }
} 