namespace NDS.ETL.BackgroundJobs.Configuration;

/// <summary>
/// Configuration for retry policies used in address migration
/// </summary>
public class RetryPolicyConfig
{
    /// <summary>
    /// Maximum number of retry attempts
    /// </summary>
    public int MaxAttempts { get; set; } = 3;

    /// <summary>
    /// Base delay in milliseconds for exponential backoff
    /// </summary>
    public int BaseDelayMs { get; set; } = 1000;

    /// <summary>
    /// Maximum delay in milliseconds
    /// </summary>
    public int MaxDelayMs { get; set; } = 30000;

    /// <summary>
    /// Multiplier for exponential backoff
    /// </summary>
    public double BackoffMultiplier { get; set; } = 2.0;

    /// <summary>
    /// Whether to add jitter to retry delays to avoid thundering herd
    /// </summary>
    public bool UseJitter { get; set; } = true;

    /// <summary>
    /// Jitter factor (0.0 to 1.0) for randomizing retry delays
    /// </summary>
    public double JitterFactor { get; set; } = 0.1;

    /// <summary>
    /// Types of exceptions that should trigger a retry
    /// </summary>
    public List<string> RetryableExceptions { get; set; } = new()
    {
        "System.Data.SqlClient.SqlException",
        "Npgsql.NpgsqlException",
        "System.TimeoutException",
        "System.Net.Http.HttpRequestException",
        "Microsoft.EntityFrameworkCore.DbUpdateException"
    };

    /// <summary>
    /// SQL error codes that should trigger a retry (PostgreSQL specific)
    /// </summary>
    public List<string> RetryableSqlErrorCodes { get; set; } = new()
    {
        "53300", // Too many connections
        "57P01", // Admin shutdown
        "08006", // Connection failure
        "08001", // Unable to connect
        "40001"  // Serialization failure
    };
}
