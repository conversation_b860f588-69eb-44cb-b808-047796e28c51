using log4net.Appender;
using log4net.Core;
using System.Text;
using Confluent.Kafka;

namespace log4net.Kafka.Appender
{
    public class KafkaAppender : AppenderSkeleton
    {
        private IProducer<Null, string> _producer;

        public KafkaSettings KafkaSettings { get; set; }

        public override void ActivateOptions()
        {
            base.ActivateOptions();
            Start();
        }

        private void Start()
        {
            try
            {
                if (KafkaSettings == null)
                    throw new LogException("KafkaSettings is missing");

                if (KafkaSettings.Brokers == null || KafkaSettings.Brokers.Count == 0)
                    throw new Exception("Broker is not found");

                var config = new ProducerConfig
                {
                    BootstrapServers = string.Join(",", KafkaSettings.Brokers)
                };

                _producer = new ProducerBuilder<Null, string>(config).Build();
            }
            catch (Exception ex)
            {
                ErrorHandler.Error("Could not start Kafka producer", ex);
            }
        }

        private string GetTopic(LoggingEvent loggingEvent)
        {
            if (KafkaSettings.Topic != null)
            {
                var sb = new StringBuilder();
                using (var sw = new StringWriter(sb))
                {
                    KafkaSettings.Topic.Format(sw, loggingEvent);
                    return sb.ToString().Replace("?", ""); // Clean invalid
                }
            }

            return $"{loggingEvent.LoggerName}.{loggingEvent.Level.Name}";
        }

        private string GetMessage(LoggingEvent loggingEvent)
        {
            var sb = new StringBuilder();
            using (var sw = new StringWriter(sb))
            {
                Layout.Format(sw, loggingEvent);
                if (Layout.IgnoresException && loggingEvent.ExceptionObject != null)
                    sw.Write(loggingEvent.GetExceptionString());

                return sb.ToString();
            }
        }

        protected override void Append(LoggingEvent loggingEvent)
        {
            try
            {
                string topic = GetTopic(loggingEvent);
                string message = GetMessage(loggingEvent);

                _producer.Produce(topic, new Message<Null, string> { Value = message },
                    deliveryReport =>
                    {
                        if (deliveryReport.Status == PersistenceStatus.NotPersisted)
                        {
                            ErrorHandler.Error($"Message to topic {topic} was not persisted.");
                        }
                    });
            }
            catch (Exception ex)
            {
                ErrorHandler.Error("Could not send message to Kafka broker", ex);
            }
        }

        protected override void OnClose()
        {
            base.OnClose();
            try
            {
                _producer?.Flush(TimeSpan.FromSeconds(5));
                _producer?.Dispose();
            }
            catch (Exception ex)
            {
                ErrorHandler.Error("Error while closing Kafka producer", ex);
            }
        }
    }
}
