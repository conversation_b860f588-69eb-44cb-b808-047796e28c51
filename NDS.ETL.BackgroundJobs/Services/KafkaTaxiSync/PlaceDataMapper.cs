using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Model.KafkaSyncModel;
using NDS.ETL.Infrastructure.Utils;
using Newtonsoft.Json.Linq;

namespace NDS.ETL.BackgroundJobs.Services.KafkaTaxiSync;

// Lớp ánh xạ dữ liệu từ Ka<PERSON>ka sang ElasticSearch cho đối tượng Place (Địa điểm).
// Chịu trách nhiệm chuyển đổi dữ liệu từ định dạng Kafka sang định dạng ElasticSearch.
public class PlaceDataMapper
{
    public static PlacesEs MapToPlaceEs(JToken data)
    {
        var placeKafka = data.ToObject<KafkaPlaceModel>();
        return new PlacesEs
        {
            Id = placeKafka.Id,
            PlaceDetailId = placeKafka.PlaceDetailId,
            Location = new Location
            {
                Lon = placeKafka.Location?[0] ?? 0.0,
                Lat = placeKafka.Location?[1] ?? 0.0
            },
            PhoneNumber = placeKafka.PhoneNumber,
            PlaceId = placeKafka.PlaceId,
            Name = placeKafka.Name,
            FullAddress = placeKafka.FullAddress,
            StreetNumber = placeKafka.StreetNumber,
            Route = placeKafka.Route,
            Ward = placeKafka.Ward,
            District = placeKafka.District,
            City = placeKafka.City,
            Type = placeKafka.Type,
            Active = placeKafka.Active,
            Tags = placeKafka.Tags,
            TagInput = placeKafka.TagInput,
            Popular = placeKafka.Popular,
            Processed = placeKafka.Processed,
            QuoteCount = placeKafka.QuoteCount,
            BookingCount = placeKafka.BookingCount,
            Removed = placeKafka.Removed,
            Hidden = placeKafka.Hidden,
            CreatedAt = DateTimeHelper.FromUnixTimeMillisecondsToLocal(placeKafka.CreatedAt, DateTime.MinValue),
            UpdatedAt = DateTimeHelper.FromUnixTimeMillisecondsToLocal(placeKafka.UpdatedAt, DateTime.MinValue),
            CreatorId = placeKafka.CreatorId,
        };
    }
}