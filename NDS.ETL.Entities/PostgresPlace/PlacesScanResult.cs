using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using NodaTime;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("places_scan_result")]
[Index("HangfireJobId", Name = "places_scan_result_hangfire_job_id_idx")]
[Index("Placeid", Name = "places_scan_result_placeid_idx")]
[Index("ScanProcessed", Name = "places_scan_result_scan_processed_idx")]
[Index("SyncStatus", Name = "places_scan_result_sync_status_idx")]
[Index("Placeid", Name = "places_scan_result_unique", IsUnique = true)]
public partial class PlacesScanResult
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("placeid")]
    [StringLength(500)]
    public string? Placeid { get; set; }

    [Column("source_placeid")]
    [StringLength(500)]
    public string? SourcePlaceid { get; set; }

    [Column("hangfire_job_id", TypeName = "character varying")]
    public string? HangfireJobId { get; set; }

    [Column("compare_score_max")]
    public decimal CompareScoreMax { get; set; }

    [Column("total_scans")]
    public int TotalScans { get; set; }

    [Column("create_at", TypeName = "timestamp without time zone")]
    public DateTime CreateAt { get; set; }

    [Column("update_at", TypeName = "timestamp without time zone")]
    public DateTime UpdateAt { get; set; }

    [Column("scan_type", TypeName = "character varying")]
    public string? ScanType { get; set; }

    [Column("note")]
    public string? Note { get; set; }

    [Column("reason")]
    [StringLength(200)]
    public string? Reason { get; set; }

    [Column("reason_operations")]
    public List<PlacesScanResultReasonUpdate>? ReasonOperations { get; set; }

    [Column("scan_processed")]
    public PlacesScanResultProcessed? ScanProcessed { get; set; }

    [Column("sync_status")]
    public int SyncStatus { get; set; }

    [Column("lastUpdateUser")]
    public string? LastUpdateUser { get; set; }
}

public enum PlacesScanResultReasonUpdate
{
    [Display(Name = "Sai điểm pin")]
    WrongPin = 1,

    [Display(Name = "Sai/thiếu thông tin địa chỉ")]
    WrongOrMiss = 2,

    [Display(Name = "Khác")]
    Other = 3
}
public enum PlacesScanResultProcessed
{
    [Display(Name = "Đã xử lý")]
    Success = 1,

    [Display(Name = "Đang xử lý")]
    Pending = 2,

    [Display(Name = "Không xử lý")]
    Not = 3,

    [Display(Name = "Chưa xử lý")]
    NotYet = 0
}