using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using NDS.ETL.Entities.Context;
using NDS.ETL.Entities.ESEntities;

namespace NDS.ETL.BackgroundJobs.Services
{
    public interface IApiKeyValidationService
    {
        Task<(bool IsValid, IEnumerable<PermissionName> Permissions)> ValidateApiKeyAsync(string apiKey);
        void ClearApiKeysCache();
    }

    public class ApiKeyValidationService : IApiKeyValidationService
    {
        private readonly IDbContextFactory<ESDbContext> _contextFactory;
        private readonly IMemoryCache _cache;
        private readonly ILogger<ApiKeyValidationService> _logger;
        private readonly TimeSpan _cacheDuration;
        private readonly ICacheManager _cacheManager;

        private const string CACHE_KEY_PREFIX = "ApiKey_";

        public ApiKeyValidationService(
            IDbContextFactory<ESDbContext> contextFactory,
            IMemoryCache cache,
            IConfiguration configuration,
            ILogger<ApiKeyValidationService> logger,
            ICacheManager cacheManager)
        {
            _contextFactory = contextFactory;
            _cache = cache;
            _logger = logger;
            _cacheManager = cacheManager;

            // Get the cache duration from configuration or use default (30 minutes)
            var cacheDurationMinutes = configuration.GetValue<int>("ApiKey:CacheDurationMinutes", 30);
            _cacheDuration = TimeSpan.FromMinutes(cacheDurationMinutes);
        }

        public async Task<(bool IsValid, IEnumerable<PermissionName> Permissions)> ValidateApiKeyAsync(string apiKey)
        {
            if (string.IsNullOrEmpty(apiKey))
            {
                return (false, Array.Empty<PermissionName>());
            }

            var cacheKey = $"{CACHE_KEY_PREFIX}{apiKey}";

            // Try to get the API key info from the cache
            var cachedApiKeyInfo = _cacheManager.GetFromCache(cacheKey) as CachedApiKeyInfo;
            if (cachedApiKeyInfo != null)
            {
                _logger.LogInformation("API key validation result found in cache");
                return (cachedApiKeyInfo.IsValid, cachedApiKeyInfo.Permissions);
            }

            // If not found in cache, query the database
            _logger.LogInformation("API key not found in cache, querying database");
            
            try
            {
                using var dbContext = await _contextFactory.CreateDbContextAsync();
                
                var apiKeyEntity = await dbContext.ApiKeys
                    .AsNoTracking()
                    .Include(k => k.Permissions)
                    .FirstOrDefaultAsync(k => k.Key == apiKey && k.IsActive && (k.ExpirationDate == null || k.ExpirationDate > DateTime.UtcNow));

                if (apiKeyEntity == null)
                {
                    // Cache the negative result to prevent repeated database queries
                    var negativeResult = new CachedApiKeyInfo(false, Array.Empty<PermissionName>());
                    _cacheManager.AddToCache(cacheKey, negativeResult, _cacheDuration);
                    return (false, Array.Empty<PermissionName>());
                }

                // API key is valid, get its permissions
                var permissions = apiKeyEntity.Permissions
                    .Select(p => p.PermissionName)
                    .ToList();

                // Cache the result
                var apiKeyInfo = new CachedApiKeyInfo(true, permissions);
                _cacheManager.AddToCache(cacheKey, apiKeyInfo, _cacheDuration);

                return (true, permissions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating API key from database");
                return (false, Array.Empty<PermissionName>());
            }
        }

        public void ClearApiKeysCache()
        {
            _logger.LogInformation("Clearing all API key caches with prefix: {Prefix}", CACHE_KEY_PREFIX);
            _cacheManager.ClearCacheByPrefix(CACHE_KEY_PREFIX);
        }

        /// <summary>
        /// Private class to store API key validation information in the cache
        /// </summary>
        private class CachedApiKeyInfo
        {
            public bool IsValid { get; }
            public IEnumerable<PermissionName> Permissions { get; }

            public CachedApiKeyInfo(bool isValid, IEnumerable<PermissionName> permissions)
            {
                IsValid = isValid;
                Permissions = permissions;
            }
        }
    }
}
