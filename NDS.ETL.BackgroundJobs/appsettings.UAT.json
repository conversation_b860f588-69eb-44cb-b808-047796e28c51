{"PathBase": "/backgroundjob", "AllowedHosts": "*", "ConnectionStrings": {"Redis": "*************:6379,*************:6379,*************:6379,password=redisvnpay,abortConnect=false,connectRetry=3,connectTimeout=5000,syncTimeout=5000,keepAlive=180", "PgPlace": "Host=*************;Port=5444;Database=******;Username=******;Password=******;Timeout=600;Command Timeout=10000", "HangfireSchema": "es-backend-log"}, "ElasticSearchConfig": {"Index": ["place-uat2", "fav-place-uat", "user-place-uat", "place-pair-uat"], "LogIndex": "place-log-1", "Url": ["http://*************:9200/", "http://*************:9200/", "http://*************:9200/"], "UserName": "elastic", "Password": ""}, "BasicAuth": {"Username": "esb", "Password": "esb@2025"}, "JobPgSyncPlace": [{"CronExpression": "*/5 * * * *", "JobId": "SyncFavPlaces", "Disable": false, "Immediately": false}, {"CronExpression": "*/20 * * * *", "JobId": "SyncPlaces", "Disable": false, "Immediately": false}, {"CronExpression": "*/30 * * * *", "JobId": "SyncPlacePairs", "Disable": false, "Immediately": false}, {"CronExpression": "* * 31 2 *", "Disable": false, "Immediately": false, "JobId": "SyncPlaceScanResult"}, {"CronExpression": "* * 31 2 *", "Disable": false, "Immediately": false, "JobId": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"CronExpression": "* * 31 2 *", "Disable": false, "Immediately": false, "JobId": "SyncDataScanedToEs"}, {"CronExpression": "* * 31 2 *", "Disable": false, "Immediately": false, "JobId": "SyncFavoritePlace"}, {"CronExpression": "0 23 * * *", "JobId": "CompareScoreChangePlacesJob", "Disable": false, "Immediately": false}, {"CronExpression": "0 23 28 * *", "JobId": "SyncPlaceAfterApproveJob", "Disable": false, "Immediately": false}], "KafkaConfig": {"Connection": "************:6091,************:6092,************:6093", "Disable": "true", "GroupSyncPlaces": "sync_es_places_gr", "TopicSyncPlaces": "sync_es_places"}, "Logging": {"LogLevel": {"Default": "Debug", "Hangfire": "Warning", "Microsoft": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ApiKey": {"CacheDurationMinutes": 30}}