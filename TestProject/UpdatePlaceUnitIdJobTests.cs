using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using NDS.ETL.BackgroundJobs.RecurringJobs.AdminUnitUpdate;
using NDS.ETL.Entities.Context;
using NDS.ETL.Entities.PostgresPlace;
using NDS.ETL.Infrastructure.Common;
using System.Diagnostics;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;
using Microsoft.Extensions.Caching.Memory; // Thêm namespace cho IMemoryCache

namespace TestProject;

/// <summary>
/// Test class for debugging and testing the UpdatePlaceUnitIdJob with real database access
/// </summary>
[TestFixture]
public class UpdatePlaceUnitIdJobTests
{
    private Mock<ILogger<UpdatePlaceUnitIdJob>> _loggerMock;
    private IConfiguration _configuration;
    private UpdatePlaceUnitIdJob _job;
    private NpgPlaceContext _dbContext;
    private Mock<IScopedResolver<NpgPlaceContext>> _dbContextResolverMock;
    private Mock<IMemoryCache> _memoryCacheMock; // Thêm mock cho IMemoryCache

    // Database connection settings
    private string _connectionString = "Host=localhost;Port=5432;Database=place_test;Username=********;Password=********";

    [OneTimeSetUp]
    public void OneTimeSetup()
    {
        // Create the configuration from appsettings.test.json
        _configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: false)
            .Build();

        // Override the connection string if provided in configuration
        var configConnectionString = _configuration.GetConnectionString("PgPlace");
        if (!string.IsNullOrEmpty(configConnectionString))
        {
            _connectionString = configConnectionString;
        }

        // Create DbContextOptions
        var optionsBuilder = new DbContextOptionsBuilder<NpgPlaceContext>();
        optionsBuilder.UseNpgsql(_connectionString, 
            x => x.UseNetTopologySuite());

        // Create actual DbContext
        _dbContext = new NpgPlaceContext(optionsBuilder.Options);

        // Set up mocks
        _loggerMock = new Mock<ILogger<UpdatePlaceUnitIdJob>>();
        _dbContextResolverMock = new Mock<IScopedResolver<NpgPlaceContext>>();
        _memoryCacheMock = new Mock<IMemoryCache>(); // Khởi tạo mock cho IMemoryCache
        
        // Cấu hình cho cache mock để trả về null khi GetOrCreate được gọi (buộc job phải tạo mới giá trị)
        object nullValue = null;
        _memoryCacheMock
            .Setup(m => m.TryGetValue(It.IsAny<object>(), out nullValue))
            .Returns(false);

        // Cấu hình CreateEntry để trả về một đối tượng mock
        var mockCacheEntry = new Mock<ICacheEntry>();
        _memoryCacheMock
            .Setup(m => m.CreateEntry(It.IsAny<object>()))
            .Returns(mockCacheEntry.Object);
        
        // Configure the resolver mock to return the actual DbContext
        _dbContextResolverMock
            .Setup(x => x.ResolveAsync(It.IsAny<Func<NpgPlaceContext, Task<bool>>>()))
            .Returns<Func<NpgPlaceContext, Task<bool>>>(async func => await func(_dbContext));

        // Create the job instance with the mocked dependencies (thêm _memoryCacheMock.Object)
        _job = new UpdatePlaceUnitIdJob(
            _configuration,
            _loggerMock.Object, 
            _dbContextResolverMock.Object,
            _memoryCacheMock.Object // Truyền mock của IMemoryCache vào constructor
        );
    }

    [SetUp]
    public void Setup()
    {
        // Verify database connection before each test
        _dbContext.Database.CanConnect();
    }

    /// <summary>
    /// Test that runs the job's DoWorkAsync method against a real database
    /// </summary>
    [Test]
    public async Task DoWorkAsync_WithRealDatabase_ProcessesPlacesSuccessfully()
    {
        // Arrange
        using var cts = new CancellationTokenSource(TimeSpan.FromMinutes(10)); // 10 minute timeout for debugging
        var stopwatch = Stopwatch.StartNew();

        // Act
        await _job.DoWorkAsync(cts.Token);
        
        stopwatch.Stop();

        // Log execution time
        Console.WriteLine($"Job execution completed in {stopwatch.ElapsedMilliseconds}ms");
        
        // Assert
        // The job should complete without throwing exceptions
        Assert.Pass("Job executed successfully");
    }

    /// <summary>
    /// Test that verifies if places with missing province IDs are properly updated
    /// </summary>
    [Test]
    public async Task UpdateProvincesIds_WithMissingProvince_UpdatesCorrectly()
    {
        // Arrange
        // Find or create test data with missing province IDs
        var testPlaces = await CreateOrFindTestPlacesWithMissingProvinceId();
        if (testPlaces.Count == 0)
        {
            Assert.Inconclusive("No test places with missing province IDs found or created");
            return;
        }

        using var cts = new CancellationTokenSource(TimeSpan.FromMinutes(5));

        // Act
        await _job.DoWorkAsync(cts.Token);

        // Refresh our context to get updated entities
        _dbContext.ChangeTracker.Clear();
        
        // Assert
        foreach (var placeId in testPlaces)
        {
            var updatedPlace = await _dbContext.Places.FindAsync(placeId);
            Assert.That(updatedPlace, Is.Not.Null, $"Could not find test place with ID {placeId}");
            Assert.That(updatedPlace.ProvinceId, Is.Not.Null, $"Place with ID {placeId} still has null ProvinceId after job execution");
            
            Console.WriteLine($"Place {placeId} was updated with ProvinceId={updatedPlace.ProvinceId}");
        }
    }

    /// <summary>
    /// Test verifies if places with missing district IDs are properly updated
    /// </summary>
    [Test]
    public async Task UpdateDistrictIds_WithMissingDistrict_UpdatesCorrectly()
    {
        // Arrange
        // Find or create test data with missing district IDs but known province
        var testPlaces = await CreateOrFindTestPlacesWithMissingDistrictId();
        if (testPlaces.Count == 0)
        {
            Assert.Inconclusive("No test places with missing district IDs found or created");
            return;
        }

        using var cts = new CancellationTokenSource(TimeSpan.FromMinutes(5));

        // Act
        await _job.DoWorkAsync(cts.Token);

        // Refresh our context to get updated entities
        _dbContext.ChangeTracker.Clear();
        
        // Assert
        foreach (var placeId in testPlaces)
        {
            var updatedPlace = await _dbContext.Places.FindAsync(placeId);
            Assert.That(updatedPlace, Is.Not.Null, $"Could not find test place with ID {placeId}");
            Assert.That(updatedPlace.DistrictId, Is.Not.Null, $"Place with ID {placeId} still has null DistrictId after job execution");
            
            Console.WriteLine($"Place {placeId} was updated with DistrictId={updatedPlace.DistrictId}");
        }
    }

    /// <summary>
    /// Test verifies if places with missing ward IDs are properly updated
    /// </summary>
    [Test]
    public async Task UpdateWardIds_WithMissingWard_UpdatesCorrectly()
    {
        // Arrange
        // Find or create test data with missing ward IDs but known district
        var testPlaces = await CreateOrFindTestPlacesWithMissingWardId();
        if (testPlaces.Count == 0)
        {
            Assert.Inconclusive("No test places with missing ward IDs found or created");
            return;
        }

        using var cts = new CancellationTokenSource(TimeSpan.FromMinutes(5));

        // Act
        await _job.DoWorkAsync(cts.Token);

        // Refresh our context to get updated entities
        _dbContext.ChangeTracker.Clear();
        
        // Assert
        foreach (var placeId in testPlaces)
        {
            var updatedPlace = await _dbContext.Places.FindAsync(placeId);
            Assert.That(updatedPlace, Is.Not.Null, $"Could not find test place with ID {placeId}");
            Assert.That(updatedPlace.WardId, Is.Not.Null, $"Place with ID {placeId} still has null WardId after job execution");
            
            Console.WriteLine($"Place {placeId} was updated with WardId={updatedPlace.WardId}");
        }
    }

    /// <summary>
    /// Helper method to find or create test places with missing province ID
    /// </summary>
    private async Task<List<long>> CreateOrFindTestPlacesWithMissingProvinceId(int limit = 3)
    {
        // First try to find existing records with missing province IDs
        var existingPlaces = await _dbContext.Places
            .Where(p => p.ProvinceId == null && !p.Removed)
            .Take(limit)
            .Select(p => p.Id)
            .ToListAsync();

        if (existingPlaces.Count > 0)
        {
            return existingPlaces;
        }

        // If no existing records found, create test data
        // Find a random province to use for address data
        var randomProvince = await _dbContext.Provinces
            .OrderBy(p => Guid.NewGuid())
            .FirstOrDefaultAsync();

        if (randomProvince == null)
        {
            Console.WriteLine("No provinces found in database. Cannot create test data.");
            return new List<long>();
        }

        // Create test places
        var testPlaces = new List<Place>();
        for (int i = 0; i < limit; i++)
        {
            var place = new Place
            {
                Name = $"Test Place {Guid.NewGuid()}",
                FullAddress = $"Test Address, {randomProvince.Name}, Việt Nam",
                City = randomProvince.Name, // This should help with matching via address
                ProvinceId = null, // Explicitly set to null for the test
                Location = new Point(106.0 + i * 0.01, 10.0 + i * 0.01) { SRID = 4326 }, // Set points in Vietnam
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            testPlaces.Add(place);
        }

        _dbContext.Places.AddRange(testPlaces);
        await _dbContext.SaveChangesAsync();

        return testPlaces.Select(p => p.Id).ToList();
    }

    /// <summary>
    /// Helper method to find or create test places with missing district ID
    /// </summary>
    private async Task<List<long>> CreateOrFindTestPlacesWithMissingDistrictId(int limit = 3)
    {
        // First try to find existing records with missing district IDs but has province ID
        var existingPlaces = await _dbContext.Places
            .Where(p => p.DistrictId == null && p.ProvinceId != null && !p.Removed)
            .Take(limit)
            .Select(p => p.Id)
            .ToListAsync();

        if (existingPlaces.Count > 0)
        {
            return existingPlaces;
        }

        // If no existing records found, create test data
        // Find a random province and district to use for address data
        var randomProvince = await _dbContext.Provinces
            .OrderBy(p => Guid.NewGuid())
            .FirstOrDefaultAsync();

        var randomDistrict = await _dbContext.Districts
            .Where(d => d.ProvinceId == randomProvince.Id)
            .OrderBy(p => Guid.NewGuid())
            .FirstOrDefaultAsync();

        if (randomProvince == null || randomDistrict == null)
        {
            Console.WriteLine("No provinces or districts found in database. Cannot create test data.");
            return new List<long>();
        }

        // Create test places
        var testPlaces = new List<Place>();
        for (int i = 0; i < limit; i++)
        {
            var place = new Place
            {
                Name = $"Test Place {Guid.NewGuid()}",
                FullAddress = $"Test Address, {randomDistrict.Name}, {randomProvince.Name}, Việt Nam",
                City = randomProvince.Name,
                District = randomDistrict.Name, // This should help with matching via address
                ProvinceId = randomProvince.Id,
                DistrictId = null, // Explicitly set to null for the test
                Location = new Point(106.0 + i * 0.01, 10.0 + i * 0.01) { SRID = 4326 }, // Set points in Vietnam
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            testPlaces.Add(place);
        }

        _dbContext.Places.AddRange(testPlaces);
        await _dbContext.SaveChangesAsync();

        return testPlaces.Select(p => p.Id).ToList();
    }

    /// <summary>
    /// Helper method to find or create test places with missing ward ID
    /// </summary>
    private async Task<List<long>> CreateOrFindTestPlacesWithMissingWardId(int limit = 3)
    {
        // First try to find existing records with missing ward IDs but has district ID
        var existingPlaces = await _dbContext.Places
            .Where(p => p.WardId == null && p.DistrictId != null && !p.Removed)
            .Take(limit)
            .Select(p => p.Id)
            .ToListAsync();

        if (existingPlaces.Count > 0)
        {
            return existingPlaces;
        }

        // If no existing records found, create test data
        // Find a random district and ward to use for address data
        var randomDistrict = await _dbContext.Districts
            .OrderBy(p => Guid.NewGuid())
            .FirstOrDefaultAsync();

        var randomProvince = await _dbContext.Provinces
            .Where(p => p.Id == randomDistrict.ProvinceId)
            .FirstOrDefaultAsync();

        var randomWard = await _dbContext.Wards
            .Where(w => w.DistrictId == randomDistrict.Id)
            .OrderBy(p => Guid.NewGuid())
            .FirstOrDefaultAsync();

        if (randomDistrict == null || randomWard == null)
        {
            Console.WriteLine("No districts or wards found in database. Cannot create test data.");
            return new List<long>();
        }

        // Create test places
        var testPlaces = new List<Place>();
        for (int i = 0; i < limit; i++)
        {
            var place = new Place
            {
                Name = $"Test Place {Guid.NewGuid()}",
                FullAddress = $"Test Address, {randomWard.Name}, {randomDistrict.Name}, {randomProvince.Name}, Việt Nam",
                City = randomProvince.Name,
                District = randomDistrict.Name,
                Ward = randomWard.Name, // This should help with matching via address
                ProvinceId = randomProvince.Id,
                DistrictId = randomDistrict.Id,
                WardId = null, // Explicitly set to null for the test
                Location = new Point(106.0 + i * 0.01, 10.0 + i * 0.01) { SRID = 4326 }, // Set points in Vietnam
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            testPlaces.Add(place);
        }

        _dbContext.Places.AddRange(testPlaces);
        await _dbContext.SaveChangesAsync();

        return testPlaces.Select(p => p.Id).ToList();
    }

    /// <summary>
    /// Clean up test data created by our tests
    /// </summary>
    [OneTimeTearDown]
    public async Task TearDown()
    {
        // Note: In a real-world scenario, you might want to use a transaction and roll it back
        // instead of actually deleting data. However, this depends on the test strategy.

        // If you want to clean up test data, uncomment this code
        /*
        // Delete any test places created in the last hour with our test prefix
        var testPlaces = await _dbContext.Places
            .Where(p => p.Name.StartsWith("Test Place ") && p.CreatedAt > DateTime.UtcNow.AddHours(-1))
            .ToListAsync();

        if (testPlaces.Any())
        {
            _dbContext.Places.RemoveRange(testPlaces);
            await _dbContext.SaveChangesAsync();
            Console.WriteLine($"Cleaned up {testPlaces.Count} test places");
        }
        */

        // Dispose DbContext
        await _dbContext.DisposeAsync();
    }
}