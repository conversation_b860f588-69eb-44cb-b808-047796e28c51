using Elastic.Clients.Elasticsearch;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;

namespace NDS.ETL.BackgroundJobs.Services.ES
{
    /// <summary>
    /// Interface định nghĩa các dịch vụ xử lý cặp địa điểm
    /// </summary>
    public interface IESPlacePairs
    {
        /// <summary>
        /// Lấy danh sách các lộ trình gần đây của người dùng bao gồm cả thông tin cặp địa điểm
        /// </summary>
        Task<List<PlacePairRoute>> GetRecentRoutesWithPairedPlaces(
            string? userId,
            string? phoneNumber,
            int limit = 5,
            int page = 0,
            LatLonGeoLocation? currentLocation = null,
            double? minDistanceMeters = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách lộ trình phổ biến theo giờ và ngày trong tuần
        /// </summary>
        Task<List<PlacePairRoute>> GetFrequentRoutesByTimeAndDay(
            string? userId,
            string? phoneNumber,
            int hourOfDay,
            int dayOfWeek,
            int limit = 5,
            LatLonGeoLocation? currentLocation = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách lộ trình phổ biến trong khoảng giờ và ngày trong tuần
        /// </summary>
        Task<List<PlacePairRoute>> GetFrequentRoutesByTimeRange(
            string? userId,
            string? phoneNumber,
            int startHour,
            int endHour,
            int dayOfWeek,
            int limit = 5,
            LatLonGeoLocation? currentLocation = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách lộ trình đã sử dụng trong ngày hôm nay
        /// </summary>
        Task<List<PlacePairRoute>> GetTodayRoutes(
            string? userId,
            string? phoneNumber,
            DateTime date,
            int limit = 10,
            LatLonGeoLocation? currentLocation = null,
            int? minDistanceMeters = null,
            CancellationToken cancellationToken = default);
    }
} 