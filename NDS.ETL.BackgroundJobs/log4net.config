<?xml version="1.0" encoding="utf-8" ?>
<log4net>
	<appender name="Console" type="log4net.Appender.ConsoleAppender">
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date %-5level: %message%newline" />
		</layout>
		<filter type="log4net.Filter.LoggerMatchFilter">
			<!-- allows this sub-namespace to be logged... -->
			<loggerToMatch value="NDS.ETL" />
		</filter>
		<filter type="log4net.Filter.LoggerMatchFilter">
			<!-- ...but not the rest of it -->
			<loggerToMatch value="Microsoft.EntityFrameworkCore" />
			<acceptOnMatch value="false" />
		</filter>
	</appender>
	<appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender" >
		<file value="App_Data/Logs/Logs.txt" />
		<appendToFile value="true" />
		<rollingStyle value="Size" />
		<maxSizeRollBackups value="10" />
		<maximumFileSize value="10000KB" />
		<staticLogFileName value="true" />
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%-5level %date [%-5.5thread] %-40.40logger - %message%newline" />
		</layout>
		<filter type="log4net.Filter.LoggerMatchFilter">
			<!-- allows this sub-namespace to be logged... -->
			<loggerToMatch value="NDS.ETL" />
		</filter>
		<filter type="log4net.Filter.LoggerMatchFilter">
			<!-- ...but not the rest of it -->
			<loggerToMatch value="Microsoft.EntityFrameworkCore" />
			<acceptOnMatch value="false" />
		</filter>
	</appender>
	<appender name="KafkaAppender" type="log4net.Kafka.Appender.KafkaAppender, log4net.Kafka.Appender">
		<KafkaSettings>
			<brokers>
				<add value="10.22.17.162:6091" />
				<add value="10.22.17.162:6092" />
				<add value="10.22.17.162:6093" />
			</brokers>
			<topic type="log4net.Layout.PatternLayout">
				<!--<conversionPattern value="kafka.logstash.%level" />-->
				<!--<conversionPattern value="appname-%property{component}-%class-%method-%level" />-->
				<conversionPattern value="kafka-logs" />
			</topic>
		</KafkaSettings>
		<layout type="Gelf4Net.Layout.GelfLayout, Gelf4Net">
			<param name="AdditionalFields" value="app:ES,version:1.0,Environment:Staging,Level:%level" />
			<param name="Facility" value="es-backend-stag" />
			<param name="IncludeLocationInformation" value="true" />
			<param name="SendTimeStampAsString" value="true"/>
		</layout>
		<filter type="log4net.Filter.LoggerMatchFilter">
			<!-- allows this sub-namespace to be logged... -->
			<loggerToMatch value="NDS.ETL" />
		</filter>
		<filter type="log4net.Filter.LoggerMatchFilter">
			<!-- ...but not the rest of it -->
			<loggerToMatch value="Microsoft.EntityFrameworkCore" />
			<acceptOnMatch value="false" />
		</filter>
	</appender>

	<root>
		<appender-ref ref="KafkaAppender" />
		<appender-ref ref="Console" />
		<appender-ref ref="RollingFileAppender" />
		<level value="ALL" />
	</root>
</log4net>