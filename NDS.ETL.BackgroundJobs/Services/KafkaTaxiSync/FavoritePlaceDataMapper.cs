using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Model.ES;
using NDS.ETL.BackgroundJobs.Model.KafkaSyncModel;
using NDS.ETL.Infrastructure.Utils;
using Newtonsoft.Json.Linq;

namespace NDS.ETL.BackgroundJobs.Services.KafkaTaxiSync;

// Lớp ánh xạ dữ liệu từ Ka<PERSON>ka sang ElasticSearch cho đối tượng Địa điểm yêu thích (FavoritePlace).
// Chuyển đổi dữ liệu từ định dạng Kafka sang định dạng ElasticSearch để lưu trữ và tìm kiếm.
public class FavoritePlaceDataMapper
{
    public static FavPlaceEs MapToFavPlaceEs(JToken data)
    {
        var favoritePlace = data.ToObject<KafkaFavoritePlaceModel>();
        if (favoritePlace == null)
        {
            return null;
        }

        return new FavPlaceEs
        {
            Id = (int)favoritePlace.Id,
            PhoneNumber = favoritePlace.PhoneNumber,
            PlaceId = favoritePlace.PlaceId,
            Location = favoritePlace.Location is { Length: 2 }
                ? new Location { Lon = favoritePlace.Location[0], Lat = favoritePlace.Location[1] }
                : null,
            Name = favoritePlace.Name,
            Address = favoritePlace.Address,
            FullAddress = favoritePlace.FullAddress,
            Type = favoritePlace.Type,
            BookingCount = favoritePlace.BookingCount,
            Active = favoritePlace.Active,
            Removed = favoritePlace.Removed,
            UserId = favoritePlace.UserId,
            CreatedAt = DateTimeHelper.FromUnixTimeMillisecondsToLocal(favoritePlace.CreatedAt),
            UpdatedAt = DateTimeHelper.FromUnixTimeMillisecondsToLocal(favoritePlace.UpdatedAt)
        };
    }
} 