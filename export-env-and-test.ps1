# Script này sẽ tự động export biến môi trường từ file .env cho phiên làm việc hiện tại
Get-Content ".\NDS.ETL.BackgroundJobs\.env" | ForEach-Object {
    # Bỏ qua dòng comment hoặc dòng trống
    if ($_ -match "^\s*([^#][^=]+)=(.+)$") {
        $name = $matches[1].Trim()
        $value = $matches[2].Trim()
        # Export biến môi trường cho process hiện tại
        [System.Environment]::SetEnvironmentVariable($name, $value, "Process")
    }
}

Write-Host "Exported environment variables from .env."
