using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("osm2pgsql_properties")]
public partial class Osm2pgsqlProperty
{
    [Key]
    [Column("property")]
    public string Property { get; set; } = null!;

    [Column("value")]
    public string Value { get; set; } = null!;
}
