using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using NodaTime;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("ward_boundaries")]
public partial class WardBoundary
{
    [Key]
    [Column("_id")]
    public int Id { get; set; }

    [Column("_class")]
    [StringLength(255)]
    public string? Class { get; set; }

    [Column("created_at")]
    public LocalDateTime? CreatedAt { get; set; }

    [Column("location")]
    [StringLength(10485760)]
    public string? Location { get; set; }

    [Column("modified_at")]
    public LocalDateTime? ModifiedAt { get; set; }

    [Column("modified_by")]
    [StringLength(50)]
    public string? ModifiedBy { get; set; }

    [Column("name")]
    [StringLength(50)]
    public string? Name { get; set; }

    [Column("provinces")]
    [StringLength(25)]
    public string? Provinces { get; set; }

    [Column("district")]
    public int? District { get; set; }

    [Column("ward_type")]
    [StringLength(50)]
    public string? WardType { get; set; }

    [Column("status")]
    public int? Status { get; set; }

    [Column("postcode")]
    [StringLength(50)]
    public string? Postcode { get; set; }

    [Column("province")]
    public int? Province { get; set; }
}
