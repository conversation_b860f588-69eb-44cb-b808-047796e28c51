using System.Text.Json.Serialization;

namespace NDS.ETL.BackgroundJobs.Model.API;

/// <summary>
/// Response model for GetNewAddressByLocation API
/// </summary>
public class GetNewAddressByLocationResponse : WardResponse
{
    /// <summary>
    /// ??a ch? ??y ?? ???c c?p nh?t v?i ph??ng/x� v� t?nh/th�nh m?i (sau 1/7/2025)
    /// </summary>
    [JsonPropertyName("newAddress")]
    public string? NewAddress { get; set; }
    [JsonPropertyName("currentAddress")]

    public string? CurrentAddress { get; set; }
}