using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using NodaTime;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("district_boundaries")]
public partial class DistrictBoundary
{
    [Key]
    [Column("_id")]
    public int Id { get; set; }

    [Column("_class")]
    [StringLength(255)]
    public string? Class { get; set; }

    [Column("created_at")]
    public LocalDateTime? CreatedAt { get; set; }

    [Column("location")]
    [StringLength(10485760)]
    public string? Location { get; set; }

    [Column("modified_at")]
    public LocalDateTime? ModifiedAt { get; set; }

    [Column("modified_by")]
    [StringLength(50)]
    public string? ModifiedBy { get; set; }

    [Column("name")]
    [StringLength(50)]
    public string? Name { get; set; }

    [Column("data")]
    [StringLength(10485760)]
    public string? Data { get; set; }

    [Column("provinces")]
    [StringLength(25)]
    public string? Provinces { get; set; }

    [Column("status")]
    public int? Status { get; set; }

    [Column("postcode")]
    public int? Postcode { get; set; }

    [Column("province")]
    public int? Province { get; set; }

    [Column("iddistrict")]
    public int? Iddistrict { get; set; }
}
