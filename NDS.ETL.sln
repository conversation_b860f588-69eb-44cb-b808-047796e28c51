
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35919.96
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{33CF53DB-397D-4D72-990C-DFF3D8B8C22F}"
	ProjectSection(SolutionItems) = preProject
		.gitlab-ci.yml = .gitlab-ci.yml
		bamboo.yml = bamboo.yml
		export-env-and-test.ps1 = export-env-and-test.ps1
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NDS.ETL.BackgroundJobs", "NDS.ETL.BackgroundJobs\NDS.ETL.BackgroundJobs.csproj", "{9FEADD14-85B9-4E30-B686-A169561A2A2B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NDS.ETL.Entities", "NDS.ETL.Entities\NDS.ETL.Entities.csproj", "{CFF7218F-FC37-4B2E-9A06-A06990D00554}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NDS.ETL.Infrastructure", "NDS.ETL.Infrastructure\NDS.ETL.Infrastructure.csproj", "{33282301-0BC9-4C37-A4B4-A22166A3C8B8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TestProject", "TestProject\TestProject.csproj", "{FADD4F8A-22F7-4929-8723-1EF027618EAC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ESTestRunner", "ESTestRunner\ESTestRunner.csproj", "{C6BDB785-177B-944F-9FC2-20BA18859305}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "log4net.Kafka.Appender", "log4net.Kafka.Appender\log4net.Kafka.Appender.csproj", "{B75853DB-6F43-FEE1-679A-AE6761D34E2A}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{9FEADD14-85B9-4E30-B686-A169561A2A2B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9FEADD14-85B9-4E30-B686-A169561A2A2B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9FEADD14-85B9-4E30-B686-A169561A2A2B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9FEADD14-85B9-4E30-B686-A169561A2A2B}.Release|Any CPU.Build.0 = Release|Any CPU
		{CFF7218F-FC37-4B2E-9A06-A06990D00554}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CFF7218F-FC37-4B2E-9A06-A06990D00554}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CFF7218F-FC37-4B2E-9A06-A06990D00554}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CFF7218F-FC37-4B2E-9A06-A06990D00554}.Release|Any CPU.Build.0 = Release|Any CPU
		{33282301-0BC9-4C37-A4B4-A22166A3C8B8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{33282301-0BC9-4C37-A4B4-A22166A3C8B8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{33282301-0BC9-4C37-A4B4-A22166A3C8B8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{33282301-0BC9-4C37-A4B4-A22166A3C8B8}.Release|Any CPU.Build.0 = Release|Any CPU
		{FADD4F8A-22F7-4929-8723-1EF027618EAC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FADD4F8A-22F7-4929-8723-1EF027618EAC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FADD4F8A-22F7-4929-8723-1EF027618EAC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FADD4F8A-22F7-4929-8723-1EF027618EAC}.Release|Any CPU.Build.0 = Release|Any CPU
		{C6BDB785-177B-944F-9FC2-20BA18859305}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C6BDB785-177B-944F-9FC2-20BA18859305}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C6BDB785-177B-944F-9FC2-20BA18859305}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C6BDB785-177B-944F-9FC2-20BA18859305}.Release|Any CPU.Build.0 = Release|Any CPU
		{B75853DB-6F43-FEE1-679A-AE6761D34E2A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B75853DB-6F43-FEE1-679A-AE6761D34E2A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B75853DB-6F43-FEE1-679A-AE6761D34E2A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B75853DB-6F43-FEE1-679A-AE6761D34E2A}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {951C3B58-C0A1-4C8F-97A7-BEA197605F12}
	EndGlobalSection
EndGlobal
