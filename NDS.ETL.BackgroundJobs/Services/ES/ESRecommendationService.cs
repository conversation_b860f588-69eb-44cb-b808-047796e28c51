using Elastic.Clients.Elasticsearch;
using NDS.ETL.BackgroundJobs.Extensions;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using System.Diagnostics;
using KnowledgeBase.Core.Extensions;
using NDS.ETL.Infrastructure.Utils;
using System.Text.RegularExpressions;

namespace NDS.ETL.BackgroundJobs.Services.ES
{
    /// <summary>
    /// Dịch vụ gợi ý địa điểm và lộ trình sử dụng Elasticsearch
    /// </summary>
    public partial class ESRecommendationService : IESRecommendationService
    {
        #region Private Fields

        private readonly ElasticsearchClient _elasticClient;
        private readonly IESPlacesService _iesPlacesServiceService;
        private readonly IESNearbyService _nearbyService;
        private readonly IESAutocompleteService _autocompleteService;
        private readonly IESPlacePairs _placePairsService;
        private readonly IESPlaceDetailsService _placeDetailsService;
        private readonly ILogger<ESRecommendationService> _logger;
        private readonly string _placesIdxName;
        private readonly string _favoritePlacesIdxName;
        private readonly string _placePairIdxName;
        private readonly string? _searchHistoryIdxName;
        private static readonly Regex _subPlaceRegex = new Regex("_S_\\d{3,10}", RegexOptions.Compiled);

        #endregion

        #region Constructor

        /// <summary>
        /// Khởi tạo dịch vụ gợi ý địa điểm và lộ trình
        /// </summary>
        public ESRecommendationService(
            ElasticsearchClient elasticClient,
            IESPlacesService iesPlacesServiceService,
            IESPlacePairs placePairsService,
            IESNearbyService nearbyService,
            IESPlaceDetailsService placeDetailsService,
            ILogger<ESRecommendationService> logger,
            IConfiguration configuration)
        {
            _elasticClient = elasticClient;
            _iesPlacesServiceService = iesPlacesServiceService;
            _placePairsService = placePairsService;
            _nearbyService = nearbyService;
            _placeDetailsService = placeDetailsService;
            _logger = logger;

            var indexNames = configuration.Bind<ElasticSearchConfig>(nameof(ElasticSearchConfig));
            _placesIdxName = indexNames.Index?.FirstOrDefault(x => x.StartsWith("place-")) ?? "places";
            _favoritePlacesIdxName = indexNames.Index?.FirstOrDefault(x => x.StartsWith("fav-place")) ?? "fav-place";
            _placePairIdxName = indexNames.Index?.Where(x => x !=  _placesIdxName).FirstOrDefault(x => x.StartsWith("place-pair")) ?? "place-pair";
            _searchHistoryIdxName = indexNames.Index?.FirstOrDefault(x => x.StartsWith("user-place")) ?? "user-place";
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Get địa điểm từ Elasticsearch dựa trên ID địa điểm
        /// </summary>
        /// <param name="placeIds">Danh sách ID địa điểm</param>
        /// <returns>Tuple chứa địa điểm đầu tiên và danh sách tất cả các địa điểm</returns>
        /// <exception cref="Exception">Ném ra nếu không tìm thấy địa điểm nào</exception>
        private async Task<Tuple<PlacesEs, List<PlacesEs>>> GetESPlace(params string[] placeIds)
        {
            // Sử dụng dịch vụ mới để lấy chi tiết địa điểm
            return await _placeDetailsService.GetPlaceDetailsAsync(placeIds);
        }

        /// <summary>
        /// Kiểm tra xem ngôn ngữ có phải là tiếng Việt hay không
        /// </summary>
        private bool IsVietnamese(string lang)
        {
            return !string.IsNullOrEmpty(lang) && (lang.Equals("vi", StringComparison.OrdinalIgnoreCase) ||
                                                   lang.Equals("vn", StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Trích xuất địa chỉ ngắn gọn từ địa chỉ đầy đủ
        /// </summary>
        private string ExtractAddress(string fullAddress)
        {
            if (string.IsNullOrEmpty(fullAddress))
                return string.Empty;

            // Tách địa chỉ theo dấu phẩy và lấy phần đầu tiên
            var parts = fullAddress.Split(',', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length > 0)
                return parts[0].Trim();

            return fullAddress;
        }
        
        /// <summary>
        /// Lấy mô tả thời gian trong ngày phù hợp với ngôn ngữ
        /// </summary>
        private string GetTimeOfDayText(int hour, string lang)
        {
            if (IsVietnamese(lang))
            {
                if (hour is >= 5 and < 12) return "buổi sáng";
                if (hour is >= 12 and < 18) return "buổi chiều";
                return "buổi tối";
            }

            if (hour is >= 5 and < 12) return "in the morning";
            if (hour >= 12 && hour < 18) return "in the afternoon";
            return "in the evening";
        }

        #endregion
    }
}