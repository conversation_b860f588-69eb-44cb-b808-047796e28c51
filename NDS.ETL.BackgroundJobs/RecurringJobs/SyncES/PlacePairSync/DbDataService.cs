using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch.Dto;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch.PlacePairSync;
using NDS.ETL.Entities.Context;
using System.Collections.Concurrent;
using System.Linq.Expressions;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.PlacePairSync
{
    /// <summary>
    /// Dịch vụ làm việc với dữ liệu cơ sở dữ liệu
    /// </summary>
    public class DbDataService
    {
        private readonly ILogger _logger;
        private const int COMMAND_TIMEOUT = 600;

        public DbDataService(ILogger logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// L<PERSON>y thông tin thống kê từ cơ sở dữ liệu với tối ưu performance
        /// </summary>
        public async Task<DatabaseStats> GetDatabaseStats(NpgPlaceContext dbContext, DateTime sixMonthsAgo, CancellationToken stoppingToken)
        {
            // Đặt timeout cho DbContext để tránh timeout trong các truy vấn lớn
            dbContext.Database.SetCommandTimeout(COMMAND_TIMEOUT);
            
            var totalRecords = await dbContext.BookingPlacePairs
                .Where(x => x.UpdateAt >= sixMonthsAgo)
                .CountAsync(stoppingToken);
                
            if (totalRecords == 0)
            {
                _logger.LogInformation("No records found in the specified time range");
                return new DatabaseStats
                {
                    MaxId = 0,
                    MaxUpdateAt = DateTime.MinValue,
                    MaxCreateAt = DateTime.MinValue,
                    TotalRecords = 0,
                    NewRecordsCount = 0
                };
            }
            
            var maxId = await dbContext.BookingPlacePairs
                .Where(x => x.UpdateAt >= sixMonthsAgo)
                .MaxAsync(m => (int?)m.Id, cancellationToken: stoppingToken) ?? 0;
            
            // Lấy thời gian cập nhật mới nhất với tối ưu query
            var maxUpdateAt = await dbContext.BookingPlacePairs
                .Where(x => x.UpdateAt != null)
                .MaxAsync(i => i.UpdateAt, stoppingToken);

            // Lấy thời gian tạo mới nhất cho các bản ghi mới với query tối ưu
            var newRecord = await dbContext.BookingPlacePairs
                .Where(x => (x.UpdateAt == null && x.CreatedAt != null) || 
                           (x.UpdateAt != null && x.CreatedAt != null && x.UpdateAt == x.CreatedAt))
                .OrderByDescending(x => x.CreatedAt)
                .Select(x => new { x.Id, x.CreatedAt, x.UpdateAt }) // Chỉ select fields cần thiết
                .FirstOrDefaultAsync(stoppingToken);
                
            var maxCreateAt = newRecord?.CreatedAt ?? DateTime.MinValue;
                
            if (newRecord != null)
            {
                _logger.LogInformation($"🔍 Found newest record: ID={newRecord.Id}, CreatedAt={maxCreateAt:HH:mm:ss.fff}, UpdateAt={newRecord.UpdateAt.ToString("HH:mm:ss.fff") ?? "null"}");
            }
                
            // Đếm số lượng bản ghi mới với query tối ưu
            var newRecordsCount = await dbContext.BookingPlacePairs
                .Where(x => (x.UpdateAt == null && x.CreatedAt != null) || 
                           (x.UpdateAt != null && x.CreatedAt != null && x.UpdateAt == x.CreatedAt))
                .CountAsync(stoppingToken);
            
            _logger.LogInformation($"📊 DB Stats - Total: {totalRecords:N0}, New: {newRecordsCount:N0}, MaxUpdateAt: {maxUpdateAt:HH:mm:ss.fff}, MaxCreateAt: {maxCreateAt:HH:mm:ss.fff}");
            
            return new DatabaseStats
            {
                MaxId = maxId,
                MaxUpdateAt = maxUpdateAt,
                MaxCreateAt = maxCreateAt,
                TotalRecords = totalRecords,
                NewRecordsCount = newRecordsCount
            };
        }
        
        /// <summary>
        /// Tìm các batch có dữ liệu để tránh xử lý các batch rỗng
        /// </summary>
        public async Task<List<long>> GetBatchesWithData(NpgPlaceContext dbContext, DateTime sixMonthsAgo, int pageSize, CancellationToken stoppingToken)
        {
            // Lấy ID nhỏ nhất và lớn nhất trong dữ liệu
            var minId = await dbContext.BookingPlacePairs
                .Where(x => x.UpdateAt >= sixMonthsAgo)
                .MinAsync(m => (int?)m.Id, cancellationToken: stoppingToken) ?? 0;
                
            var maxId = await dbContext.BookingPlacePairs
                .Where(x => x.UpdateAt >= sixMonthsAgo)
                .MaxAsync(m => (int?)m.Id, cancellationToken: stoppingToken) ?? 0;
                
            if (minId == 0 && maxId == 0)
                return new List<long>();
            
            _logger.LogInformation($"Data range: ID from {minId} to {maxId}");
            
            // Tính toán số lượng batch cần kiểm tra
            int startBatch = minId / pageSize;
            int endBatch = maxId / pageSize;
            int totalBatchesToCheck = endBatch - startBatch + 1;
            
            _logger.LogInformation($"Checking {totalBatchesToCheck} potential batches from {startBatch} to {endBatch}");
            
            // Sử dụng cách tiếp cận tương thích với Npgsql
            var result = await dbContext.BookingPlacePairs
                .Where(x => x.UpdateAt >= sixMonthsAgo)
                .Where(x => x.Id >= minId && x.Id <= maxId)
                .Select(x => x.Id / pageSize) // Sử dụng phép chia nguyên thay vì Floor
                .Distinct()
                .OrderBy(x => x)
                .ToListAsync(stoppingToken);
            
            _logger.LogInformation($"Found {result.Count} batches with actual data out of {totalBatchesToCheck} potential batches");
            
            return result;
        }

        /// <summary>
        /// Lấy dữ liệu theo khoảng ID
        /// </summary>
        public async Task<List<BookingPlacePairDto>> FetchBatchByIdRangeAsync(
            NpgPlaceContext dbContext, 
            long startId, 
            long endId,
            DateTime sixMonthsAgo,
            CancellationToken stoppingToken)
        {
            return await dbContext.BookingPlacePairs
                .Where(x => x.UpdateAt >= sixMonthsAgo)
                .Where(x => x.Id >= startId && x.Id <= endId)
                .Select(MapToBookingPlacePairDto())
                .ToListAsync(stoppingToken);
        }

        /// <summary>
        /// Lấy dữ liệu đã cập nhật theo khoảng ID và thời gian cập nhật
        /// </summary>
        public async Task<List<BookingPlacePairDto>> FetchUpdatedBatchDataAsync(
            NpgPlaceContext dbContext,
            long startId,
            long endId,
            DateTime esMaxUpdateAt,
            CancellationToken stoppingToken)
        {
            return await dbContext.BookingPlacePairs
                .Where(x => x.Id >= startId && x.Id <= endId)
                .Where(x => x.UpdateAt > esMaxUpdateAt)
                .Select(MapToBookingPlacePairDto())
                .ToListAsync(stoppingToken);
        }

        /// <summary>
        /// Lấy dữ liệu mới (createdAt có giá trị và updateAt = null HOẶC updateAt = createdAt) theo khoảng ID
        /// </summary>
        public async Task<List<BookingPlacePairDto>> FetchNewBatchDataAsync(
            NpgPlaceContext dbContext,
            long startId,
            long endId,
            CancellationToken stoppingToken)
        {
            return await dbContext.BookingPlacePairs
                .Where(x => x.Id >= startId && x.Id <= endId)
                .Where(x => (x.UpdateAt == null && x.CreatedAt != null) || 
                           (x.UpdateAt != null && x.CreatedAt != null && x.UpdateAt == x.CreatedAt))
                .Select(MapToBookingPlacePairDto())
                .ToListAsync(stoppingToken);
        }

        /// <summary>
        /// Lấy dữ liệu cập nhật (updateAt != null và updateAt > createdAt) theo khoảng ID và thời gian cập nhật
        /// </summary>
        public async Task<List<BookingPlacePairDto>> FetchUpdatedRecordsAsync(
            NpgPlaceContext dbContext,
            long startId,
            long endId,
            DateTime esMaxUpdateAt,
            CancellationToken stoppingToken)
        {
            return await dbContext.BookingPlacePairs
                .Where(x => x.Id >= startId && x.Id <= endId)
                .Where(x => x.UpdateAt != null && x.CreatedAt != null && 
                           x.UpdateAt > x.CreatedAt && x.UpdateAt > esMaxUpdateAt)
                .Select(MapToBookingPlacePairDto())
                .ToListAsync(stoppingToken);
        }

        /// <summary>
        /// Chuyển đổi dữ liệu từ cơ sở dữ liệu sang định dạng ElasticSearch
        /// </summary>
        public List<PlacePairEs> MapToEsDocuments(List<BookingPlacePairDto> data)
        {
            // Sử dụng ConcurrentBag để lưu trữ kết quả từ việc xử lý song song
            var result = new ConcurrentBag<PlacePairEs>();

            Parallel.ForEach(data, pairDto =>
            {
                result.Add(PlacePairEs.CreateHistoryBooking(pairDto, true)); // Pickup place
                result.Add(PlacePairEs.CreateHistoryBooking(pairDto, false)); // Arrival place
            });

            return result.ToList();
        }

        /// <summary>
        /// Tạo expression để chuyển đổi BookingPlacePair thành BookingPlacePairDto
        /// </summary>
        private static Expression<Func<Entities.PostgresPlace.BookingPlacePair, BookingPlacePairDto>> MapToBookingPlacePairDto()
        {
            return x => new BookingPlacePairDto
            {
                Id = x.Id,
                ArrivalPlaceId = x.ArrivalPlaceId,
                BookingCount = x.BookingCount,
                CustomerId = x.CustomerId,
                CustomerPhone = x.CustomerPhone,
                Distance = x.Distance,
                FromAddress = x.FromAddress,
                FromCity = x.FromCity,
                FromFullAddress = x.FromFullAddress,
                Location1 = x.Location1,
                Location2 = x.Location2,
                PaidCount = x.PaidCount,
                PickupPlaceId = x.PickupPlaceId,
                SearchCount = x.SearchCount,
                ToAddress = x.ToAddress,
                ToCity = x.ToCity,
                ToFullAddress = x.ToFullAddress,
                CreatedAt = x.CreatedAt,
                UpdateAt = x.UpdateAt,
                LastBookingAt = x.LastBookingAt,
                LastSearchAt = x.LastSearchAt,
                EstimateAmount = x.EstimateAmount,
                LastEstimateAmount = x.LastEstimateAmount,
                LastNote = x.LastNote
            };
        }
    }
} 