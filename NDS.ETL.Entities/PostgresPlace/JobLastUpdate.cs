using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("job_last_update")]
[Index("Origin", Name = "job_last_update_origin_idx")]
public partial class JobLastUpdate
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("lastUpdate", TypeName = "timestamp without time zone")]
    public DateTime LastUpdate { get; set; }

    [Column("origin")]
    public int? Origin { get; set; }
}
