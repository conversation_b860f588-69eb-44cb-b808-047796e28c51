using NDS.ETL.BackgroundJobs.Model.ElasticSearch;

namespace NDS.ETL.BackgroundJobs.Services.ES
{
    /// <summary>
    /// Service interface cho chức năng tìm kiếm địa điểm gần đây trên Elasticsearch
    /// </summary>
    public interface IESNearbyService
    {
        /// <summary>
        /// Tìm kiếm địa điểm gần đây dựa trên tọa độ và độ phân giải H3
        /// </summary>
        /// <param name="userId">ID của người dùng</param>
        /// <param name="customerPhone">Số điện thoại khách hàng</param>
        /// <param name="latitude">Vĩ độ hiện tại</param>
        /// <param name="longitude">Kinh độ hiện tại</param>
        /// <param name="h3Resolution"><PERSON><PERSON> phân giải H3 (gi<PERSON> trị mặc định là 8)</param>
        /// <param name="limit"><PERSON><PERSON> lượng kết quả tối đa</param>
        /// <param name="popular">Lọ<PERSON> theo địa điểm phổ biến (nếu true), không lọc (nếu null)</param>
        /// <param name="maxRadiusInMeters">Bán kính tìm kiếm theo mét, dùng để lọc kết quả cuối cùng theo khoảng cách chính xác</param>
        /// <param name="minRadiusMeters">Bán kính tối thiểu</param>
        /// <returns>Danh sách các địa điểm gần đây</returns>
        Task<List<PlacesEs>> SearchNearbyAsync(string userId,
            string customerPhone,
            double latitude,
            double longitude,
            int? h3Resolution = 8,
            int limit = 5,
            bool? popular = null,
            double? maxRadiusInMeters = null,
            double? minRadiusMeters = null);
    }
} 