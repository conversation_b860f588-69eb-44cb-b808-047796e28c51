using System;
using System.Collections.Generic;
using System.Text;

namespace NDS.ETL.Entities.TaxiMongoDB.MongoAttributes
{
	[AttributeUsage(AttributeTargets.Property, AllowMultiple = false, Inherited = false)]
	public class BucketSetOptionsAttribute : DbSetOptionsAttribute
	{
		public int BucketSize { get; private set; }

		public BucketSetOptionsAttribute(int bucketSize)
		{
			BucketSize = bucketSize;
		}

		public override IDbSetOptions GetOptions()
		{
			return new BucketSetOptions
			{
				BucketSize = BucketSize
			};
		}
	}
}
namespace NDS.ETL.Entities.TaxiMongoDB
{
    public interface IDbSetOptions
    {
    }
    public class BucketSetOptions : IDbSetOptions
    {
        public int BucketSize { get; set; }
    }
}
