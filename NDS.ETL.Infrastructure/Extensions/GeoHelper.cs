using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NDS.ETL.Infrastructure.Extensions
{
    public static class GeoHelper
    {
        /// <summary>
        /// Tính khoảng cách giữa 2 điểm theo tọa độ địa lý (vĩ độ, kinh độ).
        /// </summary>
        /// <param name="lat1">Latitude điểm 1 (đơn vị độ)</param>
        /// <param name="lon1">Longitude điểm 1 (đơn vị độ)</param>
        /// <param name="lat2">Latitude điểm 2 (đơn vị độ)</param>
        /// <param name="lon2">Longitude điểm 2 (đơn vị độ)</param>
        /// <returns>Khoảng cách tính bằng mét</returns>
        public static double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
        {
            const double R = 6371000; // <PERSON><PERSON> k<PERSON>h Trá<PERSON> (mét)

            var latRad1 = DegreesToRadians(lat1);
            var latRad2 = DegreesToRadians(lat2);
            var deltaLat = DegreesToRadians(lat2 - lat1);
            var deltaLon = DegreesToRadians(lon2 - lon1);

            var a = Math.Sin(deltaLat / 2) * Math.Sin(deltaLat / 2) +
                    Math.Cos(latRad1) * Math.Cos(latRad2) *
                    Math.Sin(deltaLon / 2) * Math.Sin(deltaLon / 2);

            var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));

            return R * c;
        }

        private static double DegreesToRadians(double degrees)
        {
            return degrees * Math.PI / 180.0;
        }
    }

}
