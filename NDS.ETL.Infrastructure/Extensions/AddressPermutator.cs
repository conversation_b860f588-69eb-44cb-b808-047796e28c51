using System.Text;
using System.Text.RegularExpressions;

namespace NDS.ETL.Infrastructure.Extensions;

public static class AddressPermutator
{
    private static readonly string[] StopWords = {
        "va", "cua", "cho", "tai", "tu", "den", "trong", "ngoai", "tren", "duoi",
        "and", "of", "to", "at", "from", "in", "out", "on", "under"
    };

    private static readonly Dictionary<string, string> AddressAbbreviationExpansions = new(StringComparer.OrdinalIgnoreCase)
    {
        { "đ.", "đường" },
        { "x", "xã" },
        { "x.", "xã" },
        { "tt", "thị trấn" },
        { "tt.", "thị trấn" },
        { "tx.", "thị xã" },
        { "h", "huyện" },
        { "h.", "huyện" },
        { "tp.", "thành phố" },
        { "tp", "thành phố" },
        { "t", "tỉnh" },
        { "t.", "tỉnh" },
        { "p.", "phố" },
        { "ph.", "phố" },
        { "ph", "phố" },
        { "q.", "quận" },
        { "q", "quận" },
        { "qu", "quận" },
        { "so.", "số" },
        { "ng.", "ngõ" },
        { "ngh.", "ngách" },
        { "thanh pho", "" },
        { "pho", "" },
    };

    private static readonly Dictionary<string, string> AbbreviationMap = new(StringComparer.OrdinalIgnoreCase)
    {
        { "đ.", "duong" },
        { "x", "xa" },
        { "x.", "xa" },
        { "tt", "thi tran" },
        { "tt.", "thi tran" },
        { "tx.", "thi xa" },
        { "h", "huyen" },
        { "h.", "huyen" },
        { "tp.", "thanh pho" },
        { "tp", "thanh pho" },
        { "t", "tinh" },
        { "t.", "tinh" },
        { "p.", "pho" },
        { "ph.", "pho" },
        { "ph", "pho" },
        { "q.", "quan" },
        { "q", "quan" },
        { "qu", "quan" },
        { "so.", "so" },
        { "ng.", "ngo" },
        { "ngh.", "ngach" },
        { "thanh pho", "" },
        { "pho", "" },
    };

    private static readonly string[] Prefixes = { "so ", "ngo ", "ngach ", "hem ", "hẻm " };

    public static List<string> GenerateOptimizedPermutations(string address, int maxPermutations = 15)
    {
        if (string.IsNullOrWhiteSpace(address)) return new List<string>();
        string normalized = NormalizeVietnamese(address);
        var parts = SplitAddressParts(normalized);
        var permutations = new HashSet<string>();

        string joined = string.Join(" ", parts);
        permutations.Add(joined);
        permutations.Add(Cleanup(joined));
        permutations.Add(RemovePostalCode(joined));
        permutations.Add(RemovePrefixes(joined));
        permutations.Add(RemoveStopWords(joined));

        AddReversedPermutations(parts, permutations);
        AddStandardPermutations(parts, permutations, maxPermutations);

        return permutations.Take(maxPermutations).ToList();
    }

    private static List<string> SplitAddressParts(string address)
    {
        var parts = address.Split(',')
            .Select(p => p.Trim())
            .Where(p => !string.IsNullOrWhiteSpace(p))
            .ToList();

        return parts.Count <= 1 ? SmartChunk(address) : parts;
    }

    private static void AddReversedPermutations(List<string> parts, HashSet<string> permutations)
    {
        if (parts.Count < 2) return;

        for (int i = parts.Count - 1; i >= 0; i--)
        {
            var reversed = new List<string> { parts[i] };
            reversed.AddRange(parts.Where((_, j) => j != i));
            permutations.Add(string.Join(" ", reversed));
        }
    }

    private static void AddStandardPermutations(List<string> parts, HashSet<string> permutations, int max)
    {
        foreach (var perm in GetPermutations(parts, Math.Min(5, max)))
        {
            if (permutations.Count >= max) break;
            permutations.Add(string.Join(" ", perm));
        }
    }

    private static IEnumerable<List<string>> GetPermutations(List<string> list, int max)
    {
        return Permute(list, 0).Take(max);
    }

    private static IEnumerable<List<string>> Permute(List<string> list, int start)
    {
        if (start == list.Count - 1)
        {
            yield return new List<string>(list);
        }
        else
        {
            for (int i = start; i < list.Count; i++)
            {
                Swap(list, start, i);
                foreach (var perm in Permute(list, start + 1))
                    yield return perm;
                Swap(list, start, i);
            }
        }
    }

    private static void Swap(List<string> list, int i, int j)
    {
        if (i != j)
        {
            (list[i], list[j]) = (list[j], list[i]);
        }
    }

    public static string NormalizeVietnamese(this string input, bool removeAccent = true)
    {
        if (string.IsNullOrEmpty(input)) return input;
        string cleaned = RemoveDuplicateParentheses(input.ToLowerInvariant());

        if (removeAccent)
            cleaned = RemoveVietnameseDiacritics(cleaned);

        if (removeAccent)
        {
            // Loop over the map and replace abbreviations with full terms
            foreach (var abbreviation in AbbreviationMap)
            {
                var pattern = $@"(?<=\s|^){Regex.Escape(abbreviation.Key)}(?=\s|$|,)";
                cleaned = Regex.Replace(cleaned, pattern, abbreviation.Value, RegexOptions.IgnoreCase);
            }

            cleaned = cleaned
                .Replace("đ", "d");
        }
        else
        {
            // Loop over the map and replace abbreviations with full terms
            foreach (var abbreviation in AddressAbbreviationExpansions)
            {
                var pattern = $@"(?<=\s|^){Regex.Escape(abbreviation.Key)}(?=\s|$|,)";
                cleaned = Regex.Replace(cleaned, pattern, abbreviation.Value, RegexOptions.IgnoreCase);
            }
        }

        cleaned = cleaned
            .Replace(" /", "/")
            .Replace("/ ", "/");

        cleaned = Regex.Replace(cleaned, @"\b(viet\s?nam)\b", "", RegexOptions.IgnoreCase);
        cleaned = Regex.Replace(cleaned, @"\b\d{5,6}\b", "");

        // Làm sạch khoảng trắng dư thừa
        cleaned = Regex.Replace(cleaned, @"\s{2,}", " ").Trim();

        var parts = cleaned.Split(',')
            .Select(p => p.Trim())
            .Where(p => !string.IsNullOrEmpty(p))
            .Distinct()
            .ToList();

        return string.Join(", ", parts).Trim();
    }

    private static string RemoveVietnameseDiacritics(this string text)
    {
        var normalized = text.Normalize(NormalizationForm.FormD);
        return Regex.Replace(normalized, "\\p{IsCombiningDiacriticalMarks}+", "");
    }

    private static string Cleanup(string part)
    {
        return Regex.Replace(part, @"[,\.;:""'\[\]\(\)]", " ").Trim().Replace("  ", " ");
    }

    private static string RemoveStopWords(string input)
    {
        return string.Join(" ", input.Split(' ').Where(w => !StopWords.Contains(w)));
    }

    private static string RemovePostalCode(string input)
    {
        return Regex.Replace(input, @"\b\d{5,6}\b", "").Trim();
    }

    private static string RemovePrefixes(string input)
    {
        string result = input;
        foreach (var prefix in Prefixes)
        {
            result = Regex.Replace(result, $@"(^|\s){Regex.Escape(prefix)}\s*\d+", " ");
        }
        return Regex.Replace(result, @"\s+", " ").Trim();
    }

    private static string RemoveDuplicateParentheses(string input)
    {
        return Regex.Replace(input, @"\(([^()]+)\)", match =>
        {
            var content = match.Groups[1].Value.Trim();
            return input.Contains(content, StringComparison.OrdinalIgnoreCase) ? "" : match.Value;
        }).Replace(" ,", ",").Trim();
    }

    private static List<string> SmartChunk(string input)
    {
        var parts = new List<string>();
        var match = Regex.Match(input, @"^([\w\s\/\.\-]+?(ngach|ngo|hem|duong|đ|d|street)?[\w\s\/\-]*)\s+(phuong|quan|huyen|tp|thanh pho|tinh|xa|thi tran)", RegexOptions.IgnoreCase);

        if (match.Success)
        {
            parts.Add(match.Groups[1].Value.Trim());
            input = input[match.Groups[1].Value.Length..].Trim();
        }

        var keywords = new[] {
            "phuong", "quan", "huyen", "tp", "thanh pho", "tinh", "xa", "thi tran", "pho", "duong"
        };

        foreach (var keyword in keywords)
        {
            var index = input.IndexOf(keyword, StringComparison.OrdinalIgnoreCase);
            if (index > 0)
            {
                parts.Add(input[..index].Trim());
                input = input[index..].Trim();
            }
        }

        if (!string.IsNullOrWhiteSpace(input))
            parts.Add(input);

        return parts;
    }
}
