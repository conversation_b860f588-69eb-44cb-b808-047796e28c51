using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch.Request;
using NDS.ETL.Entities.PostgresPlace;

namespace NDS.ETL.BackgroundJobs.Services.ES;

public interface IESPlacesService
{
    Task<List<SearchResponseBase>> SearchPlaces(PlaceEsSearchRq searchRequest, CancellationToken cancellationToken = default);
    Task<List<PlacesEs>> GetPlacesByPlaceIdsAsync(List<string> placeIds, CancellationToken cancellationToken = default, int size = 100);
    Task<List<SubPlacesEs>> GetPlacesBySubPlaceIdsAsync(List<string> placeIds, CancellationToken cancellationToken = default, int size = 100);
    Task<List<SearchResponseBase>> GetPlaceToScan(Place searchRequest,double distance,int take, CancellationToken cancellationToken = default);
    Task<List<string>?> GetDuplicateAddress(int districtId, CancellationToken cancellationToken = default);
    Task<bool> SyncDataTrustedToEs(string placeId, CancellationToken cancellationToken = default);
    Task<List<string>?> RevertDataUpdated(CancellationToken cancellationToken = default);
    Task<bool> RemoveListDuplicate(List<string> lstAddress, CancellationToken cancellationToken = default);
    Task<bool> InsertPlacesScanResult(int districtId, CancellationToken cancellationToken = default);
    Task<PlacesEs> GetPlace(string placeId, CancellationToken cancellationToken = default);
    Task<bool> UpdateLocationPlaceApproved(string placeId,bool isAll, CancellationToken cancellationToken = default);
    Task<List<PlacesEs>> GetPlacesByDistrictId(int districtId, bool processed, bool removed, CancellationToken cancellationToken = default);
    Task<VerifyProcessedPlace> GetDistance(string placeIdSource,string placeIdDestination, CancellationToken cancellationToken = default);
    Task<List<ResultVerifyProcessedPlace>> CompareVerify(CancellationToken cancellationToken = default);
    //Create service to upSert data to ElasticSearch using T type
    Task UpsertDocumentAsync<T>(T source,CancellationToken cancellationToken, string esIndex, string? pipeline = null) where T : class;
    Task DeleteDocumentAsync<T>(T source,CancellationToken cancellationToken, string esIndex) where T : class;
}