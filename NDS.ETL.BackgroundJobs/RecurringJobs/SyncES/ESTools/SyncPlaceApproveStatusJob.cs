using Elastic.Clients.Elasticsearch;
using KnowledgeBase.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Entities.Context;
using NDS.ETL.Entities.PostgresPlace;
using NDS.ETL.Infrastructure.Common;
using NDS.ETL.Infrastructure.Utils;
using System.Diagnostics;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.ESTools
{
    public class SyncPlaceApproveStatusJob : RecurringJobBaseElastic
    {
        private readonly ILogger<RecurringJobBaseElastic> _logger;
        private readonly IScopedResolver<NpgPlaceContext> _dbContext;
        private readonly ElasticSearchConfig _indexName;
        private readonly IESPlacesService _iesPlacesService;

        public SyncPlaceApproveStatusJob(IConfiguration configuration, IESPlacesService iesPlacesService, ElasticsearchClient elasticClient, ILogger<SyncPlaceAfterApproveJob> logger, IScopedResolver<NpgPlaceContext> dbContext) : base(configuration, logger, elasticClient, null)
        {
            _logger = logger;
            _dbContext = dbContext;
            _indexName = configuration.Bind<ElasticSearchConfig>(nameof(ElasticSearchConfig));
            _iesPlacesService = iesPlacesService;
        }

        public override string JobId => "SyncPlaceApproveStatusJob";

        public override string ConfigSection => "JobPgSyncPlace";
        public readonly HashSet<int> syncStatus = new HashSet<int> { 0, 4, 6 };
        public override async Task DoWorkAsync(CancellationToken stoppingToken)
        {
            try
            {
                await _dbContext.ResolveAsync(async (context) =>
                {
                    Stopwatch stw = new Stopwatch();
                    stw.Start();
                    var take = 100;
                    int totalProcess = 0, totalSuccess = 0, totalFailed = 0;
                    long lastId = 0;
                    while (true)
                    {
                        var batch = await context.PlaceApproves.Where(x => x.Id > lastId).OrderBy(x => x.Id)
                            .Take(take)
                            .ToListAsync(stoppingToken);
                        if (batch.Count == 0) break;
                        lastId = batch.Last().Id;
                        _logger.LogInformation($"Last placeId: {lastId}");
                        var placeIds = batch.Select(x => x.PlaceId).ToList();
                        if (placeIds.Count == 0) break;
                        var esData = await _iesPlacesService.GetPlacesByPlaceIdsAsync(placeIds, stoppingToken);
                        if (!esData.Any())
                        {
                            totalFailed += batch.Count;
                            continue;
                        }
                        var missingPlaceIds = placeIds.Except(esData.Select(x => x.PlaceId)).ToList();
                        if (missingPlaceIds.Any())
                        {
                            _logger.LogWarning($"Missing PlaceIds in ES: {string.Join(", ", missingPlaceIds)}");
                        }
                        var esDict = esData.ToDictionary(x => x.PlaceId, x => x);
                        var updateList = new List<PlacesEs>();
                        int batchSuccess = 0, batchFailed = 0;
                        foreach (var item in batch)
                        {
                            if (!esDict.TryGetValue(item.PlaceId, out var placeEs))
                            {
                                batchFailed++;
                                continue;
                            }
                            if (ShouldUpdate(item, placeEs))
                            {
                                placeEs.StatusApprovePlace = item.StatusApprove;
                                updateList.Add(placeEs);
                                batchSuccess++;
                            }
                            else
                            {
                                batchFailed++;
                            }
                        }
                        if (updateList.Count > 0)
                            await IndexMany(updateList, stoppingToken, _indexName.Index[0], updateList.Count);

                        // Cộng dồn kết quả
                        totalProcess += batch.Count;
                        totalSuccess += batchSuccess;
                        totalFailed += batchFailed;

                        _logger.LogInformation(
                            $"Batch done: success {batchSuccess}, failed {batchFailed}. " +
                            $"Total data: success {totalSuccess}, failed {totalFailed}, processed {totalProcess}");
                    }
                    stw.Stop();
                    return true;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while comparing score change places.");
                throw;
            }
        }

        private bool ShouldUpdate(PlaceApprove pa, PlacesEs es)
        {
            if (pa == null || es == null) return false;
            if (syncStatus.Contains(pa.StatusApprove) && pa.IsDeleted == false)
            {
                return es.StatusApprovePlace == null || es.StatusApprovePlace != pa.StatusApprove;
            }
            return false;
        }
    }
}


