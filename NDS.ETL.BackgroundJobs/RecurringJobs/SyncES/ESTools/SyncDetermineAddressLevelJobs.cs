using System.Diagnostics;
using Elastic.Clients.Elasticsearch;
using KnowledgeBase.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Entities.Context;
using NDS.ETL.Infrastructure.Extensions;
using NDS.ETL.Infrastructure.Utils;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.ESTools;

public class SyncDetermineAddressLevelJobs : RecurringJobBaseElastic
{
    private readonly ILogger<SyncDetermineAddressLevelJobs> _logger;
    private readonly ElasticsearchClient _elasticClient;
    private readonly IESPlacesService _iesPlacesService;
    private readonly ElasticSearchConfig _indexName;
    private readonly IServiceProvider _serviceProvider;

    public SyncDetermineAddressLevelJobs(IServiceProvider serviceProvider, IConfiguration configuration) : base(configuration, serviceProvider.GetRequiredService<ILogger<SyncDetermineAddressLevelJobs>>(), serviceProvider.GetRequiredService<ElasticsearchClient>(), null)
    {
        _serviceProvider = serviceProvider;
        _indexName = configuration.Bind<ElasticSearchConfig>(nameof(ElasticSearchConfig));
        _logger = _serviceProvider.GetRequiredService<ILogger<SyncDetermineAddressLevelJobs>>();
        _elasticClient = _serviceProvider.GetRequiredService<ElasticsearchClient>();
        _iesPlacesService = _serviceProvider.GetRequiredService<IESPlacesService>();
    }

    public override string JobId => "SyncDetermineAddressLevel";
    public override string ConfigSection => "JobPgSyncPlace";
    public override async Task DoWorkAsync(CancellationToken stoppingToken)
    {
        var globalStopwatch = new Stopwatch(); // Khởi tạo đồng hồ bấm giờ để đo thời gian thực hiện
        globalStopwatch.Start(); // Bắt đầu đếm thời gian
        Console.WriteLine($"SyncDetermineAddressLevelJobs DoWorkAsync");
        try
        {
            // Tạo scope mới để quản lý dependency injection
            using var scope = _serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<NpgPlaceContext>();
            dbContext.Database.SetCommandTimeout(300); // Đặt thời gian chờ cho các lệnh SQL

            // Lấy danh sách kết quả quét từ cơ sở dữ liệu
            var dataPlacesScanResults = await dbContext.PlacesScanResults
                .Select(x => new { x.Id, x.Placeid })
                .ToListAsync(cancellationToken: stoppingToken);

            var totalRecords = dataPlacesScanResults.Count; // Đếm tổng số bản ghi
            _logger.LogInformation($"Total records to process: {totalRecords}");

            if (totalRecords == 0) return; // Nếu không có bản ghi nào, thoát khỏi hàm

            const int batchSize = 1000; // Kích thước mỗi batch
                                        // Chia dữ liệu thành các batch
            var batches = dataPlacesScanResults
                .Select((value, index) => new { value, index })
                .GroupBy(x => x.index / batchSize)
                .Select(g => g.Select(x => x.value).ToList())
                .ToList();

            // Sử dụng Parallel.ForEach để xử lý các batch song song
            Parallel.ForEach(batches, new ParallelOptions { MaxDegreeOfParallelism = 4 }, async (batch) =>
            {
                var esPlaces = new List<PlacesEs>(); // Danh sách để lưu trữ các đối tượng cần đẩy lên Elasticsearch

                // Lấy dữ liệu từ Elasticsearch theo danh sách Placeid
                var dataEsPlacesBatch = await _iesPlacesService.GetPlacesByPlaceIdsAsync(
                    batch.Select(x => x.Placeid).ToList(), stoppingToken);

                // Xử lý từng item để xác định cấp độ địa chỉ
                foreach (var item in dataEsPlacesBatch)
                {
                    item.DetermineAddressLevel = AddressProcessor.DetermineAddressLevel(item.FullAddress);
                }
                esPlaces.AddRange(dataEsPlacesBatch); // Thêm vào danh sách esPlaces

                if (esPlaces.Count > 0)
                {
                    bool success = false;
                    int retryCount = 0;
                    const int maxRetries = 3; // Số lần thử lại tối đa
                    while (!success && retryCount < maxRetries)
                    {
                        try
                        {
                            // Đẩy dữ liệu lên Elasticsearch
                            await IndexMany(esPlaces, stoppingToken, _indexName.Index[0], 10000);
                            success = true; // Đánh dấu thành công nếu không có lỗi
                        }
                        catch (Exception ex)
                        {
                            retryCount++; // Tăng số lần thử lại
                            _logger.LogWarning(ex, $"Lỗi khi đẩy dữ liệu lên ElasticSearch, thử lại lần {retryCount}/{maxRetries}");
                            if (retryCount >= maxRetries)
                            {
                                _logger.LogError(ex, "Đã vượt quá số lần thử lại tối đa khi đẩy dữ liệu lên ElasticSearch");
                                throw; // Ném ngoại lệ nếu vượt quá số lần thử lại
                            }
                        }
                    }
                }
            });

            globalStopwatch.Stop(); // Dừng đồng hồ bấm giờ
            _logger.LogInformation($"Time to update DetermineAddressLevel total: {totalRecords} records to es---------------: {globalStopwatch.Elapsed.TotalSeconds} seconds");
        }
        catch (Exception e)
        {
            Console.WriteLine(e); // In ra lỗi nếu có
            throw; // Ném ngoại lệ để xử lý bên ngoài
        }
    }
}