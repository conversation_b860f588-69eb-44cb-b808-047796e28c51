using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.QueryDsl;
using KnowledgeBase.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch.Request;
using NDS.ETL.Entities.Context;
using NDS.ETL.Entities.PostgresPlace;
using NDS.ETL.Infrastructure.Common;
using NDS.ETL.Infrastructure.Extensions;
using NDS.ETL.Infrastructure.Utils;
using System.Diagnostics;

namespace NDS.ETL.BackgroundJobs.Services.ES;

public class ESPlacesService : IESPlacesService
{
    private readonly ElasticsearchClient _elasticClient;
    private readonly IScopedResolver<NpgPlaceContext> _dbContextResolver;
    private readonly ISearchConfigService _searchConfigService;
    private readonly string _indexName;
    private readonly ILogger<ESPlacesService> _logger;

    public ESPlacesService
    (ElasticsearchClient elasticClient,
        IConfiguration configuration,
        IScopedResolver<NpgPlaceContext> dbContextResolver,
        ISearchConfigService searchConfigService,
        ILogger<ESPlacesService> logger)
    {
        _elasticClient = elasticClient;
        _dbContextResolver = dbContextResolver;
        _searchConfigService = searchConfigService;

        var indexNames = configuration.Bind<ElasticSearchConfig>(nameof(ElasticSearchConfig));
        _indexName = indexNames.Index.FirstOrDefault(x => x.StartsWith("place")) ?? "place";
        _logger = logger;
    }


    public async Task<List<SearchResponseBase>> SearchPlaces(PlaceEsSearchRq searchRequest,
        CancellationToken cancellationToken = default)
    {
        var location = GeoLocation.LatitudeLongitude(new LatLonGeoLocation
        { Lat = searchRequest.Lat, Lon = searchRequest.Lon });

        var searchResponse = await _elasticClient.SearchAsync<PlacesEs>(s => s
                .Index(_indexName)
                .Size(200)
                .TrackScores(true)
                .Query(q => BuildScoreQuery(q, searchRequest)) // Hàm build query giữ nguyên
                .Sort(sort => sort
                        .Score(s => s.Order(SortOrder.Desc))
                    , s => s.GeoDistance(g => g
                        .Field(p => p.Location)
                        .DistanceType(GeoDistanceType.Arc)
                        .Order(SortOrder.Asc)
                        .Unit(DistanceUnit.Meters)
                        .Mode(SortMode.Max)
                        .Location(new List<GeoLocation> { location })
                    )),
            cancellationToken
        );

        _logger.LogInformation(
            $"Time to query total:{searchResponse.Documents.Count} Place --------------: {searchResponse.Took} ms");

        var result = searchResponse.Hits.Select((t, idx) => new SearchResponseBase
        {
            PlaceSearchResponse = t.Source,
            Score = t.Score,
            Distance = t.Sort.ToArray()[1].ToString()
        }).ToList();
        return result;
    }

    public async Task<List<PlacesEs>> GetPlacesByPlaceIdsAsync(List<string> placeIds,
        CancellationToken cancellationToken = default, int size = 100)
    {
        var response = await _elasticClient.SearchAsync<PlacesEs>(selector => selector
                .Index(_indexName)
                .From(0)
                .Size(size)
                .Query(q =>
                    q.Bool(b => b
                        .Filter(ft =>
                        {
                            ft.Terms(t => t
                                .Field(f => f.PlaceId) // Trường PlaceId
                                .Terms(new TermsQueryField(placeIds.Select(FieldValue.String!).ToList())
                                ));
                        })
                    ))
            , cancellationToken);
        if (response.TryGetOriginalException(out var ex))
            throw new Exception(ex?.Message);
        return response.Documents.ToList();
    }

    public async Task<List<SubPlacesEs>> GetPlacesBySubPlaceIdsAsync(List<string> placeIds,
        CancellationToken cancellationToken = default, int size = 100)
    {
        var response = await _elasticClient.SearchAsync<PlacesEs>(selector => selector
                .Index(_indexName)
                .From(0)
                .Size(size)
                .Query(q =>
                    q.Nested(n => n
                        .Path("subPlaces")
                        .Query(nq =>
                            nq.Terms(t => t
                                .Field("subPlaces.placeId")
                                .Terms(new TermsQueryField(placeIds.Select(FieldValue.String!).ToList())
                                ))
                        )
                    )
                )
            , cancellationToken);
        if (response.TryGetOriginalException(out var ex))
            throw new Exception(ex?.Message);
        return response.Documents.Select(x =>
        {
            var matchPlace = x.SubPlaces.FirstOrDefault(s => placeIds.Contains(s.PlaceId));
            var subPlacesEs = matchPlace ?? x.SubPlaces.First();
            subPlacesEs.Name = !string.IsNullOrWhiteSpace(x.Name) ? $"{x.Name?.Trim()}, {subPlacesEs.Name?.Trim()}" : subPlacesEs.Name?.Trim();
            subPlacesEs.Address = x.Address;
            subPlacesEs.FullAddress = x.FullAddress;
            return subPlacesEs;
        }).ToList()!;
    }

    public async Task<List<SearchResponseBase>> GetPlaceToScan(Place searchRequest, double distance, int take = 200,
        CancellationToken cancellationToken = default)
    {
        if (searchRequest.Location == null) return [];

        var geoLocation = GeoLocation.LatitudeLongitude(new LatLonGeoLocation
        { Lat = searchRequest.Location.Y, Lon = searchRequest.Location.X });

        var response = await _elasticClient.SearchAsync<PlacesEs>(selector => selector
                .Index(_indexName)
                .From(0)
                .Size(take)
                .Query(q => q
                    .Bool(b => b
                        .Must(m => m.Term(t => t
                            .Field(f => f.DistrictId)
                            .Value(FieldValue.Long((long)searchRequest.DistrictId))
                        ))
                        .Must(m => m.Term(t => t
                            .Field(f => f.Processed)
                            .Value(false)
                        ))
                        .Must(m => m.Bool(b1 => b1
                            .Filter(f => f
                                .GeoDistance(gd => gd
                                    .Distance($"{distance:N}m")
                                    .Field(fi => fi.Location)
                                    .Location(geoLocation)
                                )
                            )
                        ))
                        .MustNot(mn => mn
                            .Term(t => t
                                .Field(f => f.PlaceId)
                                .Value(searchRequest.PlaceId)
                            )
                        )
                        .MustNot(mn => mn
                            .Term(t => t
                                .Field(f => f.ScanStatus)
                                .Value(1)
                            )
                        )
                        .Filter(f => f
                            .GeoDistance(gd => gd
                                .Distance($"{distance:N}m")
                                .Field(fi => fi.Location)
                                .Location(geoLocation)
                            )
                        )
                    )
                )
                .Sort(so => so
                    .GeoDistance(gd => gd
                        .Field(f => f.Location)
                        .Order(SortOrder.Asc)
                        .Location(new List<GeoLocation> { geoLocation })
                        .Unit(DistanceUnit.Meters)
                        .Mode(SortMode.Max)
                        .DistanceType(GeoDistanceType.Plane)
                    )
                )
            , cancellationToken);
        if (response.TryGetOriginalException(out var ex))
            throw new Exception(ex?.Message);

        var result = response.Hits.Select((t, idx) => new SearchResponseBase
        {
            PlaceSearchResponse = t.Source,
            Score = t.Score,
            Distance = t.Sort.ToArray()[0].ToString()
        }).ToList();
        return result;
    }

    // Phương thức lấy địa chỉ trùng lặp
    public async Task<List<string>?> GetDuplicateAddress(int districtId, CancellationToken cancellationToken = default)
    {
        var response = await _elasticClient.SearchAsync<PlacesEs>(s => s
            .Index(_indexName)
            .Size(0)
            .Query(q => q
                .Bool(b => b
                    .Must(m => m
                        .Term(t => t
                            .Field(f => f.DistrictId)
                            .Value(districtId)
                        )
                    )
                    .MustNot(mn => mn
                        .Term(t => t
                            .Field(f => f.ScanProcessed)
                            .Value(5)
                        )
                    )
                )
            )
            .Aggregations(a => a
                .Add("duplicate_addresses", d =>
                {
                    d.Terms(t => t
                        .Field(f => f.FullAddress.Suffix("keyword"))
                        .Size(10000)
                        .MinDocCount(5)
                    );
                })
            )
            .Timeout("300s"), cancellationToken);


        if (response.TryGetOriginalException(out var ex))
            throw new Exception(ex?.Message);
        var duplicateAddresses = response.Aggregations.GetStringTerms("duplicate_addresses").Buckets
            .Select(x => x.Key.ToString()).ToList();
        await RemoveListDuplicate(duplicateAddresses, cancellationToken);
        return duplicateAddresses;
    }

    public async Task<bool> SyncDataTrustedToEs(string placeId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting Sync Data Trusted To ElasticSearch!");
        var globalStopwatch = Stopwatch.StartNew(); // Bắt đầu đo tổng thời gian toàn bộ process
        var count = 0; // Đếm tổng số bản ghi đã xử lý
        var skip = 0;
        var batchSize = 2000; // Kích thước batch
        return await _dbContextResolver.ResolveAsync(async (dbContext) =>
        {
            while (true)
            {
                var itemStopwatch = Stopwatch.StartNew(); // Đo thời gian từng batch
                var items = await dbContext.Places
                    .AsNoTracking()
                    .Where(x => x.Processed == true && x.Removed == false
                                                    && x.UpdatedAt >= new DateTime(2025, 3, 1)
                                                    && x.UpdatedAt <= DateTime.UtcNow)
                    .OrderBy(x => x.UpdatedAt)
                    .Skip(skip)
                    .Take(batchSize)
                    .Select(PlacesEs.Selector) // Chỉ select những field cần thiết để nhẹ payload
                    .ToListAsync(cancellationToken);
                _logger.LogInformation(
                    $"Time to Query DB batch :{items.Count} Place --------------: {itemStopwatch.Elapsed.Milliseconds} ms");
                var listPlaceIds = items.Select(x => x.PlaceId).ToList();
                if (items.Count == 0)
                    break;
                itemStopwatch.Reset();
                var itemsFromEs = await GetPlacesByPlaceIdsAsync(listPlaceIds!, cancellationToken, 5000);
                _logger.LogInformation(
                    $"Time to get total:{items.Count} Place From ES --------------: {itemStopwatch.Elapsed.Milliseconds} ms");
                //update itemsFromEs data address , fullAddress by items where placeId
                var dbItemsDictionary = items.ToDictionary(x => x.PlaceId!);
                foreach (var item in itemsFromEs)
                {
                    if (dbItemsDictionary.TryGetValue(item.PlaceId!, out var dbItem))
                    {
                        // Cập nhật các trường địa chỉ và thông tin khác từ DB vào ES
                        item.Address = dbItem.Address;
                        item.FullAddress = dbItem.FullAddress;
                        item.UpdatedAt = dbItem.UpdatedAt;
                        item.Processed = dbItem.Processed;
                        item.Removed = dbItem.Removed;
                        item.LastUpdateUser = dbItem.LastUpdateUser;
                        item.LastUpdateUser = dbItem.LastUpdateUser;
                        item.Location = dbItem.Location;
                        item.DistrictId = dbItem.DistrictId;
                        item.StatusApprovePlace = 5;
                        item.ScanProcessed = 1;
                    }
                }
                var respEs = await _elasticClient.BulkAsync(b => b
                    .IndexMany(itemsFromEs)
                    .Index(_indexName)
                    .Pipeline("location-to-h3"), cancellationToken);
                if (!respEs.IsSuccess())
                {
                    _logger.LogError($"Error when indexing documents to Elasticsearch. " +
                                     $"Error code: {respEs.ApiCallDetails.HttpStatusCode}, " +
                                     $"Error details: {respEs.ElasticsearchServerError?.Error?.Reason}, " +
                                     $"Debug info: {respEs.DebugInformation}");
                }
                _logger.LogInformation(
                    $"Time to Update batch :{items.Count} Place To ES --------------: {itemStopwatch.Elapsed.Milliseconds} ms");
                itemStopwatch.Stop();
                count += items.Count;
                skip = count;
            }
            // Get relevant job IDs
            _logger.LogInformation(
                $"Time to Update total:{count} Place Trusted To ES --------------: {globalStopwatch.Elapsed.TotalSeconds} seconds");
            globalStopwatch.Stop();
            return true;
        });
    }

    public async Task<List<string>?> RevertDataUpdated(CancellationToken cancellationToken = default)
    {
        return await _dbContextResolver.ResolveAsync(async (dbContext) =>
        {
            int skip = 0, take = 1000;
            int count = 0;
            var esStopwatch = new Stopwatch();
            esStopwatch.Start();
            var lstPlaceIdSync = new List<string>();
            List<FieldValue>? searchAfter = null;
            while (true)
            {
                esStopwatch.Restart();
                var response = await _elasticClient.SearchAsync<PlacesEs>(s => s
                        .Index(_indexName)
                        .Size(take)
                        .Query(q => q
                            .Bool(b => b
                                .Must(m => m
                                    .Term(t => t
                                        .Field(f => f.ScanProcessed)
                                        .Value(1)
                                    )
                                )
                                .Filter(f => f
                                    .Range(r =>
                                    {
                                        r.DateRange(dr => dr
                                            .Field(f => f.UpdatedAt)
                                            .Gte(new DateTime(2024, 12, 11))
                                            .Lte(new DateTime(2024, 12, 13))
                                        );
                                    })
                                )
                            )
                        )
                        .Sort(s => { s.Field(f => f.PlaceId); })
                        .SearchAfter(searchAfter)
                    , cancellationToken);
                esStopwatch.Stop();
                _logger.LogInformation(
                    $"------ES--------- Time to Get Place ES total: {response.Documents.Count} Collection Place to es---------------: {esStopwatch.Elapsed.TotalSeconds} seconds");
                if (!response.IsValidResponse || response.Documents.Count == 0)
                    break;
                //Get places from db by list placeId
                var placeIds = response.Documents.Select(x => x.PlaceId).ToList();
                esStopwatch.Restart();
                var placesDb = await dbContext.Places.Where(x => placeIds.Contains(x.PlaceId))
                    .Select(x => new { x.Id, x.PlaceId, x.PlaceDetailId, x.Address, x.UpdatedAt })
                    .ToListAsync(cancellationToken);
                esStopwatch.Stop();
                _logger.LogInformation(
                    $"------DB--------- Time to Get Place DB total: {response.Documents.Count} Collection Place to es---------------: {esStopwatch.Elapsed.TotalSeconds} seconds");
                //Set address and lastupdateAt from placesDb to response
                esStopwatch.Restart();
                foreach (var item in response.Documents)
                {
                    var placeDb = placesDb.FirstOrDefault(x => x.PlaceId == item.PlaceId);
                    if (placeDb != null)
                    {
                        item.Address = placeDb.Address;
                        item.UpdatedAt = placeDb.UpdatedAt;
                    }
                }

                var resp = await _elasticClient.BulkAsync(b => b
                        .IndexMany(response.Documents)
                        .Index("place-dev")
                    , cancellationToken);
                esStopwatch.Stop();
                _logger.LogInformation(
                    $"------ES--------- Time to Update Place ES total: {response.Documents.Count} Collection Place to es---------------: {esStopwatch.Elapsed.TotalSeconds} seconds");
                count = count + response.Documents.Count;
                lstPlaceIdSync.AddRange(placeIds);
                if (response.Documents.Count > 0)
                {
                    searchAfter = response.Hits.Last().Sort.ToList();
                }
            }

            return lstPlaceIdSync;
        });
    }

    public async Task<bool> RemoveListDuplicate(List<string> lstAddress, CancellationToken cancellationToken = default)
    {
        var total = lstAddress.Count;
        int i = 1;
        foreach (var item in lstAddress)
        {
            var response = await _elasticClient.UpdateByQueryAsync<PlacesEs>(u => u
                .Indices(_indexName)
                .Query(q => q
                    .Term(t => { t.Field(p => p.FullAddress.Suffix("keyword")).Value(FieldValue.String(item)); })
                )
                .Script(new Script { Source = "ctx._source.scanProcessed = 5" }), cancellationToken);


            if (response.TryGetOriginalException(out var ex))
                throw new Exception(ex?.Message);
            _logger.LogInformation($"Processed {i++}/{total}");
        }

        return true;
    }

    public async Task<bool> InsertPlacesScanResult(int districtId, CancellationToken cancellationToken = default)
    {
        return await _dbContextResolver.ResolveAsync(async (dbContext) =>
        {
            var stopwatch = Stopwatch.StartNew();

            // Get relevant job IDs
            var placeQueue =
                await dbContext.PlacesQueues.FirstOrDefaultAsync(x => x.Districtid == districtId, cancellationToken);
            if (placeQueue == null)
            {
                _logger.LogWarning($"No PlaceQueue found for districtId: {districtId}");
                return false;
            }

            var esQueue = await dbContext.EsQueues
                .Where(x => x.PlacesQueueId == placeQueue.Id)
                .OrderBy(x => x.OrderNum)
                .ToListAsync(cancellationToken);
            if (esQueue.Count == 0)
            {
                _logger.LogWarning($"No esQueue found for districtId: {districtId}");
                return false;
            }

            var hangfireJobIds = esQueue.Select(x => x.HangfireJobId).ToList();
            _logger.LogInformation("Total hangfireJobIds: " + hangfireJobIds.Count);

            // Get all distinct PlaceIds from scan histories
            var allPlaceIds = await dbContext.PlacesScanHistories
                .Where(x => hangfireJobIds.Contains(x.HangfireJobId))
                .Select(x => x.Placeid)
                .Distinct()
                .ToListAsync(cancellationToken);

            _logger.LogInformation($"Found {allPlaceIds.Count} distinct places to process");
            // Set timeout for long-running operations
            dbContext.Database.SetCommandTimeout(300); // 5 minutes

            // Process in batches
            const int batchSize = 10000;
            long lastProcessedId = 0;
            int totalProcessed = 0;
            int totalResults = 0;

            for (int i = 0; i < allPlaceIds.Count; i += batchSize)
            {
                var batchStopwatch = Stopwatch.StartNew();

                // Get current batch of PlaceIds
                var batchPlaceIds = allPlaceIds
                    .Skip(i)
                    .Take(batchSize)
                    .ToList();

                // Get all scan histories for these PlaceIds
                var scanHistoriesBatch = await dbContext.PlacesScanHistories
                    .Where(x => hangfireJobIds.Contains(x.HangfireJobId) && batchPlaceIds.Contains(x.Placeid))
                    .ToListAsync(cancellationToken);

                // Group and process
                var resultsBatch = scanHistoriesBatch
                    .GroupBy(psh => psh.Placeid)
                    .Select(g =>
                    {
                        var maxScoreRecord = g.OrderByDescending(x => x.CompareScore).First();
                        return new PlacesScanResult
                        {
                            Placeid = g.Key,
                            SourcePlaceid = maxScoreRecord.SourcePlaceid,
                            HangfireJobId = maxScoreRecord.HangfireJobId,
                            CompareScoreMax = (decimal)maxScoreRecord.CompareScore,
                            TotalScans = g.Count()
                        };
                    })
                    .ToList();

                // Save this batch
                var listPlaceId = resultsBatch.Select(x => x.Placeid).ToList();
                var listPlaceScanResult = await dbContext.PlacesScanResults.Where(x => listPlaceId.Contains(x.Placeid))
                    .ToListAsync(cancellationToken);

                // Tạo lookup từ listPlaceScanResult để tìm kiếm nhanh hơn
                var placeScanLookup = listPlaceScanResult.ToDictionary(x => x.Placeid);

                // Phân loại resultsBatch thành các bản ghi cần cập nhật và các bản ghi cần chèn mới
                var updateItems = resultsBatch
                    .Where(item => placeScanLookup.ContainsKey(item.Placeid) &&
                                   placeScanLookup[item.Placeid].CompareScoreMax <= item.CompareScoreMax)
                    .ToList();

                var insertItems = resultsBatch
                    .Where(item => !placeScanLookup.ContainsKey(item.Placeid))
                    .ToList();

                // Cập nhật các bản ghi hiện có thỏa mãn điều kiện
                foreach (var item in updateItems)
                {
                    var placeScan = placeScanLookup[item.Placeid];
                    placeScan.SourcePlaceid = item.SourcePlaceid;
                    placeScan.HangfireJobId = item.HangfireJobId;
                    placeScan.CompareScoreMax = item.CompareScoreMax;
                    placeScan.TotalScans = item.TotalScans;
                }

                // Thêm các bản ghi mới chưa tồn tại
                if (insertItems.Count != 0)
                {
                    await dbContext.PlacesScanResults.AddRangeAsync(insertItems, cancellationToken);
                }

                // Lưu tất cả thay đổi vào database
                await dbContext.SaveChangesAsync(cancellationToken);
                batchStopwatch.Stop();
                totalProcessed += resultsBatch.Count;

                _logger.LogInformation(
                    $"Batch {i / batchSize + 1}/{(allPlaceIds.Count + batchSize - 1) / batchSize}: " +
                    $"Processed {resultsBatch.Count} results in {batchStopwatch.Elapsed.TotalSeconds:F2}s");

                // Clear change tracker to prevent memory growth
                dbContext.ChangeTracker.Clear();
            }

            stopwatch.Stop();
            _logger.LogInformation(
                $"InsertPlacesScanResult completed: Processed {totalProcessed} histories, inserted/updated {totalResults} results in {stopwatch.Elapsed.TotalSeconds:F2}s");

            return true;
        });
    }


    public async Task<PlacesEs> GetPlace(string placeId, CancellationToken cancellationToken = default)
    {
        var response = await _elasticClient.SearchAsync<PlacesEs>(selector => selector
                .Index(_indexName)
                .Query(q => q.Term(b => b
                    .Field(p => p.PlaceId)
                    .Value(placeId))
                )
            , cancellationToken);

        if (!response.IsSuccess())
            throw new Exception(response.DebugInformation);

        return response.Documents.FirstOrDefault();
    }

    public async Task<bool> UpdateLocationPlaceApproved(string placeId, bool isAll,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Start Update Location Place Approved, is all: " + isAll);
        const int batchSize = 5000;

        return await _dbContextResolver.ResolveAsync(async (dbContext) =>
        {
            try
            {
                IQueryable<PlaceApprove> baseQuery = dbContext.PlaceApproves;

                // Lọc theo placeId nếu có
                if (!string.IsNullOrEmpty(placeId))
                {
                    baseQuery = baseQuery.Where(p => p.PlaceId == placeId);
                }
                else if (!isAll)
                {
                    _logger.LogInformation("No update performed: placeId is empty and isAll is false");
                    return true;
                }

                // Đếm tổng số bản ghi cần xử lý
                int totalRecords = await baseQuery.CountAsync(cancellationToken);
                _logger.LogInformation($"Total records to process: {totalRecords}");

                // Xử lý theo batch
                int processedRecords = 0;
                int totalUpdatedRecords = 0;
                int batchNumber = 0;

                while (processedRecords < totalRecords)
                {
                    batchNumber++;
                    _logger.LogInformation(
                        $"Processing batch #{batchNumber}, records {processedRecords} to {Math.Min(processedRecords + batchSize, totalRecords)}");

                    // Lấy batch hiện tại
                    var currentBatch = await baseQuery
                        .Skip(processedRecords)
                        .Take(batchSize)
                        .ToListAsync(cancellationToken);

                    int batchUpdatedCount = 0;

                    // Xử lý từng bản ghi trong batch
                    foreach (var item in currentBatch)
                    {
                        if (item.Location.X < item.Location.Y)
                        {
                            var temp = item.Location.X;
                            item.Location.X = item.Location.Y;
                            item.Location.Y = temp;
                            batchUpdatedCount++;
                        }
                    }

                    // Lưu thay đổi của batch hiện tại
                    if (batchUpdatedCount > 0)
                    {
                        _logger.LogInformation($"Saving {batchUpdatedCount} updated records in batch #{batchNumber}");
                        await dbContext.SaveChangesAsync(cancellationToken);
                        totalUpdatedRecords += batchUpdatedCount;
                    }
                    else
                    {
                        _logger.LogInformation($"No records needed updating in batch #{batchNumber}");
                    }

                    processedRecords += currentBatch.Count;
                    _logger.LogInformation(
                        $"Completed {processedRecords}/{totalRecords} records ({(processedRecords * 100.0 / totalRecords):F2}%)");
                }

                _logger.LogInformation(
                    $"Update completed. Total records updated: {totalUpdatedRecords}/{totalRecords}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating location data");
                throw;
            }
        });
    }


    public async Task<List<PlacesEs>> GetPlacesByDistrictId(int districtId, bool processed, bool removed,
        CancellationToken cancellationToken = default)
    {
        var allDocuments = new List<PlacesEs>();
        List<FieldValue>? searchAfter = null;
        int countItems = 0;
        while (true)
        {
            var response = await _elasticClient.SearchAsync<PlacesEs>(selector => selector
                    .Index(_indexName)
                    .Size(1000) // Fetch in batches of 1000
                    .Query(q => q
                        .Bool(b => b
                            .Filter(f => f
                                .Bool(bb => bb
                                    .Must(
                                        m => m.Term(t =>
                                            t.Field(p => p.DistrictId).Value(districtId)), // Filter by DistrictId
                                        m => m.Term(t =>
                                            t.Field(p => p.Processed).Value(processed)), // Filter by Processed flag
                                        m => m.Term(t =>
                                            t.Field(p => p.Removed).Value(removed)) // Filter by Removed flag
                                    )
                                    .MustNot(m =>
                                            m.Term(t => t.Field(p => p.ScanStatus)
                                                .Value(1)) // Filter by Removed flag)
                                    )
                                )
                            )
                        )
                    )
                    .Sort(d => d
                            .Field(new Field("id"), s => s.Order(SortOrder.Asc)) // Sorting by 'id' in ascending order
                    )
                    .SearchAfter(searchAfter) // Use search_after for pagination
                , cancellationToken);


            if (!response.IsValidResponse || response.Documents.Count == 0)
                break;

            allDocuments.AddRange(response.Documents);
            countItems += response.Documents.Count;
            _logger.LogInformation("Get {0} places from ES , Total: {1}", response.Documents.Count, countItems);
            // Update searchAfter for the next batch
            searchAfter = response.Hits.Last().Sort.ToList();
        }

        return allDocuments;
    }

    public async Task<VerifyProcessedPlace> GetDistance(string placeIdSource, string placeIdDestination,
        CancellationToken cancellationToken = default)
    {
        var verifyProcessedPlace = new VerifyProcessedPlace();

        // Fetch source and destination places
        var source = await GetPlace(placeIdSource, cancellationToken);
        var destination = await GetPlace(placeIdDestination, cancellationToken);

        var srcLat = source.Location.Lat;
        var srcLon = source.Location.Lon;
        var destLat = destination.Location.Lat;
        var destLon = destination.Location.Lon;

        var distance = GeoHelper.CalculateDistance(srcLat, srcLon, destLat, destLon);
        verifyProcessedPlace.Distance = distance;

        // Calculate similarity score
        if (!string.IsNullOrEmpty(source.Address) && !string.IsNullOrEmpty(destination.Address))
        {
            verifyProcessedPlace.Score = source.Address.CalculateSimilarity(destination.Address);
        }

        return verifyProcessedPlace;
    }

    private static double DegreesToRadians(double deg) => deg * (Math.PI / 180);


    public async Task<List<ResultVerifyProcessedPlace>> CompareVerify(CancellationToken cancellationToken = default)
    {
        return await _dbContextResolver.ResolveAsync(async (dbContext) =>
        {
            var result = await dbContext.PlacesScanResults
                .AsNoTracking()
                .Select(x => new ResultVerifyProcessedPlace
                {
                    SourcePlaceId = x.SourcePlaceid,
                    CompareScoreMax = x.CompareScoreMax,
                    TotalScans = x.TotalScans,
                    HangfireJobId = x.HangfireJobId
                })
                .ToListAsync(cancellationToken: cancellationToken);
            foreach (var item in result)
            {
                var sourcePlace = await GetPlace(item.SourcePlaceId, cancellationToken);
                item.SourcePlaceAddress = sourcePlace.Address;
                if (item.CompareScoreMax < 40)
                {
                    continue;
                }
            }

            return result;
        });
    }

    public async Task UpsertDocumentAsync<T>(T source, CancellationToken cancellationToken, string esIndex, string? pipeline = null)
        where T : class
    {
        var indexResponse = await _elasticClient.IndexAsync(source, idx =>
        {
            idx.Index(esIndex)
                .Refresh(Refresh.True);
            if (!string.IsNullOrWhiteSpace(pipeline))
            {
                idx.Pipeline(pipeline);
            }
        }, cancellationToken);

        if (!indexResponse.IsValidResponse)
        {
            _logger.LogError($"Unable to index document: {indexResponse.DebugInformation}");
            throw new Exception($"Unable to index document: {indexResponse.DebugInformation}");
        }
    }

    public async Task DeleteDocumentAsync<T>(T source, CancellationToken cancellationToken, string esIndex)
        where T : class
    {
        var documentId = _elasticClient.Infer.Id(source);
        //_logger.LogInformation($"[DEBUG] Delete {esIndex} by ID = {documentId}");
        var deleteRequest = new DeleteRequest(esIndex, documentId)
        {
            Refresh = Refresh.True,
            ErrorTrace = true
        };
        var response = await _elasticClient.DeleteAsync(deleteRequest, CancellationToken.None);

        if (!response.IsValidResponse)
        {
            _logger.LogError($"Unable to delete document: {response.DebugInformation}");
            throw new Exception($"Unable to delete document: {response.DebugInformation}");
        }
    }

    private void BuildScoreQuery(QueryDescriptor<PlacesEs> selector, PlaceEsSearchRq searchRequest)
    {
        var location = GeoLocation.LatitudeLongitude(new LatLonGeoLocation
        { Lat = searchRequest.Lat, Lon = searchRequest.Lon });

        //return _searchConfigService.ScoreQuery(selector, searchRequest.Keyword, location);
        selector.Bool(d => d.Must(m =>
        {
            m.Terms(d => d.Field(f => f.Active)
                .Terms(new TermsQueryField(new[] { FieldValue.Boolean(true) })));
        }));
    }
}