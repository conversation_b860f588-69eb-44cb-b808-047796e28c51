using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;
using NpgsqlTypes;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Table("sub_places")]
[Index("BookingCount", Name = "IX_sub_places_bookingCount")]
[Index("BookingCount", "QuoteCount", Name = "IX_sub_places_bookingCount_quoteCount")]
[Index("City", Name = "IX_sub_places_city")]
[Index("DistrictId", Name = "IX_sub_places_districtId")]
[Index("FullAddressHash", Name = "IX_sub_places_fullAddressHash")]
[Index("Hidden", Name = "IX_sub_places_hidden")]
[Index("LocationHash", Name = "IX_sub_places_locationHash")]
[Index("LocationHash2", Name = "IX_sub_places_locationHash2")]
[Index("LocationHash3", Name = "IX_sub_places_locationHash3")]
[Index("ParentId", Name = "IX_sub_places_parentId")]
[Index("Pending", Name = "IX_sub_places_pending")]
[Index("PlaceDetailId", Name = "IX_sub_places_placeDetailId")]
[Index("PlaceId", Name = "IX_sub_places_placeId", IsUnique = true)]
[Index("Popular", Name = "IX_sub_places_popular")]
[Index("Popular", "Processed", "Removed", "Hidden", Name = "IX_sub_places_popular_processed_removed_hidden")]
[Index("Processed", Name = "IX_sub_places_processed")]
[Index("Processed", "Pending", Name = "IX_sub_places_processed_pending")]
[Index("ProvinceId", Name = "IX_sub_places_provinceId")]
[Index("QuoteCount", Name = "IX_sub_places_quoteCount")]
[Index("Removed", Name = "IX_sub_places_removed")]
[Index("Removed", "Processed", "Pending", Name = "IX_sub_places_removed_processed_pending")]
[Index("Removed", "Processed", "Pending", "BookingCount", "QuoteCount", Name = "IX_sub_places_removed_processed_pending_bookingCount_quoteCount")]
[Index("WardId", Name = "IX_sub_places_wardId")]
public partial class SubPlace
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("parentId")]
    public long? ParentId { get; set; }

    [Column("placeDetailId")]
    [StringLength(128)]
    public string? PlaceDetailId { get; set; }

    [Column("phoneNumber")]
    [StringLength(128)]
    public string? PhoneNumber { get; set; }

    [Column("placeId")]
    [StringLength(500)]
    public string PlaceId { get; set; } = null!;

    [Column("location", TypeName = "geometry(Point,4326)")]
    public Point? Location { get; set; }

    [Column("name")]
    [StringLength(500)]
    public string? Name { get; set; }

    [Column("address")]
    [StringLength(1000)]
    public string? Address { get; set; }

    [Column("keywords")]
    [StringLength(1000)]
    public string? Keywords { get; set; }

    [Column("fullAddress")]
    [StringLength(10000)]
    public string FullAddress { get; set; } = null!;

    [Column("streetNumber")]
    [StringLength(200)]
    public string? StreetNumber { get; set; }

    [Column("route")]
    [StringLength(200)]
    public string? Route { get; set; }

    [Column("ward")]
    [StringLength(200)]
    public string? Ward { get; set; }

    [Column("district")]
    [StringLength(200)]
    public string? District { get; set; }

    [Column("city")]
    [StringLength(200)]
    public string? City { get; set; }

    [Column("type")]
    public int Type { get; set; }

    [Column("active")]
    public bool Active { get; set; }

    [Column("tags")]
    public List<string>? Tags { get; set; }

    [Column("tagInput")]
    public string? TagInput { get; set; }

    [Column("popular")]
    public bool Popular { get; set; }

    [Column("processed")]
    public bool Processed { get; set; }

    [Column("bookingCount")]
    public int BookingCount { get; set; }

    [Column("removed")]
    public bool Removed { get; set; }

    [Column("hidden")]
    public bool Hidden { get; set; }

    [Column("createdAt", TypeName = "timestamp without time zone")]
    public DateTime CreatedAt { get; set; }

    [Column("updatedAt", TypeName = "timestamp without time zone")]
    public DateTime UpdatedAt { get; set; }

    [Column("creatorId")]
    [StringLength(255)]
    public string? CreatorId { get; set; }

    [Column("lastUpdateUser")]
    [StringLength(255)]
    public string? LastUpdateUser { get; set; }

    [Column("dataFrom")]
    [StringLength(100)]
    public string? DataFrom { get; set; }

    [Column("ts")]
    public NpgsqlTsVector? Ts { get; set; }

    [Column("locationHash")]
    [StringLength(64)]
    public string? LocationHash { get; set; }

    [Column("locationHash2")]
    [StringLength(64)]
    public string? LocationHash2 { get; set; }

    [Column("locationHash3")]
    [StringLength(64)]
    public string? LocationHash3 { get; set; }

    [Column("parentPlaceId")]
    [StringLength(500)]
    public string? ParentPlaceId { get; set; }

    [Column("fullAddressHash")]
    [StringLength(64)]
    public string? FullAddressHash { get; set; }

    [Column("ts2")]
    public NpgsqlTsVector? Ts2 { get; set; }

    [Column("districtId")]
    public int? DistrictId { get; set; }

    [Column("provinceId")]
    public int? ProvinceId { get; set; }

    [Column("wardId")]
    public int? WardId { get; set; }

    [Column("pending")]
    public bool Pending { get; set; }

    [Column("quoteCount")]
    public int QuoteCount { get; set; }

    [ForeignKey("ParentId")]
    [InverseProperty("SubPlaces")]
    public virtual Place? Parent { get; set; }
}
