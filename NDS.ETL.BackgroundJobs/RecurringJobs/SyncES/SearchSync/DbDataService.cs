using MongoDB.Driver;
using NDS.ETL.Entities.TaxiMongoDB;
using MongoDB.Bson;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.SearchSync;

/// <summary>
/// Dịch vụ truy xuất dữ liệu lịch sử tìm kiếm từ MongoDB
/// </summary>
public class DbDataService
{
    private readonly IMongoCollection<UserSearchHistory> _userSearchHistoryCollection;
    private readonly ILogger<SyncUserSearchJob> _logger;

    public DbDataService(IMongoCollection<UserSearchHistory> userSearchHistoryCollection, ILogger<SyncUserSearchJob> logger)
    {
        _userSearchHistoryCollection = userSearchHistoryCollection;
        _logger = logger;
    }

    /// <summary>
    /// Lấy danh sách lịch sử tìm kiếm theo paging động (CreatedAt, Id)
    /// </summary>
    public async Task<List<UserSearchHistory>> GetUpdatedSearchHistoriesAsync(DateTime fromDate, DateTime toDate, int batchSize, DateTime? lastCreatedAt = null, string? lastId = null)
    {
        try
        {
            var builder = Builders<UserSearchHistory>.Filter;
            var filter = builder.And(
                builder.Gt(x => x.CreatedAt, fromDate),
                builder.Lt(x => x.CreatedAt, toDate)
            );
            if (lastCreatedAt.HasValue && !string.IsNullOrEmpty(lastId))
            {
                // Nếu có lastCreatedAt và lastId, chỉ lấy các bản ghi lớn hơn batch trước
                var objectId = MongoDB.Bson.ObjectId.Parse(lastId);
                var pagingFilter = builder.Or(
                    builder.Gt(x => x.CreatedAt, lastCreatedAt.Value),
                    builder.And(
                        builder.Eq(x => x.CreatedAt, lastCreatedAt.Value),
                        builder.Gt(x => x.Id, objectId)
                    )
                );
                filter = builder.And(filter, pagingFilter);
            }
            var sort = Builders<UserSearchHistory>.Sort.Ascending(x => x.CreatedAt).Ascending(x => x.Id);
            var searchHistories = await _userSearchHistoryCollection
                .Find(filter)
                .Sort(sort)
                .Limit(batchSize)
                .ToListAsync();
            _logger.LogInformation("Retrieved {Count} search histories from MongoDB between {FromDate} and {ToDate} (paging)", 
                searchHistories.Count, fromDate, toDate);
            return searchHistories;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving search histories from MongoDB between {FromDate} and {ToDate} (paging)", fromDate, toDate);
            throw;
        }
    }

    /// <summary>
    /// Lấy tổng số lượng lịch sử tìm kiếm
    /// </summary>
    public async Task<long> GetTotalCountAsync()
    {
        try
        {
            return await _userSearchHistoryCollection.CountDocumentsAsync(FilterDefinition<UserSearchHistory>.Empty);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting total count of search histories");
            throw;
        }
    }

    /// <summary>
    /// Lấy lịch sử tìm kiếm theo ID
    /// </summary>
    public async Task<UserSearchHistory?> GetByIdAsync(string id)
    {
        try
        {
            var objectId = MongoDB.Bson.ObjectId.Parse(id);
            var filter = Builders<UserSearchHistory>.Filter.Eq(x => x.Id, objectId);
            return await _userSearchHistoryCollection.Find(filter).FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving search history with ID {Id}", id);
            throw;
        }
    }

    /// <summary>
    /// Lấy danh sách lịch sử tìm kiếm theo danh sách ID
    /// </summary>
    public async Task<List<UserSearchHistory>> GetByIdsAsync(List<string> ids)
    {
        try
        {
            var objectIds = ids.Select(MongoDB.Bson.ObjectId.Parse).ToList();
            var filter = Builders<UserSearchHistory>.Filter.In(x => x.Id, objectIds);
            var searchHistories = await _userSearchHistoryCollection.Find(filter).ToListAsync();

            _logger.LogInformation("Retrieved {Count} search histories by IDs", searchHistories.Count);
            return searchHistories;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving search histories by IDs");
            throw;
        }
    }

    /// <summary>
    /// Lấy lịch sử tìm kiếm mới nhất theo thời gian cập nhật
    /// </summary>
    public async Task<UserSearchHistory?> GetLatestUpdatedAsync()
    {
        try
        {
            var sort = Builders<UserSearchHistory>.Sort.Descending(x => x.CreatedAt);
            return await _userSearchHistoryCollection.Find(FilterDefinition<UserSearchHistory>.Empty)
                .Sort(sort)
                .FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting latest updated search history");
            throw;
        }
    }

    /// <summary>
    /// Lấy lịch sử tìm kiếm có CreatedAt nhỏ nhất
    /// </summary>
    public async Task<UserSearchHistory?> GetEarliestCreatedAsync()
    {
        try
        {
            var sort = Builders<UserSearchHistory>.Sort.Ascending(x => x.CreatedAt);
            return await _userSearchHistoryCollection.Find(FilterDefinition<UserSearchHistory>.Empty)
                .Sort(sort)
                .FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting earliest created search history");
            throw;
        }
    }
}
