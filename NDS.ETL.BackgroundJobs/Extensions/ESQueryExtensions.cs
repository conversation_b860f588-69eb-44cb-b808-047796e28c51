using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.QueryDsl;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;

namespace NDS.ETL.BackgroundJobs.Extensions
{
    /// <summary>
    /// <PERSON><PERSON><PERSON> phương thức mở rộng cho việc tìm kiếm trên ES
    /// </summary>
    public static class ESQueryExtensions
    {
        /// <summary>
        /// Tạo query tìm kiếm theo userId hoặc phoneNumber
        /// </summary>
        public static void CreateUserIdentifierQuery<T>(
            this List<Action<QueryDescriptor<T>>> queries,
            string userIdField,
            string userId,
            string phoneNumberField,
            string phoneNumber)
            where T : class
        {
            void filterConfig(QueryDescriptor<T> fq)
            {
                if (!string.IsNullOrWhiteSpace(userId))
                {
                    // Thay thế Term bằng Match cho CustomerId
                    fq.Match(m => m
                        .Field(userIdField)
                        .Query(userId)
                        .Operator(Operator.And));
                }
                else if (!string.IsNullOrWhiteSpace(phoneNumber))
                {
                    // Thay thế Term bằng Match cho CustomerPhone
                    fq.Match(m => m
                        .Field(phoneNumberField)
                        .Query(phoneNumber)
                        .Operator(Operator.And));
                }
            }
            queries.Add(q => q
                .Bool(b => b
                    .Should(filterConfig)
                    .MinimumShouldMatch(1)
                )
            );
        }

        /// <summary>
        /// Tạo query tìm kiếm theo khoảng thời gian
        /// </summary>
        public static void CreateDateRangeQuery<T>(
            this List<Action<QueryDescriptor<T>>> queries,
            string fieldName,
            DateTime? fromDate = null,
            DateTime? toDate = null)
            where T : class
        {
            queries.Add(q => q.Range(d =>
                {
                    d.DateRange(dr =>
                    {
                        dr.Field(fieldName);

                        if (fromDate.HasValue)
                        {
                            dr.Gte(fromDate.Value);
                        }

                        if (toDate.HasValue)
                        {
                            dr.Lte(toDate.Value);
                        }
                    });
                })
            );
        }

        /// <summary>
        /// Tạo query tìm kiếm theo danh sách ID
        /// </summary>
        public static void CreateTermsQuery<T>(
            this List<Action<QueryDescriptor<T>>> queries,
            string fieldName,
            List<string> values)
            where T : class
        {
            queries.Add(q => q
                .Terms(t => t
                    .Field(fieldName)
                    .Terms(new TermsQueryField(values.Select(FieldValue.String).ToArray()))
                )
            );
        }

        /// <summary>
        /// Tạo query tìm kiếm theo khoảng cách địa lý
        /// </summary>
        public static void CreateGeoDistanceQuery<T>(
            this List<Action<QueryDescriptor<T>>> queries,
            double latitude,
            double longitude,
            double distanceMeters)
            where T : IElasticSearchModel
        {
            var currentLocation = new LatLonGeoLocation { Lat = latitude, Lon = longitude };
            var h3CellsInRadius = H3Helper.GetH3CellsInRadiusInMeters(latitude, longitude, distanceMeters, out var res);
            queries.Add(mu => mu.Terms(t => t
                .Field($"h3.{res}.keyword")
                .Terms(new TermsQueryField(h3CellsInRadius.Select(h3Idx => (FieldValue)h3Idx).ToArray()))
            ));
            queries.Add(mu =>
            {
                mu.GeoDistance(g => g
                    .Field(p => p.Location)
                    .Distance($"{distanceMeters}m")
                    .Location(currentLocation)
                );
            });
        }

        /// <summary>
        /// Tạo query tìm kiếm theo khoảng cách địa lý tối thiểu
        /// </summary>
        public static void CreateMinGeoDistanceQuery<T>(
            this List<Action<QueryDescriptor<T>>> queries,
            double latitude,
            double longitude,
            double minDistanceMeters)
            where T : IElasticSearchModel
        {
            var currentLocation = new LatLonGeoLocation { Lat = latitude, Lon = longitude };
            
            // Sử dụng Bool query với must_not để loại bỏ các document trong phạm vi khoảng cách tối thiểu
            queries.Add(mu => mu.Bool(b => b
                .MustNot(mn => mn.GeoDistance(g => g
                    .Field(p => p.Location)
                    .Distance($"{minDistanceMeters}m")
                    .Location(currentLocation)
                ))
            ));
        }
    }
} 