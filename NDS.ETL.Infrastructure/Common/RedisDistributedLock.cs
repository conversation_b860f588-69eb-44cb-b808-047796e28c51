using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System;
using System.Threading.Tasks;

namespace NDS.ETL.Infrastructure.Common
{
    /// <summary>
    /// Triển khai khóa phân tán sử dụng Redis
    /// </summary>
    public class RedisDistributedLock : IDistributedLock
    {
        private readonly ILogger<RedisDistributedLock> _logger;
        private readonly IConnectionMultiplexer _redis;
        private readonly string _instanceId;
        private const string LOCK_PREFIX = "lock:";
        private const string LOCK_CREATED_SUFFIX = ":created";

        public RedisDistributedLock(
            IConfiguration configuration,
            ILogger<RedisDistributedLock> logger,
            IConnectionMultiplexer redis)
        {
            _logger = logger;
            _redis = redis;
            // Tạo ID duy nhất cho instance này để xác định owner của khóa
            _instanceId = $"{Environment.MachineName}:{Guid.NewGuid():N}";
        }

        /// <summary>
        /// Cố gắng lấy khóa Redis với thời gian hết hạn
        /// </summary>
        public async Task<bool> TryAcquireLockAsync(string key, TimeSpan expiry)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentNullException(nameof(key));

            var db = _redis.GetDatabase();
            var lockKey = GetFullLockKey(key);
            var createdKey = GetCreatedTimeKey(key);
            
            try
            {
                // Sử dụng SET NX (SET if Not eXists) với thời gian hết hạn
                bool acquired = await db.StringSetAsync(
                    lockKey,
                    _instanceId,
                    expiry,
                    When.NotExists);

                if (acquired)
                {
                    // Lưu thời gian tạo khóa
                    await db.StringSetAsync(
                        createdKey,
                        DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString(),
                        expiry);
                        
                    _logger.LogDebug($"Acquired lock '{key}' for instance {_instanceId}");
                }
                else
                {
                    _logger.LogDebug($"Failed to acquire lock '{key}', already held by another instance");
                }
                
                return acquired;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error acquiring Redis lock for key '{key}'");
                // Trong trường hợp lỗi, coi như không lấy được khóa để đảm bảo an toàn
                return false;
            }
        }

        /// <summary>
        /// Giải phóng khóa Redis, kiểm tra owner để đảm bảo chỉ instance hiện tại mới có thể giải phóng khóa
        /// </summary>
        public async Task ReleaseLockAsync(string key)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentNullException(nameof(key));

            var db = _redis.GetDatabase();
            var lockKey = GetFullLockKey(key);
            var createdKey = GetCreatedTimeKey(key);
            
            try
            {
                // Lấy giá trị hiện tại của khóa để kiểm tra xem instance này có phải owner không
                var value = await db.StringGetAsync(lockKey);
                
                if (!value.HasValue)
                {
                    _logger.LogDebug($"Lock '{key}' already released or expired");
                    return;
                }
                
                if (value == _instanceId)
                {
                    // Chỉ xóa khóa nếu chúng ta là owner
                    bool deleted = await db.KeyDeleteAsync(lockKey);
                    await db.KeyDeleteAsync(createdKey);
                    _logger.LogDebug($"Released lock '{key}' for instance {_instanceId}: {(deleted ? "success" : "failed")}");
                }
                else
                {
                    _logger.LogWarning($"Cannot release lock '{key}' as it is owned by another instance. Current: {value}, Ours: {_instanceId}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error releasing Redis lock for key '{key}'");
                throw;
            }
        }

        /// <summary>
        /// Giải phóng khóa bất kể owner là ai (sử dụng trong trường hợp khẩn cấp)
        /// </summary>
        public async Task ForceReleaseLockAsync(string key)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentNullException(nameof(key));

            var db = _redis.GetDatabase();
            var lockKey = GetFullLockKey(key);
            var createdKey = GetCreatedTimeKey(key);
            
            try
            {
                // Lấy thông tin về owner hiện tại để log
                var currentOwner = await db.StringGetAsync(lockKey);
                
                // Xóa khóa bất kể owner là ai
                var deleted = await db.KeyDeleteAsync(lockKey);
                await db.KeyDeleteAsync(createdKey);
                
                if (deleted)
                {
                    _logger.LogWarning($"Force released lock '{key}' owned by {currentOwner} (our instance: {_instanceId})");
                }
                else
                {
                    _logger.LogDebug($"Force release not needed for '{key}', lock does not exist");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error force releasing Redis lock for key '{key}'");
                throw;
            }
        }

        /// <summary>
        /// Lấy thông tin về khóa hiện tại
        /// </summary>
        public async Task<LockInfo> GetLockInfoAsync(string key)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentNullException(nameof(key));

            var db = _redis.GetDatabase();
            var lockKey = GetFullLockKey(key);
            var createdKey = GetCreatedTimeKey(key);
            
            try
            {
                // Lấy thông tin owner và thời gian tạo
                var ownerTask = db.StringGetAsync(lockKey);
                var createdTask = db.StringGetAsync(createdKey);
                var ttlTask = db.KeyTimeToLiveAsync(lockKey);
                
                await Task.WhenAll(ownerTask, createdTask, ttlTask);
                
                var owner = ownerTask.Result;
                var created = createdTask.Result;
                var ttl = ttlTask.Result;
                
                if (!owner.HasValue)
                {
                    return null; // Khóa không tồn tại
                }
                
                // Parse thời gian tạo từ Unix timestamp
                DateTime createdAt = DateTime.UtcNow;
                if (created.HasValue && long.TryParse(created.ToString(), out long createdTimestamp))
                {
                    createdAt = DateTimeOffset.FromUnixTimeMilliseconds(createdTimestamp).UtcDateTime;
                }
                
                // Tính thời gian hết hạn
                DateTime expiryTime = DateTime.UtcNow;
                if (ttl.HasValue)
                {
                    expiryTime = DateTime.UtcNow.Add(ttl.Value);
                }
                
                return new LockInfo
                {
                    OwnerId = owner.ToString(),
                    CreatedAt = createdAt,
                    ExpiryTime = expiryTime
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting Redis lock info for key '{key}'");
                return null;
            }
        }

        private string GetFullLockKey(string key) => $"{LOCK_PREFIX}{key}";
        
        private string GetCreatedTimeKey(string key) => $"{LOCK_PREFIX}{key}{LOCK_CREATED_SUFFIX}";
    }
} 