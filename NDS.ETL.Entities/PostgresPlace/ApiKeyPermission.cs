using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Index("ApiKeyId", Name = "IX_ApiKeyPermissions_ApiKeyId")]
[Index("PermissionName", Name = "IX_ApiKeyPermissions_PermissionName")]
public partial class ApiKeyPermission
{
    [Key]
    public Guid Id { get; set; }

    public int PermissionName { get; set; }

    public Guid ApiKeyId { get; set; }

    [ForeignKey("ApiKeyId")]
    [InverseProperty("ApiKeyPermissions")]
    public virtual ApiKey ApiKey { get; set; } = null!;
}
