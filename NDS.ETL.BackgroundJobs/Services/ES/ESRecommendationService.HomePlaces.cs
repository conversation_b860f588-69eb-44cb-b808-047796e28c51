using Elastic.Clients.Elasticsearch;
using NDS.ETL.BackgroundJobs.Extensions;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using System.Diagnostics;

namespace NDS.ETL.BackgroundJobs.Services.ES
{
    public partial class ESRecommendationService
    {
        #region Public API - Place Recommendations
        const double MinRadiusMeters = 1000; // B<PERSON> kính tối thiểu 1km cho gợi ý địa điểm

        /// <summary>
        /// Lấy địa điểm gợi ý cho người dùng - Tối ưu dựa trên logic Routes và Search
        /// </summary>
        public async Task<List<RecommendedPlace>> GetRecommendedPlacesAsync(string userId,
            string phoneNumber,
            double? latitude = null,
            double? longitude = null,
            int? h3Resolution = null,
            int limit = 3, // Giảm xuống 3 cho phù hợp với mobile
            int page = 1,
            string lang = "en")
        {
            var stopwatch = Stopwatch.StartNew();
            _logger.LogInformation("Getting {Limit} recommended places for user: {UserId}, phone: {PhoneNumber}",
                limit, userId, phoneNumber);

            // Timeout ngắn cho màn hình home (3 giây như Routes)
            const int taskTimeoutMs = 5000;

            try
            {
                using var mainCts = new CancellationTokenSource(taskTimeoutMs);
                var cancellationToken = mainCts.Token;

                var result = new List<RecommendedPlace>();
                var now = DateTime.UtcNow;

                // Chạy song song 3 bước chính để tăng tốc độ
                var parallelTasks = new List<Task<List<RecommendedPlace>>>();

                // Task 1: Location-based recommendations (ưu tiên cao nhất)
                if (latitude.HasValue && longitude.HasValue)
                {
                    parallelTasks.Add(GetLocationBasedRecommendationsAsync(
                        userId, phoneNumber, latitude.Value, longitude.Value,
                        h3Resolution, limit, lang, now, cancellationToken));
                }
                else
                {
                    // Fallback: History-based nếu không có tọa độ
                    parallelTasks.Add(GetHistoryBasedRecommendationsAsync(
                        userId, phoneNumber, limit, page, lang, cancellationToken));
                }

                // Task 2: Favorite places
                parallelTasks.Add(GetFavoriteRecommendationsAsync(
                    userId, phoneNumber, 3, latitude, longitude, cancellationToken));

                // Task 3: Recent search history
                parallelTasks.Add(GetRecentSearchRecommendationsAsync(
                    userId, phoneNumber, latitude, longitude, 3, cancellationToken));

                // Chờ tất cả tasks hoàn thành
                var parallelStopwatch = Stopwatch.StartNew();
                var results = await Task.WhenAll(parallelTasks).ConfigureAwait(false);
                parallelStopwatch.Stop();

                // Tổng hợp kết quả từ tất cả tasks
                var allRecommendations = results.SelectMany(r => r).ToList();

                _logger.LogInformation("Parallel execution completed in {ParallelMs}ms. " +
                    "Got {TotalCount} total recommendations from {TaskCount} tasks",
                    parallelStopwatch.ElapsedMilliseconds, allRecommendations.Count, results.Length);

                // Loại bỏ trùng lặp dựa trên PlaceId và giữ item có Seq cao nhất
                var uniqueRecommendations = allRecommendations
                    .GroupBy(r => r.PlaceId)
                    .Select(g => g.OrderByDescending(r => r.Seq).First())
                    .ToList();

                _logger.LogInformation("After deduplication: {UniqueCount} unique recommendations",
                    uniqueRecommendations.Count);

                // Nếu số lượng kết quả sau khi lọc trùng < 3 thì gọi thêm GetPopularDestinationsAsync để bổ sung
                if (uniqueRecommendations.Count < 3 && latitude.HasValue && longitude.HasValue)
                {
                    // Số lượng cần bổ sung
                    var additionalRoutesNeeded = 3 - uniqueRecommendations.Count;

                    // Lấy danh sách PlaceId đã có để tránh trùng lặp
                    var existingPlaceIds = new HashSet<string>(uniqueRecommendations.Select(x => x.PlaceId));

                    // Gọi hàm lấy các điểm đến phổ biến bổ sung
                    // Chú ý: taskTimeoutMs_Individual đặt là 2000ms cho mỗi lần gọi bổ sung
                    var popularPlaces = await GetPopularDestinationsAsync(
                        null, null, latitude.Value, longitude.Value,
                        h3Resolution, additionalRoutesNeeded, MinRadiusMeters, null, lang, 2000);

                    // Lọc bỏ các địa điểm đã có trong danh sách
                    var newPopularPlaces = popularPlaces
                        .Where(x => !existingPlaceIds.Contains(x.PlaceId))
                        .Select(x => new RecommendedPlace(x))
                        .ToList();

                    // Thêm vào danh sách kết quả
                    uniqueRecommendations.AddRange(newPopularPlaces);

                    _logger.LogInformation("Added {Count} popular destinations to fill up recommendations", newPopularPlaces.Count);
                }

                // Áp dụng phân trang và tính khoảng cách cuối cùng
                var finalResults = ApplyPaginationAndDistance(uniqueRecommendations, latitude, longitude, limit, page);

                _logger.LogInformation("Found {Count} recommended places in {ElapsedMs}ms",
                    finalResults.Count, stopwatch.ElapsedMilliseconds);

                return finalResults;
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("Recommendation operation timed out after {TimeoutMs}ms", taskTimeoutMs);
                return new List<RecommendedPlace>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recommended places: {Message}", ex.Message);
                return new List<RecommendedPlace>();
            }
        }



        /// <summary>
        /// Xử lý gợi ý dựa trên vị trí - Ưu tiên cao nhất, lấy 1 kết quả trước
        /// </summary>
        private async Task ProcessLocationBasedRecommendations(
            string userId, string phoneNumber, double latitude, double longitude,
            int? h3Resolution, int limit, string lang, DateTime now,
            HashSet<string> uniqueIds, List<RecommendedPlace> result,
            CancellationToken cancellationToken)
        {
            h3Resolution ??= H3Helper.GetH3ResolutionFromRadiusKm(3); // Giảm xuống 3km
            var initialCount = result.Count;

            // Bước 1: Return trip check (ưu tiên cao nhất - timeout 700ms)
            try
            {
                using var cts1 = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                cts1.CancelAfter(700);

                var returnTrip = await TryGetReturnTripAsync(userId, phoneNumber, latitude, longitude, now, lang)
                    .WaitAsync(cts1.Token).ConfigureAwait(false);

                if (returnTrip != null && uniqueIds.Add(returnTrip.PlaceId))
                {
                    returnTrip.Seq = 2000; // Seq cao nhất cho return trip
                    result.Add(returnTrip);
                    _logger.LogInformation("Added return trip suggestion");

                    // Nếu chỉ cần 1 kết quả và đã có return trip, return ngay
                    if (limit == 1) return;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Return trip check failed");
            }

            // Early return nếu đã đủ (cho cả limit = 1 và limit > 1)
            if (result.Count >= limit || (limit == 1 && result.Count > initialCount)) return;

            // Bước 2: Frequent routes theo thời gian (ưu tiên cao - timeout 800ms)
            try
            {
                using var cts2 = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                cts2.CancelAfter(800);

                var remainingCount = limit - result.Count;
                var maxRoutes = limit == 1 ? 1 : Math.Min(2, remainingCount);

                var frequentRoutes = await GetFrequentRoutesByTimeOfDayAsync(
                    userId, phoneNumber, now, maxRoutes, lang, latitude, longitude)
                    .WaitAsync(cts2.Token).ConfigureAwait(false);

                var addedCount = 0;
                foreach (var route in frequentRoutes)
                {
                    if (result.Count >= limit || addedCount >= maxRoutes) break;

                    var destination = RecommendedPlace.FromPlaceLocation(route.Destination);
                    if (uniqueIds.Add(destination.PlaceId))
                    {
                        destination.Seq = 1500 + (maxRoutes - addedCount); // Seq cao hơn popular places
                        result.Add(destination);
                        addedCount++;

                        // Nếu chỉ cần 1 kết quả, return ngay khi có
                        if (limit == 1) return;
                    }
                }
                _logger.LogInformation("Added {Count} frequent route destinations", addedCount);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Frequent routes check failed");
            }
        }



        /// <summary>
        /// Áp dụng phân trang và tính khoảng cách cuối cùng
        /// </summary>
        private List<RecommendedPlace> ApplyPaginationAndDistance(
            List<RecommendedPlace> result, double? latitude, double? longitude,
            int limit, int page)
        {
            // Tính khoảng cách nếu có tọa độ
            if (latitude.HasValue && longitude.HasValue)
            {
                foreach (var item in result)
                {
                    item.Distance = H3Helper.CalculateDistance(
                        latitude.Value, longitude.Value,
                        item.Location.Lat, item.Location.Lon) / 1000;
                }
            }
            

            var dateNow = DateTime.Now.Date;
            // Sắp xếp theo Seq (ưu tiên) và distance, áp dụng phân trang
            return result
                .OrderByDescending(x => x.LastSearchAt?.Date == dateNow) // Seq cao hơn = ưu tiên cao hơn
                .ThenByDescending(x => x.Seq) // Seq cao hơn = ưu tiên cao hơn
                .ThenBy(x => x.Distance ?? double.MaxValue)
                .Skip((page - 1) * limit)
                .Take(limit)
                .ToList();
        }

        #endregion

        #region Parallel Task Methods - Place Recommendations

        /// <summary>
        /// Lấy gợi ý dựa trên vị trí - chạy song song
        /// </summary>
        private async Task<List<RecommendedPlace>> GetLocationBasedRecommendationsAsync(
            string userId, string phoneNumber, double latitude, double longitude,
            int? h3Resolution, int limit, string lang, DateTime now,
            CancellationToken cancellationToken)
        {
            var result = new List<RecommendedPlace>();
            var uniqueIds = new HashSet<string>();

            try
            {
                // Timeout 2s cho location-based (quan trọng nhất)
                using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                cts.CancelAfter(2000);

                await ProcessLocationBasedRecommendations(
                    userId, phoneNumber, latitude, longitude,
                    h3Resolution, limit, lang, now, uniqueIds, result, cts.Token);

                return result;
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("Location-based recommendations timed out after 2s");
                return new List<RecommendedPlace>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in location-based recommendations: {Message}", ex.Message);
                return new List<RecommendedPlace>();
            }
        }

        /// <summary>
        /// Lấy địa điểm yêu thích - chạy song song
        /// </summary>
        private async Task<List<RecommendedPlace>> GetFavoriteRecommendationsAsync(
            string userId, string phoneNumber, int limit, double? latitude, double? longitude, CancellationToken cancellationToken)
        {
            try
            {
                // Timeout 800ms cho favorites (nhanh)
                using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                cts.CancelAfter(800);

                // Sử dụng khoảng cách tối thiểu để tránh gợi ý địa điểm quá gần
                var minDistance = latitude.HasValue && longitude.HasValue ? MinRadiusMeters : (double?)null;

                var favoritePlaces = await GetUserFavoritePlacesAsync(userId, phoneNumber, limit, 1, "en",
                    latitude, longitude, minDistance)
                    .WaitAsync(cts.Token).ConfigureAwait(false);

                var result = new List<RecommendedPlace>();
                var addedCount = 0;

                foreach (var place in favoritePlaces)
                {
                    if (addedCount >= limit) break;

                    place.Seq = 800 + (limit - addedCount); // Seq thấp hơn location-based
                    place.IsFavorite = true;
                    result.Add(place);
                    addedCount++;
                }

                _logger.LogInformation("Parallel: Added {Count} favorite places", addedCount);
                return result;
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("Favorite recommendations timed out after 800ms");
                return new List<RecommendedPlace>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in favorite recommendations: {Message}", ex.Message);
                return new List<RecommendedPlace>();
            }
        }

        /// <summary>
        /// Lấy lịch sử tìm kiếm gần đây - chạy song song
        /// </summary>
        private async Task<List<RecommendedPlace>> GetRecentSearchRecommendationsAsync(
            string userId, string phoneNumber, double? latitude, double? longitude,
            int limit, CancellationToken cancellationToken)
        {
            try
            {
                // Timeout 1s cho search history
                using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                cts.CancelAfter(1000);

                // Lấy lịch sử tìm kiếm 7 ngày gần đây
                var validSearchTime = DateTime.UtcNow.AddDays(-7);
                var (recentSearches, _) = await GetUserHistoryPlacesAsync(
                    userId, phoneNumber, validSearchTime, DateTime.UtcNow.AddDays(-30),
                    latitude, longitude, limit * 2, 0) // Lấy nhiều hơn để có lựa chọn
                    .WaitAsync(cts.Token).ConfigureAwait(false);

                var result = new List<RecommendedPlace>();
                var addedCount = 0;

                foreach (var place in recentSearches)
                {
                    if (addedCount >= limit) break;

                    var recommendedPlace = RecommendedPlace.FromPlacesEs(place);
                    recommendedPlace.Seq = 600 + (limit - addedCount); // Seq thấp hơn favorites
                    result.Add(recommendedPlace);
                    addedCount++;
                }

                _logger.LogInformation("Parallel: Added {Count} places from recent search history", addedCount);
                return result;
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("Recent search recommendations timed out after 1s");
                return new List<RecommendedPlace>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in recent search recommendations: {Message}", ex.Message);
                return new List<RecommendedPlace>();
            }
        }

        /// <summary>
        /// Lấy gợi ý dựa trên lịch sử khi không có tọa độ - chạy song song
        /// </summary>
        private async Task<List<RecommendedPlace>> GetHistoryBasedRecommendationsAsync(
            string userId, string phoneNumber, int limit, int page, string lang,
            CancellationToken cancellationToken)
        {
            try
            {
                // Timeout 1.5s cho history-based
                using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                cts.CancelAfter(1500);

                // Lấy lộ trình gần đây (không có vị trí nên không filter theo minimum distance)
                var recentRoutes = await GetUserRecentRoutesAsync(userId, phoneNumber, limit * 2, page, lang)
                    .WaitAsync(cts.Token).ConfigureAwait(false);

                var result = new List<RecommendedPlace>();
                var addedCount = 0;

                foreach (var route in recentRoutes.Take(limit))
                {
                    var destination = RecommendedPlace.FromPlaceLocation(route.Destination);
                    destination.Seq = 400 + addedCount; // Seq thấp hơn search history
                    result.Add(destination);
                    addedCount++;
                }

                _logger.LogInformation("Parallel: Added {Count} destinations from recent routes", addedCount);
                return result;
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("History-based recommendations timed out after 1.5s");
                return new List<RecommendedPlace>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in history-based recommendations: {Message}", ex.Message);
                return new List<RecommendedPlace>();
            }
        }

        #endregion

        #region Private Methods - Place Recommendations

        /// <summary>
        /// Lấy các địa điểm yêu thích của người dùng
        /// </summary>
        private async Task<List<RecommendedPlace>> GetUserFavoritePlacesAsync(
            string userId,
            string phoneNumber,
            int limit,
            int page,
            string lang = "en",
            double? latitude = null,
            double? longitude = null,
            double? minDistanceMeters = null)
        {
            var stopwatch = Stopwatch.StartNew();
            _logger.LogInformation("Querying favorite places for user: {UserId}, phone: {PhoneNumber}",
                userId, phoneNumber);

            try
            {
                // Tạo GeoLocation từ latitude/longitude nếu có
                GeoLocation? geoLocation = null;
                if (latitude.HasValue && longitude.HasValue)
                {
                    geoLocation = new LatLonGeoLocation { Lat = latitude.Value, Lon = longitude.Value };
                }

                // Tạo query tìm kiếm với ESFavPlacesExtensions
                var searchQuery = _elasticClient.CreateFavPlaceSearchQuery(
                    page: page,
                    userId: userId,
                    phoneNumber: phoneNumber,
                    currentLocation: geoLocation,
                    keyword: "", // Không lọc theo từ khóa
                    pageSize: limit,
                    indexName: _favoritePlacesIdxName,
                    minDistanceMeters: minDistanceMeters
                );

                // Thực hiện truy vấn
                var searchResponse = await _elasticClient.SearchAsync(searchQuery);

                if (!searchResponse.IsValidResponse)
                {
                    _logger.LogError("Error querying favorite places: {ErrorReason}",
                        searchResponse.ElasticsearchServerError?.Error?.Reason);
                    return new List<RecommendedPlace>();
                }

                // Chuyển đổi kết quả sang RecommendedPlace (filter đã được áp dụng trong query)
                var result = searchResponse.Documents
                    .Select(p => new RecommendedPlace
                    {
                        PlaceId = p.PlaceId ?? string.Empty,
                        Name = p.Name ?? string.Empty,
                        FullAddress = p.FullAddress ?? string.Empty,
                        Address = p.Address ?? string.Empty,
                        PhoneNumber = p.PhoneNumber ?? string.Empty,
                        Location = new LocationDto
                        {
                            Lat = p.Location?.Lat ?? 0,
                            Lon = p.Location?.Lon ?? 0
                        },
                        Active = p.Active,
                        Popular = p.BookingCount > 5 // Đánh dấu là phổ biến nếu đã đặt nhiều lần
                    })
                    .ToList();

                // Log thông tin về filter minimum distance nếu có
                if (minDistanceMeters.HasValue && minDistanceMeters.Value > 0)
                {
                    _logger.LogInformation("Applied minimum distance filter in query: {MinDistance}m. " +
                        "Found {Count} favorite places after filtering",
                        minDistanceMeters.Value, result.Count);
                }

                _logger.LogInformation("Found {Count} favorite places for user in {ElapsedMs}ms",
                    result.Count, stopwatch.ElapsedMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting favorite places: {Message}", ex.Message);
                return new List<RecommendedPlace>();
            }
        }

        /// <summary>
        /// Cố gắng xác định và gợi ý 1 điểm đến duy nhất cho chuyến về trong cùng ngày (tối ưu tốc độ)
        /// </summary>
        private async Task<RecommendedPlace?> TryGetReturnTripAsync(
            string userId,
            string phoneNumber,
            double currentLat,
            double currentLon,
            DateTime currentTime,
            string lang = "en")
        {
            var stopwatch = Stopwatch.StartNew();
            _logger.LogInformation("Starting return trip analysis for user: {UserId} at ({Lat}, {Lon})",
                userId, currentLat, currentLon);

            try
            {
                // Bước 1: Lấy tối đa 5 lộ trình gần nhất trong ngày (tối ưu tốc độ)
                var routeStopwatch = Stopwatch.StartNew();
                var todayRoutes = await _placePairsService.GetTodayRoutes(
                    userId,
                    phoneNumber,
                    currentTime.Date,
                    1,
                    new LatLonGeoLocation { Lat = currentLat, Lon = currentLon },
                    1000);

                // Lấy tối đa 5 routes gần nhất để tối ưu performance
                var limitedRoutes = todayRoutes.Take(5).ToList();
                routeStopwatch.Stop();

                _logger.LogInformation("Retrieved {RouteCount}/{TotalCount} today routes in {ElapsedMs}ms",
                    limitedRoutes.Count, todayRoutes.Count, routeStopwatch.ElapsedMilliseconds);

                if (limitedRoutes.Count == 0)
                {
                    _logger.LogInformation("No routes found for today, return trip analysis completed in {TotalMs}ms",
                        stopwatch.ElapsedMilliseconds);
                    return null; // Không có lộ trình nào trong ngày
                }
                // Người dùng đang ở điểm đi, gợi ý đi đến điểm đến
                var pairRoute = limitedRoutes.First();
                var placeItem = pairRoute.Arrival ?? pairRoute.Pickup;
                var note = IsVietnamese(lang) ? "Chuyến chiều về" : "Return trip";
                if (!string.IsNullOrWhiteSpace(pairRoute.Pickup.PlaceId))
                    note = IsVietnamese(lang) ? "Chuyến chiều đi" : "Outbound trip";
                return new RecommendedPlace
                {
                    PlaceId = placeItem.PlaceId,
                    Name = placeItem.Name,
                    Address = placeItem.Address,
                    FullAddress = placeItem.FullAddress,
                    Location = new LocationDto
                    {
                        Lat = placeItem.Location.Lat,
                        Lon = placeItem.Location.Lon
                    },
                    Active = true,
                    Popular = true,
                    Desc = note
                };

                //// Bước 2: Tính toán khoảng cách cho routes giới hạn
                //var distanceStopwatch = Stopwatch.StartNew();
                //var routesWithDistances = limitedRoutes
                //    .Where(r => r.Pickup?.Location != null && r.Arrival?.Location != null)
                //    .Select(r => new
                //    {
                //        Route = r,
                //        PickupDistance = H3Helper.CalculateDistance(
                //            currentLat,
                //            currentLon,
                //            r.Pickup?.Location?.Lat ?? 0,
                //            r.Pickup?.Location?.Lon ?? 0),
                //        ArrivalDistance = H3Helper.CalculateDistance(
                //            currentLat,
                //            currentLon,
                //            r.Arrival?.Location?.Lat ?? 0,
                //            r.Arrival?.Location?.Lon ?? 0)
                //    })
                //    .ToList();
                //distanceStopwatch.Stop();

                //_logger.LogInformation("Calculated distances for {ValidRoutes} valid routes in {ElapsedMs}ms", 
                //    routesWithDistances.Count, distanceStopwatch.ElapsedMilliseconds);

                //// Không có lộ trình nào có đủ thông tin
                //if (!routesWithDistances.Any())
                //{
                //    _logger.LogWarning("No valid routes with location data found, completed in {TotalMs}ms", 
                //        stopwatch.ElapsedMilliseconds);
                //    return null;
                //}

                //// Bước 3: Tìm 1 lộ trình gần nhất mà người dùng đang ở gần điểm đến (tối ưu tốc độ)
                //var arrivalAnalysisStopwatch = Stopwatch.StartNew();
                //var nearArrivalRoute = routesWithDistances
                //    .Where(r => r.ArrivalDistance < 300) // Người dùng đang gần điểm đến
                //    .OrderBy(r => r.ArrivalDistance) // Ưu tiên gần nhất trước
                //    .ThenByDescending(r => r.Route.LastSearchAt) // Sau đó mới theo thời gian
                //    .FirstOrDefault();
                //arrivalAnalysisStopwatch.Stop();

                //_logger.LogInformation("Near arrival analysis completed in {ElapsedMs}ms. Found: {HasNearArrival}", 
                //    arrivalAnalysisStopwatch.ElapsedMilliseconds, nearArrivalRoute != null);

                //// Tối ưu: Return trip có ưu tiên cao hơn outbound trip
                //if (nearArrivalRoute != null)
                //{
                //    _logger.LogInformation("User is near arrival point (distance: {Distance:F1}m). Suggesting return trip to origin", 
                //        nearArrivalRoute.ArrivalDistance);

                //    var returnTripStopwatch = Stopwatch.StartNew();

                //    // Kiểm tra xem đã có lộ trình quay về chưa
                //    var pickup = nearArrivalRoute.Route.Pickup;
                //    var arrival = nearArrivalRoute.Route.Arrival;

                //    // Tối ưu: kiểm tra trước khi gọi ES
                //    if (string.IsNullOrEmpty(pickup?.PlaceId))
                //    {
                //        _logger.LogWarning("Invalid pickup PlaceId, skipping return trip suggestion");
                //        return null;
                //    }

                //    var hasReturnTrip = limitedRoutes.Any(r =>
                //        r.Arrival?.PlaceId == pickup?.PlaceId &&
                //        r.Pickup?.PlaceId == arrival?.PlaceId);

                //    _logger.LogInformation("Return trip check: hasReturnTrip={HasReturn}, pickupId={PickupId}", 
                //        hasReturnTrip, pickup?.PlaceId);

                //    if (!hasReturnTrip)
                //    {
                //        //pickup.PlaceId = "ChIJoY6oGhgCNTERBZHae_HVE4s_S_485724459"; // Test subplace

                //        var esStopwatch = Stopwatch.StartNew();
                //        var results = await GetESPlace(pickup.PlaceId);
                //        var placesEs = results.Item1;
                //        esStopwatch.Stop();

                //        returnTripStopwatch.Stop();

                //        _logger.LogInformation("Return trip suggestion created in {ReturnMs}ms (ES lookup: {ESMs}ms). " +
                //            "Total analysis time: {TotalMs}ms", 
                //            returnTripStopwatch.ElapsedMilliseconds, esStopwatch.ElapsedMilliseconds, stopwatch.ElapsedMilliseconds);

                //        // Tìm được return trip - return ngay để tối ưu tốc độ (không cần tìm outbound)
                //        return new RecommendedPlace
                //        {
                //            PlaceId = placesEs.PlaceId,
                //            Name = placesEs.Name,
                //            Address = placesEs.Address,
                //            FullAddress = placesEs.FullAddress,
                //            Location = new LocationDto
                //            {
                //                Lat = placesEs.Location.Lat,
                //                Lon = placesEs.Location.Lon
                //            },
                //            Active = true,
                //            Popular = true,
                //            Desc = IsVietnamese(lang) ? "Chuyến chiều về" : "Return trip"
                //        };
                //    }
                //    else
                //    {
                //        _logger.LogInformation("Return trip not suggested: user already has return trip today");
                //    }
                //}

                //// Bước 4: Tìm 1 lộ trình gần nhất mà người dùng đang ở gần điểm đi (tối ưu tốc độ)
                //var pickupAnalysisStopwatch = Stopwatch.StartNew();
                //var nearPickupRoute = routesWithDistances
                //    .Where(r => r.PickupDistance < 300) // Người dùng đang gần điểm đi
                //    .OrderBy(r => r.PickupDistance) // Ưu tiên gần nhất
                //    .FirstOrDefault();
                //pickupAnalysisStopwatch.Stop();

                //_logger.LogInformation("Near pickup analysis completed in {ElapsedMs}ms. Found: {HasNearPickup}", 
                //    pickupAnalysisStopwatch.ElapsedMilliseconds, nearPickupRoute != null);

                //// Chỉ xử lý nếu chưa tìm được return trip
                //if (nearPickupRoute != null)
                //{
                //    _logger.LogInformation("User is near pickup point (distance: {Distance:F1}m). Suggesting trip to destination", 
                //        nearPickupRoute.PickupDistance);

                //    var outboundTripStopwatch = Stopwatch.StartNew();

                //    // Kiểm tra xem đã có lộ trình quay về chưa
                //    var arrival = nearPickupRoute.Route.Arrival;

                //    // Tối ưu: kiểm tra trước khi gọi ES
                //    if (string.IsNullOrEmpty(arrival?.PlaceId))
                //    {
                //        _logger.LogWarning("Invalid arrival PlaceId, skipping outbound trip suggestion");
                //        return null;
                //    }

                //    // Kiểm tra xem lộ trình này đã có chuyến đi trước đó hay không
                //    var hasOutboundTrip = limitedRoutes.Any(r =>
                //        r.Pickup?.PlaceId == nearPickupRoute.Route.Arrival?.PlaceId &&
                //        r.Arrival?.PlaceId == nearPickupRoute.Route.Pickup?.PlaceId &&
                //        r != nearPickupRoute.Route);

                //    _logger.LogInformation("Outbound trip check: hasOutboundTrip={HasOutbound}, arrivalId={ArrivalId}", 
                //        hasOutboundTrip, arrival?.PlaceId);

                //    if (!hasOutboundTrip)
                //    {
                //        var esStopwatch = Stopwatch.StartNew();
                //        var results = await GetESPlace(arrival.PlaceId);
                //        var placesEs = results.Item1;
                //        esStopwatch.Stop();

                //        outboundTripStopwatch.Stop();

                //        _logger.LogInformation("Outbound trip suggestion created in {OutboundMs}ms (ES lookup: {ESMs}ms). " +
                //            "Total analysis time: {TotalMs}ms", 
                //            outboundTripStopwatch.ElapsedMilliseconds, esStopwatch.ElapsedMilliseconds, stopwatch.ElapsedMilliseconds);

                //        // Người dùng đang ở điểm đi, gợi ý đi đến điểm đến
                //        return new RecommendedPlace
                //        {
                //            PlaceId = placesEs.PlaceId,
                //            Name = placesEs.Name,
                //            Address = placesEs.Address,
                //            FullAddress = placesEs.FullAddress,
                //            Location = new LocationDto
                //            {
                //                Lat = placesEs.Location.Lat,
                //                Lon = placesEs.Location.Lon
                //            },
                //            Active = true,
                //            Popular = true,
                //            Desc = IsVietnamese(lang)
                //                ? "Chuyến chiều đi"
                //                : "Outbound trip"
                //        };
                //    }
                //    else
                //    {
                //        _logger.LogInformation("Outbound trip not suggested: user already has outbound trip today");
                //    }
                //}

                _logger.LogInformation("No return trip suggestion found. Total analysis time: {TotalMs}ms",
                    stopwatch.ElapsedMilliseconds);
                return null;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Error in return trip analysis after {ElapsedMs}ms: {Message}",
                    stopwatch.ElapsedMilliseconds, ex.Message);
                return null;
            }
        }

        #endregion
    }
}