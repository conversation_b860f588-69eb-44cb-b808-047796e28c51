using Confluent.Kafka;
using KnowledgeBase.Core.Extensions;
using MongoDB.Driver;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Model.KafkaSyncModel;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.BackgroundJobs.Services.KafkaTaxiSync;
using NDS.ETL.Infrastructure.Common;
using NDS.ETL.Infrastructure.Utils;
using Newtonsoft.Json;

// Dịch vụ chính để tiêu thụ và xử lý các thông điệp <PERSON>ka.
// Dịch vụ này lắng nghe các sự kiện từ Kafka và chuyển đổi thành các đối tượng ElasticSearch tương ứng.
public class KafkaConsumerService : IHostedService
{
    private readonly Queue<(PlacesEs, string)> _placesEsQueue = new Queue<(PlacesEs, string)>();
    private readonly Queue<(FavPlaceEs, string)> _favoritePlaceEsQueue = new Queue<(FavPlaceEs, string)>();

    private readonly Queue<(PlacePairEs, string)> _placePairEsQueue = new Queue<(PlacePairEs, string)>();

    private readonly Queue<(UserSearchHistoryEs, string)> _userSearchHistoryEsQueue = new Queue<(UserSearchHistoryEs, string)>();

    private readonly object _queuePlaceLock = new object();
    private readonly object _queueFavoriteLock = new object();
    private readonly object _queuePlacePairLock = new object();
    private readonly object _queueUserSearchHistoryLock = new object();
    
    // Thêm SemaphoreSlim để tối ưu việc xử lý queue
    private readonly SemaphoreSlim _placesQueueSemaphore = new SemaphoreSlim(0);
    private readonly SemaphoreSlim _favoritePlaceQueueSemaphore = new SemaphoreSlim(0);
    private readonly SemaphoreSlim _placePairQueueSemaphore = new SemaphoreSlim(0);
    private readonly SemaphoreSlim _userSearchHistoryQueueSemaphore = new SemaphoreSlim(0);
    
    private readonly ILogger<KafkaConsumerService> _logger;
    private readonly IConsumer<Ignore, string> _consumer;

    private readonly ESPlacesService _esPlacesService;

    private readonly IConfiguration _configuration;
    private readonly ElasticSearchConfig _indexName;

    public KafkaConsumerService(ILogger<KafkaConsumerService> logger, IScopedResolver<IMongoDatabase> dbContext,
        IConsumer<Ignore, string> consumer, IServiceProvider serviceProvider,
        ESPlacesService esPlacesService, IConfiguration configuration)
    {
        _logger = logger;
        _consumer = consumer;
        _esPlacesService = esPlacesService;
        _configuration = configuration;
        _indexName = configuration.Bind<ElasticSearchConfig>(nameof(ElasticSearchConfig));
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _consumer.Subscribe(_configuration["KafkaConfig:TopicSyncPlaces"]);
        Task.Run(() => ConsumeMessages(cancellationToken), cancellationToken);
        Task.Run(() => ProcessQueue(_placesEsQueue, _queuePlaceLock, _indexName.Index[0], _placesQueueSemaphore, cancellationToken),
            cancellationToken);
        Task.Run(() => ProcessQueue(_favoritePlaceEsQueue, _queueFavoriteLock, _indexName.Index[1], _favoritePlaceQueueSemaphore, cancellationToken),
            cancellationToken);
        Task.Run(() => ProcessQueue(_userSearchHistoryEsQueue, _queueUserSearchHistoryLock, _indexName.Index[2], _userSearchHistoryQueueSemaphore, cancellationToken), cancellationToken);
        Task.Run(() => ProcessQueue(_placePairEsQueue, _queuePlacePairLock, _indexName.Index[3], _placePairQueueSemaphore, cancellationToken), cancellationToken);

        //return await Task.CompletedTask;
    }

    private void ConsumeMessages(CancellationToken cancellationToken)
    {
        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                ConsumeResult<Ignore, string> consumeResult = null;
                try
                {
                    consumeResult = _consumer.Consume(cancellationToken);
                    if (consumeResult.Message == null)
                    {
                        continue;
                    }

                    var result = JsonConvert.DeserializeObject<KafkaTaxiSyncBaseModel>(consumeResult.Message.Value);

                    // Nếu lỗi deserialize hoặc data null thì log và commit luôn để tránh retry vô hạn
                    if (result == null)
                    {
                        _logger.LogWarning("Failed to deserialize message");
                        try { _consumer.Commit(consumeResult); } catch (Exception ex) { _logger.LogError(ex, "Kafka commit failed"); }
                        continue;
                    }

                    if (result.Data == null)
                    {
                        _logger.LogWarning($"Received null data for table: {result.Table}");
                        try { _consumer.Commit(consumeResult); } catch (Exception ex) { _logger.LogError(ex, "Kafka commit failed"); }
                        continue;
                    }

                    bool enqueueSuccess = false;
                    bool mappingError = false;
                    try
                    {
                        switch (result.Table)
                        {
                            case "places":
                                _logger.LogInformation($"[{result.Action}] {result.Table} -> Map to Places: {JsonConvert.SerializeObject(result.Data)}");
                                var placeEs = PlaceDataMapper.MapToPlaceEs(result.Data);
                                EnqueueMessage(placeEs, _placesEsQueue, _queuePlaceLock, result.Action, _placesQueueSemaphore);
                                enqueueSuccess = true;
                                break;
                            case "favourite_places":
                                _logger.LogInformation($"[{result.Action}] {result.Table} -> Mapped to FavoritePlaces: {JsonConvert.SerializeObject(result.Data)}");
                                var favoritePlaceEs = FavoritePlaceDataMapper.MapToFavPlaceEs(result.Data);
                                EnqueueMessage(favoritePlaceEs, _favoritePlaceEsQueue, _queueFavoriteLock, result.Action, _favoritePlaceQueueSemaphore);
                                enqueueSuccess = true;
                                break;
                            case "history_places":
                                _logger.LogInformation($"[{result.Action}] {result.Table} -> Mapped to HistoryPlaces: {JsonConvert.SerializeObject(result.Data)}");
                                var placePairEsItems = PlacePairDataMapper.MapToPlacePairEs(result.Data);
                                foreach (var esItem in placePairEsItems)
                                {
                                    EnqueueMessage(esItem, _placePairEsQueue, _queuePlacePairLock, result.Action, _placePairQueueSemaphore);
                                }
                                enqueueSuccess = true;
                                break;
                            case "search_histories":
                                _logger.LogInformation($"[{result.Action}] {result.Table} -> Mapped to UserSearchHistory: {JsonConvert.SerializeObject(result.Data)}");
                                var userSearchHistoryEs = UserSearchHistoryDataMapper.MapToUserSearchHistoryEs(result.Data);
                                EnqueueMessage(userSearchHistoryEs, _userSearchHistoryEsQueue, _queueUserSearchHistoryLock, result.Action, _userSearchHistoryQueueSemaphore);
                                enqueueSuccess = true;
                                break;
                            default:
                                _logger.LogInformation("Unknown table type");
                                // Không cần retry, commit luôn
                                enqueueSuccess = false;
                                mappingError = true;
                                break;
                        }
                    }
                    catch (Exception ex)
                    {
                        // Nếu lỗi mapping dữ liệu, log và commit luôn để tránh retry vô hạn
                        _logger.LogError(ex, "Mapping or enqueue error, commit to skip this message");
                        mappingError = true;
                    }
                    // Commit nếu enqueue thành công hoặc lỗi mapping/data
                    if (enqueueSuccess || mappingError)
                    {
                        try
                        {
                            _consumer.Commit(consumeResult);
                        }
                        catch (Exception commitEx)
                        {
                            _logger.LogError(commitEx, "Kafka commit failed");
                        }
                    }
                }
                catch (Exception e)
                {
                    // Lỗi hệ thống (ví dụ: mất kết nối Kafka, lỗi tạm thời), không commit để retry
                    _logger.LogError(e, "Error while processing message, skipping to next message");
                }
            }
        }
        catch (OperationCanceledException)
        {
            // Không log lỗi khi dừng service (OperationCanceledException)
        }
        catch (Exception ex)
        {
            // Log lỗi khi có exception thực sự
            _logger.LogError(ex, "Error while consuming topic");
        }
        finally
        {
            // Đảm bảo luôn đóng consumer khi kết thúc
            _consumer.Close();
        }
    }

    private void EnqueueMessage<T>(T message, Queue<(T, string)> queue, object queueLock, string action, SemaphoreSlim semaphore)
    {
        lock (queueLock)
        {
            queue.Enqueue((message, action));
        }
        // Báo hiệu có item mới trong queue
        semaphore.Release();
    }

    private async Task ProcessQueue<T>(Queue<(T, string)> queue, object queueLock, string indexName,
        SemaphoreSlim semaphore, CancellationToken cancellationToken) where T : class
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                // Chờ có item trong queue hoặc cancellation
                await semaphore.WaitAsync(cancellationToken);
                
                (T item, string action) = (null, null);
                lock (queueLock)
                {
                    if (queue.Count > 0)
                    {
                        (item, action) = queue.Dequeue();
                    }
                }

                if (item != null)
                {
                    if (action != null && action.Equals("DELETE", StringComparison.InvariantCultureIgnoreCase))
                    {
                        await _esPlacesService.DeleteDocumentAsync(item, cancellationToken, indexName);
                    }
                    else
                    {
                        await _esPlacesService.UpsertDocumentAsync(item, cancellationToken, indexName, "location-to-h3");
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing queue item for index {IndexName}", indexName);
                // Continue processing other items
            }
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _consumer.Close();
        
        // Dispose semaphores
        _placesQueueSemaphore?.Dispose();
        _favoritePlaceQueueSemaphore?.Dispose();
        _placePairQueueSemaphore?.Dispose();
        _userSearchHistoryQueueSemaphore?.Dispose();
        
        return Task.CompletedTask;
    }
}