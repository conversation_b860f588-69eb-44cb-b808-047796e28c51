using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Table("blocked_places")]
[Index("PlaceId", Name = "IX_blocked_places_placeId", IsUnique = true)]
public partial class BlockedPlace
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("placeId")]
    public string? PlaceId { get; set; }

    [Column("createdAt", TypeName = "timestamp without time zone")]
    public DateTime CreatedAt { get; set; }
}
