namespace NDS.ETL.BackgroundJobs.Model.ES.Dto
{
    /// <summary>
    /// Enum định nghĩa các loại địa điểm
    /// </summary>
    public enum PlaceTypeEnum
    {
        Home = 0, // Nhà
        Work = 1, // C<PERSON> quan
        Favorite = 2, // Yêu thích
        Departure = 3, // Điểm đi
        Destination = 4, // Điểm đến
        Starred = 5, // Gắn dấu sao
        SavedPlace = 6, // Địa điểm đã lưu
        UserSearch = 7, // Tìm kiếm của người dùng
        BookingHistory = 8, // L<PERSON>ch sử đặt xe
        Normal = 9, // <PERSON><PERSON>nh thường
    }

    /// <summary>
    /// C<PERSON><PERSON> phương thức mở rộng cho PlaceTypeEnum
    /// </summary>
    public static class PlaceTypeEnumExtensions
    {
        /// <summary>
        /// Chuyển đổi từ enum sang tên loại địa điểm
        /// </summary>
        public static string GetName(this PlaceTypeEnum placeType)
        {
            return placeType switch
            {
                PlaceTypeEnum.Home => "Nhà",
                PlaceTypeEnum.Work => "Cơ quan",
                PlaceTypeEnum.Favorite => "Yêu thích",
                PlaceTypeEnum.Departure => "Điểm đi",
                PlaceTypeEnum.Destination => "Điểm đến",
                PlaceTypeEnum.Starred => "Gắn dấu sao",
                PlaceTypeEnum.SavedPlace => "Địa điểm đã lưu",
                PlaceTypeEnum.UserSearch => "Tìm kiếm của người dùng",
                PlaceTypeEnum.BookingHistory => "Lịch sử đặt xe",
                PlaceTypeEnum.Normal => "Bình thường",
                _ => "Không xác định"
            };
        }

        /// <summary>
        /// Chuyển đổi từ giá trị số sang enum PlaceTypeEnum
        /// </summary>
        public static PlaceTypeEnum ToPlaceTypeEnum(this int value)
        {
            if (Enum.IsDefined(typeof(PlaceTypeEnum), value))
            {
                return (PlaceTypeEnum)value;
            }
            
            return PlaceTypeEnum.Normal;
        }

        /// <summary>
        /// Chuyển đổi từ enum sang tên tiếng Anh (tên gốc của enum)
        /// </summary>
        public static string GetEnglishName(this PlaceTypeEnum placeType)
        {
            return placeType.ToString();
        }
    }
} 