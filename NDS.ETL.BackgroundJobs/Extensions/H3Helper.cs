using H3.Extensions;
using NetTopologySuite.Geometries;

namespace NDS.ETL.BackgroundJobs.Extensions;

using H3.Algorithms;
using System;
using System.Collections.Generic;
using System.Linq;

public static class H3Helper
{
    // Bảng đường kính cell trung bình (km) theo resolution
    private static readonly Dictionary<int, double> CellDiameters = new()
    {
        [2] = 158,
        [3] = 63,
        [4] = 24,
        [5] = 9,
        [6] = 3.6,
        [7] = 1.4,
        [8] = 0.55,
        [9] = 0.2,
    };

 
    //Chọn resolution sao cho đường kính cell <= bán kính tìm kiếm
    public static int GetBestResolution(double radiusInKm)
    {
        // Duyệt từ nhỏ đến lớn (cell lớn đến nhỏ)
        foreach (var kvp in CellDiameters.OrderBy(x => x.Key))
        {
            // Nếu đường kính cell nhỏ hơn hoặc bằng bán kính
            if (kvp.Value <= radiusInKm)
                return kvp.Key;
        }
        // Nếu radius quá nhỏ, trả về độ phân giải cao nhất (cell nhỏ nhất)
        return CellDiameters.Keys.Max();
    }



    /// <summary>
    /// Trả về tập hợp các H3 index bao phủ vùng bán kính quanh tọa độ
    /// </summary>
    public static HashSet<string> GetH3CellsInRadius(double latitude, double longitude, double radiusInKm, out int res)
    {
        res = GetBestResolution(radiusInKm);

        if (!CellDiameters.TryGetValue(res, out var cellDiameterKm))
        {
            throw new ArgumentException($"Resolution {res} not supported.");
        }

        var origin = new Coordinate(longitude, latitude);
        var h3Index = origin.ToH3Index(res);

        int k = (int)Math.Ceiling(radiusInKm / (cellDiameterKm / 2.0));

        var cells = h3Index.GridDiskDistances(k);

        var filtered = cells
            .Where(cell =>
            {
                var coord = cell.Index.ToCoordinate(); // Ensure this method exists
                var distanceKm = GeoUtils.HaversineDistanceKm(origin, coord);

                //Console.WriteLine($"H3: {cell.Index}, Dist: {distanceKm:0.000} km");
                return distanceKm <= radiusInKm;
            })
            .Select(x => x.Index.ToString());

        return [..filtered];
    }

    /// <summary>
    /// Trả về tập hợp các H3 index bao phủ vùng bán kính quanh tọa độ (đơn vị: mét)
    /// </summary>
    public static HashSet<string> GetH3CellsInRadiusInMeters(double latitude, double longitude, double radiusInMeters, out int res)
    {
        double radiusInKm = radiusInMeters / 1000.0;
        return GetH3CellsInRadius(latitude, longitude, radiusInKm, out res);
    }


    // Bảng đường kính cell H3 (km) theo độ phân giải
    private static readonly Dictionary<int, double> CellDiameters2 = new()
    {
        {0, 1107.712591}, {1, 418.6760055}, {2, 158.2446558}, {3, 59.81085794},
        {4, 22.6063794}, {5, 8.544408276}, {6, 3.231277777}, {7, 1.220629759},
        {8, 0.461354684}, {9, 0.174375668}, {10, 0.065932015}, {11, 0.024920952},
        {12, 0.009414229}, {13, 0.003558327}, {14, 0.001347116}, {15, 0.000509713}
    };


    /// <summary>
    /// Tính toán bán kính tìm kiếm gần đúng (đơn vị: km) dựa trên độ phân giải H3.
    /// Dùng cho các phép tìm kiếm không gian (geo search) như Elasticsearch.
    /// Lưu ý: đây không phải là bán kính đường tròn nội tiếp của hexagon H3.
    /// </summary>
    public static double GetRadiusKmFromH3Res(this int h3Resolution)
    {
        // Độ phân giải H3 càng cao => kích thước cell càng nhỏ => bán kính tìm kiếm càng nhỏ
        // Dưới đây là các giá trị gần đúng của bán kính bao phủ một ô H3 ở từng cấp độ độ phân giải.

        switch (h3Resolution)
        {
            case 3:
                return 50.0;    // Resolution 3: ~50km
            case 4:
                return 20.0;    // Resolution 4: ~20km
            case 5:
                return 10.0;    // Resolution 5: ~10km
            case 6:
                return 5.0;     // Resolution 6: ~5km
            case 7:
                return 2.0;     // Resolution 7: ~2km
            case 8:
                return 1.0;     // Resolution 8: ~1km
            case 9:
                return 0.5;     // Resolution 9: ~500m
            case 10:
                return 0.25;    // Resolution 10: ~250m
            case 11:
                return 0.1;     // Resolution 11: ~100m
            case 12:
                return 0.05;    // Resolution 12: ~50m
            default:
                // Nếu không khớp độ phân giải nào được định nghĩa, trả về bán kính mặc định là 100km.
                return 100.0;
        }
    }


    /// <summary>
    /// Xác định độ phân giải H3 phù hợp nhất dựa trên bán kính tìm kiếm (đơn vị: km).
    /// Dùng để chọn resolution sao cho phạm vi tìm kiếm tương đương bán kính đầu vào.
    /// </summary>
    public static int GetH3ResolutionFromRadiusKm(double radiusKm)
    {
        // So sánh ngược với bảng ước lượng đường kính cell H3 theo resolution
        // Radius càng nhỏ => cần độ phân giải cao hơn (cell nhỏ hơn)

        if (radiusKm <= 0.05) return 12;    // ~50m
        if (radiusKm <= 0.1) return 11;     // ~100m
        if (radiusKm <= 0.25) return 10;    // ~250m
        if (radiusKm <= 0.5) return 9;      // ~500m
        if (radiusKm <= 1.0) return 8;      // ~1km
        if (radiusKm <= 2.0) return 7;      // ~2km
        if (radiusKm <= 5.0) return 6;      // ~5km
        if (radiusKm <= 10.0) return 5;     // ~10km
        if (radiusKm <= 20.0) return 4;     // ~20km
        if (radiusKm <= 50.0) return 3;     // ~50km

        // Nếu lớn hơn tất cả, mặc định chọn resolution rất thô (res = 2 hoặc thấp hơn)
        return 2;
    }


    /// <summary>
    /// Xác định độ phân giải H3 phù hợp nhất dựa trên bán kính tìm kiếm (đơn vị: mét).
    /// Dùng cho các trường hợp nhập bán kính theo mét.
    /// </summary>
    /// <param name="radiusMeters">Bán kính tìm kiếm (mét)</param>
    /// <returns>Độ phân giải H3 tương ứng</returns>
    public static int GetH3ResolutionFromRadiusInMeters(this double radiusMeters)
    {
        // Chuyển đổi từ mét sang km
        double radiusKm = radiusMeters / 1000.0;

        // Gọi lại hàm dùng km để tái sử dụng logic
        return GetH3ResolutionFromRadiusKm(radiusKm);
    }
    
    /// <summary>
    /// Lấy tập các H3 cells bao phủ bán kính radiusInMeters quanh tọa độ (latitude, longitude) - Bán kính nhỏ không cần dùng đến
    /// </summary>
    public static HashSet<string> GetH3CellsInRadiusMeters(double latitude, double longitude, int resolution = 5, int radiusInMeters = 50000)
    {
        if (!CellDiameters2.TryGetValue(resolution, out var cellDiameterKm))
            throw new ArgumentException($"Resolution {resolution} not supported.");

        var origin = new Coordinate(latitude, longitude);
        var h3Index = origin.ToH3Index(resolution);

        // Đổi radius từ mét sang km
        double radiusInKm = radiusInMeters / 1000.0;

        // Tính số bước lân cận cần mở rộng (k)
        int k = (int)Math.Ceiling(radiusInKm / cellDiameterKm);

        var cells = h3Index.GridDiskDistances(k);

        return new HashSet<string>(cells.Select(x => x.Index.ToString()));
    }

    
    
    private const double EARTH_RADIUS_METERS = 6371000; // Bán kính trái đất (meters)
    /// <summary>
    /// Tính khoảng cách Haversine giữa hai tọa độ (trả về kết quả bằng meters)
    /// </summary>
    public static double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        // Chuyển đổi từ độ sang radian
        var dLat = ToRadians(lat2 - lat1);
        var dLon = ToRadians(lon2 - lon1);

        // Công thức Haversine
        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(ToRadians(lat1)) * Math.Cos(ToRadians(lat2)) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);

        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        var distance = EARTH_RADIUS_METERS * c;

        return Math.Round(distance, 0); // Làm tròn đến meters
    }

    /// <summary>
    /// Chuyển đổi từ độ sang radian
    /// </summary>
    public static  double ToRadians(double degree)
    {
        return degree * Math.PI / 180;
    }


}


public static class GeoUtils
{
    private const double EarthRadiusKm = 6371.0;

    public static double HaversineDistanceMeters(Coordinate point1, Coordinate point2)
    {
        return HaversineDistanceKm(point1, point2) * 1000.0;
    }

    /// <summary>
    /// Tính khoảng cách giữa 2 điểm địa lý theo công thức Haversine
    /// </summary>
    public static double HaversineDistanceKm(Coordinate point1, Coordinate point2)
    {
        double lat1Rad = DegreesToRadians(point1.Y);
        double lon1Rad = DegreesToRadians(point1.X);
        double lat2Rad = DegreesToRadians(point2.Y);
        double lon2Rad = DegreesToRadians(point2.X);

        double deltaLat = lat2Rad - lat1Rad;
        double deltaLon = lon2Rad - lon1Rad;

        double a = Math.Pow(Math.Sin(deltaLat / 2), 2) +
                   Math.Cos(lat1Rad) * Math.Cos(lat2Rad) *
                   Math.Pow(Math.Sin(deltaLon / 2), 2);

        double c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return EarthRadiusKm * c;
    }

    private static double DegreesToRadians(double degrees)
    {
        return degrees * (Math.PI / 180);
    }
}