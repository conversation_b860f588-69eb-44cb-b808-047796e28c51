using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Keyless]
[Table("blocked_places")]
public partial class BlockedPlace
{
    [Column("id")]
    public long Id { get; set; }

    [Column("placeId")]
    public string? PlaceId { get; set; }

    [Column("createdAt", TypeName = "timestamp without time zone")]
    public DateTime CreatedAt { get; set; }
}
