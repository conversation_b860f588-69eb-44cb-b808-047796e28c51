using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("place_approve")]
[Index("PlaceId", Name = "place_approve_placeid_idx")]
public partial class PlaceApprove
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("name")]
    public string? Name { get; set; }

    [Column("location", TypeName = "geometry(Point,4326)")]
    public Point? Location { get; set; }

    [Column("address")]
    public string? Address { get; set; }

    [Column("placeId")]
    public string? PlaceId { get; set; }

    [Column("provinceId")]
    public int? ProvinceId { get; set; }

    [Column("districtId")]
    public int? DistrictId { get; set; }

    [Column("wardId")]
    public int? WardId { get; set; }

    [Column("scanProcessed")]
    public int ScanProcessed { get; set; }

    [Column("reasonOperations")]
    public List<int>? ReasonOperations { get; set; }

    [Column("reason")]
    public string? Reason { get; set; }

    [Column("note")]
    public string? Note { get; set; }

    [Column("statusApprove")]
    public int StatusApprove { get; set; }

    [Column("descriptionReasonApprove")]
    public string? DescriptionReasonApprove { get; set; }

    [Column("reasonApprove")]
    public List<int>? ReasonApprove { get; set; }

    [Column("lastUpdateUser")]
    public string? LastUpdateUser { get; set; }

    [Column(TypeName = "timestamp without time zone")]
    public DateTime? UpdatedAt { get; set; }

    public string? CreatorUserId { get; set; }

    [Column(TypeName = "timestamp without time zone")]
    public DateTime CreatedAt { get; set; }

    public bool IsDeleted { get; set; }

    [Column("statusCalculate")]
    public int StatusCalculate { get; set; }

    [Column("locationAddressDiffScore")]
    public string? LocationAddressDiffScore { get; set; }

    [Column("determineAddressLevel")]
    public int? DetermineAddressLevel { get; set; }

    [Column("shortAddress")]
    public string? ShortAddress { get; set; }
}
