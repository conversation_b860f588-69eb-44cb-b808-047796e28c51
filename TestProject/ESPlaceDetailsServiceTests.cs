using Elastic.Clients.Elasticsearch;
using Elastic.Transport;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Entities.Context;
using NDS.ETL.Infrastructure.Common;
using NDS.ETL.Infrastructure.Utils;
using System.Diagnostics;

namespace TestProject
{
    /// <summary>
    /// Unit test cho ESPlaceDetailsService - tập trung vào phương thức GetPlaceDetailsAsync với dữ liệu thật từ Elasticsearch
    /// </summary>
    [TestFixture]
    public class ESPlaceDetailsServiceTests
    {
        #region Private Fields

        private ElasticsearchClient _elasticClient;
        private ESPlacesService _realESPlacesService;
        private Mock<ILogger<ESPlaceDetailsService>> _mockLogger;
        private ESPlaceDetailsService _service;
        private IConfiguration _configuration;
        private string _testIndex;
        private ElasticSearchConfig _elasticSearchConfig;

        // Regex để kiểm tra sub place pattern
        private static readonly System.Text.RegularExpressions.Regex _subPlaceRegex =
            new System.Text.RegularExpressions.Regex("_S_\\d{3,10}", System.Text.RegularExpressions.RegexOptions.Compiled);

        #endregion

        #region Setup and Teardown

        [OneTimeSetUp]
        public async Task OneTimeSetUp()
        {
            // Thiết lập configuration từ appsettings.json
            _configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            // Lấy index name từ configuration hoặc sử dụng mặc định
            _testIndex = "place-sit"; // Sử dụng index thật từ UAT-SIT

            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            // Bind ElasticSearchConfig from appsettings.json
            _elasticSearchConfig = configuration.GetSection("ElasticSearchConfig").Get<ElasticSearchConfig>()!;

            // Thiết lập kết nối ElasticSearch với Basic Authentication
            var url = _elasticSearchConfig!.Url.First();
            var settings = new ElasticsearchClientSettings(new Uri(url))
                .DefaultIndex(_testIndex)
                .Authentication(new BasicAuthentication(_elasticSearchConfig!.UserName!, _elasticSearchConfig.Password!))
                .ServerCertificateValidationCallback((sender, cert, chain, errors) => true)
                .EnableDebugMode()
                .DisableDirectStreaming()
                .RequestTimeout(TimeSpan.FromSeconds(30));

            _elasticClient = new ElasticsearchClient(settings);

            // Kiểm tra kết nối đến Elasticsearch
            var pingResponse = await _elasticClient.PingAsync();
            if (!pingResponse.IsSuccess())
            {
                Assert.Fail($"Không thể kết nối đến Elasticsearch: {pingResponse.DebugInformation}");
            }

            Console.WriteLine("✓ Đã kết nối thành công đến Elasticsearch");

            // Tạo mock cho các dependency cần thiết
            var mockDbContextResolver = new Mock<IScopedResolver<NpgPlaceContext>>();
            var mockSearchConfigService = new Mock<ISearchConfigService>();
            var mockESPlacesLogger = new Mock<ILogger<ESPlacesService>>();

            // Khởi tạo ESPlacesService thật với dependencies
            _realESPlacesService = new ESPlacesService(
                _elasticClient,
                _configuration,
                mockDbContextResolver.Object,
                mockSearchConfigService.Object,
                mockESPlacesLogger.Object
            );

            Console.WriteLine("✓ Đã khởi tạo ESPlacesService thật");
        }

        [SetUp]
        public void Setup()
        {
            // Khởi tạo mock logger cho ESPlaceDetailsService
            _mockLogger = new Mock<ILogger<ESPlaceDetailsService>>();

            // Cấu hình logger mock để hiển thị log trong console
            _mockLogger.Setup(
                x => x.Log(
                    It.IsAny<LogLevel>(),
                    It.IsAny<EventId>(),
                    It.IsAny<object>(),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<object, Exception, string>>()))
                .Callback((LogLevel logLevel, EventId eventId, object state, Exception exception, Func<object, Exception, string> formatter) =>
                {
                    if (state != null)
                    {
                        var message = formatter(state, exception);
                        Console.WriteLine($"[{logLevel}] {message}");
                    }
                });

            // Khởi tạo service với ESPlacesService thật
            _service = new ESPlaceDetailsService(_realESPlacesService, _mockLogger.Object);
        }

        #endregion

        #region Test Methods với dữ liệu thật

        /// <summary>
        /// Test với place IDs thật từ Elasticsearch - chỉ main places
        /// </summary>
        [Test]
        public async Task GetPlaceDetailsAsync_WithRealMainPlaces_ShouldReturnCorrectResult()
        {
            // Arrange - Lấy place IDs thật từ index thay vì dùng giá trị cứng
            var searchResponse = await _elasticClient.SearchAsync<PlacesEs>(s => s
                .Index(_testIndex)
                .Size(3)
                .Query(q => q
                    .Bool(b => b
                        .Must(m => m.Exists(e => e.Field("placeId")))
                        .MustNot(mn => mn.Nested(n => n
                            .Path("subPlaces")
                            .Query(nq => nq.Exists(e => e.Field("subPlaces.placeId")))
                        ))
                    )
                )
            );

            if (!searchResponse.IsSuccess() || !searchResponse.Documents.Any())
            {
                Assert.Ignore("Không tìm thấy main places để test");
                return;
            }

            var placeIds = searchResponse.Documents
                .Where(d => !string.IsNullOrEmpty(d.PlaceId))
                .Take(3)
                .Select(d => d.PlaceId)
                .ToArray();

            if (placeIds.Length == 0)
            {
                Assert.Ignore("Không tìm thấy place IDs hợp lệ để test");
                return;
            }

            // Act
            var stopwatch = Stopwatch.StartNew();
            var result = await _service.GetPlaceDetailsAsync(placeIds);
            stopwatch.Stop();

            // Assert
            Assert.That(result, Is.Not.Null, "Kết quả không được null");
            Assert.That(result.Item1, Is.Not.Null, "Địa điểm đầu tiên không được null");
            Assert.That(result.Item2, Is.Not.Null, "Danh sách địa điểm không được null");
            Assert.That(result.Item2.Count, Is.GreaterThan(0), "Phải có ít nhất một địa điểm được tìm thấy");

            // Kiểm tra rằng tất cả địa điểm đều có dữ liệu thật
            foreach (var place in result.Item2)
            {
                Assert.That(place.PlaceId, Is.Not.Null.And.Not.Empty, "PlaceId không được null hoặc rỗng");
                Assert.That(place.FullAddress, Is.Not.Null.And.Not.Empty, "FullAddress không được null hoặc rỗng");
                Assert.That(place.Location, Is.Not.Null, "Location không được null");
                Assert.That(place.Location.Lat, Is.GreaterThan(0), "Latitude phải lớn hơn 0");
                Assert.That(place.Location.Lon, Is.GreaterThan(0), "Longitude phải lớn hơn 0");
            }

            // Kiểm tra địa điểm đầu tiên
            Assert.That(result.Item1.PlaceId, Is.EqualTo(result.Item2.First().PlaceId),
                "Địa điểm đầu tiên phải trùng với item đầu tiên trong danh sách");

            // Log kết quả
            Console.WriteLine($"✓ Test với dữ liệu thật thành công: {result.Item2.Count} địa điểm được trả về trong {stopwatch.ElapsedMilliseconds}ms");
            Console.WriteLine($"  Input PlaceIds: {string.Join(", ", placeIds)}");
            foreach (var place in result.Item2)
            {
                Console.WriteLine($"  - {place.Name}: {place.FullAddress}");
            }
        }

        /// <summary>
        /// Test với sub places thật từ Elasticsearch
        /// </summary>
        [Test]
        public async Task GetPlaceDetailsAsync_WithRealSubPlaces_ShouldReturnCorrectResult()
        {
            // Arrange - Tìm một số sub places thật từ index
            var searchResponse = await _elasticClient.SearchAsync<PlacesEs>(s => s
                .Index(_testIndex)
                .Size(5)
                .Query(q => q
                    .Nested(n => n
                        .Path("subPlaces")
                        .Query(nq => nq.Exists(e => e.Field("subPlaces.placeId")))
                    )
                )
            );

            if (!searchResponse.IsSuccess() || !searchResponse.Documents.Any())
            {
                Assert.Ignore("Không tìm thấy sub places để test");
                return;
            }

            // Lấy một số sub place IDs thật
            var subPlaceIds = searchResponse.Documents
                .SelectMany(d => d.SubPlaces ?? new List<SubPlacesEs>())
                .Take(3)
                .Select(sp => sp.PlaceId)
                .Where(id => !string.IsNullOrEmpty(id))
                .ToArray();

            if (subPlaceIds.Length == 0)
            {
                Assert.Ignore("Không tìm thấy sub place IDs hợp lệ để test");
                return;
            }

            // Act
            var stopwatch = Stopwatch.StartNew();
            var result = await _service.GetPlaceDetailsAsync(subPlaceIds);
            stopwatch.Stop();

            // Assert
            Assert.That(result, Is.Not.Null, "Kết quả không được null");
            Assert.That(result.Item2, Is.Not.Null, "Danh sách địa điểm không được null");
            Assert.That(result.Item2.Count, Is.GreaterThan(0), "Phải có ít nhất một sub place được tìm thấy");

            // Kiểm tra rằng các sub places được chuyển đổi đúng sang PlacesEs
            foreach (var place in result.Item2)
            {
                Assert.That(place.PlaceId, Is.Not.Null.And.Not.Empty, "PlaceId không được null hoặc rỗng");
                Assert.That(place.FullAddress, Is.Not.Null.And.Not.Empty, "FullAddress không được null hoặc rỗng");
            }

            // Log kết quả
            Console.WriteLine($"✓ Test với sub places thật thành công: {result.Item2.Count} địa điểm được trả về trong {stopwatch.ElapsedMilliseconds}ms");
            foreach (var place in result.Item2)
            {
                Console.WriteLine($"  - {place.Name}: {place.FullAddress}");
            }
        }

        /// <summary>
        /// Test với dữ liệu hỗn hợp thật - cả main places và sub places
        /// </summary>
        [Test]
        public async Task GetPlaceDetailsAsync_WithRealMixedPlaces_ShouldReturnCorrectResult()
        {
            // Arrange - Lấy một số main places và sub places thật
            var mainPlaceResponse = await _elasticClient.SearchAsync<PlacesEs>(s => s
                .Index(_testIndex)
                .Size(2)
                .Query(q => q
                    .Bool(b => b
                        .Must(m => m.Exists(e => e.Field("placeId")))
                        .MustNot(mn => mn.Nested(n => n
                            .Path("subPlaces")
                            .Query(nq => nq.Exists(e => e.Field("subPlaces.placeId")))
                        ))
                    )
                )
            );

            var subPlaceResponse = await _elasticClient.SearchAsync<PlacesEs>(s => s
                .Index(_testIndex)
                .Size(2)
                .Query(q => q
                    .Nested(n => n
                        .Path("subPlaces")
                        .Query(nq => nq.Exists(e => e.Field("subPlaces.placeId")))
                    )
                )
            );

            var mainPlaceIds = mainPlaceResponse.Documents
                .Where(d => !string.IsNullOrEmpty(d.PlaceId))
                .Take(2)
                .Select(d => d.PlaceId)
                .ToList();

            var subPlaceIds = subPlaceResponse.Documents
                .SelectMany(d => d.SubPlaces ?? new List<SubPlacesEs>())
                .Take(2)
                .Select(sp => sp.PlaceId)
                .Where(id => !string.IsNullOrEmpty(id))
                .ToList();

            var mixedPlaceIds = mainPlaceIds.Concat(subPlaceIds).Where(id => id != null).ToArray();

            if (mixedPlaceIds.Length == 0)
            {
                Assert.Ignore("Không tìm thấy place IDs hợp lệ để test");
                return;
            }

            // Act
            var stopwatch = Stopwatch.StartNew();
            var result = await _service.GetPlaceDetailsAsync(mixedPlaceIds);
            stopwatch.Stop();

            // Assert
            Assert.That(result, Is.Not.Null, "Kết quả không được null");
            Assert.That(result.Item2, Is.Not.Null, "Danh sách địa điểm không được null");
            Assert.That(result.Item2.Count, Is.GreaterThan(0), "Phải có ít nhất một địa điểm được tìm thấy");

            // Kiểm tra thứ tự trả về theo đúng placeIds đầu vào (những địa điểm tìm thấy)
            var foundPlaceIds = result.Item2.Select(p => p.PlaceId).ToList();
            var inputFoundIds = mixedPlaceIds.Where(id => foundPlaceIds.Contains(id)).ToList();

            for (int i = 0; i < Math.Min(result.Item2.Count, inputFoundIds.Count); i++)
            {
                Assert.That(foundPlaceIds.Contains(inputFoundIds[i]), Is.True,
                    $"Địa điểm {inputFoundIds[i]} không được tìm thấy trong kết quả");
            }

            // Log kết quả
            Console.WriteLine($"✓ Test với mixed places thật thành công: {result.Item2.Count} địa điểm được trả về trong {stopwatch.ElapsedMilliseconds}ms");
            Console.WriteLine($"  Input: {string.Join(", ", mixedPlaceIds)}");
            Console.WriteLine($"  Found: {string.Join(", ", foundPlaceIds)}");
            foreach (var place in result.Item2)
            {
                Console.WriteLine($"  - {place.Name}: {place.FullAddress}");
            }
        }

        /// <summary>
        /// Test với place IDs không tồn tại - phải throw Exception
        /// </summary>
        [Test]
        public void GetPlaceDetailsAsync_WithNonExistentRealPlaces_ShouldThrowException()
        {
            // Arrange - Sử dụng place IDs chắc chắn không tồn tại
            var placeIds = new[] { "nonexistent_001_real", "nonexistent_002_real", "fake_place_id_123" };

            // Act & Assert
            Assert.ThrowsAsync<Exception>(async () => await _service.GetPlaceDetailsAsync(placeIds));

            Console.WriteLine($"✓ Test exception với dữ liệu thật thành công");
        }

        /// <summary>
        /// Test hiệu suất với nhiều place IDs thật
        /// </summary>
        [Test]
        public async Task GetPlaceDetailsAsync_RealPerformanceTest_ShouldCompleteWithinTimeLimit()
        {
            // Arrange - Lấy nhiều place IDs thật từ index
            var searchResponse = await _elasticClient.SearchAsync<PlacesEs>(s => s
                .Index(_testIndex)
                .Size(50) // Lấy 50 địa điểm để test performance
                .Query(q => q.Exists(e => e.Field("placeId")))
            );

            if (!searchResponse.IsSuccess() || !searchResponse.Documents.Any())
            {
                Assert.Ignore("Không tìm thấy dữ liệu để test performance");
                return;
            }

            var realPlaceIds = searchResponse.Documents
                .Where(d => !string.IsNullOrEmpty(d.PlaceId))
                .Select(d => d.PlaceId)
                .Take(20) // Test với 20 địa điểm
                .ToArray();

            if (realPlaceIds.Length == 0)
            {
                Assert.Ignore("Không tìm thấy place IDs hợp lệ để test performance");
                return;
            }

            // Act
            var stopwatch = Stopwatch.StartNew();
            var result = await _service.GetPlaceDetailsAsync(realPlaceIds);
            stopwatch.Stop();

            // Assert
            Assert.That(result, Is.Not.Null, "Kết quả không được null");
            Assert.That(result.Item2.Count, Is.GreaterThan(0), "Phải có ít nhất một địa điểm được tìm thấy");
            Assert.That(stopwatch.ElapsedMilliseconds, Is.LessThan(10000), "Thời gian xử lý không được vượt quá 10 giây với dữ liệu thật");

            Console.WriteLine($"✓ Performance test với dữ liệu thật thành công: {result.Item2.Count}/{realPlaceIds.Length} địa điểm được xử lý trong {stopwatch.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// Test regex pattern với dữ liệu thật
        /// </summary>
        [Test]
        public async Task GetPlaceDetailsAsync_RealData_ShouldCorrectlyIdentifySubPlaces()
        {
            // Arrange - Tìm địa điểm có sub places từ dữ liệu thật
            var mainPlaceResponse = await _elasticClient.SearchAsync<PlacesEs>(s => s
                .Index(_testIndex)
                .Size(1)
                .Query(q => q
                    .Bool(b => b
                        .Must(m => m.Exists(e => e.Field("placeId")))
                        .MustNot(mn => mn.Nested(n => n
                            .Path("subPlaces")
                            .Query(nq => nq.Exists(e => e.Field("subPlaces.placeId")))
                        ))
                    )
                )
            );

            var subPlaceResponse = await _elasticClient.SearchAsync<PlacesEs>(s => s
                .Index(_testIndex)
                .Size(1)
                .Query(q => q
                    .Nested(n => n
                        .Path("subPlaces")
                        .Query(nq => nq.Exists(e => e.Field("subPlaces.placeId")))
                    )
                )
            );

            var mainPlaceId = mainPlaceResponse.Documents.FirstOrDefault()?.PlaceId;
            var subPlaceId = subPlaceResponse.Documents
                .SelectMany(d => d.SubPlaces ?? new List<SubPlacesEs>())
                .FirstOrDefault()?.PlaceId;

            if (string.IsNullOrEmpty(mainPlaceId) || string.IsNullOrEmpty(subPlaceId))
            {
                Assert.Ignore("Không tìm thấy dữ liệu phù hợp để test regex pattern");
                return;
            }

            var mixedIds = new[] { mainPlaceId, subPlaceId };

            // Act
            var result = await _service.GetPlaceDetailsAsync(mixedIds);

            // Assert
            Assert.That(result.Item2.Count, Is.GreaterThan(0), "Phải tìm thấy ít nhất một địa điểm");

            // Kiểm tra rằng service đã gọi đúng method cho main và sub places
            var foundIds = result.Item2.Select(p => p.PlaceId).ToList();

            Console.WriteLine($"✓ Regex pattern test với dữ liệu thật thành công");
            Console.WriteLine($"  Main place: {mainPlaceId} - Found: {foundIds.Contains(mainPlaceId)}");
            Console.WriteLine($"  Sub place: {subPlaceId} - Found: {foundIds.Contains(subPlaceId)}");
        }

        /// <summary>
        /// Test với dữ liệu thật từ database qua GetPlacesDetailsByIdsAsync
        /// </summary>
        [Test]
        public async Task GetPlacesDetailsByIdsAsync_WithRealData_ShouldReturnCorrectResult()
        {
            // Arrange - Lấy một số place IDs thật
            var searchResponse = await _elasticClient.SearchAsync<PlacesEs>(s => s
                .Index(_testIndex)
                .Size(5)
                .Query(q => q.Exists(e => e.Field("placeId")))
            );

            if (!searchResponse.IsSuccess() || !searchResponse.Documents.Any())
            {
                Assert.Ignore("Không tìm thấy dữ liệu để test GetPlacesDetailsByIdsAsync");
                return;
            }

            var realPlaceIds = searchResponse.Documents
                .Where(d => !string.IsNullOrEmpty(d.PlaceId))
                .Select(d => d.PlaceId)
                .Take(3)
                .ToList();

            // Act
            var stopwatch = Stopwatch.StartNew();
            var result = await _service.GetPlacesDetailsByIdsAsync(realPlaceIds);
            stopwatch.Stop();

            // Assert
            Assert.That(result, Is.Not.Null, "Kết quả không được null");
            Assert.That(result.Count, Is.GreaterThan(0), "Phải có ít nhất một địa điểm được tìm thấy");

            // Kiểm tra dữ liệu thật
            foreach (var place in result)
            {
                Assert.That(place.PlaceId, Is.Not.Null.And.Not.Empty, "PlaceId không được null hoặc rỗng");
                Assert.That(place.FullAddress, Is.Not.Null.And.Not.Empty, "FullAddress không được null hoặc rỗng");
            }

            Console.WriteLine($"✓ GetPlacesDetailsByIdsAsync test với dữ liệu thật thành công: {result.Count} địa điểm được trả về trong {stopwatch.ElapsedMilliseconds}ms");
            foreach (var place in result)
            {
                Console.WriteLine($"  - {place.Name}: {place.FullAddress}");
            }
        }

        /// <summary>
        /// Test với danh sách trống
        /// </summary>
        [Test]
        public async Task GetPlacesDetailsByIdsAsync_WithEmptyList_ShouldReturnEmptyList()
        {
            // Arrange
            var emptyList = new List<string>();

            // Act
            var result = await _service.GetPlacesDetailsByIdsAsync(emptyList);

            // Assert
            Assert.That(result, Is.Not.Null, "Kết quả không được null");
            Assert.That(result.Count, Is.EqualTo(0), "Kết quả phải là danh sách trống");

            Console.WriteLine("✓ Test với danh sách trống thành công");
        }

        /// <summary>
        /// Test với place ID cụ thể - ChIJlU9_eQCrNTERgioKzjJWrNI_S_102033266
        /// Nếu place ID này không tồn tại, test sẽ tìm place ID thật khác để test
        /// </summary>
        [Test]
        public async Task GetPlaceDetailsAsync_WithSpecificPlaceId_ShouldReturnCorrectResult()
        {
            // Arrange - Sử dụng place ID cụ thể hoặc lấy place ID thật nếu không tồn tại
            var specificPlaceId = "ChIJlU9_eQCrNTERgioKzjJWrNI_S_102033266";
            string[] placeIds;

            try
            {
                // Thử với place ID cụ thể trước
                placeIds = new[] { specificPlaceId };

                // Kiểm tra nếu place ID này tồn tại bằng cách tìm kiếm trực tiếp
                var testSearchResponse = await _elasticClient.SearchAsync<PlacesEs>(s => s
                    .Index(_testIndex)
                    .Size(1)
                    .Query(q => q.Term(t => t.Field("placeId").Value(specificPlaceId)))
                );

                // Nếu không tìm thấy, lấy place ID thật từ index
                if (!testSearchResponse.IsSuccess() || !testSearchResponse.Documents.Any())
                {
                    Console.WriteLine($"Place ID {specificPlaceId} không tồn tại, sẽ lấy place ID thật từ index");

                    var realPlaceIds = await GetRealPlaceIds(1);
                    if (realPlaceIds.Length == 0)
                    {
                        Assert.Ignore("Không tìm thấy place ID nào để test");
                        return;
                    }
                    placeIds = realPlaceIds;
                    specificPlaceId = placeIds[0]; // Cập nhật specificPlaceId để sử dụng trong assert
                }
            }
            catch (Exception)
            {
                // Nếu có lỗi, fallback sang place ID thật
                var realPlaceIds = await GetRealPlaceIds(1);
                if (realPlaceIds.Length == 0)
                {
                    Assert.Ignore("Không tìm thấy place ID nào để test");
                    return;
                }
                placeIds = realPlaceIds;
                specificPlaceId = placeIds[0];
            }

            // Act
            var stopwatch = Stopwatch.StartNew();
            var result = await _service.GetPlaceDetailsAsync(placeIds);
            stopwatch.Stop();

            // Assert
            Assert.That(result, Is.Not.Null, "Kết quả không được null");
            Assert.That(result.Item1, Is.Not.Null, "Địa điểm đầu tiên không được null");
            Assert.That(result.Item2, Is.Not.Null, "Danh sách địa điểm không được null");
            Assert.That(result.Item2.Count, Is.EqualTo(1), "Phải có đúng 1 địa điểm được trả về");

            var place = result.Item1;
            var placeInList = result.Item2.First();

            // Kiểm tra place ID khớp với input
            Assert.That(place.PlaceId, Is.EqualTo(specificPlaceId),
                $"PlaceId phải khớp với input: {specificPlaceId}");
            Assert.That(placeInList.PlaceId, Is.EqualTo(specificPlaceId),
                $"PlaceId trong danh sách phải khớp với input: {specificPlaceId}");

            // Kiểm tra rằng địa điểm đầu tiên và trong danh sách là cùng một đối tượng
            Assert.That(place.PlaceId, Is.EqualTo(placeInList.PlaceId),
                "Địa điểm đầu tiên phải trùng với item trong danh sách");

            // Kiểm tra các thuộc tính cơ bản của địa điểm
            Assert.That(place.PlaceId, Is.Not.Null.And.Not.Empty, "PlaceId không được null hoặc rỗng");
            Assert.That(place.FullAddress, Is.Not.Null.And.Not.Empty, "FullAddress không được null hoặc rỗng");
            Assert.That(place.Name, Is.Not.Null.And.Not.Empty, "Name không được null hoặc rỗng");

            // Kiểm tra location nếu có
            if (place.Location != null)
            {
                Assert.That(place.Location.Lat, Is.GreaterThan(0), "Latitude phải lớn hơn 0");
                Assert.That(place.Location.Lon, Is.GreaterThan(0), "Longitude phải lớn hơn 0");
            }

            // Kiểm tra xem có phải là sub place không dựa trên pattern _S_
            var isSubPlace = _subPlaceRegex.IsMatch(specificPlaceId);
            Console.WriteLine($"✓ Test với place ID cụ thể thành công: {place.PlaceId}");
            Console.WriteLine($"  - Tên: {place.Name}");
            Console.WriteLine($"  - Địa chỉ: {place.FullAddress}");
            Console.WriteLine($"  - Loại: {(isSubPlace ? "Sub Place" : "Main Place")}");
            Console.WriteLine($"  - Thời gian xử lý: {stopwatch.ElapsedMilliseconds}ms");

            // Log thêm thông tin chi tiết nếu có
            if (place.Location != null)
            {
                Console.WriteLine($"  - Tọa độ: {place.Location.Lat}, {place.Location.Lon}");
            }
            Console.WriteLine($"  - Type: {place.Type}");
            Console.WriteLine($"  - Popular: {place.Popular}");
        }

        /// <summary>
        /// Test với place ID cụ thể bằng method GetPlacesDetailsByIdsAsync
        /// Nếu place ID không tồn tại, test sẽ tìm place ID thật khác để test
        /// </summary>
        [Test]
        public async Task GetPlacesDetailsByIdsAsync_WithSpecificPlaceId_ShouldReturnCorrectResult()
        {
            // Arrange - Sử dụng place ID cụ thể hoặc lấy place ID thật nếu không tồn tại
            var specificPlaceId = "ChIJlU9_eQCrNTERgioKzjJWrNI_S_102033266";
            List<string> placeIds;

            try
            {
                // Thử với place ID cụ thể trước
                placeIds = new List<string> { specificPlaceId };

                // Kiểm tra nếu place ID này tồn tại bằng cách gọi service
                var testResult = await _service.GetPlacesDetailsByIdsAsync(new List<string> { specificPlaceId });

                // Nếu không tìm thấy, lấy place ID thật từ service
                if (testResult == null || testResult.Count == 0)
                {
                    Console.WriteLine($"Place ID {specificPlaceId} không tồn tại, sẽ lấy place ID thật từ service");

                    var realPlaceIds = await GetRealPlaceIdsFromService(1);
                    if (realPlaceIds.Length == 0)
                    {
                        Assert.Ignore("Không tìm thấy place ID nào để test");
                        return;
                    }
                    placeIds = realPlaceIds.ToList();
                    specificPlaceId = placeIds[0]; // Cập nhật specificPlaceId để sử dụng trong assert
                }
            }
            catch (Exception ex)
            {
                // Nếu có lỗi, fallback sang place ID thật từ service
                Console.WriteLine($"Lỗi khi kiểm tra place ID cụ thể: {ex.Message}");
                var realPlaceIds = await GetRealPlaceIdsFromService(1);
                if (realPlaceIds.Length == 0)
                {
                    Assert.Ignore("Không tìm thấy place ID nào để test");
                    return;
                }
                placeIds = realPlaceIds.ToList();
                specificPlaceId = placeIds[0];
            }

            // Act
            var stopwatch = Stopwatch.StartNew();
            var result = await _service.GetPlacesDetailsByIdsAsync(placeIds);
            stopwatch.Stop();

            // Assert
            Assert.That(result, Is.Not.Null, "Kết quả không được null");
            Assert.That(result.Count, Is.EqualTo(1), "Phải có đúng 1 địa điểm được trả về");

            var place = result.First();

            // Kiểm tra place ID khớp với input
            Assert.That(specificPlaceId.StartsWith(place.PlaceId), Is.True, $"PlaceId phải khớp với input: {specificPlaceId}");

            // Kiểm tra các thuộc tính cơ bản của địa điểm
            Assert.That(place.PlaceId, Is.Not.Null.And.Not.Empty, "PlaceId không được null hoặc rỗng");
            Assert.That(place.FullAddress, Is.Not.Null.And.Not.Empty, "FullAddress không được null hoặc rỗng");
            Assert.That(place.Name, Is.Not.Null.And.Not.Empty, "Name không được null hoặc rỗng");

            Console.WriteLine($"✓ Test GetPlacesDetailsByIdsAsync với place ID cụ thể thành công");
            Console.WriteLine($"  - PlaceId: {place.PlaceId}");
            Console.WriteLine($"  - Tên: {place.Name}");
            Console.WriteLine($"  - Địa chỉ: {place.FullAddress}");
            Console.WriteLine($"  - Thời gian xử lý: {stopwatch.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// Test so sánh kết quả giữa GetPlaceDetailsAsync và GetPlacesDetailsByIdsAsync với cùng place ID
        /// Nếu place ID không tồn tại, test sẽ tìm place ID thật khác để test
        /// </summary>
        [Test]
        public async Task CompareMethods_WithSpecificPlaceId_ShouldReturnSameResult()
        {
            // Arrange - Sử dụng place ID cụ thể hoặc lấy place ID thật nếu không tồn tại
            var specificPlaceId = "ChIJlU9_eQCrNTERgioKzjJWrNI_S_102033266";

            try
            {
                // Kiểm tra nếu place ID này tồn tại bằng cách tìm kiếm trực tiếp
                var testSearchResponse = await _elasticClient.SearchAsync<PlacesEs>(s => s
                    .Index(_testIndex)
                    .Size(1)
                    .Query(q => q.Term(t => t.Field("placeId").Value(specificPlaceId)))
                );

                // Nếu không tìm thấy, lấy place ID thật từ index
                if (!testSearchResponse.IsSuccess() || !testSearchResponse.Documents.Any())
                {
                    Console.WriteLine($"Place ID {specificPlaceId} không tồn tại, sẽ lấy place ID thật từ index");

                    var realPlaceIds = await GetRealPlaceIds(1);
                    if (realPlaceIds.Length == 0)
                    {
                        Assert.Ignore("Không tìm thấy place ID nào để test");
                        return;
                    }
                    specificPlaceId = realPlaceIds[0]; // Cập nhật specificPlaceId
                }
            }
            catch (Exception)
            {
                // Nếu có lỗi, fallback sang place ID thật
                var realPlaceIds = await GetRealPlaceIds(1);
                if (realPlaceIds.Length == 0)
                {
                    Assert.Ignore("Không tìm thấy place ID nào để test");
                    return;
                }
                specificPlaceId = realPlaceIds[0];
            }

            // Act - Gọi cả hai method
            var stopwatch1 = Stopwatch.StartNew();
            var result1 = await _service.GetPlaceDetailsAsync(specificPlaceId);
            stopwatch1.Stop();

            var stopwatch2 = Stopwatch.StartNew();
            var result2 = await _service.GetPlacesDetailsByIdsAsync(new List<string> { specificPlaceId });
            stopwatch2.Stop();

            // Assert - So sánh kết quả
            Assert.That(result1, Is.Not.Null, "Kết quả GetPlaceDetailsAsync không được null");
            Assert.That(result2, Is.Not.Null, "Kết quả GetPlacesDetailsByIdsAsync không được null");
            Assert.That(result1.Item2.Count, Is.EqualTo(result2.Count), "Số lượng địa điểm trả về phải bằng nhau");

            if (result1.Item2.Any() && result2.Any())
            {
                var place1 = result1.Item1;
                var place2 = result2.First();

                // So sánh các thuộc tính chính
                Assert.That(place1.PlaceId, Is.EqualTo(place2.PlaceId), "PlaceId phải giống nhau");
                Assert.That(place1.Name, Is.EqualTo(place2.Name), "Name phải giống nhau");
                Assert.That(place1.FullAddress, Is.EqualTo(place2.FullAddress), "FullAddress phải giống nhau");

                // So sánh location nếu có
                if (place1.Location != null && place2.Location != null)
                {
                    Assert.That(place1.Location.Lat, Is.EqualTo(place2.Location.Lat), "Latitude phải giống nhau");
                    Assert.That(place1.Location.Lon, Is.EqualTo(place2.Location.Lon), "Longitude phải giống nhau");
                }

                Console.WriteLine($"✓ Test so sánh methods thành công cho place ID: {specificPlaceId}");
                Console.WriteLine($"  - GetPlaceDetailsAsync: {stopwatch1.ElapsedMilliseconds}ms");
                Console.WriteLine($"  - GetPlacesDetailsByIdsAsync: {stopwatch2.ElapsedMilliseconds}ms");
                Console.WriteLine($"  - Tên: {place1.Name}");
                Console.WriteLine($"  - Địa chỉ: {place1.FullAddress}");
            }
            else
            {
                Assert.Fail($"Không tìm thấy địa điểm với ID: {specificPlaceId}");
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Lấy một số place IDs thật từ Elasticsearch để sử dụng trong test
        /// </summary>
        private async Task<string[]> GetRealPlaceIds(int count = 5)
        {
            var searchResponse = await _elasticClient.SearchAsync<PlacesEs>(s => s
                .Index(_testIndex)
                .Size(count)
                .Query(q => q.Exists(e => e.Field("placeId")))
            );

            if (!searchResponse.IsSuccess() || !searchResponse.Documents.Any())
            {
                return Array.Empty<string>();
            }

            return searchResponse.Documents
                .Where(d => !string.IsNullOrEmpty(d.PlaceId))
                .Select(d => d.PlaceId)
                .ToArray();
        }

        /// <summary>
        /// Lấy một số place IDs thật từ service để sử dụng trong test
        /// </summary>
        private async Task<string[]> GetRealPlaceIdsFromService(int count = 5)
        {
            try
            {
                // Lấy dữ liệu từ Elasticsearch thông qua service
                var searchResponse = await _elasticClient.SearchAsync<PlacesEs>(s => s
                    .Index(_testIndex)
                    .Size(count)
                    .Query(q => q.Exists(e => e.Field("placeId")))
                );

                if (!searchResponse.IsSuccess() || !searchResponse.Documents.Any())
                {
                    return Array.Empty<string>();
                }

                var placeIds = searchResponse.Documents
                    .Where(d => !string.IsNullOrEmpty(d.PlaceId))
                    .Select(d => d.PlaceId)
                    .Take(count)
                    .ToList();

                // Kiểm tra các place IDs này có tồn tại trong service không
                var validPlaceIds = new List<string>();
                foreach (var placeId in placeIds)
                {
                    try
                    {
                        var result = await _service.GetPlacesDetailsByIdsAsync(new List<string> { placeId });
                        if (result != null && result.Count > 0)
                        {
                            validPlaceIds.Add(placeId);
                        }
                    }
                    catch
                    {
                        // Bỏ qua place ID không hợp lệ
                        continue;
                    }
                }

                return validPlaceIds.Take(count).ToArray();
            }
            catch
            {
                return Array.Empty<string>();
            }
        }

        /// <summary>
        /// Kiểm tra kết nối đến Elasticsearch
        /// </summary>
        private async Task<bool> CheckElasticsearchConnection()
        {
            try
            {
                var response = await _elasticClient.PingAsync();
                return response.IsSuccess();
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }
}