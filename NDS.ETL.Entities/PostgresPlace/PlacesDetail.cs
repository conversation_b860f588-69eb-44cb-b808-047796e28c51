using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Keyless]
[Table("places_details")]
[Index("PlaceFkId", Name = "IX_places_details_placeFkId")]
[Index("Rating", Name = "IX_places_details_rating")]
[Index("ReviewCount", Name = "IX_places_details_reviewCount")]
public partial class PlacesDetail
{
    [Column("id")]
    public long Id { get; set; }

    [Column("placeFkId")]
    public long? PlaceFkId { get; set; }

    [Column("placeId")]
    public string? PlaceId { get; set; }

    [Column("imageUrl")]
    [StringLength(255)]
    public string? ImageUrl { get; set; }

    [Column("rating")]
    public double? Rating { get; set; }

    [Column("reviewCount")]
    public int? ReviewCount { get; set; }

    [Column("types", TypeName = "jsonb")]
    public string? Types { get; set; }

    [Column("address_components", TypeName = "jsonb")]
    public string? AddressComponents { get; set; }

    [Column("createdAt", TypeName = "timestamp without time zone")]
    public DateTime CreatedAt { get; set; }

    [Column("updatedAt", TypeName = "timestamp without time zone")]
    public DateTime? UpdatedAt { get; set; }
}
