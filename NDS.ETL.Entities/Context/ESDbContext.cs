using Microsoft.EntityFrameworkCore;
using NDS.ETL.Entities.ESEntities;
using NDS.ETL.Entities.PostgresPlace;
using Microsoft.EntityFrameworkCore.Design;
using System.IO;
using Microsoft.Extensions.Configuration;

namespace NDS.ETL.Entities.Context;

public partial class ESDbContext : DbContext
{
    public ESDbContext()
    {
    }

    public ESDbContext(DbContextOptions<ESDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<ESEntities.ApiKey> ApiKeys { get; set; } = null!;
    public virtual DbSet<ESEntities.ApiKeyPermission> ApiKeyPermissions { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<ESEntities.ApiKey>(entity =>
        {
            entity.HasIndex(e => e.Key).IsUnique(); // Add unique index for Key
            entity.HasIndex(e => e.IsActive); // Add index for IsActive
        });

        modelBuilder.Entity<ESEntities.ApiKeyPermission>(entity =>
        {
            entity.HasIndex(e => e.PermissionName); // Add index for PermissionName
        });
        
        modelBuilder.HasAnnotation(
            "Relational:MigrationHistoryTable", "__EFMigrationsHistory_ESDbContext"
        );
        
        base.OnModelCreating(modelBuilder);
    }
}

// Separate the factory into its own class
public class ESDbContextFactory : IDesignTimeDbContextFactory<ESDbContext>
{
    public ESDbContext CreateDbContext(string[] args)
    {
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: true)
            .AddJsonFile("appsettings.Development.json", optional: true)
            .Build();

        var optionsBuilder = new DbContextOptionsBuilder<ESDbContext>();
        var connectionString = configuration.GetConnectionString("PgPlace");
        //var connectionString = "Host=************;Port=5444;Database=******;Username=******;Password=******;Timeout=600;Command Timeout=10000";
        optionsBuilder.UseNpgsql(connectionString, x =>
        {
            x.UseNetTopologySuite();
            x.MigrationsHistoryTable("__EFMigrationsHistory_ESDbContext");
        });

        return new ESDbContext(optionsBuilder.Options);
    }
}
