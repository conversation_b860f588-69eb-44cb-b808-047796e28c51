using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("wards_bridge")]
[Index("WardsNewCode", Name = "wards_bridge_wards_new_code_idx")]
[Index("WardsOldCode", Name = "wards_bridge_wards_old_code_idx")]
public partial class WardsBridge
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    /// <summary>
    /// Ward Code cũ trước 1/7/2025
    /// </summary>
    [Column("wards_old_code", TypeName = "character varying")]
    public string WardsOldCode { get; set; } = null!;

    /// <summary>
    /// wards code mới từ ngày 1/7/2025 từ bảng ward_new
    /// </summary>
    [Column("wards_new_code", TypeName = "character varying")]
    public string WardsNewCode { get; set; } = null!;

    [ForeignKey("WardsNewCode")]
    [InverseProperty("WardsBridges")]
    public virtual WardNew WardsNewCodeNavigation { get; set; } = null!;

    [ForeignKey("WardsOldCode")]
    [InverseProperty("WardsBridges")]
    public virtual Ward WardsOldCodeNavigation { get; set; } = null!;
}
