using Elastic.Clients.Elasticsearch.Mapping;
using KnowledgeBase.Core.Extensions;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch.Dto;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Infrastructure.Extensions;

namespace NDS.ETL.BackgroundJobs.Model.ElasticSearch;

public class PlacePairType
{
    public const int Pickup = 0;
    public const int Arrival = 1;
}

public class PlacePairEs : IElasticSearchModel, IPlaceEsConvertible
{
    public string Id { get; set; }
    public long BookingPlacePairId { get; set; }
    public Location Location { get; set; }
    public string? CustomerId { get; set; }
    public string? CustomerPhone { get; set; }
    public string PlaceId { get; set; }
    public string FullAddress { get; set; }


    public string MasterAddress
    {
        get
        {
            string str = FullAddress;
            ProcessKeywords();
            var keywordProcesed = !string.IsNullOrWhiteSpace(Keywords) ? $"{str}, {Keywords}" : str;
            AddressPermuted = AddressPermutator.GenerateOptimizedPermutations(keywordProcesed, 15);

            var insertItems = VietnameseAddressPermutator.GenerateOptimizedPermutations(keywordProcesed, 3);
            AddressPermuted.InsertRange(0, insertItems);
            return str;
        }
    }

    public List<string> AddressPermuted { get; set; }

    public void ProcessKeywords()
    {
        string? keywords = Keywords;
        string fullAddress = FullAddress;
        if (string.IsNullOrEmpty(keywords)) return;

        var keywordList = keywords.Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(k => k.Trim())
            .Where(k => !string.IsNullOrEmpty(k))
            .ToList();

        // Loại bỏ các từ khóa trùng lặp không dấu
        var uniqueKeywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
        var result = new List<string>();

        foreach (var keyword in keywordList)
        {
            var unsignedKeyword = keyword.ToUnsign().ToLower();
            if (!uniqueKeywords.Contains(unsignedKeyword) &&
                !string.IsNullOrEmpty(fullAddress) &&
                !fullAddress.ToUnsign().ToLower().Contains(unsignedKeyword))
            {
                uniqueKeywords.Add(unsignedKeyword);
                result.Add(keyword);
            }
        }

        if (result.Any())
        {
            Keywords = string.Join(",", result);
        }
    }

    public string Keywords { get; set; }

    public int SearchCount { get; set; }
    public int BookingCount { get; set; }
    public int Type { get; set; }

    public DateTime CreatedAt { get; set; }
    public DateTime UpdateAt { get; set; }
    public string? LastEstimateAmount { get; set; }
    public DateTime? LastBookingAt { get; set; }
    public DateTime? LastSearchAt { get; set; }
    public string? LastNote { get; set; }


    public static PlacePairEs CreateHistoryBooking(BookingPlacePairDto item, bool isPickup)
    {
        string fullAddress;
        if (isPickup)
        {
            fullAddress = item.FromFullAddress;
            if (!string.IsNullOrWhiteSpace(item.FromAddress) && !fullAddress.StartsWith(item.FromAddress))
            {
                fullAddress = $"{item.FromAddress}, {fullAddress}";
            }
        }
        else
        {
            fullAddress = item.ToFullAddress;
            if (!string.IsNullOrWhiteSpace(item.ToAddress) && !fullAddress.StartsWith(item.ToAddress))
            {
                fullAddress = $"{item.ToAddress}, {fullAddress}";
            }
        }
        var placePairEs = new PlacePairEs
        {
            Id = $"{(isPickup ? "P" : "A")}{item.Id}",
            BookingPlacePairId = item.Id,
            PlaceId = isPickup ? item.PickupPlaceId : item.ArrivalPlaceId,
            CustomerId = item.CustomerId,
            CustomerPhone = item.CustomerPhone,
            FullAddress = fullAddress,
            Location = new Location
            {
                Lat = (isPickup ? item.Location1?.Y : item.Location2?.Y) ?? 0.0,
                Lon = (isPickup ? item.Location1?.X : item.Location2?.X) ?? 0.0
            },
            CreatedAt = (DateTime)item.CreatedAt,
            UpdateAt = (DateTime)item.UpdateAt,
            LastEstimateAmount = item.LastEstimateAmount,
            LastBookingAt = item.LastBookingAt,
            LastSearchAt = item.LastSearchAt,
            Type = isPickup ? 0 : 1,
            LastNote = item.LastNote,
            SearchCount = item.SearchCount,
            BookingCount = item.BookingCount
        };
        placePairEs.ProcessKeywords();
        return placePairEs;
    }

    public static void MappingProperties(PropertiesDescriptor<PlacePairEs> d)
    {
        d
            .Keyword(k => k.Id)
            .LongNumber(k => k.BookingPlacePairId)
            .GeoPoint(g => g.Location)
            .Text(t => t.CustomerId,
                t =>
                    t.Analyzer("icu")
                    .Fields(ff => ff
                        .Keyword("keyword",
                            st => st.IgnoreAbove(256))
                    ))
            .Text(t => t.CustomerPhone,
                t =>
                    t.Analyzer("icu")
                    .Fields(ff => ff
                        .Keyword("keyword",
                            st => st.IgnoreAbove(256))
                    ))
            .Text(t => t.FullAddress,
                t =>
                    t.Analyzer("icu")
                    .Fields(ff => ff
                        .Keyword("keyword",
                            st => st.IgnoreAbove(256))
                    ))
            .Keyword(k => k.PlaceId)
            .IntegerNumber(k => k.BookingCount)
            .IntegerNumber(k => k.Type)
            .Date(d => d.CreatedAt)
            .Date(d => d.UpdateAt)
            .Date(d => d.LastBookingAt)
            .Date(d => d.LastSearchAt)
            .Text(t => t.LastEstimateAmount)
            .Text(t => t.LastNote, t => t.Analyzer("icu"));
    }

    public PlacesEs ToPlacesEs()
    {
        return new PlacesEs
        {
            Id = BookingPlacePairId,
            PlaceId = PlaceId,
            FullAddress = FullAddress,
            Location = Location,
            Keywords = Keywords,
            BookingCount = BookingCount,
            Type = Type,
            CreatedAt = CreatedAt,
            UpdatedAt = UpdateAt,
            Active = true, // Assuming PlacePairEs is always active
            Removed = false, // Assuming PlacePairEs is not removed
            Popular = false, // Default value
            Processed = false, // Default value
            Hidden = false, // Default value
            Tags = null, // Default value
        };
    }
}
