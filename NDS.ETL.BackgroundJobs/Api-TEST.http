@NDS.ETL.BackgroundJobs_HostAddress = http://localhost:5012
@apiKey = {{API_KEY}}
@phoneNo = {{PHONE_NO}}
@pathBase = /backgroundjob

### Autocomplete API từ ESController
# API tìm kiếm địa điểm theo văn bản và tọa độ
GET {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/api/ES/autocomplete?query=22+lang+ha&latitude=21.021673&longitude=105.803715&radius=50.0&limit=10
X-Api-Key: {{apiKey}}
Accept: application/json

### Nearby API từ ESController
# API tìm kiếm địa điểm gần đó dựa trên tọa độ và bán kính
GET {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/api/ES/nearby?latitude=21.021673&longitude=105.803715&radius=1000&limit=5
X-Api-Key: {{apiKey}}
X-Phone-Number: {{phoneNo}}
Accept: application/json

### Geocoding API từ ESController
# API chuyển đổi địa chỉ văn bản thành tọa độ
GET {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/api/ES/geocoding?address=22+lang+ha
X-Api-Key: {{apiKey}}
Accept: application/json

### RecommendPlaces API từ ESController
# API lấy các địa điểm được đề xuất dựa trên lịch sử và vị trí của người dùng
GET {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/api/ES/recommendPlaces?phoneNumber={{phoneNo}}&latitude=21.021673&longitude=105.803715&limit=10&page=0
X-Api-Key: {{apiKey}}
X-Phone-Number: {{phoneNo}}
Accept: application/json

### RecommendSearch API từ ESController
# API lấy các từ khóa tìm kiếm được đề xuất dựa trên lịch sử tìm kiếm của người dùng
GET {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/api/ES/recommendSearch?phoneNumber={{phoneNo}}&latitude=21.021673&longitude=105.803715&limit=10
X-Api-Key: {{apiKey}}
X-Phone-Number: {{phoneNo}}
Accept: application/json

### RecommendRoutes API từ ESController
# API lấy các tuyến đường được đề xuất dựa trên lịch sử di chuyển của người dùng
GET {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/api/ES/recommendRoutes?phoneNumber={{phoneNo}}&latitude=21.021673&longitude=105.803715&limit=10&page=1
X-Api-Key: {{apiKey}}
X-Phone-Number: {{phoneNo}}
Accept: application/json

### Clear API keys cache
# API xóa cache cho API keys
POST {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/api/ES/clearApiKeysCache
X-Api-Key: {{apiKey}}
Accept: application/json

### Test API placeDetails từ ESController
# API lấy chi tiết địa điểm theo danh sách placeIds (dạng GET)
GET {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/api/ES/placeDetails?placeIds=ChIJESRVlXSrNTERN5qrIEZLL7Q_S_1771523190,ChIJYXhLT0CtNTERodtRW1xxnSk,ChIJlU9_eQCrNTERgioKzjJWrNI_S_102033266,ChIJq6oaDeqrNTERQJqaPRwP3as
X-Api-Key: {{apiKey}}


### RecurringJob API từ HomeController
# API test cho RecurringJob
GET {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/RecurringJob
Authorization: Basic ZXNiOmVzYkAyMDI1
Accept: application/json

### Get Duplicate Address từ HomeController
# API lấy các địa chỉ trùng lặp theo district ID
GET {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/GetDuplicateAddress?districtId=1
Authorization: Basic ZXNiOmVzYkAyMDI1
Accept: application/json

### Insert To Scan Result từ HomeController
# API chèn địa điểm vào bảng kết quả quét
GET {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/InsertToScanResult?districtId=1
Authorization: Basic ZXNiOmVzYkAyMDI1
Accept: application/json

### Revert Data Updated từ HomeController
# API hoàn tác dữ liệu đã cập nhật
GET {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/RevertDataUpdated
Authorization: Basic ZXNiOmVzYkAyMDI1
Accept: application/json

### Get Distance từ HomeController
# API lấy khoảng cách giữa hai địa điểm
GET {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/GetDistance?placeIdSource=abc123&placeIdDestination=def456
Authorization: Basic ZXNiOmVzYkAyMDI1
Accept: application/json

### Compare Score từ HomeController
# API so sánh điểm tương đồng giữa hai chuỗi
GET {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/CompareScore?source=22+lang+ha
Authorization: Basic ZXNiOmVzYkAyMDI1
Accept: application/json

### Compare Verify từ HomeController
# API so sánh xác minh
GET {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/CompareVerify
Authorization: Basic ZXNiOmVzYkAyMDI1
Accept: application/json

### Test từ HomeController
# API Test kết nối với MongoDB
GET {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/Test
Authorization: Basic ZXNiOmVzYkAyMDI1
Accept: application/json

### Test Address Permutation từ HomeController
# API test xử lý hoán vị địa chỉ
GET {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/TestAddressPermutation?address=22+lang+ha
Authorization: Basic ZXNiOmVzYkAyMDI1
Accept: application/json

### Search Places từ HomeController
# API tìm kiếm địa điểm
POST {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/SearchPlaces
Authorization: Basic ZXNiOmVzYkAyMDI1
Content-Type: application/json

{
  "query": "coffee",
  "lat": 21.021673,
  "lng": 105.803715,
  "distance": 5000,
  "limit": 10
}

### Check Address Level từ HomeController
# API kiểm tra cấp độ địa chỉ
POST {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/CheckAddressLevel
Authorization: Basic ZXNiOmVzYkAyMDI1
Content-Type: application/json

{
  "query": "123 Nguyễn Huệ, Quận 1, TP.HCM",
  "lat": 10.772944,
  "lng": 106.698746,
  "distance": 5000,
  "limit": 5
}

### Check Address Level 2 từ HomeController
# API kiểm tra cấp độ địa chỉ từ danh sách chuỗi
POST {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/CheckAddressLevel2
Authorization: Basic ZXNiOmVzYkAyMDI1
Content-Type: application/json

[
  "123 Nguyễn Huệ, Quận 1, TP.HCM",
  "456 Lê Lợi, Phường Bến Thành, Quận 1, TP.HCM",
  "789 Đường Đồng Khởi, Quận 1"
]

### Kiểm tra trạng thái hệ thống
GET {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/status
Accept: application/json

### Xem dashboard Hangfire
GET {{NDS.ETL.BackgroundJobs_HostAddress}}{{pathBase}}/hangfire
Authorization: Basic ZXNiOmVzYkAyMDI1
Accept: text/html

