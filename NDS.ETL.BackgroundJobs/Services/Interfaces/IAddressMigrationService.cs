using NDS.ETL.BackgroundJobs.Models;

namespace NDS.ETL.BackgroundJobs.Services.Interfaces;

/// <summary>
/// Service interface for handling address migration operations
/// </summary>
public interface IAddressMigrationService
{
    /// <summary>
    /// Start the address migration process
    /// </summary>
    /// <param name="migrationId">Unique identifier for this migration run</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Migration progress information</returns>
    Task<MigrationProgress> StartMigrationAsync(string migrationId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Resume a previously interrupted migration
    /// </summary>
    /// <param name="migrationId">Migration identifier to resume</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Migration progress information</returns>
    Task<MigrationProgress> ResumeMigrationAsync(string migrationId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Pause the currently running migration
    /// </summary>
    /// <param name="migrationId">Migration identifier to pause</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if successfully paused</returns>
    Task<bool> PauseMigrationAsync(string migrationId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancel the currently running migration
    /// </summary>
    /// <param name="migrationId">Migration identifier to cancel</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if successfully cancelled</returns>
    Task<bool> CancelMigrationAsync(string migrationId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get the current progress of a migration
    /// </summary>
    /// <param name="migrationId">Migration identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Current migration progress</returns>
    Task<MigrationProgress?> GetMigrationProgressAsync(string migrationId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all migration progress records
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of all migration progress records</returns>
    Task<List<MigrationProgress>> GetAllMigrationProgressAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Migrate a specific batch of places
    /// </summary>
    /// <param name="startId">Starting place ID</param>
    /// <param name="batchSize">Number of records to process</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of successfully migrated records</returns>
    Task<int> MigrateBatchAsync(long startId, int batchSize, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate migrated data for a specific place
    /// </summary>
    /// <param name="placeId">Place ID to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if validation passes</returns>
    Task<bool> ValidateMigratedDataAsync(long placeId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get migration statistics
    /// </summary>
    /// <param name="migrationId">Migration identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Migration statistics</returns>
    Task<MigrationStatistics> GetMigrationStatisticsAsync(string migrationId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Clean up completed or failed migrations older than specified days
    /// </summary>
    /// <param name="olderThanDays">Number of days</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of cleaned up records</returns>
    Task<int> CleanupOldMigrationsAsync(int olderThanDays, CancellationToken cancellationToken = default);
}

/// <summary>
/// Migration statistics
/// </summary>
public class MigrationStatistics
{
    public string MigrationId { get; set; } = string.Empty;
    public long TotalRecords { get; set; }
    public long ProcessedRecords { get; set; }
    public long SuccessfulRecords { get; set; }
    public long FailedRecords { get; set; }
    public long SkippedRecords { get; set; }
    public TimeSpan TotalDuration { get; set; }
    public double AverageProcessingRate { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public Dictionary<string, int> ErrorCounts { get; set; } = new();
}
