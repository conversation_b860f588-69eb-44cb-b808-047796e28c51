using Elastic.Clients.Elasticsearch;
using KnowledgeBase.Core.UnitOfWork;
using NDS.ETL.BackgroundJobs.Context;
using NDS.ETL.Infrastructure.Common;
using Polly;
using System.Diagnostics;

namespace NDS.ETL.BackgroundJobs.RecurringJobs;

public abstract class RecurringJobBaseElastic(
    IConfiguration configuration,
    ILogger<RecurringJobBaseElastic> logger,
    ElasticsearchClient elasticClient,
    IScopedResolver<PgPlaceContext> dbContext)
    : RecurringJobBaseElastic<PgPlaceContext>(configuration, logger, elasticClient, dbContext);

public abstract class RecurringJobBaseElastic<TContext>(
    IConfiguration configuration,
    ILogger<RecurringJobBaseElastic<TContext>> logger,
    ElasticsearchClient elasticClient,
    IScopedResolver<TContext> dbContext)
    : RecurringJobBase<TContext>(configuration, logger, dbContext)
    where TContext : UnitOfWorkBase
{
    protected virtual int RetryCount => 5;

    // Các hằng số từ InitPlaceJobs
    protected const int MAX_RETRIES = 5; // Số lần retry tối đa cho ES operations
    protected const int RETRY_DELAY_MS = 10000; // Thời gian delay giữa các lần retry

    public override string ConfigSection => "JobESConfigs";

    /// <summary>
    /// Tìm và xác định loại lỗi để ghi log tốt hơn
    /// </summary>
    protected string DetermineErrorType(Exception ex)
    {
        if (ex.Message.Contains("timeout") || ex.Message.Contains("The request was canceled"))
            return "Timeout";
        if (ex.Message.Contains("circuit_breaking_exception") || ex.Message.Contains("Data too large"))
            return "Circuit breaker (memory limit exceeded)";
        if (ex.Message.Contains("429") || ex.Message.Contains("rejected execution"))
            return "Rejected execution (429 Too Many Requests)";

        return "Unknown error";
    }

    /// <summary>
    /// Indexing với kiểm soát timeout rõ ràng
    /// </summary>
    protected async Task IndexWithTimeout<T>(List<T> items, CancellationToken cancellationToken, string indexName, int batchSize) where T : class
    {
        try
        {
            await IndexMany(items, cancellationToken, indexName, batchSize);
        }
        catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
        {
            logger.LogWarning("Indexing operation timed out or was cancelled");
            throw new TimeoutException("Elasticsearch indexing operation timed out");
        }
    }

    /// <summary>
    /// Xử lý một batch với cơ chế retry
    /// </summary>
    protected async Task ProcessBatchWithRetryAsync<T>(List<T> batch, CancellationToken stoppingToken, string indexName, int timeoutMinutes = 2) where T : class
    {
        int retryCount = 0;
        bool success = false;

        while (!success && retryCount < MAX_RETRIES)
        {
            try
            {
                // Tạo timeout-specific cancellation token
                using var timeoutCts = new CancellationTokenSource(TimeSpan.FromMinutes(timeoutMinutes));
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(timeoutCts.Token, stoppingToken);

                await IndexMany(batch, linkedCts.Token, indexName, batch.Count);
                success = true;
            }
            catch (Exception ex) when (
                ex.Message.Contains("429") ||
                ex.Message.Contains("rejected execution") ||
                ex.Message.Contains("es_rejected_execution_exception") ||
                ex.Message.Contains("circuit_breaking_exception") ||
                ex.Message.Contains("Data too large") ||
                ex.Message.Contains("timeout") ||
                ex.Message.Contains("The request was canceled"))
            {
                retryCount++;

                if (retryCount >= MAX_RETRIES)
                {
                    logger.LogError($"Failed to process batch after {MAX_RETRIES} attempts");
                    throw;
                }

                string errorType = DetermineErrorType(ex);
                logger.LogWarning($"Retrying batch processing after {errorType}... (Attempt {retryCount} of {MAX_RETRIES})");
                await Task.Delay(RETRY_DELAY_MS + (retryCount * 2000), stoppingToken);
            }
        }
    }

    /// <summary>
    /// Xử lý batch với cơ chế giảm kích thước batch nếu gặp lỗi
    /// </summary>
    protected async Task ProcessBatchWithRetryAndResizeAsync<T>(
        List<T> batch,
        CancellationToken stoppingToken,
        string indexName,
        int timeoutMinutes = 5,
        Action<int> onBatchProcessed = null) where T : class
    {
        int retryCount = 0;
        int currentBatchSize = batch.Count;
        bool success = false;
        List<T> currentBatch = batch;

        while (!success && retryCount < MAX_RETRIES)
        {
            try
            {
                // Tạo timeout-specific cancellation token
                using var timeoutCts = new CancellationTokenSource(TimeSpan.FromMinutes(timeoutMinutes));
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(timeoutCts.Token, stoppingToken);

                await IndexWithTimeout(currentBatch, linkedCts.Token, indexName, currentBatchSize);
                success = true;

                // Gọi callback để thông báo số lượng items đã xử lý thành công
                onBatchProcessed?.Invoke(currentBatch.Count);
            }
            catch (Exception ex) when (
                ex.Message.Contains("429") ||
                ex.Message.Contains("rejected execution") ||
                ex.Message.Contains("es_rejected_execution_exception") ||
                ex.Message.Contains("circuit_breaking_exception") ||
                ex.Message.Contains("Data too large") ||
                ex.Message.Contains("timeout") ||
                ex.Message.Contains("The request was canceled"))
            {
                retryCount++;

                // Xử lý lỗi timeout
                if (ex.Message.Contains("timeout") || ex.Message.Contains("The request was canceled"))
                {
                    logger.LogWarning($"Request timeout detected. Reducing batch size and retrying.");

                    // Giảm kích thước batch đáng kể khi timeout
                    currentBatchSize = Math.Max(100, currentBatchSize / 4);
                }
                // Xử lý lỗi circuit breaking
                else if (ex.Message.Contains("circuit_breaking_exception") ||
                        ex.Message.Contains("Data too large"))
                {
                    // Giảm một nửa kích thước batch
                    currentBatchSize = Math.Max(100, currentBatchSize / 2);
                }

                logger.LogWarning($"Reducing batch size to {currentBatchSize} after error");

                // Chia batch nếu cần
                if (currentBatch.Count > currentBatchSize)
                {
                    var firstHalf = currentBatch.Take(currentBatchSize).ToList();
                    var secondHalf = currentBatch.Skip(currentBatchSize).ToList();

                    // Xử lý nửa đầu ngay, nửa sau để sau
                    currentBatch = firstHalf;

                    // Lên lịch xử lý nửa sau với concurrency thấp hơn
                    if (secondHalf.Any())
                    {
                        logger.LogInformation($"Scheduling remaining {secondHalf.Count} records for later processing");

                        // Đợi lâu hơn để Elasticsearch phục hồi
                        await Task.Delay(RETRY_DELAY_MS * 2, stoppingToken);

                        // Xử lý nửa sau với batch nhỏ hơn
                        int smallBatchSize = 100;
                        for (int i = 0; i < secondHalf.Count; i += smallBatchSize)
                        {
                            var smallBatch = secondHalf.Skip(i).Take(smallBatchSize).ToList();
                            await ProcessBatchWithRetryAsync(smallBatch, stoppingToken, indexName);

                            // Thông báo số lượng xử lý thành công
                            onBatchProcessed?.Invoke(smallBatch.Count);

                            // Thêm delay giữa các small batches
                            await Task.Delay(3000, stoppingToken);
                        }
                    }
                }

                if (retryCount >= MAX_RETRIES)
                {
                    logger.LogError($"Failed to index batch after {MAX_RETRIES} attempts. Last error: {ex.Message}");
                    throw; // Rethrow nếu vượt quá số lần retry tối đa
                }

                string errorType = DetermineErrorType(ex);

                logger.LogWarning($"Elasticsearch error: {errorType}. " +
                                 $"Retrying in {RETRY_DELAY_MS / 1000} seconds... (Attempt {retryCount} of {MAX_RETRIES})");

                // Đợi lâu hơn giữa các lần retry
                await Task.Delay(RETRY_DELAY_MS + (retryCount * 3000), stoppingToken);
            }
        }
    }


    public override async Task IndexMany<T>(List<T> source, CancellationToken cancellationToken, string esIndex, int take = 1000)
    {
        if (source?.Any() != true) return;

        var stopwatch = Stopwatch.StartNew();
        var retryPolicy = Polly.Policy
            .Handle<Exception>(ShouldRetryOn)
            .WaitAndRetryAsync(RetryCount, Sleep, OnRetry);

        try
        {
            int totalCount = 0;
            if (take > 10_000)
            {
                take = 10_000; // Giới hạn số lượng tối đa trong 1 lần xử lý
            }

            // Automatically split into batches of "take" size (default 1000)
            int maxParallelism = Math.Min(32, Environment.ProcessorCount * 2); // Giới hạn 32 task song song

            var allBatches = source.Chunk(take);

            await Parallel.ForEachAsync(allBatches, new ParallelOptions
            {
                MaxDegreeOfParallelism = maxParallelism / 2,
                CancellationToken = cancellationToken
            }, async (batch, ct) =>
            {
                await retryPolicy.ExecuteAsync(async () =>
                {
                    var resp = await elasticClient.BulkAsync(b => b
                        .IndexMany(batch)
                        .Index(esIndex)
                        .Pipeline("location-to-h3"), ct);

                    if (!resp.IsValidResponse)
                    {
                        logger.LogError($"Error when indexing documents to Elasticsearch. " +
                            $"Error code: {resp.ApiCallDetails.HttpStatusCode}, " +
                            $"Error details: {resp.ElasticsearchServerError?.Error?.Reason}");
                        //$"Debug info: {resp.DebugInformation}");
                    }
                    else
                    {
                        Interlocked.Add(ref totalCount, batch.Length);
                    }
                });
            });

            stopwatch.Stop();
            logger.LogInformation($"Synced {totalCount} documents to Elasticsearch in {stopwatch.Elapsed.TotalSeconds:F2} seconds. Speed: {(totalCount / stopwatch.Elapsed.TotalSeconds):F2} records/sec");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while indexing to Elasticsearch.");
            throw;
        }
    }


    public override async Task IndexMany<T>(List<T> source, CancellationToken cancellationToken, int take = 1000)
    {
        if (source?.Any() != true) return;
        var skip = 0;
        var countI = 0;
        var retryPolicy = Polly.Policy.Handle<Exception>(ShouldRetryOn).WaitAndRetryAsync(RetryCount, Sleep, OnRetry);
        var begin = Stopwatch.GetTimestamp();
        while (true)
        {

            var data = source.Skip(skip).Take(take);
            var enumerable = data.ToList();
            if (enumerable.Count == 0) break;
            await retryPolicy.ExecuteAsync(async () =>
            {
                var resp = await elasticClient.BulkAsync(b => b
                    .IndexMany(enumerable)
                    .Pipeline("location-to-h3"), cancellationToken);

                if (!resp.IsSuccess())
                {
                    logger.LogError($"Error when indexing documents to Elasticsearch. " +
                                    $"Error code: {resp.ApiCallDetails.HttpStatusCode}, " +
                                    $"Error details: {resp.ElasticsearchServerError?.Error?.Reason}, " +
                                    $"Debug info: {resp.DebugInformation}");
                }
            });
            skip += take;
            countI = countI + enumerable.Count;
        }
        var end = Stopwatch.GetTimestamp();
        Console.WriteLine($"------2--------- Time to sync ES total: {countI} Collection Place to es---------------: {TimeSpan.FromTicks(end - begin).TotalSeconds} seconds");
    }

    protected virtual TimeSpan Sleep(int retryAttempt)
    {
        var retryTime = Math.Pow(2, retryAttempt);
        if (retryTime > 30)
            retryTime = 30;
        return TimeSpan.FromSeconds(retryTime);
    }

    protected virtual void OnRetry(Exception ex, TimeSpan t)
    {
        logger.LogWarning(ex, "{Name} retries after {TimeOut}s ({ExceptionMessage})", GetType().Name, $"{t.TotalSeconds:n1}", ex.Message);
    }

    protected virtual bool ShouldRetryOn(Exception ex)
    {
        // Không retry trên task cancellation không phải do timeout
        if (ex is TaskCanceledException tce && !ex.Message.Contains("timeout"))
            return false;

        // Retry với timeout và các lỗi liên quan đến Elasticsearch
        return ex is TimeoutException ||
               ex.Message.Contains("timeout") ||
               ex.Message.Contains("circuit_breaking_exception") ||
               ex.Message.Contains("rejected execution") ||
               ex.Message.Contains("429") ||
               true; // Mặc định retry với tất cả ngoại lệ khác
    }
}