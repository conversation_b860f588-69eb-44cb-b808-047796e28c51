using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("provinces_bridge")]
[Index("ProvincesNewCode", Name = "provinces_provinces_new_code_idx")]
[Index("ProvincesOldCode", Name = "provinces_provinces_old_code_idx")]
public partial class ProvincesBridge
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    /// <summary>
    /// Mã tỉnh mới sâu 1/7/2025
    /// </summary>
    [Column("provinces_new_code", TypeName = "character varying")]
    public string ProvincesNewCode { get; set; } = null!;

    /// <summary>
    /// Mã tỉnh cũ trước 1/7/2025
    /// </summary>
    [Column("provinces_old_code", TypeName = "character varying")]
    public string ProvincesOldCode { get; set; } = null!;

    [ForeignKey("ProvincesNewCode")]
    [InverseProperty("ProvincesBridges")]
    public virtual ProvincesNew ProvincesNewCodeNavigation { get; set; } = null!;

    [ForeignKey("ProvincesOldCode")]
    [InverseProperty("ProvincesBridges")]
    public virtual Province ProvincesOldCodeNavigation { get; set; } = null!;
}
