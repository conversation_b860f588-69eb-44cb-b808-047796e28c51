using NetTopologySuite.Geometries;

namespace NDS.ETL.BackgroundJobs.Model.ElasticSearch.Dto
{
    public class BookingPlacePairDto
    {
        public long Id { get; set; }
        public string ArrivalPlaceId { get; set; }
        public int BookingCount { get; set; }
        public string CustomerId { get; set; }
        public string CustomerPhone { get; set; }
        public double Distance { get; set; }
        public string FromAddress { get; set; }
        public string FromCity { get; set; }
        public string FromFullAddress { get; set; }
        public Point? Location1 { get; set; }
        public Point? Location2 { get; set; }
        public int PaidCount { get; set; }
        public string PickupPlaceId { get; set; }
        public int SearchCount { get; set; }
        public string ToAddress { get; set; }
        public string ToCity { get; set; }
        public string ToFullAddress { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdateAt { get; set; }
        public DateTime? LastBookingAt { get; set; }
        public DateTime? LastSearchAt { get; set; }
        public long? EstimateAmount { get; set; }
        public string? LastEstimateAmount { get; set; }
        public string LastNote { get; set; }
    }
}
