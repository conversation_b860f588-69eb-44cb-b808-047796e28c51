using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch.FavPlaceSync;
using NDS.ETL.Entities.Context;
using NDS.ETL.Infrastructure.Common;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.FavPlaceSync;

/// <summary>
/// Dịch vụ xác định chiến lược đồng bộ
/// CHÍNH SÁCH MỚI: Chỉ thực hiện INCREMENTAL SYNC, không bao giờ FULL SYNC
/// (trừ khi index ES không tồn tại)
/// </summary>
public class SyncStrategyService
{
    private readonly ILogger _logger;
    private readonly IScopedResolver<NpgPlaceContext> _dbContextResolver;
    
    // NOTE: Các hằng số này được giữ lại để reference, nhưng không sử dụng
    private const int FULL_SYNC_DAYS_THRESHOLD = 7; // Không sử dụng
    private const double INCREMENTAL_SYNC_PERCENTAGE_THRESHOLD = 50.0; // Không sử dụng

    public SyncStrategyService(
        ILogger logger,
        IScopedResolver<NpgPlaceContext> dbContextResolver)
    {
        _logger = logger;
        _dbContextResolver = dbContextResolver;
    }

    /// <summary>
    /// Quyết định chiến lược đồng bộ
    /// </summary>
    public SyncStrategy DetermineSyncStrategy(DatabaseStats dbStats, ElasticsearchStats esStats, CancellationToken stoppingToken)
    {
        // Nếu index chưa tồn tại hoặc trống, thực hiện full sync
        if (!esStats.IndexExists)
        {
            return new SyncStrategy
            {
                IsFullSync = true,
                RecordsToSync = dbStats.TotalRecords,
                Reason = "Index doesn't exist or is empty"
            };
        }
        
        // Kiểm tra nếu không có dữ liệu mới hoặc cập nhật để đồng bộ
        var hasUpdates = esStats.MaxUpdatedAt < dbStats.MaxUpdatedAt;
        var hasNewRecords = esStats.MaxCreateAt < dbStats.MaxCreateAt && dbStats.NewRecordsCount > 0;
        
        if (!hasUpdates && !hasNewRecords)
        {
            return new SyncStrategy
            {
                IsFullSync = false,
                RecordsToSync = 0,
                Reason = "No new or updated records"
            };
        }

        // Tính số lượng bản ghi cần cập nhật dựa trên UpdatedAt
        var recordsToUpdate = 0;
        if (hasUpdates)
        {
            recordsToUpdate = _dbContextResolver.Resolve(dbContext =>
                dbContext.FavoritePlaces
                    .AsNoTracking()
                    .Count(x => x.UpdatedAt != null && x.UpdatedAt > esStats.MaxUpdatedAt));
        }
        
        // Lấy số lượng bản ghi mới (NewRecordsCount) từ dbStats
        var recordsToSync = recordsToUpdate + dbStats.NewRecordsCount;
        
        var recordsToSyncPercentage = dbStats.TotalRecords > 0 
            ? (double)recordsToSync / dbStats.TotalRecords * 100 
            : 0;

        // CHÍNH SÁCH MỚI: Luôn thực hiện incremental sync, không bao giờ full sync
        // (trừ khi index không tồn tại)
        bool isFullSync = false; // Luôn là false

        _logger.LogInformation($"Sync strategy: " +
            $"Records to sync: {recordsToSync:N0} (new: {dbStats.NewRecordsCount:N0}, updates: {recordsToUpdate:N0}, " +
            $"percentage: {recordsToSyncPercentage:F2}%), " +
            $"Updated records: {(hasUpdates ? "Yes" : "No")}, New records: {(hasNewRecords ? "Yes" : "No")}, " +
            $"Strategy: INCREMENTAL ONLY");
            
        return new SyncStrategy
        {
            IsFullSync = isFullSync,
            RecordsToSync = recordsToSync,
            Reason = recordsToSync > 0 ? "Incremental sync for all changes" : "No changes to sync"
        };
    }
} 