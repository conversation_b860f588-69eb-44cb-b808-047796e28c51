namespace NDS.ETL.BackgroundJobs.Model.ES.Dto
{
    /// <summary>
    /// Định nghĩa các loại địa điểm
    /// </summary>
    public class PlaceType
    {
        public static readonly int Home = 0; // Nhà
        public static readonly int Work = 1; // C<PERSON> quan
        public static readonly int Favorite = 2; // Yêu thích
        public static readonly int Departure = 3; // Điểm đi
        public static readonly int Destination = 4; // Điểm đến
        public static readonly int Starred = 5; // Gắn dấu sao
        public static readonly int SavedPlace = 6; // Đ<PERSON>a điểm đã lưu
        public static readonly int UserSearch = 7; // Tìm kiếm của người dùng
        public static readonly int BookingHistory = 8; // Lịch sử đặt xe
        public static readonly int Normal = 9; // Bình thường

        /// <summary>
        /// Kiểm tra xem một giá trị có phải là loại địa điểm hợp lệ hay không
        /// </summary>
        public static bool IsValidPlaceType(int placeType)
        {
            return placeType >= Home && placeType <= Normal;
        }

        /// <summary>
        /// Chuyển đổi từ giá trị số sang tên loại địa điểm
        /// </summary>
        public static string GetPlaceTypeName(int placeType)
        {
            return placeType switch
            {
                0 => "Nhà",
                1 => "Cơ quan",
                2 => "Yêu thích",
                3 => "Điểm đi",
                4 => "Điểm đến",
                5 => "Gắn dấu sao",
                6 => "Địa điểm đã lưu",
                7 => "Tìm kiếm của người dùng",
                8 => "Lịch sử đặt xe",
                9 => "Bình thường",
                _ => "Không xác định"
            };
        }
    }
} 