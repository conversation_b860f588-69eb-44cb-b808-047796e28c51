using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using NDS.ETL.Entities.PostgresPlace;

namespace NDS.ETL.Entities.Context;

public partial class NpgPlaceContext : DbContext
{
    public NpgPlaceContext()
    {
    }

    public NpgPlaceContext(DbContextOptions<NpgPlaceContext> options)
        : base(options)
    {
    }

    public virtual DbSet<ApiKey> ApiKeys { get; set; }

    public virtual DbSet<ApiKeyPermission> ApiKeyPermissions { get; set; }

    public virtual DbSet<BlockedPlace> BlockedPlaces { get; set; }

    public virtual DbSet<BookingHistoriesItem> BookingHistoriesItems { get; set; }

    public virtual DbSet<BookingHistory> BookingHistories { get; set; }

    public virtual DbSet<BookingPlacePair> BookingPlacePairs { get; set; }

    public virtual DbSet<BuildingBoundary> BuildingBoundaries { get; set; }

    public virtual DbSet<BuildingBoundaryPlace> BuildingBoundaryPlaces { get; set; }

    public virtual DbSet<Country> Countries { get; set; }

    public virtual DbSet<Customer> Customers { get; set; }

    public virtual DbSet<Danhmuc> Danhmucs { get; set; }

    public virtual DbSet<DefaultColumn> DefaultColumns { get; set; }

    public virtual DbSet<District> Districts { get; set; }

    public virtual DbSet<DistrictBoundary> DistrictBoundaries { get; set; }

    public virtual DbSet<EfmigrationsHistoryEsdbContext> EfmigrationsHistoryEsdbContexts { get; set; }

    public virtual DbSet<ElsQueryConfig> ElsQueryConfigs { get; set; }

    public virtual DbSet<EsQueue> EsQueues { get; set; }

    public virtual DbSet<FavoritePlace> FavoritePlaces { get; set; }

    public virtual DbSet<GisProvince> GisProvinces { get; set; }

    public virtual DbSet<HanoiBoundary> HanoiBoundaries { get; set; }

    public virtual DbSet<HiddenPlace> HiddenPlaces { get; set; }

    public virtual DbSet<JobConfig> JobConfigs { get; set; }

    public virtual DbSet<JobConfigPlace> JobConfigPlaces { get; set; }

    public virtual DbSet<JobLastUpdate> JobLastUpdates { get; set; }

    public virtual DbSet<JwtClient> JwtClients { get; set; }

    public virtual DbSet<MigrationCheckpoint> MigrationCheckpoints { get; set; }

    public virtual DbSet<Osm2pgsqlProperty> Osm2pgsqlProperties { get; set; }

    public virtual DbSet<Place> Places { get; set; }

    public virtual DbSet<PlaceApprove> PlaceApproves { get; set; }

    public virtual DbSet<PlaceChangeHistory> PlaceChangeHistories { get; set; }

    public virtual DbSet<PlaceTag> PlaceTags { get; set; }

    public virtual DbSet<PlaceUatUpLive> PlaceUatUpLives { get; set; }

    public virtual DbSet<Placechangehistory1> Placechangehistories1 { get; set; }

    public virtual DbSet<PlacesDetail> PlacesDetails { get; set; }

    public virtual DbSet<PlacesQueue> PlacesQueues { get; set; }

    public virtual DbSet<PlacesRemove> PlacesRemoves { get; set; }

    public virtual DbSet<PlacesScanHistory> PlacesScanHistories { get; set; }

    public virtual DbSet<PlacesScanResult> PlacesScanResults { get; set; }

    public virtual DbSet<PlacesUatBak> PlacesUatBaks { get; set; }

    public virtual DbSet<Placesdatae> Placesdataes { get; set; }

    public virtual DbSet<PlanetOsmLine> PlanetOsmLines { get; set; }

    public virtual DbSet<PlanetOsmNode> PlanetOsmNodes { get; set; }

    public virtual DbSet<PlanetOsmPoint> PlanetOsmPoints { get; set; }

    public virtual DbSet<PlanetOsmPolygon> PlanetOsmPolygons { get; set; }

    public virtual DbSet<PlanetOsmRel> PlanetOsmRels { get; set; }

    public virtual DbSet<PlanetOsmRoad> PlanetOsmRoads { get; set; }

    public virtual DbSet<PlanetOsmWay> PlanetOsmWays { get; set; }

    public virtual DbSet<Province> Provinces { get; set; }

    public virtual DbSet<ProvinceBoundary> ProvinceBoundaries { get; set; }

    public virtual DbSet<ProvincesBridge> ProvincesBridges { get; set; }

    public virtual DbSet<ProvincesNew> ProvincesNews { get; set; }

    public virtual DbSet<Sheet2> Sheet2s { get; set; }

    public virtual DbSet<Sheet3> Sheet3s { get; set; }

    public virtual DbSet<SubPlace> SubPlaces { get; set; }

    public virtual DbSet<SubplaceChangeHistory> SubplaceChangeHistories { get; set; }

    public virtual DbSet<SysNo> SysNos { get; set; }

    public virtual DbSet<TrustedPlace> TrustedPlaces { get; set; }

    public virtual DbSet<UatPlaceMigrate> UatPlaceMigrates { get; set; }

    public virtual DbSet<UatPlaceMigrate2> UatPlaceMigrate2s { get; set; }

    public virtual DbSet<UserBookingHistory> UserBookingHistories { get; set; }

    public virtual DbSet<Ward> Wards { get; set; }

    public virtual DbSet<WardBoundary> WardBoundaries { get; set; }

    public virtual DbSet<WardNew> WardNews { get; set; }

    public virtual DbSet<WardsBridge> WardsBridges { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder
            .HasPostgresEnum("sys", "dss_freq_enum", new[] { "YEARLY", "MONTHLY", "WEEKLY", "DAILY", "HOURLY", "MINUTELY", "SECONDLY" })
            .HasPostgresEnum("sys", "dss_program_type_enum", new[] { "PLSQL_BLOCK", "STORED_PROCEDURE" })
            .HasPostgresExtension("pg_catalog", "edb_dblink_libpq")
            .HasPostgresExtension("pg_catalog", "edb_dblink_oci")
            .HasPostgresExtension("pg_catalog", "edbspl")
            .HasPostgresExtension("btree_gist")
            .HasPostgresExtension("pg_stat_statements")
            .HasPostgresExtension("pgstattuple")
            .HasPostgresExtension("postgis")
            .HasPostgresExtension("unaccent");

        modelBuilder.Entity<ApiKey>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<ApiKeyPermission>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<BlockedPlace>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedOnAdd();
        });

        modelBuilder.Entity<BookingHistoriesItem>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("booking_histories_item_pkey");
        });

        modelBuilder.Entity<BookingHistory>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("booking_histories_pkey");
        });

        modelBuilder.Entity<BookingPlacePair>(entity =>
        {
            entity.HasIndex(e => e.Ts, "IX_booking_place_pairs_ts").HasMethod("gin");

            entity.HasIndex(e => e.Ts2, "IX_booking_place_pairs_ts2").HasMethod("gin");

            entity.Property(e => e.Ts).HasComputedColumnSql("to_tsvector('english'::regconfig, (((((((COALESCE(\"fromAddress\", ''::character varying))::text || ' '::text) || (COALESCE(\"fromFullAddress\", ''::character varying))::text) || ' '::text) || (COALESCE(\"toAddress\", ''::character varying))::text) || ' '::text) || (COALESCE(\"toFullAddress\", ''::character varying))::text))", true);
            entity.Property(e => e.Ts2).HasComputedColumnSql("to_tsvector('english'::regconfig, regexp_replace(((COALESCE(vn_unaccent((\"fromFullAddress\")::text), ''::text) || ' '::text) || COALESCE(vn_unaccent((\"toFullAddress\")::text), ''::text)), '[^\\w]+'::text, ' '::text, 'g'::text))", true);
        });

        modelBuilder.Entity<BuildingBoundary>(entity =>
        {
            entity.Property(e => e.PlaceFkId).HasDefaultValue(0L);
        });

        modelBuilder.Entity<Country>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("countries_pkey");
        });

        modelBuilder.Entity<Customer>(entity =>
        {
            entity.HasKey(e => e.UserId).HasName("customers_pkey");
        });

        modelBuilder.Entity<District>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("districts_pkey");

            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<DistrictBoundary>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("district_boundaries_pk");

            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<ElsQueryConfig>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("els_query_config_copy1_pkey");

            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<EsQueue>(entity =>
        {
            entity.Property(e => e.CreateDate).HasDefaultValueSql("current_timestamp");
            entity.Property(e => e.UpdateDate).HasDefaultValueSql("current_timestamp");
        });

        modelBuilder.Entity<HiddenPlace>(entity =>
        {
            entity.HasIndex(e => e.Location, "IX_hidden_places_location").HasMethod("gist");

            entity.HasIndex(e => e.Ts, "IX_hidden_places_ts").HasMethod("gin");

            entity.HasIndex(e => e.Ts2, "IX_hidden_places_ts2").HasMethod("gin");

            entity.Property(e => e.Pending).HasDefaultValue(false);
            entity.Property(e => e.QuoteCount).HasDefaultValue(0);
            entity.Property(e => e.Ts).HasComputedColumnSql("to_tsvector('english'::regconfig, (((((COALESCE(name, ''::character varying))::text || ' '::text) || (\"fullAddress\")::text) || ' '::text) || (COALESCE(keywords, ''::character varying))::text))", true);
            entity.Property(e => e.Ts2).HasComputedColumnSql("to_tsvector('english'::regconfig, regexp_replace(((((COALESCE(vn_unaccent((name)::text), ''::text) || ' '::text) || COALESCE(vn_unaccent((\"fullAddress\")::text), ''::text)) || ' '::text) || COALESCE(vn_unaccent((keywords)::text), ''::text)), '[^\\w]+'::text, ' '::text, 'g'::text))", true);
        });

        modelBuilder.Entity<JwtClient>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("jwt_clients_pkey");

            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<MigrationCheckpoint>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("migration_checkpoints_pkey");
        });

        modelBuilder.Entity<Osm2pgsqlProperty>(entity =>
        {
            entity.HasKey(e => e.Property).HasName("osm2pgsql_properties_pkey");
        });

        modelBuilder.Entity<Place>(entity =>
        {
            entity.HasIndex(e => e.Location, "IX_places_location").HasMethod("gist");

            entity.HasIndex(e => e.Ts, "IX_places_ts").HasMethod("gin");

            entity.HasIndex(e => e.Ts2, "IX_places_ts2").HasMethod("gin");

            entity.Property(e => e.BookingCount).HasDefaultValue(0);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("'01-JAN-01 00:00:00'::timestamp without time zone");
            entity.Property(e => e.HasChild).HasDefaultValue(false);
            entity.Property(e => e.Pending).HasDefaultValue(false);
            entity.Property(e => e.QuoteCount).HasDefaultValue(0);
            entity.Property(e => e.Ts).HasComputedColumnSql("to_tsvector('english'::regconfig, (((((COALESCE(name, ''::character varying))::text || ' '::text) || (\"fullAddress\")::text) || ' '::text) || (COALESCE(keywords, ''::character varying))::text))", true);
            entity.Property(e => e.Ts2).HasComputedColumnSql("to_tsvector('english'::regconfig, regexp_replace(((((COALESCE(vn_unaccent((name)::text), ''::text) || ' '::text) || COALESCE(vn_unaccent((\"fullAddress\")::text), ''::text)) || ' '::text) || COALESCE(vn_unaccent((keywords)::text), ''::text)), '[^\\w]+'::text, ' '::text, 'g'::text))", true);
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("'01-JAN-01 00:00:00'::timestamp without time zone");
        });

        modelBuilder.Entity<PlaceApprove>(entity =>
        {
            entity.Property(e => e.DistrictId).HasDefaultValue(0);
            entity.Property(e => e.ProvinceId).HasDefaultValue(0);
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("'-infinity'::timestamp without time zone");
            entity.Property(e => e.WardId).HasDefaultValue(0);
        });

        modelBuilder.Entity<PlaceUatUpLive>(entity =>
        {
            entity.HasKey(e => e.Placeid).HasName("place_up_live_pk");

            entity.Property(e => e.Type).HasDefaultValue(0);
        });

        modelBuilder.Entity<PlacesDetail>(entity =>
        {
            entity.HasIndex(e => e.AddressComponents, "IX_places_details_address_components").HasMethod("gin");

            entity.HasIndex(e => e.Types, "IX_places_details_types").HasMethod("gin");

            entity.Property(e => e.Id).ValueGeneratedOnAdd();
        });

        modelBuilder.Entity<PlacesScanHistory>(entity =>
        {
            entity.Property(e => e.Createat).HasDefaultValueSql("current_timestamp");
            entity.Property(e => e.Updateat).HasDefaultValueSql("current_timestamp");
        });

        modelBuilder.Entity<PlacesScanResult>(entity =>
        {
            entity.Property(e => e.CreateAt).HasDefaultValueSql("current_timestamp");
            entity.Property(e => e.SyncStatus).HasDefaultValue(0);
            entity.Property(e => e.UpdateAt).HasDefaultValueSql("current_timestamp");
        });

        modelBuilder.Entity<PlanetOsmLine>(entity =>
        {
            entity.HasIndex(e => e.Way, "planet_osm_line_way_idx")
                .HasMethod("gist")
                .HasAnnotation("Npgsql:StorageParameter:fillfactor", "100");
        });

        modelBuilder.Entity<PlanetOsmNode>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("planet_osm_nodes_pkey");

            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<PlanetOsmPoint>(entity =>
        {
            entity.HasIndex(e => e.Way, "planet_osm_point_way_idx")
                .HasMethod("gist")
                .HasAnnotation("Npgsql:StorageParameter:fillfactor", "100");
        });

        modelBuilder.Entity<PlanetOsmPolygon>(entity =>
        {
            entity.HasIndex(e => e.Way, "planet_osm_polygon_way_idx")
                .HasMethod("gist")
                .HasAnnotation("Npgsql:StorageParameter:fillfactor", "100");
        });

        modelBuilder.Entity<PlanetOsmRel>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("planet_osm_rels_pkey");

            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<PlanetOsmRoad>(entity =>
        {
            entity.HasIndex(e => e.Way, "planet_osm_roads_way_idx")
                .HasMethod("gist")
                .HasAnnotation("Npgsql:StorageParameter:fillfactor", "100");
        });

        modelBuilder.Entity<PlanetOsmWay>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("planet_osm_ways_pkey");

            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<Province>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("pk_provinces");

            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<ProvincesBridge>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("provinces_pk");

            entity.Property(e => e.Id).UseIdentityAlwaysColumn();
            entity.Property(e => e.ProvincesNewCode).HasComment("Mã tỉnh mới sâu 1/7/2025");
            entity.Property(e => e.ProvincesOldCode).HasComment("Mã tỉnh cũ trước 1/7/2025");

            entity.HasOne(d => d.ProvincesNewCodeNavigation).WithMany(p => p.ProvincesBridges)
                .HasPrincipalKey(p => p.Code)
                .HasForeignKey(d => d.ProvincesNewCode)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("provinces_bridge_provinces_new_fk");

            entity.HasOne(d => d.ProvincesOldCodeNavigation).WithMany(p => p.ProvincesBridges)
                .HasPrincipalKey(p => p.Code)
                .HasForeignKey(d => d.ProvincesOldCode)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("provinces_bridge_provinces_fk");
        });

        modelBuilder.Entity<ProvincesNew>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("provinces__pkey");

            entity.Property(e => e.Id).HasDefaultValueSql("nextval('provinces__id_seq'::regclass)");
        });

        modelBuilder.Entity<Sheet3>(entity =>
        {
            entity.HasKey(e => e.Column1Id).HasName("sheet3_pk");

            entity.Property(e => e.Column1Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<SubPlace>(entity =>
        {
            entity.HasIndex(e => e.Location, "IX_sub_places_location").HasMethod("gist");

            entity.HasIndex(e => e.Ts, "IX_sub_places_ts").HasMethod("gin");

            entity.HasIndex(e => e.Ts2, "IX_sub_places_ts2").HasMethod("gin");

            entity.Property(e => e.BookingCount).HasDefaultValue(0);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("'01-JAN-01 00:00:00'::timestamp without time zone");
            entity.Property(e => e.Pending).HasDefaultValue(false);
            entity.Property(e => e.QuoteCount).HasDefaultValue(0);
            entity.Property(e => e.Ts).HasComputedColumnSql("to_tsvector('english'::regconfig, (((((COALESCE(name, ''::character varying))::text || ' '::text) || (\"fullAddress\")::text) || ' '::text) || (COALESCE(keywords, ''::character varying))::text))", true);
            entity.Property(e => e.Ts2).HasComputedColumnSql("to_tsvector('english'::regconfig, regexp_replace(((((COALESCE(vn_unaccent((name)::text), ''::text) || ' '::text) || COALESCE(vn_unaccent((\"fullAddress\")::text), ''::text)) || ' '::text) || COALESCE(vn_unaccent((keywords)::text), ''::text)), '[^\\w]+'::text, ' '::text, 'g'::text))", true);
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("'01-JAN-01 00:00:00'::timestamp without time zone");

            entity.HasOne(d => d.ParentPlace).WithMany(p => p.SubPlaces)
                .HasPrincipalKey(p => p.PlaceId)
                .HasForeignKey(d => d.ParentPlaceId)
                .HasConstraintName("fk_sub_places_places_parentplaceid");

            entity.HasOne(d => d.WardNavigation).WithMany(p => p.SubPlaces).OnDelete(DeleteBehavior.Restrict);
        });

        modelBuilder.Entity<SubplaceChangeHistory>(entity =>
        {
            entity.Property(e => e.SubPlaceId).HasDefaultValue(0L);
        });

        modelBuilder.Entity<TrustedPlace>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedOnAdd();
        });

        modelBuilder.Entity<UatPlaceMigrate>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("uat_place_migrate_pk");

            entity.Property(e => e.Status).HasDefaultValue(0);
        });

        modelBuilder.Entity<UserBookingHistory>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedOnAdd();
        });

        modelBuilder.Entity<Ward>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("wards_pkey");

            entity.Property(e => e.Processed).HasDefaultValue(false);
        });

        modelBuilder.Entity<WardBoundary>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ward_boundaries_pk");

            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<WardNew>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ward__pkey");

            entity.Property(e => e.Id).HasDefaultValueSql("nextval('ward__id_seq'::regclass)");
        });

        modelBuilder.Entity<WardsBridge>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("wards_bridge_pk");

            entity.Property(e => e.Id).UseIdentityAlwaysColumn();
            entity.Property(e => e.WardsNewCode).HasComment("wards code mới từ ngày 1/7/2025 từ bảng ward_new");
            entity.Property(e => e.WardsOldCode).HasComment("Ward Code cũ trước 1/7/2025");

            entity.HasOne(d => d.WardsNewCodeNavigation).WithMany(p => p.WardsBridges)
                .HasPrincipalKey(p => p.Code)
                .HasForeignKey(d => d.WardsNewCode)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("wards_bridge_ward_new_fk");

            entity.HasOne(d => d.WardsOldCodeNavigation).WithMany(p => p.WardsBridges)
                .HasPrincipalKey(p => p.Code)
                .HasForeignKey(d => d.WardsOldCode)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("wards_bridge_wards_fk");
        });
        modelBuilder.HasSequence("countries_id_seq");
        modelBuilder.HasSequence("hibernate_sequence");
        modelBuilder.HasSequence("public.user_booking_histories_SEQ");
        modelBuilder.HasSequence("user_booking_histories_seq");
        modelBuilder.HasSequence("user_booking_histories_SEQ");

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
