using System.Reflection;
using AutoMapper;
using KnowledgeBase.Core.Extensions;
using KnowledgeBase.Core.Mapper;
using Microsoft.Extensions.DependencyInjection;

namespace NDS.ETL.Infrastructure;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddMapper(this IServiceCollection services, params Assembly[] assemblies)
    {
        assemblies = GetAssemblies(assemblies);
        IMapper mapper = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile(new MappingProfile(assemblies));
            cfg.AddMaps(assemblies);
        }).CreateMapper();

        return services
            .AddSingleton(mapper)
            .AddMapperInstance(mapper);
    }
    
    private static Assembly[] GetAssemblies(params Assembly[] assemblies)
    {
        return assemblies?.Any() == true ? assemblies : new[] { Assembly.GetEntryAssembly()! };
    }
}