using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using NDS.ETL.BackgroundJobs.Controllers;
using NDS.ETL.BackgroundJobs.Model.API;
using NDS.ETL.BackgroundJobs.Services;
using NDS.ETL.Entities.PostgresPlace;
using NUnit.Framework;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace TestProject
{
    [TestFixture]
    public class AdminWardsControllerTests
    {
        private Mock<IWardsService> _mockWardsService;
        private Mock<IRequestInfoLogger> _mockRequestInfoLogger;
        private Mock<ILogger<AdminWardsController>> _mockLogger;
        private AdminWardsController _controller;

        [SetUp]
        public void Setup()
        {
            // Khởi tạo các mock object
            _mockWardsService = new Mock<IWardsService>();
            _mockRequestInfoLogger = new Mock<IRequestInfoLogger>();
            _mockLogger = new Mock<ILogger<AdminWardsController>>();

            // Khởi tạo controller với các mock dependency
            _controller = new AdminWardsController(
                _mockWardsService.Object,
                _mockRequestInfoLogger.Object,
                _mockLogger.Object);
        }

        [Test]
        public async Task GetAllWards_ReturnsPagedWards()
        {
            // Arrange
            int pageNumber = 1;
            int pageSize = 10;
            
            var wards = new List<WardNew>
            {
                new WardNew { Id = 1, Code = "01001", Provincecode = "01", Name = "Phường Phúc Xá", Type = "Phường" },
                new WardNew { Id = 2, Code = "01002", Provincecode = "01", Name = "Phường Trúc Bạch", Type = "Phường" }
            };
            
            var pagedResult = new PagedResult<WardNew>
            {
                Items = wards,
                TotalCount = 2,
                CurrentPage = pageNumber,
                PageSize = pageSize,
                TotalPages = 1
                // Không cài đặt HasPreviousPage và HasNextPage vì là thuộc tính chỉ đọc
            };
            
            _mockWardsService.Setup(service => service.GetAllWardsAsync(pageNumber, pageSize))
                .ReturnsAsync(pagedResult);

            // Act
            var result = await _controller.GetAllWards(pageNumber, pageSize);

            // Assert
            Assert.That(result.Result, Is.TypeOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult, Is.Not.Null);
            
            var wardResponse = okResult.Value as PagedWardResponse;
            Assert.That(wardResponse, Is.Not.Null);
            Assert.That(wardResponse.Items.Count, Is.EqualTo(2));
            Assert.That(wardResponse.Items[0].Code, Is.EqualTo("01001"));
            Assert.That(wardResponse.Items[0].Name, Is.EqualTo("Phường Phúc Xá"));
            Assert.That(wardResponse.TotalCount, Is.EqualTo(2));
        }

        [Test]
        public async Task GetWardByCode_WithValidCode_ReturnsWard()
        {
            // Arrange
            string code = "01001";
            var ward = new WardNew
            {
                Id = 1,
                Code = code,
                Provincecode = "01",
                Name = "Phường Phúc Xá",
                Type = "Phường"
            };
            
            _mockWardsService.Setup(service => service.GetWardByCodeAsync(code))
                .ReturnsAsync(ward);

            // Act
            var result = await _controller.GetWardByCode(code);

            // Assert
            Assert.That(result.Value, Is.Not.Null);
            Assert.That(result.Value.Code, Is.EqualTo("01001"));
            Assert.That(result.Value.Name, Is.EqualTo("Phường Phúc Xá"));
        }

        [Test]
        public async Task GetWardByCode_WithInvalidCode_ReturnsNotFound()
        {
            // Arrange
            string code = "invalid";
            
            _mockWardsService.Setup(service => service.GetWardByCodeAsync(code))
                .ReturnsAsync((WardNew)null);

            // Act
            var result = await _controller.GetWardByCode(code);

            // Assert
            Assert.That(result.Result, Is.TypeOf<NotFoundObjectResult>());
        }

        [Test]
        public async Task SearchWardsByName_WithValidRequest_ReturnsPagedMatchingWards()
        {
            // Arrange
            string searchName = "Phúc";
            int pageNumber = 1;
            int pageSize = 10;
            
            var request = new SearchByNamePaginatedRequest 
            { 
                Name = searchName,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
            
            var wards = new List<WardNew>
            {
                new WardNew { Id = 1, Code = "01001", Provincecode = "01", Name = "Phường Phúc Xá", Type = "Phường" }
            };
            
            var pagedResult = new PagedResult<WardNew>
            {
                Items = wards,
                TotalCount = 1,
                CurrentPage = pageNumber,
                PageSize = pageSize,
                TotalPages = 1
                // Không cài đặt HasPreviousPage và HasNextPage vì là thuộc tính chỉ đọc
            };
            
            _mockWardsService.Setup(service => service.GetWardsByNameAsync(searchName, pageNumber, pageSize))
                .ReturnsAsync(pagedResult);

            // Act
            var result = await _controller.SearchWardsByName(request);

            // Assert
            Assert.That(result.Result, Is.TypeOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult, Is.Not.Null);
            
            var wardResponse = okResult.Value as PagedWardResponse;
            Assert.That(wardResponse, Is.Not.Null);
            Assert.That(wardResponse.Items.Count, Is.EqualTo(1));
            Assert.That(wardResponse.Items[0].Name, Is.EqualTo("Phường Phúc Xá"));
        }

        [Test]
        public async Task SearchWardsByName_WithNullRequest_ReturnsBadRequest()
        {
            // Arrange
            SearchByNamePaginatedRequest request = null;

            // Act
            var result = await _controller.SearchWardsByName(request);

            // Assert
            Assert.That(result.Result, Is.TypeOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task SearchWardsByName_WithEmptyName_ReturnsBadRequest()
        {
            // Arrange
            var request = new SearchByNamePaginatedRequest { Name = "", PageNumber = 1, PageSize = 10 };

            // Act
            var result = await _controller.SearchWardsByName(request);

            // Assert
            Assert.That(result.Result, Is.TypeOf<BadRequestObjectResult>());
        }

        [Test]
        public async Task SearchWardsByName_WithInvalidPagination_NormalizesParameters()
        {
            // Arrange
            string searchName = "Phúc";
            
            // Thiết lập tham số phân trang không hợp lệ để kiểm tra việc chuẩn hóa
            var request = new SearchByNamePaginatedRequest 
            { 
                Name = searchName,
                PageNumber = 0,  // Không hợp lệ, nên được đặt thành 1
                PageSize = 0     // Không hợp lệ, nên được đặt thành 10
            };
            
            var wards = new List<WardNew>
            {
                new WardNew { Id = 1, Code = "01001", Provincecode = "01", Name = "Phường Phúc Xá", Type = "Phường" }
            };
            
            var pagedResult = new PagedResult<WardNew>
            {
                Items = wards,
                TotalCount = 1,
                CurrentPage = 1, // Giá trị chuẩn hóa
                PageSize = 10,   // Giá trị chuẩn hóa
                TotalPages = 1
                // Không cài đặt HasPreviousPage và HasNextPage vì là thuộc tính chỉ đọc
            };
            
            // Dịch vụ nên được gọi với các tham số đã chuẩn hóa
            _mockWardsService.Setup(service => service.GetWardsByNameAsync(searchName, 1, 10))
                .ReturnsAsync(pagedResult);

            // Act
            var result = await _controller.SearchWardsByName(request);

            // Assert
            Assert.That(result.Result, Is.TypeOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult, Is.Not.Null);
            
            var wardResponse = okResult.Value as PagedWardResponse;
            Assert.That(wardResponse, Is.Not.Null);
            Assert.That(wardResponse.CurrentPage, Is.EqualTo(1)); // Kiểm tra đã được chuẩn hóa
            Assert.That(wardResponse.PageSize, Is.EqualTo(10));   // Kiểm tra đã được chuẩn hóa
        }

        [Test]
        public async Task FilterWardsByType_ReturnsMatchingWards()
        {
            // Arrange
            string type = "Phường";
            int pageNumber = 1;
            int pageSize = 10;
            
            var wards = new List<WardNew>
            {
                new WardNew { Id = 1, Code = "01001", Provincecode = "01", Name = "Phường Phúc Xá", Type = type },
                new WardNew { Id = 2, Code = "01002", Provincecode = "01", Name = "Phường Trúc Bạch", Type = type }
            };
            
            var pagedResult = new PagedResult<WardNew>
            {
                Items = wards,
                TotalCount = 2,
                CurrentPage = pageNumber,
                PageSize = pageSize,
                TotalPages = 1
                // Không cài đặt HasPreviousPage và HasNextPage vì là thuộc tính chỉ đọc
            };
            
            _mockWardsService.Setup(service => service.GetWardsByTypeAsync(type, pageNumber, pageSize))
                .ReturnsAsync(pagedResult);

            // Act
            var result = await _controller.FilterWardsByType(type, pageNumber, pageSize);

            // Assert
            Assert.That(result.Result, Is.TypeOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult, Is.Not.Null);
            
            var wardResponse = okResult.Value as PagedWardResponse;
            Assert.That(wardResponse, Is.Not.Null);
            Assert.That(wardResponse.Items.Count, Is.EqualTo(2));
            Assert.That(wardResponse.Items[0].Type, Is.EqualTo("Phường"));
            Assert.That(wardResponse.Items[1].Type, Is.EqualTo("Phường"));
        }
    }
}
