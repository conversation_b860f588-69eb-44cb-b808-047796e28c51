using System;

namespace NDS.ETL.BackgroundJobs.Model.ElasticSearch.FavPlaceSync
{
    /// <summary>
    /// Thông tin thống kê từ cơ sở dữ liệu
    /// </summary>
    public class DatabaseStats
    {
        /// <summary>
        /// ID lớn nhất trong cơ sở dữ liệu
        /// </summary>
        public long MaxId { get; set; }
        
        /// <summary>
        /// Thời gian cập nhật mới nhất trong cơ sở dữ liệu
        /// </summary>
        public DateTime MaxUpdatedAt { get; set; }
        
        /// <summary>
        /// Thời gian tạo mới nhất trong cơ sở dữ liệu (cho các bản ghi mới có UpdatedAt = null)
        /// </summary>
        public DateTime MaxCreateAt { get; set; }
        
        /// <summary>
        /// Tổng số bản ghi trong cơ sở dữ liệu
        /// </summary>
        public int TotalRecords { get; set; }
        
        /// <summary>
        /// <PERSON><PERSON> bản ghi mới (có CreatedAt nhưng UpdatedAt là null)
        /// </summary>
        public int NewRecordsCount { get; set; }
    }

    /// <summary>
    /// Thông tin thống kê từ Elasticsearch
    /// </summary>
    public class ElasticsearchStats
    {
        /// <summary>
        /// Index có tồn tại hay không
        /// </summary>
        public bool IndexExists { get; set; }
        
        /// <summary>
        /// ID lớn nhất trong Elasticsearch
        /// </summary>
        public long MaxId { get; set; }
        
        /// <summary>
        /// Thời gian cập nhật mới nhất trong Elasticsearch
        /// </summary>
        public DateTime MaxUpdatedAt { get; set; }
        
        /// <summary>
        /// Thời gian tạo mới nhất trong Elasticsearch (cho các bản ghi có UpdatedAt = null)
        /// </summary>
        public DateTime MaxCreateAt { get; set; }
        
        /// <summary>
        /// Thông tin về index
        /// </summary>
        public IndexState? IndexInfo { get; set; }
    }

    /// <summary>
    /// Chiến lược đồng bộ
    /// </summary>
    public class SyncStrategy
    {
        /// <summary>
        /// Có thực hiện full sync hay không
        /// </summary>
        public bool IsFullSync { get; set; }
        
        /// <summary>
        /// Số bản ghi cần đồng bộ
        /// </summary>
        public int RecordsToSync { get; set; }
        
        /// <summary>
        /// Lý do chọn chiến lược đồng bộ
        /// </summary>
        public string Reason { get; set; } = string.Empty;
    }
} 