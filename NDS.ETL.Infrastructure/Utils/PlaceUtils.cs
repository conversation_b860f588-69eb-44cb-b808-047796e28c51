using System.Text.RegularExpressions;
using KnowledgeBase.Core.Extensions;

namespace NDS.ETL.Infrastructure.Utils;

public static partial class PlaceUtils
{
    private static readonly List<string> PREFIXES_PROVINCE = new List<string>
    {
        "thành phố", "tp.", "tỉnh", "t.", "city", "province", "thủ đô", "thành phố trực thuộc trung ương", "thành phố trực thuộc tỉnh"
    };

    private static readonly List<string> PREFIXES_DISTRICT = new List<string>
    {
        "huyện", "thị xã", "thị xã trực thuộc tỉnh", "thị xã trực thuộc trung ương", "h.", "tx.", "q."
    };

    private static readonly List<string> PREFIXES_WARD = new List<string>
    {
        "phường", "xã", "thị trấn", "p.", "x."
    };

    private static readonly Dictionary<string, string?> PROVINCE_MAP = new Dictionary<string, string?>
    {
        { "saigon", "hochiminh" },
        { "hochiminh", "hochiminh" },
        { "hcm", "hochiminh" },
        { "tp.hcm", "hochiminh" }
    };

    public static string? NormalizeProvinceFromAddress(string? address)
    {
        if (string.IsNullOrWhiteSpace(address)) return address;
        string? province = GetProvince(address);
        province = RemovePrefixProvince(province);
        return NormalizeProvince(province);
    }

    public static string? NormalizeProvince(string? province)
    {
        if (string.IsNullOrWhiteSpace(province)) return province;
        province = RemovePrefixProvince(province);
        province = StringExtensions.ToUnsign(province);
        if (PROVINCE_MAP.ContainsKey(province))
        {
            return PROVINCE_MAP[province];
        }
        return province;
    }

    public static string? NormalizeDistrict(string? district)
    {
        if (string.IsNullOrWhiteSpace(district)) return district;
        return RemovePrefixDistrict(district);
    }

    public static string? NormalizeWard(string? ward)
    {
        if (string.IsNullOrWhiteSpace(ward)) return ward;
        return RemovePrefixWard(ward);
    }

    public static string? RemovePrefix(string? input, List<string> prefixes)
    {
        if (string.IsNullOrWhiteSpace(input)) return input;
        foreach (string prefix in prefixes)
        {
            var pattern = new Regex(Regex.Escape(prefix.ToLower()), RegexOptions.IgnoreCase);
            var matcher = pattern.Match(input.ToLower());
            if (!matcher.Success) continue;
            input = Regex.Replace(input, "(?i)" + Regex.Escape(prefix), "").Trim();
            break;
        }

        return MyRegex().Replace(input, " ").Trim();
    }

    public static string? RemovePrefixProvince(string? province)
    {
        return RemovePrefix(province, PREFIXES_PROVINCE);
    }

    public static string? RemovePrefixDistrict(string? district)
    {
        return RemovePrefix(district, PREFIXES_DISTRICT);
    }

    public static string? RemovePrefixWard(string? ward)
    {
        return RemovePrefix(ward, PREFIXES_WARD);
    }

    public static string? GetWard(string? address)
    {
        string? addressClean = CleanAddress(address);
        string[] parts = addressClean.Split(',');
        if (parts.Length < 4)
        {
            return null;
        }
        return parts[parts.Length - 3].Trim();
    }

    public static string? GetDistrict(string? address)
    {
        string? addressClean = CleanAddress(address);
        string[] parts = addressClean.Split(',');
        if (parts.Length < 3)
        {
            return null;
        }
        return parts[parts.Length - 2].Trim();
    }

    public static string? GetProvince(string? address)
    {
        string? addressClean = CleanAddress(address);
        string[] parts = addressClean.Split(',');
        if (parts.Length < 2)
        {
            return null;
        }
        return parts[parts.Length - 1].Trim();
    }

    public static string? CleanAddress(string? address)
    {
        if (string.IsNullOrWhiteSpace(address))
        {
            return "";
        }
        address = Regex.Replace(address.Trim(), "\\s+", " ").Replace(",", ",");
        address = RemovePostCode(address);
        address = RemoveCountryName(address);
        return address;
    }

    private static string? RemovePostCode(string? address)
    {
        if (string.IsNullOrWhiteSpace(address))
        {
            return address;
        }

        // Remove postal codes (5 or 6 digits)
        address = Regex.Replace(address, "\\b\\d{5,6}\\b", "");

        // Remove any extra spaces or commas left after removing postal codes
        address = Regex.Replace(address, "\\s{2,}", " ").Trim();
        address = Regex.Replace(address, "\\s*,\\s*", ", ").Trim();
        address = Regex.Replace(address, "\\s*,\\s*$", "").Trim();

        return address;
    }

    private static string? RemoveCountryName(string? address)
    {
        string[] parts = address.Split(',');
        List<string> countryNames = new List<string>
        {
            "Vietnam", "Việt Nam", "VN", "Viet Nam",
            "vietnam", "việt nam", "vn", "viet nam",
            "VIETNAM", "VIỆT NAM", "VN", "VIET NAM",
            "ViEtNaM", "ViỆt NaM", "vN", "ViEt NaM"
        };
        foreach (string part in parts)
        {
            if (countryNames.Contains(part.Trim()))
            {
                address = address.Replace(part, "");
            }
        }
        // remove comma or dot at the end of address
        address = Regex.Replace(address, "[,.]+$", "");
        if (address.EndsWith(","))
        {
            address = address.TrimEnd(',');
        }
        return address;
    }

    public enum STATUS
    {
        VERIFIED,
        CONFLICT,
        OTHER_COUNTRY
    }

    [GeneratedRegex("\\s+")]
    private static partial Regex MyRegex();
}