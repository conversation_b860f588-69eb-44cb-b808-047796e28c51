using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Keyless]
[Table("sheet2")]
public partial class Sheet2
{
    [Column("Column1.id")]
    public int? Column1Id { get; set; }

    [Column("Column1.0")]
    public int? Column10 { get; set; }

    [Column("Column1.matinh")]
    [StringLength(50)]
    public string? Column1Matinh { get; set; }

    [Column("Column1.1")]
    public int? Column11 { get; set; }

    [Column("Column1.ma")]
    [StringLength(50)]
    public string? Column1Ma { get; set; }

    [Column("Column1.2")]
    [StringLength(50)]
    public string? Column12 { get; set; }

    [Column("Column1.tentinh")]
    [StringLength(50)]
    public string? Column1Tentinh { get; set; }

    [Column("Column1.3")]
    [StringLength(50)]
    public string? Column13 { get; set; }

    [Column("Column1.loai")]
    [StringLength(50)]
    public string? Column1Loai { get; set; }

    [Column("Column1.4")]
    [StringLength(50)]
    public string? Column14 { get; set; }

    [Column("Column1.tenhc")]
    [StringLength(50)]
    public string? Column1Tenhc { get; set; }

    [Column("Column1.5")]
    [StringLength(50)]
    public string? Column15 { get; set; }

    [Column("Column1.cay")]
    [StringLength(50)]
    public string? Column1Cay { get; set; }

    [Column("Column1.6")]
    [StringLength(50)]
    public string? Column16 { get; set; }

    [Column("Column1.dientichkm2")]
    public int? Column1Dientichkm2 { get; set; }

    [Column("Column1.7")]
    public int? Column17 { get; set; }

    [Column("Column1.dansonguoi")]
    [StringLength(50)]
    public string? Column1Dansonguoi { get; set; }

    [Column("Column1.8")]
    [StringLength(50)]
    public string? Column18 { get; set; }

    [Column("Column1.trungtamhc")]
    [StringLength(64)]
    public string? Column1Trungtamhc { get; set; }

    [Column("Column1.9")]
    [StringLength(64)]
    public string? Column19 { get; set; }

    [Column("Column1.kinhdo")]
    public int? Column1Kinhdo { get; set; }

    [Column("Column1.10")]
    public int? Column110 { get; set; }

    [Column("Column1.vido")]
    public int? Column1Vido { get; set; }

    [Column("Column1.11")]
    public int? Column111 { get; set; }

    [Column("Column1.truocsapnhap")]
    [StringLength(1024)]
    public string? Column1Truocsapnhap { get; set; }

    [Column("Column1.12")]
    [StringLength(1024)]
    public string? Column112 { get; set; }

    [Column("Column1.maxa")]
    public int? Column1Maxa { get; set; }

    [Column("Column1.13")]
    public int? Column113 { get; set; }

    [Column("Column1.mahc")]
    public int? Column1Mahc { get; set; }

    [Column("Column1.con")]
    [StringLength(50)]
    public string? Column1Con { get; set; }
}
