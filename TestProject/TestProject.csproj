<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<LangVersion>latest</LangVersion>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<IsPackable>false</IsPackable>
	</PropertyGroup>

	<ItemGroup>
		<None Update="appsettings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Elastic.Clients.Elasticsearch" Version="8.17.4" />
		<PackageReference Include="coverlet.collector" Version="6.0.4">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="8.0.0" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
		<PackageReference Include="NUnit" Version="4.3.2" />
		<PackageReference Include="NUnit.Analyzers" Version="4.7.0">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="NUnit3TestAdapter" Version="5.0.0" />
		<PackageReference Include="Moq" Version="4.20.72" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Serilog.Sinks.Debug" Version="2.0.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\NDS.ETL.BackgroundJobs\NDS.ETL.BackgroundJobs.csproj" />
		<ProjectReference Include="..\NDS.ETL.Infrastructure\NDS.ETL.Infrastructure.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Using Include="NUnit.Framework" />
	</ItemGroup>

</Project>
