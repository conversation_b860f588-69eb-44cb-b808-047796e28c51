using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Keyless]
[Table("provinces_bak")]
public partial class ProvincesBak
{
    [Column("id")]
    public int? Id { get; set; }

    [Column("code", TypeName = "character varying")]
    public string? Code { get; set; }

    [Column("name", TypeName = "character varying")]
    public string? Name { get; set; }

    [Column("type")]
    public int? Type { get; set; }

    [Column("boundary")]
    public string? Boundary { get; set; }

    [Column("center_location", TypeName = "character varying")]
    public string? CenterLocation { get; set; }

    [Column("processed")]
    public bool? Processed { get; set; }

    [Column("boundary_geom")]
    public string? BoundaryGeom { get; set; }
}
