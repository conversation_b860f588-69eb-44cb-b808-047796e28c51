using System.Diagnostics;
using Elastic.Clients.Elasticsearch;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Entities.Context;
using NDS.ETL.Infrastructure.Common;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.ScanPlaces;

public class SyncDataScannedToEsTask : RecurringJobBaseElastic
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<SyncDataScannedToEsTask> _logger;
    private readonly IESPlacesService _iesPlacesServiceService;
    private readonly IScopedResolver<NpgPlaceContext> _dbContextResolver;
    
    public SyncDataScannedToEsTask(IConfiguration configuration, ILogger<SyncDataScannedToEsTask> logger, ElasticsearchClient elasticClient, IScopedResolver<NpgPlaceContext> dbContextResolver,
        IESPlacesService iesPlacesServiceService
    ) : base(configuration, logger, elasticClient, null)
    {
        _configuration = configuration;
        _logger = logger;
        _dbContextResolver = dbContextResolver;
        _iesPlacesServiceService = iesPlacesServiceService;
    }

    public override string JobId => "SyncDataScannedToEs";
    public override string ConfigSection => "JobPgSyncPlace";
    public override async Task DoWorkAsync(CancellationToken stoppingToken)
    {
        var begin = Stopwatch.GetTimestamp();
        Console.WriteLine($"SyncDataScannedToEs DoWorkAsync");
        _logger.LogInformation("SyncDataScannedToEs DoWorkAsync");
        try
        {
            await _dbContextResolver.ResolveAsync(async (dbContext) => {
                dbContext.Database.SetCommandTimeout(300);
                
                var placeQueue = await dbContext.PlacesQueues.Where(x => x.Status == 2).OrderBy(x=> x.CreateDate).FirstOrDefaultAsync(stoppingToken);
                var esQueue = await dbContext.EsQueues.Where(x => placeQueue != null && x.PlacesQueueId == placeQueue.Id && x.Status == 1).OrderBy(x => x.OrderNum).FirstOrDefaultAsync(cancellationToken: stoppingToken);
                await dbContext.PlacesQueues.Where(x => x.Id == placeQueue.Id).ExecuteUpdateAsync(s => 
                        s.SetProperty(e => e.Status, e => 2), stoppingToken // Đang xử lý
                );
                //await dbContext.SaveChangesAsync(stoppingToken);
                var skip = 0;
                const int take = 2000;
                var count = 0;
                while (true)
                {
                    var t1 = Stopwatch.GetTimestamp();
                    var data = await dbContext.PlacesScanHistories
                        .Where(x => esQueue != null && x.HangfireJobId == esQueue.HangfireJobId)
                        .Select(x => x.Placeid)
                        .Distinct()
                        .OrderBy(x => x) // Order the distinct PlaceId values
                        .Skip(skip).Take(take)
                        .ToListAsync(cancellationToken: stoppingToken);

                    if (data.Count != 0 != true) break;
                    //Chỉ đánh dấu các places đã scan vào es
                    var placeEs = await _iesPlacesServiceService.GetPlacesByPlaceIdsAsync(data, stoppingToken, 4000);
                    foreach (var item in placeEs)
                    {
                        item.ScanStatus = 1; //
                        item.Note = esQueue?.Description;
                    }
                    count = count + data.Count;
                    skip += take;
                    await base.IndexMany(placeEs, stoppingToken, 2000);
                    var t2 = Stopwatch.GetTimestamp();
                    Console.WriteLine($"Time to Scan {take} trust place in total: {count} items scan ---------------: {TimeSpan.FromTicks(t2 - t1).TotalSeconds} seconds");
                }
                
                var end = Stopwatch.GetTimestamp();
                await dbContext.EsQueues.Where(x => esQueue != null && x.Id == esQueue.Id).ExecuteUpdateAsync(s => 
                        s.SetProperty(e => e.Status, e => 3), stoppingToken
                );
                var placeQueueRs = await dbContext.EsQueues.Where(x => x.PlacesQueueId == placeQueue.Id && x.Status == 1).ToListAsync(cancellationToken: stoppingToken);
                if (placeQueueRs.Count == 0)
                {
                    await dbContext.PlacesQueues.Where(x => placeQueue != null && x.Id == placeQueue.Id).ExecuteUpdateAsync(s => 
                            s.SetProperty(e => e.Status, e => 3)
                            .SetProperty(e => e.UpdateDate, e => DateTime.Now), stoppingToken
                    );
                }
                await dbContext.SaveChangesAsync(stoppingToken);
                Console.WriteLine($"Time to Scan total: {count} Collection Place to es---------------: {TimeSpan.FromTicks(end - begin).TotalSeconds} seconds");
                RecurringJob.TriggerJob("ScanPlaces");
                await Task.CompletedTask;
                return true;
            });
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }
}