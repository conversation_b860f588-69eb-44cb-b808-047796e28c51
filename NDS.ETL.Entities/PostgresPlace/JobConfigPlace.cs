using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using NodaTime;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("job_config_place")]
public partial class JobConfigPlace
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("jobId")]
    public string? JobId { get; set; }

    [Column("keyConfig")]
    public string? KeyConfig { get; set; }

    [Column("valueConfig")]
    public string? ValueConfig { get; set; }

    [Column("description")]
    public string? Description { get; set; }

    [Column("createdAt")]
        public DateTime? CreateAt { get; set; }

    [Column("updatedAt")]
        public DateTime? UpdateAt { get; set; }

    [Column("lastUpdateUser")]
    public string? LastUpdateUser { get; set; }

    [Column("creatorUser")]
    public string? CreatorUser { get; set; }
}
