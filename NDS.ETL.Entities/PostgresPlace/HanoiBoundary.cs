using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Keyless]
[Table("hanoi_boundary")]
public partial class HanoiBoundary
{
    [Column("osm_id")]
    public int? OsmId { get; set; }

    [Column("name")]
    [StringLength(500)]
    public string? Name { get; set; }

    [Column("addr")]
    [StringLength(500)]
    public string? Addr { get; set; }

    [Column("amenity")]
    [StringLength(500)]
    public string? Amenity { get; set; }

    [Column("boundary")]
    [StringLength(500)]
    public string? Boundary { get; set; }

    [Column("building")]
    [StringLength(500)]
    public string? Building { get; set; }

    [Column("operator")]
    [StringLength(500)]
    public string? Operator { get; set; }

    [Column("way_area")]
    public float? WayArea { get; set; }

    [Column("st_astext", TypeName = "character varying")]
    public string? StAstext { get; set; }
}
