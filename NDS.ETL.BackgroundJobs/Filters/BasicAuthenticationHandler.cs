using System.Security.Claims;
using System.Text;
using System.Text.Encodings.Web;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;

namespace NDS.ETL.BackgroundJobs.Filters
{
    public class BasicAuthenticationHandler : AuthenticationHandler<AuthenticationSchemeOptions>
    {
        private readonly IConfiguration _config;

        public BasicAuthenticationHandler(
            IConfiguration config,
            IOptionsMonitor<AuthenticationSchemeOptions> options,
            ILoggerFactory logger,
            UrlEncoder encoder,
            ISystemClock clock)
            : base(options, logger, encoder, clock)
        {
            _config = config;
        }

        protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            if (!Request.Headers.ContainsKey("Authorization"))
            {
                return UnauthorizedResponse("Missing Authorization Header");
            }

            var authHeader = Request.Headers["Authorization"].ToString();
            if (!authHeader.StartsWith("Basic ", StringComparison.OrdinalIgnoreCase))
            {
                return UnauthorizedResponse("Invalid Authorization Header");
            }

            var credentials = DecodeCredentials(authHeader.Substring("Basic ".Length).Trim());
            if (credentials == null || credentials.Length != 2)
            {
                return UnauthorizedResponse("Invalid Credentials Format");
            }

            var username = credentials[0];
            var password = credentials[1];

            if (!IsValidUser(username, password))
            {
                return UnauthorizedResponse("Invalid Username or Password");
            }

            var claims = new[] { new Claim(ClaimTypes.Name, username) };
            var identity = new ClaimsIdentity(claims, Scheme.Name);
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, Scheme.Name);

            return AuthenticateResult.Success(ticket);
        }

        /// <summary>
        /// Giải mã Base64 của Authorization Header
        /// </summary>
        private string[]? DecodeCredentials(string encodedCredentials)
        {
            try
            {
                var decodedBytes = Convert.FromBase64String(encodedCredentials);
                var decodedString = Encoding.UTF8.GetString(decodedBytes);
                return decodedString.Split(':', 2);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Kiểm tra thông tin đăng nhập với cấu hình
        /// </summary>
        private bool IsValidUser(string username, string password)
        {
            return username == _config["BasicAuth:UserName"] && password == _config["BasicAuth:Password"];
        }

        /// <summary>
        /// Tạo phản hồi 401 Unauthorized với header WWW-Authenticate
        /// </summary>
        private AuthenticateResult UnauthorizedResponse(string message)
        {
            Response.Headers["WWW-Authenticate"] = "Basic realm=\"Jira Ops\"";
            return AuthenticateResult.Fail(message);
        }
    }
}
