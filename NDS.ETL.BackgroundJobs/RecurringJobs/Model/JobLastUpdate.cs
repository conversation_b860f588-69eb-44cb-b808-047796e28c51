using System;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.Model;

/// <summary>
/// Lớp lưu trữ thông tin về lần đồng bộ cuối cùng của một loại dữ liệu
/// </summary>
public class JobLastUpdate
{
    /// <summary>
    /// ID của bản ghi
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// Thời gian đồng bộ cuối cùng
    /// </summary>
    public DateTime LastUpdate { get; set; }
    
    /// <summary>
    /// Loại dữ liệu được đồng bộ
    /// </summary>
    public TableSyncType SyncType { get; set; }
} 