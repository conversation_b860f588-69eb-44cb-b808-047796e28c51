using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text;
using System.Text.Json;
using Elastic.Clients.Elasticsearch;
using Elastic.Transport;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;

namespace ESTestRunner
{
    class Program
    {
        static async Task Main(string[] args)
        {
            try
            {
                Console.WriteLine("===== ELASTICSEARCH DIRECT QUERY LOAD TEST =====");
                Console.WriteLine("Initializing test environment...");
                
                // Đọc cấu hình
                var configuration = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .Build();

                // Lấy cấu hình Elasticsearch
                var elasticSearchConfig = configuration.GetSection("ElasticSearchConfig");
                var url = elasticSearchConfig["Url:0"]!;
                var username = elasticSearchConfig["UserName"]!;
                var password = elasticSearchConfig["Password"]!;
                var indexName = elasticSearchConfig["Index:0"]!;

                // Lấy cấu hình timeout từ LoadTestConfig nếu có
                var requestTimeout = 30; // Mặc định là 30 giây
                if (int.TryParse(configuration.GetSection("LoadTestConfig")["RequestTimeout"], out int configTimeout))
                {
                    requestTimeout = configTimeout;
                }

                // Khởi tạo Elasticsearch client
                var settings = new ElasticsearchClientSettings(new Uri(url))
                    .DefaultIndex(indexName)
                    .Authentication(new BasicAuthentication(username, password))
                    .ServerCertificateValidationCallback((sender, cert, chain, errors) => true)
                    .EnableDebugMode()
                    .DisableDirectStreaming()
                    .RequestTimeout(TimeSpan.FromSeconds(requestTimeout));

                var elasticClient = new ElasticsearchClient(settings);

                Console.WriteLine("Successfully connected to Elasticsearch at: " + url);
                Console.WriteLine("Using index: " + indexName);
                
                Console.WriteLine("\nTEST OPTIONS:");
                Console.WriteLine("1. Run original query load test");
                Console.WriteLine("2. Run optimized query load test");
                Console.WriteLine("3. Run comparison (both tests sequentially)");
                Console.Write("\nEnter your choice (1-3): ");
                
                string choice = Console.ReadLine() ?? "3";
                
                if (choice == "1")
                {
                    Console.WriteLine("\nStarting LoadTest_ElasticsearchDirectQuery_MeasurePerformanceMetrics...");
                    Console.WriteLine("Please wait while the test is running...");
                    await LoadTest_ElasticsearchDirectQuery_MeasurePerformanceMetrics(elasticClient, indexName, username, password);
                }
                else if (choice == "2")
                {
                    Console.WriteLine("\nStarting LoadTest_ElasticsearchOptimizedQuery_MeasurePerformanceMetrics...");
                    Console.WriteLine("Please wait while the test is running...");
                    await LoadTest_ElasticsearchOptimizedQuery_MeasurePerformanceMetrics(elasticClient, indexName, username, password);
                }
                else
                {
                    Console.WriteLine("\nRunning comparison between original and optimized queries...");
                    Console.WriteLine("\n--- ORIGINAL QUERY TEST ---");
                    var originalResults = await LoadTest_ElasticsearchDirectQuery_MeasurePerformanceMetrics(elasticClient, indexName, username, password);
                    
                    Console.WriteLine("\n--- OPTIMIZED QUERY TEST ---");
                    var optimizedResults = await LoadTest_ElasticsearchOptimizedQuery_MeasurePerformanceMetrics(elasticClient, indexName, username, password);
                    
                    // Show comparison summary
                    Console.WriteLine("\n=== PERFORMANCE COMPARISON ===");
                    Console.WriteLine($"Total Requests: {originalResults.Count}");
                    
                    if (originalResults.Count > 0 && optimizedResults.Count > 0)
                    {
                        Console.WriteLine($"Average Response Time (Original): {originalResults.Average():F2} ms");
                        Console.WriteLine($"Average Response Time (Optimized): {optimizedResults.Average():F2} ms");
                        
                        var improvement = (1 - (optimizedResults.Average() / originalResults.Average())) * 100;
                        Console.WriteLine($"Performance Improvement: {improvement:F2}%");
                        
                        var originalP95 = originalResults.OrderBy(t => t).ElementAt((int)(originalResults.Count * 0.95));
                        var optimizedP95 = optimizedResults.OrderBy(t => t).ElementAt((int)(optimizedResults.Count * 0.95));
                        Console.WriteLine($"P95 Response Time (Original): {originalP95:F2} ms");
                        Console.WriteLine($"P95 Response Time (Optimized): {optimizedP95:F2} ms");
                        Console.WriteLine($"P95 Improvement: {(1 - (optimizedP95 / originalP95)) * 100:F2}%");
                    }
                }
                
                Console.WriteLine("Test completed successfully!");
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Test failed with error: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
                Console.WriteLine("\nPress any key to exit...");
                Console.ReadKey();
                Environment.Exit(1);
            }
        }

        static async Task<ConcurrentBag<double>> LoadTest_ElasticsearchDirectQuery_MeasurePerformanceMetrics(ElasticsearchClient elasticClient, string indexName, string username, string password)
        {
            // Đọc cấu hình từ configuration service
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            // Lấy cấu hình load test
            var loadTestConfig = configuration.GetSection("LoadTestConfig");
            
            // Arrange
            int concurrentRequests = 20; // Mặc định: 20 request đồng thời
            int iterations = 5; // Mặc định: 5 lần lặp lại
            int requestTimeout = 30; // Mặc định: 30 giây
            
            // Đọc cấu hình từ appsettings.json
            if (int.TryParse(loadTestConfig["ConcurrentRequests"], out int configConcurrentRequests))
            {
                concurrentRequests = configConcurrentRequests;
            }
            
            if (int.TryParse(loadTestConfig["Iterations"], out int configIterations))
            {
                iterations = configIterations;
            }
            
            if (int.TryParse(loadTestConfig["RequestTimeout"], out int configTimeout))
            {
                requestTimeout = configTimeout;
            }
            
            // Định nghĩa một tập các từ khóa tìm kiếm thực tế
            var testQueries = new[] {
                "sân bay nội bài",
                "bến xe mỹ đình",
                "chùa tam chúc",
                "lotte center",
                "vincom",
                "landmark 72",
                "trường học",
                "bệnh viện",
                "nhà hàng",
                "khách sạn",
                "22 láng hạ",
                "30 trần hưng đạo",
                "chợ đồng xuân",
                "640/8 láng",
                "123/45 kim mã",
                "456 lê duẩn",
                "789/12 nguyễn trãi",
                "15/20 hoàng diệu",
                "88 tây sơn",
                "250/3 cầu giấy",
                "100 nguyễn chí thanh",
                "360/1 xã đàn",
                "520/15 minh khai"
            };
            
            // Tọa độ test (Hà Nội)
            var testCoordinates = new[] {
                new { Lat = 21.021800, Lng = 105.803879 }, // Trung tâm Hà Nội
                new { Lat = 21.031219, Lng = 105.853027 }, // Khu vực khác
                new { Lat = 20.986839, Lng = 105.828629 }, // Khu vực khác nữa
            };
            
            // Chuẩn bị container để thu thập số liệu
            var responseTimes = new ConcurrentBag<double>(); // Lưu trữ theo milliseconds
            var totalRequests = concurrentRequests * testQueries.Length * iterations;
            
            // Act
            Console.WriteLine($"Starting load test with {concurrentRequests} concurrent users × {testQueries.Length} queries × {iterations} iterations = {totalRequests} total requests");
            Console.WriteLine($"Request timeout: {requestTimeout} seconds");
            
            var overallStopwatch = Stopwatch.StartNew();
            Random random = new Random();
            
            // Các mã H3 cố định cho filter
            var h3Cells = new[] {
                "84415cbffffffff",
                "84415ddffffffff",
                "8441437ffffffff",
                "8441435ffffffff"
            };
            
            // Các mã H3 cố định cho context suggest
            var h3ContextCells = new[] {
                "85415cb7fffffff",
                "85415ca7fffffff",
                "8541436bfffffff",
                "8541437bfffffff"
            };
            
            // URL cho direct query
            var uri = elasticClient.ElasticsearchClientSettings.NodePool.Nodes.First().Uri;
            var url = $"{uri.ToString().TrimEnd('/')}/{indexName}/_search";
            
            // Sử dụng một HttpClient duy nhất cho tất cả các request
            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(requestTimeout);
            
            // Thiết lập headers cho requests
            httpClient.DefaultRequestHeaders.Add("Accept", "*/*");
            httpClient.DefaultRequestHeaders.Add("Accept-Language", "en-US,en;q=0.9,vi;q=0.8");

            // Thêm basic authentication header
            var authHeaderValue = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{username}:{password}"));
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Basic {authHeaderValue}");

            httpClient.DefaultRequestHeaders.Add("Connection", "keep-alive");
            httpClient.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36");

            for (int iteration = 0; iteration < iterations; iteration++)
            {
                var iterationTasks = new List<Task>();
                
                foreach (var query in testQueries)
                {
                    for (int i = 0; i < concurrentRequests; i++)
                    {
                        // Chọn ngẫu nhiên tọa độ từ tập test
                        var coords = testCoordinates[random.Next(testCoordinates.Length)];
                        
                        iterationTasks.Add(Task.Run(async () => {
                            var taskStopwatch = Stopwatch.StartNew();
                            
                            try
                            {
                                // Tạo JSON payload với các tham số ngẫu nhiên
                                var payload = CreateElasticsearchPayload(
                                    query,
                                    coords.Lat,
                                    coords.Lng,
                                    h3Cells,
                                    h3ContextCells,
                                    random.Next(10, 21) // Kích thước kết quả ngẫu nhiên từ 5-20
                                );
                                
                                var content = new StringContent(payload, Encoding.UTF8, "application/json");
                                
                                // Thực hiện request
                                var response = await httpClient.PostAsync(url, content);
                                
                                // Ghi nhận thời gian xử lý
                                taskStopwatch.Stop();
                                responseTimes.Add(taskStopwatch.Elapsed.TotalMilliseconds);
                                
                                // Kiểm tra kết quả
                                if (response.IsSuccessStatusCode)
                                {
                                    var responseJson = await response.Content.ReadAsStringAsync();
                                    var responseObj = JsonConvert.DeserializeObject<dynamic>(responseJson);
                                    
                                    // Xác minh rằng phản hồi có cấu trúc hợp lệ
                                    if (responseObj == null)
                                    {
                                        Console.WriteLine($"ERROR: Invalid response format for query '{query}'");
                                    }
                                }
                                else
                                {
                                    // Ghi lại lỗi nếu request không thành công
                                    Console.WriteLine($"ERROR: Request failed with status code {response.StatusCode} for query '{query}'");
                                    var errorContent = await response.Content.ReadAsStringAsync();
                                    Console.WriteLine($"ERROR: Error details: {errorContent}");
                                }
                            }
                            catch (Exception ex)
                            {
                                // Ghi lại lỗi nhưng không làm hỏng toàn bộ test
                                taskStopwatch.Stop();
                                Console.WriteLine($"ERROR: Error in request for query {query}: {ex.Message}");
                                responseTimes.Add(taskStopwatch.Elapsed.TotalMilliseconds);
                            }
                        }));
                    }
                }
                
                // Đợi cho lần lặp này hoàn thành
                await Task.WhenAll(iterationTasks);
                
                // Ghi lại tiến trình
                Console.WriteLine($"Completed iteration {iteration + 1} of {iterations}");
            }
            
            overallStopwatch.Stop();
            
            // Xử lý và báo cáo số liệu
            var totalDurationSeconds = overallStopwatch.Elapsed.TotalSeconds;
            var throughput = totalRequests / totalDurationSeconds;
            
            // Tính toán các phân vị
            var sortedResponseTimes = responseTimes.OrderBy(t => t).ToArray();
            var count = sortedResponseTimes.Length;
            
            if (count == 0)
            {
                Console.WriteLine("WARNING: No results were recorded!");
                return responseTimes;
            }
            
            var min = sortedResponseTimes.First();
            var max = sortedResponseTimes.Last();
            var avg = sortedResponseTimes.Average();
            var median = count % 2 == 0 
                ? (sortedResponseTimes[count / 2 - 1] + sortedResponseTimes[count / 2]) / 2 
                : sortedResponseTimes[count / 2];
            var p95 = sortedResponseTimes[(int)(count * 0.95)];
            var p99 = sortedResponseTimes[(int)(count * 0.99)];
            
            // Kiểm tra và ghi lại kết quả
            Console.WriteLine("=== ELASTICSEARCH ORIGINAL QUERY LOAD TEST RESULTS ===");
            Console.WriteLine($"Total Requests: {count}");
            Console.WriteLine($"Total Duration: {totalDurationSeconds:F2} seconds");
            Console.WriteLine($"Throughput: {throughput:F2} requests/second");
            Console.WriteLine($"Response Time (min): {min:F2} ms");
            Console.WriteLine($"Response Time (avg): {avg:F2} ms");
            Console.WriteLine($"Response Time (median): {median:F2} ms");
            Console.WriteLine($"Response Time (p95): {p95:F2} ms");
            Console.WriteLine($"Response Time (p99): {p99:F2} ms");
            Console.WriteLine($"Response Time (max): {max:F2} ms");
            
            // Phân phối biểu đồ tần suất
            var histogram = new Dictionary<string, int>();
            var bucketSize = 50; // 50ms buckets
            foreach (var responseTime in sortedResponseTimes)
            {
                var bucket = ((int)(responseTime / bucketSize) * bucketSize).ToString();
                if (!histogram.ContainsKey(bucket))
                    histogram[bucket] = 0;
                histogram[bucket]++;
            }
            
            Console.WriteLine("\n=== RESPONSE TIME DISTRIBUTION ===");
            foreach (var bucket in histogram.OrderBy(b => int.Parse(b.Key)))
            {
                var percentage = (double)bucket.Value / count * 100;
                Console.WriteLine($"{bucket.Key}-{int.Parse(bucket.Key) + bucketSize}ms: {bucket.Value} requests ({percentage:F1}%)");
            }
            
            // Biểu đồ đơn giản sử dụng ký tự ASCII
            Console.WriteLine("\n=== RESPONSE TIME CHART ===");
            var maxBucketCount = histogram.Max(b => b.Value);
            var chartWidth = 50; // ký tự
            foreach (var bucket in histogram.OrderBy(b => int.Parse(b.Key)))
            {
                var barLength = (int)Math.Round((double)bucket.Value / maxBucketCount * chartWidth);
                var bar = new string('#', barLength);
                Console.WriteLine($"{bucket.Key}-{int.Parse(bucket.Key) + bucketSize}ms: {bar} ({bucket.Value})");
            }

            return responseTimes;
        }

        static async Task<ConcurrentBag<double>> LoadTest_ElasticsearchOptimizedQuery_MeasurePerformanceMetrics(ElasticsearchClient elasticClient, string indexName, string username, string password)
        {
            // Đọc cấu hình từ configuration service
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            // Lấy cấu hình load test
            var loadTestConfig = configuration.GetSection("LoadTestConfig");
            
            // Arrange
            int concurrentRequests = 20; // Mặc định: 20 request đồng thời
            int iterations = 5; // Mặc định: 5 lần lặp lại
            int requestTimeout = 30; // Mặc định: 30 giây
            
            // Đọc cấu hình từ appsettings.json
            if (int.TryParse(loadTestConfig["ConcurrentRequests"], out int configConcurrentRequests))
            {
                concurrentRequests = configConcurrentRequests;
            }
            
            if (int.TryParse(loadTestConfig["Iterations"], out int configIterations))
            {
                iterations = configIterations;
            }
            
            if (int.TryParse(loadTestConfig["RequestTimeout"], out int configTimeout))
            {
                requestTimeout = configTimeout;
            }
            
            // Định nghĩa một tập các từ khóa tìm kiếm thực tế
            var testQueries = new[] {
                "sân bay nội bài",
                "bến xe mỹ đình",
                "chùa tam chúc",
                "lotte center",
                "vincom",
                "landmark 72",
                "trường học",
                "bệnh viện",
                "nhà hàng",
                "khách sạn",
                "22 láng hạ",
                "30 trần hưng đạo",
                "chợ đồng xuân",
                "640/8 láng",
                "123/45 kim mã",
                "456 lê duẩn",
                "789/12 nguyễn trãi",
                "15/20 hoàng diệu",
                "88 tây sơn",
                "250/3 cầu giấy",
                "100 nguyễn chí thanh",
                "360/1 xã đàn",
                "520/15 minh khai"
            };
            
            // Tọa độ test (Hà Nội)
            var testCoordinates = new[] {
                new { Lat = 21.021800, Lng = 105.803879 }, // Trung tâm Hà Nội
                new { Lat = 21.031219, Lng = 105.853027 }, // Khu vực khác
                new { Lat = 20.986839, Lng = 105.828629 }, // Khu vực khác nữa
            };
            
            // Chuẩn bị container để thu thập số liệu
            var responseTimes = new ConcurrentBag<double>(); // Lưu trữ theo milliseconds
            var totalRequests = concurrentRequests * testQueries.Length * iterations;
            
            // Act
            Console.WriteLine($"Starting load test with {concurrentRequests} concurrent users × {testQueries.Length} queries × {iterations} iterations = {totalRequests} total requests");
            Console.WriteLine($"Request timeout: {requestTimeout} seconds");
            
            var overallStopwatch = Stopwatch.StartNew();
            Random random = new Random();
            
            // Các mã H3 cố định cho filter
            var h3Cells = new[] {
                "84415cbffffffff",
                "84415ddffffffff",
                "8441437ffffffff",
                "8441435ffffffff"
            };
            
            // URL cho direct query
            var uri = elasticClient.ElasticsearchClientSettings.NodePool.Nodes.First().Uri;
            var url = $"{uri.ToString().TrimEnd('/')}/{indexName}/_search";
            
            // Sử dụng một HttpClient duy nhất cho tất cả các request
            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(requestTimeout);
            
            // Thiết lập headers cho requests
            httpClient.DefaultRequestHeaders.Add("Accept", "*/*");
            httpClient.DefaultRequestHeaders.Add("Accept-Language", "en-US,en;q=0.9,vi;q=0.8");

            // Thêm basic authentication header
            var authHeaderValue = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{username}:{password}"));
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Basic {authHeaderValue}");

            httpClient.DefaultRequestHeaders.Add("Connection", "keep-alive");
            httpClient.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36");

            for (int iteration = 0; iteration < iterations; iteration++)
            {
                var iterationTasks = new List<Task>();
                
                foreach (var query in testQueries)
                {
                    for (int i = 0; i < concurrentRequests; i++)
                    {
                        // Chọn ngẫu nhiên tọa độ từ tập test
                        var coords = testCoordinates[random.Next(testCoordinates.Length)];
                        
                        iterationTasks.Add(Task.Run(async () => {
                            var taskStopwatch = Stopwatch.StartNew();
                            
                            try
                            {
                                // Tạo JSON payload với các tham số ngẫu nhiên
                                var payload = CreateOptimizedElasticsearchPayload(
                                    query,
                                    coords.Lat,
                                    coords.Lng,
                                    h3Cells,
                                    random.Next(10, 21) // Kích thước kết quả ngẫu nhiên từ 5-20
                                );
                                
                                var content = new StringContent(payload, Encoding.UTF8, "application/json");
                                
                                // Thực hiện request
                                var response = await httpClient.PostAsync(url, content);
                                
                                // Ghi nhận thời gian xử lý
                                taskStopwatch.Stop();
                                responseTimes.Add(taskStopwatch.Elapsed.TotalMilliseconds);
                                
                                // Kiểm tra kết quả
                                if (response.IsSuccessStatusCode)
                                {
                                    var responseJson = await response.Content.ReadAsStringAsync();
                                    var responseObj = JsonConvert.DeserializeObject<dynamic>(responseJson);
                                    
                                    // Xác minh rằng phản hồi có cấu trúc hợp lệ
                                    if (responseObj == null)
                                    {
                                        Console.WriteLine($"ERROR: Invalid response format for query '{query}'");
                                    }
                                }
                                else
                                {
                                    // Ghi lại lỗi nếu request không thành công
                                    Console.WriteLine($"ERROR: Request failed with status code {response.StatusCode} for query '{query}'");
                                    var errorContent = await response.Content.ReadAsStringAsync();
                                    Console.WriteLine($"ERROR: Error details: {errorContent}");
                                }
                            }
                            catch (Exception ex)
                            {
                                // Ghi lại lỗi nhưng không làm hỏng toàn bộ test
                                taskStopwatch.Stop();
                                Console.WriteLine($"ERROR: Error in request for query {query}: {ex.Message}");
                                responseTimes.Add(taskStopwatch.Elapsed.TotalMilliseconds);
                            }
                        }));
                    }
                }
                
                // Đợi cho lần lặp này hoàn thành
                await Task.WhenAll(iterationTasks);
                
                // Ghi lại tiến trình
                Console.WriteLine($"Completed iteration {iteration + 1} of {iterations}");
            }
            
            overallStopwatch.Stop();
            
            // Xử lý và báo cáo số liệu
            var totalDurationSeconds = overallStopwatch.Elapsed.TotalSeconds;
            var throughput = totalRequests / totalDurationSeconds;
            
            // Tính toán các phân vị
            var sortedResponseTimes = responseTimes.OrderBy(t => t).ToArray();
            var count = sortedResponseTimes.Length;
            
            if (count == 0)
            {
                Console.WriteLine("WARNING: No results were recorded!");
                return responseTimes;
            }
            
            var min = sortedResponseTimes.First();
            var max = sortedResponseTimes.Last();
            var avg = sortedResponseTimes.Average();
            var median = count % 2 == 0 
                ? (sortedResponseTimes[count / 2 - 1] + sortedResponseTimes[count / 2]) / 2 
                : sortedResponseTimes[count / 2];
            var p95 = sortedResponseTimes[(int)(count * 0.95)];
            var p99 = sortedResponseTimes[(int)(count * 0.99)];
            
            // Kiểm tra và ghi lại kết quả
            Console.WriteLine("=== ELASTICSEARCH OPTIMIZED QUERY LOAD TEST RESULTS ===");
            Console.WriteLine($"Total Requests: {count}");
            Console.WriteLine($"Total Duration: {totalDurationSeconds:F2} seconds");
            Console.WriteLine($"Throughput: {throughput:F2} requests/second");
            Console.WriteLine($"Response Time (min): {min:F2} ms");
            Console.WriteLine($"Response Time (avg): {avg:F2} ms");
            Console.WriteLine($"Response Time (median): {median:F2} ms");
            Console.WriteLine($"Response Time (p95): {p95:F2} ms");
            Console.WriteLine($"Response Time (p99): {p99:F2} ms");
            Console.WriteLine($"Response Time (max): {max:F2} ms");
            
            // Phân phối biểu đồ tần suất
            var histogram = new Dictionary<string, int>();
            var bucketSize = 50; // 50ms buckets
            foreach (var responseTime in sortedResponseTimes)
            {
                var bucket = ((int)(responseTime / bucketSize) * bucketSize).ToString();
                if (!histogram.ContainsKey(bucket))
                    histogram[bucket] = 0;
                histogram[bucket]++;
            }
            
            Console.WriteLine("\n=== RESPONSE TIME DISTRIBUTION ===");
            foreach (var bucket in histogram.OrderBy(b => int.Parse(b.Key)))
            {
                var percentage = (double)bucket.Value / count * 100;
                Console.WriteLine($"{bucket.Key}-{int.Parse(bucket.Key) + bucketSize}ms: {bucket.Value} requests ({percentage:F1}%)");
            }
            
            // Biểu đồ đơn giản sử dụng ký tự ASCII
            Console.WriteLine("\n=== RESPONSE TIME CHART ===");
            var maxBucketCount = histogram.Max(b => b.Value);
            var chartWidth = 50; // ký tự
            foreach (var bucket in histogram.OrderBy(b => int.Parse(b.Key)))
            {
                var barLength = (int)Math.Round((double)bucket.Value / maxBucketCount * chartWidth);
                var bar = new string('#', barLength);
                Console.WriteLine($"{bucket.Key}-{int.Parse(bucket.Key) + bucketSize}ms: {bar} ({bucket.Value})");
            }

            return responseTimes;
        }

        /// <summary>
        /// Tạo payload JSON cho truy vấn Elasticsearch gốc
        /// </summary>
        private static string CreateElasticsearchPayload(string query, double lat, double lon, string[] h3Cells, string[] h3ContextCells, int size)
        {
            // Xây dựng JSON payload cho truy vấn Elasticsearch
            var h3CellsString = string.Join("\", \"", h3Cells);
            var h3ContextString = string.Join("\", \"", h3ContextCells);
            
            // Xử lý escape các ký tự đặc biệt trong query
            query = JsonConvert.ToString(query).Trim('"');
            
            // Sử dụng StringBuilder để tránh vấn đề với chuỗi lớn
            var jsonBuilder = new StringBuilder();
            
            jsonBuilder.Append("{\n");
            jsonBuilder.Append("  \"collapse\": {\n");
            jsonBuilder.Append("    \"field\": \"fullAddress.keyword\"\n");
            jsonBuilder.Append("  },\n");
            jsonBuilder.Append("  \"query\": {\n");
            jsonBuilder.Append("    \"bool\": {\n");
            jsonBuilder.Append("      \"filter\": {\n");
            jsonBuilder.Append("        \"bool\": {\n");
            jsonBuilder.Append("          \"must\": [\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"terms\": {\n");
            jsonBuilder.Append("                \"h3.4.keyword\": [\n");
            jsonBuilder.Append($"                  \"{h3CellsString}\"\n");
            jsonBuilder.Append("                ]\n");
            jsonBuilder.Append("              }\n");
            jsonBuilder.Append("            },\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"geo_distance\": {\n");
            jsonBuilder.Append("                \"location\": {\n");
            jsonBuilder.Append($"                  \"lat\": {lat},\n");
            jsonBuilder.Append($"                  \"lon\": {lon}\n");
            jsonBuilder.Append("                },\n");
            jsonBuilder.Append("                \"distance\": \"50km\"\n");
            jsonBuilder.Append("              }\n");
            jsonBuilder.Append("            }\n");
            jsonBuilder.Append("          ]\n");
            jsonBuilder.Append("        }\n");
            jsonBuilder.Append("      },\n");
            jsonBuilder.Append("      \"must\": {\n");
            jsonBuilder.Append("        \"bool\": {\n");
            jsonBuilder.Append("          \"minimum_should_match\": 1,\n");
            jsonBuilder.Append("          \"should\": [\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"bool\": {\n");
            jsonBuilder.Append("                \"minimum_should_match\": 1,\n");
            jsonBuilder.Append("                \"should\": [\n");
            jsonBuilder.Append("                  {\n");
            jsonBuilder.Append("                    \"match_phrase_prefix\": {\n");
            jsonBuilder.Append("                      \"name\": {\n");
            jsonBuilder.Append("                        \"boost\": 30,\n");
            jsonBuilder.Append($"                        \"query\": \"{query}\"\n");
            jsonBuilder.Append("                      }\n");
            jsonBuilder.Append("                    }\n");
            jsonBuilder.Append("                  },\n");
            jsonBuilder.Append("                  {\n");
            jsonBuilder.Append("                    \"match_phrase\": {\n");
            jsonBuilder.Append("                      \"name\": {\n");
            jsonBuilder.Append("                        \"boost\": 25,\n");
            jsonBuilder.Append($"                        \"query\": \"{query}\"\n");
            jsonBuilder.Append("                      }\n");
            jsonBuilder.Append("                    }\n");
            jsonBuilder.Append("                  },\n");
            jsonBuilder.Append("                  {\n");
            jsonBuilder.Append("                    \"prefix\": {\n");
            jsonBuilder.Append("                      \"keywords.raw\": {\n");
            jsonBuilder.Append("                        \"boost\": 20,\n");
            jsonBuilder.Append($"                        \"value\": \"{query}\"\n");
            jsonBuilder.Append("                      }\n");
            jsonBuilder.Append("                    }\n");
            jsonBuilder.Append("                  },\n");
            jsonBuilder.Append("                  {\n");
            jsonBuilder.Append("                    \"match_phrase_prefix\": {\n");
            jsonBuilder.Append("                      \"keywords\": {\n");
            jsonBuilder.Append("                        \"boost\": 10,\n");
            jsonBuilder.Append($"                        \"query\": \"{query}\"\n");
            jsonBuilder.Append("                      }\n");
            jsonBuilder.Append("                    }\n");
            jsonBuilder.Append("                  },\n");
            jsonBuilder.Append("                  {\n");
            jsonBuilder.Append("                    \"match_phrase\": {\n");
            jsonBuilder.Append("                      \"keywords.raw\": {\n");
            jsonBuilder.Append("                        \"boost\": 5,\n");
            jsonBuilder.Append($"                        \"query\": \"{query}\"\n");
            jsonBuilder.Append("                      }\n");
            jsonBuilder.Append("                    }\n");
            jsonBuilder.Append("                  },\n");
            jsonBuilder.Append("                  {\n");
            jsonBuilder.Append("                    \"match\": {\n");
            jsonBuilder.Append("                      \"keywords.raw\": {\n");
            jsonBuilder.Append("                        \"boost\": 1,\n");
            jsonBuilder.Append($"                        \"query\": \"{query}\"\n");
            jsonBuilder.Append("                      }\n");
            jsonBuilder.Append("                    }\n");
            jsonBuilder.Append("                  }\n");
            jsonBuilder.Append("                ]\n");
            jsonBuilder.Append("              }\n");
            jsonBuilder.Append("            },\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"multi_match\": {\n");
            jsonBuilder.Append("                \"boost\": 5,\n");
            jsonBuilder.Append("                \"fields\": [\n");
            jsonBuilder.Append("                  \"addressPermuted^3\",\n");
            jsonBuilder.Append("                  \"addressPermuted.suggest^2\",\n");
            jsonBuilder.Append("                  \"keywords.suggest^4\"\n");
            jsonBuilder.Append("                ],\n");
            jsonBuilder.Append("                \"minimum_should_match\": \"75%\",\n");
            jsonBuilder.Append("                \"operator\": \"and\",\n");
            jsonBuilder.Append($"                \"query\": \"{query}\",\n");
            jsonBuilder.Append("                \"tie_breaker\": 0.3,\n");
            jsonBuilder.Append("                \"type\": \"most_fields\"\n");
            jsonBuilder.Append("              }\n");
            jsonBuilder.Append("            },\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"multi_match\": {\n");
            jsonBuilder.Append("                \"boost\": 1,\n");
            jsonBuilder.Append("                \"fields\": [\n");
            jsonBuilder.Append("                  \"addressPermuted^3\",\n");
            jsonBuilder.Append("                  \"addressPermuted.suggest^2\",\n");
            jsonBuilder.Append("                  \"keywords.suggest^4\"\n");
            jsonBuilder.Append("                ],\n");
            jsonBuilder.Append("                \"minimum_should_match\": \"75%\",\n");
            jsonBuilder.Append("                \"operator\": \"and\",\n");
            jsonBuilder.Append($"                \"query\": \"{query}\",\n");
            jsonBuilder.Append("                \"tie_breaker\": 0.3,\n");
            jsonBuilder.Append("                \"type\": \"most_fields\"\n");
            jsonBuilder.Append("              }\n");
            jsonBuilder.Append("            },\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"multi_match\": {\n");
            jsonBuilder.Append("                \"fields\": [\n");
            jsonBuilder.Append("                  \"addressPermuted^3\",\n");
            jsonBuilder.Append("                  \"addressPermuted.suggest^2\",\n");
            jsonBuilder.Append("                  \"keywords.suggest^4\"\n");
            jsonBuilder.Append("                ],\n");
            jsonBuilder.Append("                \"minimum_should_match\": \"100%\",\n");
            jsonBuilder.Append("                \"operator\": \"and\",\n");
            jsonBuilder.Append($"                \"query\": \"{query}\",\n");
            jsonBuilder.Append("                \"slop\": 0,\n");
            jsonBuilder.Append("                \"tie_breaker\": 0.3,\n");
            jsonBuilder.Append("                \"type\": \"phrase_prefix\"\n");
            jsonBuilder.Append("              }\n");
            jsonBuilder.Append("            },\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"nested\": {\n");
            jsonBuilder.Append("                \"path\": \"subPlaces\",\n");
            jsonBuilder.Append("                \"query\": {\n");
            jsonBuilder.Append("                  \"multi_match\": {\n");
            jsonBuilder.Append("                    \"fields\": [\n");
            jsonBuilder.Append("                      \"subPlaces.addressPermuted^3\",\n");
            jsonBuilder.Append("                      \"subPlaces.addressPermuted.suggest^1\",\n");
            jsonBuilder.Append("                      \"subPlaces.keywords^2\"\n");
            jsonBuilder.Append("                    ],\n");
            jsonBuilder.Append("                    \"minimum_should_match\": \"75%\",\n");
            jsonBuilder.Append("                    \"operator\": \"and\",\n");
            jsonBuilder.Append($"                    \"query\": \"{query}\",\n");
            jsonBuilder.Append("                    \"tie_breaker\": 0.3,\n");
            jsonBuilder.Append("                    \"type\": \"most_fields\"\n");
            jsonBuilder.Append("                  }\n");
            jsonBuilder.Append("                },\n");
            jsonBuilder.Append("                \"score_mode\": \"avg\"\n");
            jsonBuilder.Append("              }\n");
            jsonBuilder.Append("            }\n");
            jsonBuilder.Append("          ]\n");
            jsonBuilder.Append("        }\n");
            jsonBuilder.Append("      }\n");
            jsonBuilder.Append("    }\n");
            jsonBuilder.Append("  },\n");
            jsonBuilder.Append("  \"script_fields\": {\n");
            jsonBuilder.Append("    \"distance\": {\n");
            jsonBuilder.Append("      \"script\": {\n");
            jsonBuilder.Append("        \"params\": {\n");
            jsonBuilder.Append($"          \"lat\": {lat},\n");
            jsonBuilder.Append($"          \"lon\": {lon}\n");
            jsonBuilder.Append("        },\n");
            jsonBuilder.Append("        \"source\": \"doc['location'].arcDistance(params.lat, params.lon)/1000\"\n");
            jsonBuilder.Append("      }\n");
            jsonBuilder.Append("    }\n");
            jsonBuilder.Append("  },\n");
            jsonBuilder.Append($"  \"size\": {size},\n");
            jsonBuilder.Append("  \"sort\": [\n");
            jsonBuilder.Append("    {\n");
            jsonBuilder.Append("      \"popular\": {\n");
            jsonBuilder.Append("        \"order\": \"desc\"\n");
            jsonBuilder.Append("      }\n");
            jsonBuilder.Append("    },\n");
            jsonBuilder.Append("    {\n");
            jsonBuilder.Append("      \"processed\": {\n");
            jsonBuilder.Append("        \"order\": \"desc\"\n");
            jsonBuilder.Append("      }\n");
            jsonBuilder.Append("    },\n");
            jsonBuilder.Append("    {\n");
            jsonBuilder.Append("      \"_score\": {\n");
            jsonBuilder.Append("        \"order\": \"desc\"\n");
            jsonBuilder.Append("      }\n");
            jsonBuilder.Append("    },\n");
            jsonBuilder.Append("    {\n");
            jsonBuilder.Append("      \"_geo_distance\": {\n");
            jsonBuilder.Append("        \"location\": [\n");
            jsonBuilder.Append("          {\n");
            jsonBuilder.Append($"            \"lat\": {lat},\n");
            jsonBuilder.Append($"            \"lon\": {lon}\n");
            jsonBuilder.Append("          }\n");
            jsonBuilder.Append("        ],\n");
            jsonBuilder.Append("        \"distance_type\": \"plane\",\n");
            jsonBuilder.Append("        \"order\": \"asc\",\n");
            jsonBuilder.Append("        \"unit\": \"km\"\n");
            jsonBuilder.Append("      }\n");
            jsonBuilder.Append("    }\n");
            jsonBuilder.Append("  ],\n");
            jsonBuilder.Append("  \"_source\": true,\n");
            jsonBuilder.Append("  \"suggest\": {\n");
            jsonBuilder.Append($"    \"text\": \"{query}\",\n");
            jsonBuilder.Append("    \"suggest\": {\n");
            jsonBuilder.Append("      \"completion\": {\n");
            jsonBuilder.Append("        \"analyzer\": \"address_search\",\n");
            jsonBuilder.Append("        \"contexts\": {\n");
            jsonBuilder.Append("          \"location_50km\": [\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"boost\": 3,\n");
            jsonBuilder.Append($"              \"context\": \"{h3ContextCells[0]}\"\n");
            jsonBuilder.Append("            },\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"boost\": 3,\n");
            jsonBuilder.Append($"              \"context\": \"{h3ContextCells[1]}\"\n");
            jsonBuilder.Append("            },\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"boost\": 3,\n");
            jsonBuilder.Append($"              \"context\": \"{h3ContextCells[2]}\"\n");
            jsonBuilder.Append("            },\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"boost\": 3,\n");
            jsonBuilder.Append($"              \"context\": \"{h3ContextCells[3]}\"\n");
            jsonBuilder.Append("            }\n");
            jsonBuilder.Append("          ]\n");
            jsonBuilder.Append("        },\n");
            jsonBuilder.Append("        \"field\": \"addressPermuted.completion_with_location\",\n");
            jsonBuilder.Append("        \"fuzzy\": {\n");
            jsonBuilder.Append("          \"fuzziness\": \"AUTO\",\n");
            jsonBuilder.Append("          \"min_length\": 5,\n");
            jsonBuilder.Append("          \"prefix_length\": 3,\n");
            jsonBuilder.Append("          \"transpositions\": true,\n");
            jsonBuilder.Append("          \"unicode_aware\": true\n");
            jsonBuilder.Append("        },\n");
            jsonBuilder.Append("        \"size\": 3,\n");
            jsonBuilder.Append("        \"skip_duplicates\": true\n");
            jsonBuilder.Append("      }\n");
            jsonBuilder.Append("    }\n");
            jsonBuilder.Append("  }\n");
            jsonBuilder.Append("}");
            
            return jsonBuilder.ToString();
        }

        /// <summary>
        /// Tạo payload JSON cho truy vấn Elasticsearch tối ưu
        /// Sử dụng logic tạo biến thể query đơn giản và hiệu quả hơn
        /// </summary>
        private static string CreateOptimizedElasticsearchPayload(string query, double lat, double lon, string[] h3Cells, int size)
        {
            // Xử lý escape các ký tự đặc biệt trong query
            query = JsonConvert.ToString(query).Trim('"');
            
            // Tạo biến thể query đơn giản cho địa chỉ có dấu "/"
            var shortenedQuery = "";
            if (query.Contains("/"))
            {
                // Tạo biến thể rút gọn: "640/8 láng" -> "640 láng"
                var parts = query.Split('/');
                if (parts.Length >= 2)
                {
                    var baseNumber = parts[0].Trim();
                    var restOfQuery = string.Join(" ", query.Split(' ').Skip(1)); // Lấy phần sau số đầu tiên
                    shortenedQuery = $"{baseNumber} {restOfQuery}".Trim();
                }
            }
            
            // Sử dụng StringBuilder để tạo JSON
            var jsonBuilder = new StringBuilder();
            
            jsonBuilder.Append("{\n");
            jsonBuilder.Append("  \"collapse\": {\n");
            jsonBuilder.Append("    \"field\": \"fullAddress.keyword\"\n");
            jsonBuilder.Append("  },\n");
            jsonBuilder.Append("  \"query\": {\n");
            jsonBuilder.Append("    \"bool\": {\n");
            jsonBuilder.Append("      \"filter\": {\n");
            jsonBuilder.Append("        \"bool\": {\n");
            jsonBuilder.Append("          \"must\": {\n");
            jsonBuilder.Append("            \"geo_distance\": {\n");
            jsonBuilder.Append("              \"location\": {\n");
            jsonBuilder.Append($"                \"lat\": {lat},\n");
            jsonBuilder.Append($"                \"lon\": {lon}\n");
            jsonBuilder.Append("              },\n");
            jsonBuilder.Append("              \"distance\": \"50km\"\n");
            jsonBuilder.Append("            }\n");
            jsonBuilder.Append("          }\n");
            jsonBuilder.Append("        }\n");
            jsonBuilder.Append("      },\n");
            jsonBuilder.Append("      \"must\": {\n");
            jsonBuilder.Append("        \"bool\": {\n");
            jsonBuilder.Append("          \"minimum_should_match\": 1,\n");
            jsonBuilder.Append("          \"should\": [\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"bool\": {\n");
            jsonBuilder.Append("                \"minimum_should_match\": 1,\n");
            jsonBuilder.Append("                \"should\": [\n");
            
            // Match phrase cho name với query gốc
            jsonBuilder.Append("                  {\n");
            jsonBuilder.Append("                    \"match_phrase\": {\n");
            jsonBuilder.Append("                      \"name\": {\n");
            jsonBuilder.Append("                        \"boost\": 25,\n");
            jsonBuilder.Append($"                        \"query\": \"{query}\"\n");
            jsonBuilder.Append("                      }\n");
            jsonBuilder.Append("                    }\n");
            jsonBuilder.Append("                  },\n");
            
            // Prefix cho keywords.raw với query gốc
            jsonBuilder.Append("                  {\n");
            jsonBuilder.Append("                    \"prefix\": {\n");
            jsonBuilder.Append("                      \"keywords.raw\": {\n");
            jsonBuilder.Append("                        \"boost\": 20,\n");
            jsonBuilder.Append($"                        \"value\": \"{query}\"\n");
            jsonBuilder.Append("                      }\n");
            jsonBuilder.Append("                    }\n");
            jsonBuilder.Append("                  },\n");
            
            // Match phrase cho keywords.raw với query gốc
            jsonBuilder.Append("                  {\n");
            jsonBuilder.Append("                    \"match_phrase\": {\n");
            jsonBuilder.Append("                      \"keywords.raw\": {\n");
            jsonBuilder.Append("                        \"boost\": 5,\n");
            jsonBuilder.Append($"                        \"query\": \"{query}\"\n");
            jsonBuilder.Append("                      }\n");
            jsonBuilder.Append("                    }\n");
            jsonBuilder.Append("                  },\n");
            
            // Match cho keywords.raw với query gốc
            jsonBuilder.Append("                  {\n");
            jsonBuilder.Append("                    \"match\": {\n");
            jsonBuilder.Append("                      \"keywords.raw\": {\n");
            jsonBuilder.Append("                        \"boost\": 1,\n");
            jsonBuilder.Append($"                        \"query\": \"{query}\"\n");
            jsonBuilder.Append("                      }\n");
            jsonBuilder.Append("                    }\n");
            jsonBuilder.Append("                  },\n");
            
            // Match phrase cho keywords.raw với query gốc (duplicate với boost 15)
            jsonBuilder.Append("                  {\n");
            jsonBuilder.Append("                    \"match_phrase\": {\n");
            jsonBuilder.Append("                      \"keywords.raw\": {\n");
            jsonBuilder.Append("                        \"boost\": 15,\n");
            jsonBuilder.Append($"                        \"query\": \"{query}\"\n");
            jsonBuilder.Append("                      }\n");
            jsonBuilder.Append("                    }\n");
            jsonBuilder.Append("                  }");
            
            // Thêm query rút gọn nếu có
            if (!string.IsNullOrEmpty(shortenedQuery) && shortenedQuery != query)
            {
                jsonBuilder.Append(",\n");
                jsonBuilder.Append("                  {\n");
                jsonBuilder.Append("                    \"match_phrase\": {\n");
                jsonBuilder.Append("                      \"keywords.raw\": {\n");
                jsonBuilder.Append("                        \"boost\": 15,\n");
                jsonBuilder.Append($"                        \"query\": \"{shortenedQuery}\"\n");
                jsonBuilder.Append("                      }\n");
                jsonBuilder.Append("                    }\n");
                jsonBuilder.Append("                  }");
            }
            
            jsonBuilder.Append("\n");
            jsonBuilder.Append("                ]\n");
            jsonBuilder.Append("              }\n");
            jsonBuilder.Append("            },\n");
            
            // Multi-match với query gốc (boost 1)
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"multi_match\": {\n");
            jsonBuilder.Append("                \"boost\": 1,\n");
            jsonBuilder.Append("                \"fields\": [\n");
            jsonBuilder.Append("                  \"addressPermuted^3\",\n");
            jsonBuilder.Append("                  \"addressPermuted.suggest^2\",\n");
            jsonBuilder.Append("                  \"keywords.suggest^4\"\n");
            jsonBuilder.Append("                ],\n");
            jsonBuilder.Append("                \"minimum_should_match\": \"75%\",\n");
            jsonBuilder.Append("                \"operator\": \"and\",\n");
            jsonBuilder.Append($"                \"query\": \"{query}\",\n");
            jsonBuilder.Append("                \"tie_breaker\": 0.3,\n");
            jsonBuilder.Append("                \"type\": \"most_fields\"\n");
            jsonBuilder.Append("              }\n");
            jsonBuilder.Append("            }");
            
            // Thêm multi-match với query rút gọn nếu có
            if (!string.IsNullOrEmpty(shortenedQuery) && shortenedQuery != query)
            {
                jsonBuilder.Append(",\n");
                jsonBuilder.Append("            {\n");
                jsonBuilder.Append("              \"multi_match\": {\n");
                jsonBuilder.Append("                \"boost\": 2,\n");
                jsonBuilder.Append("                \"fields\": [\n");
                jsonBuilder.Append("                  \"addressPermuted^3\",\n");
                jsonBuilder.Append("                  \"addressPermuted.suggest^2\",\n");
                jsonBuilder.Append("                  \"keywords.suggest^4\"\n");
                jsonBuilder.Append("                ],\n");
                jsonBuilder.Append("                \"minimum_should_match\": \"75%\",\n");
                jsonBuilder.Append("                \"operator\": \"and\",\n");
                jsonBuilder.Append($"                \"query\": \"{shortenedQuery}\",\n");
                jsonBuilder.Append("                \"tie_breaker\": 0.3,\n");
                jsonBuilder.Append("                \"type\": \"most_fields\"\n");
                jsonBuilder.Append("              }\n");
                jsonBuilder.Append("            }");
            }
            
            jsonBuilder.Append("\n");
            jsonBuilder.Append("          ]\n");
            jsonBuilder.Append("        }\n");
            jsonBuilder.Append("      }\n");
            jsonBuilder.Append("    }\n");
            jsonBuilder.Append("  },\n");
            jsonBuilder.Append($"  \"size\": {size},\n");
            jsonBuilder.Append("  \"sort\": [\n");
            jsonBuilder.Append("    {\n");
            jsonBuilder.Append("      \"popular\": {\n");
            jsonBuilder.Append("        \"order\": \"desc\"\n");
            jsonBuilder.Append("      }\n");
            jsonBuilder.Append("    },\n");
            jsonBuilder.Append("    {\n");
            jsonBuilder.Append("      \"processed\": {\n");
            jsonBuilder.Append("        \"order\": \"desc\"\n");
            jsonBuilder.Append("      }\n");
            jsonBuilder.Append("    },\n");
            jsonBuilder.Append("    {\n");
            jsonBuilder.Append("      \"_score\": {\n");
            jsonBuilder.Append("        \"order\": \"desc\"\n");
            jsonBuilder.Append("      }\n");
            jsonBuilder.Append("    },\n");
            jsonBuilder.Append("    {\n");
            jsonBuilder.Append("      \"_geo_distance\": {\n");
            jsonBuilder.Append("        \"location\": [\n");
            jsonBuilder.Append("          {\n");
            jsonBuilder.Append($"            \"lat\": {lat},\n");
            jsonBuilder.Append($"            \"lon\": {lon}\n");
            jsonBuilder.Append("          }\n");
            jsonBuilder.Append("        ],\n");
            jsonBuilder.Append("        \"distance_type\": \"plane\",\n");
            jsonBuilder.Append("        \"order\": \"asc\",\n");
            jsonBuilder.Append("        \"unit\": \"km\"\n");
            jsonBuilder.Append("      }\n");
            jsonBuilder.Append("    }\n");
            jsonBuilder.Append("  ]\n");
            jsonBuilder.Append("}");
            
            return jsonBuilder.ToString();
        }
    }
}
