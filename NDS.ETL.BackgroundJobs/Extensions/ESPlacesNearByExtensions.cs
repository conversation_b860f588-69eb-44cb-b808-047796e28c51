using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.QueryDsl;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;

namespace NDS.ETL.BackgroundJobs.Extensions
{
    /// <summary>
    /// Extension methods để tìm kiếm địa điểm gần đây trên Elasticsearch
    /// </summary>
    public static class ESPlacesNearByExtensions
    {
        /// <summary>
        /// Tạo truy vấn tìm kiếm địa điểm gần đây
        /// </summary>
        /// <param name="client">Elasticsearch client</param>
        /// <param name="currentLocation">Vị trí hiện tại</param>
        /// <param name="h3Resolution">Độ phân giải H3 (mặc định: 8)</param>
        /// <param name="size">S<PERSON> lượng kết quả tối đa</param>
        /// <param name="indexName">Tên index</param>
        /// <param name="popular"><PERSON><PERSON><PERSON> độ phổ biến (null nếu không cần lọc)</param>
        /// <param name="minRadiusMeters"></param>
        /// <returns>Truy vấn tìm kiếm địa điểm gần đây</returns>
        public static SearchRequestDescriptor<PlacesEs> CreateNearbyQuery(this ElasticsearchClient client,
            GeoLocation currentLocation,
            int h3Resolution = 8, // Mặc định độ phân giải H3 là 8
            int size = 5, // Mặc định 5 kết quả
            string? indexName = null,
            bool? popular = null,
            double? minRadiusMeters = null
        )
        {
            // Lấy tọa độ lat/lng từ vị trí hiện tại
            currentLocation.TryGetLatitudeLongitude(out var latlng);

            // Tạo sorts
            var sorts = new List<Action<SortOptionsDescriptor<PlacesEs>>>();
            if (popular == true)
            {
                sorts.Add(x => x.Field(f => f.Popular, f => f.Order(SortOrder.Desc)));
                sorts.Add(x => x.Field(f => f.BookingCount, f => f.Order(SortOrder.Desc)));
                sorts.Add(x => x.Field(f => f.UpdatedAt, f => f.Order(SortOrder.Desc)));
            }
            sorts.Add(x =>
            {
                x.GeoDistance(g => g // Theo khoảng cách
                    .Field(p => p.Location)
                    .DistanceType(GeoDistanceType.Plane) // Tính toán theo mặt phẳng
                    .Unit(DistanceUnit.Meters) // Đơn vị meters
                    .Order(SortOrder.Asc) // Tăng dần
                    .Location(new List<GeoLocation> { currentLocation })
                );
            });

            // Tạo mô tả truy vấn
            var descriptor = new SearchRequestDescriptor<PlacesEs>()
                .Index(indexName) // Chỉ định index
                .Size(size) // Giới hạn số lượng kết quả
                .Query(q => q
                    .Bool(configure: b =>
                    {
                        // Tính toán bán kính từ h3Resolution
                        double radiusInKm = h3Resolution.GetRadiusKmFromH3Res();

                        // Lấy tọa độ lat/lng từ vị trí hiện tại
                        currentLocation.TryGetLatitudeLongitude(out var latlng);
                        var h3CellsInRadius = H3Helper.GetH3CellsInRadius(latlng.Lat, latlng.Lon, radiusInKm, out var res);

                        // Tạo danh sách các điều kiện lọc động
                        var filterConditions = new List<Action<QueryDescriptor<PlacesEs>>>();

                        // Thêm điều kiện lọc H3
                        filterConditions.Add(mu => mu.Terms(t => t
                            .Field($"h3.{res}.keyword")
                            .Terms(new TermsQueryField(h3CellsInRadius.Select(h3Idx => (FieldValue)h3Idx).ToArray()))
                        ));

                        // Thêm điều kiện lọc khoảng cách
                        filterConditions.Add(mu => mu.GeoDistance(g => g
                            .Field(p => p.Location)
                            .Distance($"{radiusInKm}km")
                            .Location(currentLocation)
                        ));

                        // Thêm điều kiện lọc khoảng cách tối thiểu nếu có
                        if (minRadiusMeters > 0)
                        {
                            filterConditions.Add(mu => mu.Script(s => s
                                .Script(ss => ss
                                    .Source("doc['location'].arcDistance(params.lat, params.lon) > params.radius")
                                    .Params(p => p
                                            .Add("lat", latlng.Lat)
                                            .Add("lon", latlng.Lon)
                                            .Add("radius", minRadiusMeters.Value)
                                    )
                                )
                            ));
                        }

                        // Áp dụng lọc theo mức độ phổ biến nếu được chỉ định
                        if (popular == true)
                        {
                            // Thêm điều kiện lọc phổ biến
                            filterConditions.Add(mu => mu.Bool(bf => bf
                                .Must(
                                    m => m.Exists(t => t.Field(p => p.Name)),
                                    m => m.Script(s => s
                                        .Script(ss => ss
                                            .Source("doc['name.keyword'].size() != 0 && doc['name.keyword'].value != ''")
                                        )
                                    ),
                                    m => m.Term(t => t.Field(p => p.Popular).Value(true)),
                                    m => m.Term(t => t.Field(p => p.Processed).Value(true)),
                                    m => m.Range(t => t.NumberRange(p => p.Field(x => x.BookingCount).Gte(5)))
                                )
                            ));
                        }

                        // Áp dụng tất cả các điều kiện lọc
                        b.Filter(f => f
                            .Bool(bb => bb
                                .Must(filterConditions.ToArray())
                            )
                        );
                    })
                )
                //.Source(new SourceConfig(true)) // Trả về toàn bộ thông tin
                .SourceExcludes(Fields.FromFields(
                    [
                        "h3",
                        "addressPermuted",
                        "masterAddress",
                        "keywords"
                    ]
                )) // Loại bỏ các trường không cần thiết trong kết quả
                   //.ScriptFields(sf => // Thêm trường tính toán khoảng cách
                   //    sf.Add("distance", fd =>
                   //    {
                   //        fd.Script(s => s
                   //            .Source("doc['location'].arcDistance(params.lat, params.lon)") // Tính khoảng cách theo meters
                   //            .Params(p => p
                   //                    .Add("lat", latlng.Lat) // Truyền lat vào params
                   //                    .Add("lon", latlng.Lon) // Truyền lon vào params
                   //            )
                   //        );
                   //    })
                   //)
                .Sort(sorts.ToArray());

            return descriptor;
        }

        /// <summary>
        /// Tạo truy vấn tìm kiếm địa điểm yêu thích
        /// </summary>
        public static SearchRequestDescriptor<FavPlaceEs> CreateFavPlaceNearbyQuery(this ElasticsearchClient client,
            string userId,
            string customerPhone,
            GeoLocation currentLocation,
            int h3Resolution = 8, // Mặc định độ phân giải H3 là 8
            int size = 5,
            string? indexName = null,
            double? minRadiusMeters = null
        )
        {
            // Lấy tọa độ lat/lng từ vị trí hiện tại
            currentLocation.TryGetLatitudeLongitude(out var latlng);

            // Tạo điều kiện lọc theo userId hoặc customerPhone
            Action<QueryDescriptor<FavPlaceEs>> userFilter = fq =>
            {
                if (!string.IsNullOrWhiteSpace(userId))
                {
                    fq.Term(q => q.Field(x => x.UserId!.Suffix("keyword"))
                        .Value(userId));
                }
                else
                {
                    fq.Term(q => q.Field(x => x.PhoneNumber!.Suffix("keyword"))
                        .Value(customerPhone));
                }
            };

            // Tạo mô tả truy vấn
            var descriptor = new SearchRequestDescriptor<FavPlaceEs>()
                .Index(indexName) // Chỉ định index
                .Size(size) // Giới hạn số lượng kết quả
                .Query(q => q
                    .Bool(b =>
                    {
                        // Tính toán bán kính từ h3Resolution
                        double radiusInKm = h3Resolution.GetRadiusKmFromH3Res();
                        var h3CellsInRadius = H3Helper.GetH3CellsInRadius(latlng.Lat, latlng.Lon, radiusInKm, out var res);

                        // Tạo danh sách các điều kiện lọc động
                        var filterConditions = new List<Action<QueryDescriptor<FavPlaceEs>>>();

                        // Thêm điều kiện lọc user
                        filterConditions.Add(userFilter);

                        // Thêm điều kiện lọc H3
                        filterConditions.Add(mu => mu.Terms(t => t
                            .Field($"h3.{res}.keyword")
                            .Terms(new TermsQueryField(h3CellsInRadius.Select(h3Idx => (FieldValue)h3Idx).ToArray()))
                        ));

                        // Thêm điều kiện lọc khoảng cách
                        filterConditions.Add(mu => mu.GeoDistance(g => g
                            .Field(p => p.Location)
                            .Distance($"{radiusInKm}km")
                            .Location(currentLocation)
                        ));

                        // Thêm điều kiện lọc khoảng cách tối thiểu nếu có
                        if (minRadiusMeters > 0)
                        {
                            filterConditions.Add(mu => mu.Script(s => s
                                .Script(ss => ss
                                    .Source("doc['location'].arcDistance(params.lat, params.lon) > params.radius")
                                    .Params(p => p
                                            .Add("lat", latlng.Lat)
                                            .Add("lon", latlng.Lon)
                                            .Add("radius", minRadiusMeters.Value)
                                    )
                                )
                            ));
                        }

                        // Áp dụng tất cả các điều kiện lọc
                        b.Filter(f => f
                            .Bool(bb => bb
                                .Must(filterConditions.ToArray())
                            )
                        );
                    })
                )
                //.Source(new SourceConfig(true)) // Trả về toàn bộ thông tin
                .SourceExcludes(Fields.FromFields(
                    [
                        "h3",
                        "addressPermuted",
                        "masterAddress",
                        "keywords"
                    ]
                )) // Loại bỏ các trường không cần thiết trong kết quả
                .Sort(x =>
                    {
                        x.GeoDistance(g => g // Theo khoảng cách
                            .Field(p => p.Location)
                            .DistanceType(GeoDistanceType.Plane) // Tính toán theo mặt phẳng
                            .Unit(DistanceUnit.Meters) // Đơn vị meters
                            .Order(SortOrder.Asc) // Tăng dần
                            .Location(new List<GeoLocation> { currentLocation })
                        );
                    });

            return descriptor;
        }

        /// <summary>
        /// Tạo truy vấn tìm kiếm địa điểm lịch sử
        /// </summary>
        /// <param name="client"></param>
        /// <param name="userId"></param>
        /// <param name="customerPhone"></param>
        /// <param name="currentLocation"></param>
        /// <param name="h3Resolution"></param>
        /// <param name="size"></param>
        /// <param name="indexName"></param>
        /// <param name="popular">Mức độ phổ biến (null nếu không cần lọc)</param>
        /// <param name="minRadiusMeters"></param>
        /// <returns>Truy vấn tìm kiếm địa điểm lịch sử</returns>
        public static SearchRequestDescriptor<PlacePairEs> CreatePlacePairNearbyQuery(this ElasticsearchClient client,
            string userId,
            string customerPhone,
            GeoLocation currentLocation,
            int h3Resolution = 8, // Mặc định độ phân giải H3 là 8
            int size = 5,
            string? indexName = null,
            bool? popular = null,
            double? minRadiusMeters = null
        )
        {
            // Lấy tọa độ lat/lng từ vị trí hiện tại
            currentLocation.TryGetLatitudeLongitude(out var latlng);

            // Tạo danh sách các điều kiện lọc động
            var filterConditions = new List<Action<QueryDescriptor<PlacePairEs>>>();

            // Thêm điều kiện lọc theo userId hoặc customerPhone
            filterConditions.Add(fq =>
            {
                if (!string.IsNullOrWhiteSpace(userId))
                {
                    fq.Term(q => q.Field(x => x.CustomerId!.Suffix("keyword"))
                        .Value(userId));
                }
                else
                {
                    fq.Term(q => q.Field(x => x.CustomerPhone!.Suffix("keyword"))
                        .Value(customerPhone));
                }
            });

            filterConditions.Add(fq =>
            {
                fq.Bool(b => b
                    .MustNot(mn => mn
                        .Term(t => t.Field(x => x.PlaceId).Value("current_location"))
                    )
                );
            });

            // Áp dụng lọc theo mức độ phổ biến nếu được chỉ định
            if (popular == true)
            {
                const double minimumDistanceMeters = 1500; //Loại dưới 1.5km
                filterConditions.Add(fq =>
                {
                    fq.Range(r => r.NumberRange(nr => nr.Field(p => p.BookingCount).Gte(3)));
                });
                filterConditions.Add(fq =>
                {
                    fq.Range(r => r.NumberRange(nr => nr.Field(p => p.SearchCount).Gte(10)));
                });
            }

            // Thêm điều kiện lọc khoảng cách tối thiểu
            if (minRadiusMeters > 0)
            {
                filterConditions.Add(fq =>
                {
                    fq.Script(s => s
                        .Script(ss => ss
                            .Source("doc['location'].arcDistance(params.lat, params.lon) > params.radius")
                            .Params(p => p
                                    .Add("lat", latlng.Lat)
                                    .Add("lon", latlng.Lon)
                                    .Add("radius", minRadiusMeters.Value)
                            )
                        ));
                });
            }
            else if (popular == true)
            {
                // Nếu không có minRadiusMeters nhưng đang lọc popular, vẫn áp dụng khoảng cách tối thiểu
                const double minimumDistanceMeters = 1500; //Loại dưới 1.5km
                filterConditions.Add(fq =>
                {
                    fq.Script(s => s
                        .Script(ss => ss
                            .Source("doc['location'].arcDistance(params.lat, params.lon) > params.radius")
                            .Params(p => p
                                    .Add("lat", latlng.Lat)
                                    .Add("lon", latlng.Lon)
                                    .Add("radius", minimumDistanceMeters)
                            )
                        ));
                });
            }

            // Tạo sorts
            var sorts = new List<Action<SortOptionsDescriptor<PlacePairEs>>>();
            if (popular == true)
            {
                sorts.Add(x => x.Field(f => f.BookingCount, f => f.Order(SortOrder.Desc)));
                sorts.Add(x => x.Field(f => f.SearchCount, f => f.Order(SortOrder.Desc)));
            }
            sorts.Add(x =>
            {
                x.GeoDistance(g => g // Theo khoảng cách
                    .Field(p => p.Location)
                    .DistanceType(GeoDistanceType.Plane) // Tính toán theo mặt phẳng
                    .Unit(DistanceUnit.Meters) // Đơn vị meters
                    .Order(SortOrder.Asc) // Tăng dần
                    .Location(new List<GeoLocation> { currentLocation })
                );
            });

            var descriptor = new SearchRequestDescriptor<PlacePairEs>()
                .Index(indexName) // Chỉ định index
                .Size(size) // Giới hạn số lượng kết quả
                .Query(q => q
                    .Bool(b =>
                    {
                        // Tính toán bán kính từ h3Resolution
                        double radiusInKm = h3Resolution.GetRadiusKmFromH3Res();
                        var h3CellsInRadius = H3Helper.GetH3CellsInRadius(latlng.Lat, latlng.Lon, radiusInKm, out var res);

                        // Thêm điều kiện lọc H3
                        filterConditions.Add(mu => mu.Terms(t => t
                            .Field($"h3.{res}.keyword")
                            .Terms(new TermsQueryField(h3CellsInRadius.Select(h3Idx => (FieldValue)h3Idx).ToArray()))
                        ));

                        // Thêm điều kiện lọc khoảng cách
                        filterConditions.Add(mu => mu.GeoDistance(g => g
                            .Field(p => p.Location)
                            .Distance($"{radiusInKm}km")
                            .Location(currentLocation)
                        ));

                        // Áp dụng tất cả các điều kiện lọc
                        b.Filter(f => f
                            .Bool(bb => bb
                                .Must(filterConditions.ToArray())
                            )
                        );
                    })
                )
                //.Source(new SourceConfig(true)) // Trả về toàn bộ thông tin
                .SourceExcludes(Fields.FromFields(
                    [
                        "h3",
                        "addressPermuted",
                        "masterAddress",
                        "keywords"
                    ]
                )) // Loại bỏ các trường không cần thiết trong kết quả
                .Sort(sorts.ToArray());

            return descriptor;
        }
    }
}