using Microsoft.AspNetCore.Mvc;
using NDS.ETL.BackgroundJobs.Authentication;
using NDS.ETL.BackgroundJobs.Model.API;
using NDS.ETL.BackgroundJobs.Services;
using Swashbuckle.AspNetCore.Annotations;

namespace NDS.ETL.BackgroundJobs.Controllers;

/// <summary>
/// Controller providing administrative endpoints for provinces data
/// Protected by ApiKey authentication
/// </summary>
[ApiController]
[Route("api/admin/provinces")]
[ApiKey] // Bảo vệ bằng xac thuc ApiKey
public class AdminProvincesController : ESControllerBase
{
    private readonly IProvincesService _provincesService;

    public AdminProvincesController(
        IProvincesService provincesService,
        IRequestInfoLogger requestInfoLogger,
        ILogger<AdminProvincesController> logger)
        : base(requestInfoLogger, logger)
    {
        _provincesService = provincesService;
    }

    /// <summary>
    /// Gets all provinces
    /// </summary>
    [HttpGet]
    [SwaggerOperation(
        Summary = "Get all provinces",
        Description = "Returns a list of all provinces",
        OperationId = "GetAllProvinces"
    )]
    [SwaggerResponse(200, "Returns a list of provinces")]
    [SwaggerResponse(401, "Unauthorized - Invalid API Key")]
    [SwaggerResponse(500, "Internal server error")]
    public async Task<ActionResult<List<ProvinceResponse>>> GetAllProvinces()
    {
        var requestInfo = ProcessRequestInfo("getAllProvinces");

        return await ExecuteWithErrorHandling(
            async () => {
                var provinces = await _provincesService.GetAllProvincesAsync();
                return provinces.Select(MapToProvinceResponse).ToList();
            },
            "get all provinces");
    }

    /// <summary>
    /// Gets a province by its code
    /// </summary>
    /// <param name="code">Province code</param>
    [HttpGet("code/{code}")]
    [SwaggerOperation(
        Summary = "Get province by code",
        Description = "Returns province details for the specified code",
        OperationId = "GetProvinceByCode"
    )]
    [SwaggerResponse(200, "Returns province details")]
    [SwaggerResponse(404, "Province not found")]
    [SwaggerResponse(401, "Unauthorized - Invalid API Key")]
    [SwaggerResponse(500, "Internal server error")]
    public async Task<ActionResult<ProvinceResponse>> GetProvinceByCode(string code)
    {
        var requestInfo = ProcessRequestInfo($"getProvinceByCode:{code}");

        try
        {
            var province = await _provincesService.GetProvinceByCodeAsync(code);
            if (province == null)
            {
                return NotFound($"Province with code {code} not found");
            }
            
            return MapToProvinceResponse(province);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error getting province by code {Code}: {Message}", code, ex.Message);
            return StatusCode(500, "An error occurred while processing your request");
        }
    }

    /// <summary>
    /// Searches for provinces by name
    /// </summary>
    /// <param name="request">Search request containing the name to search for</param>
    [HttpPost("search/name")]
    [SwaggerOperation(
        Summary = "Search provinces by name",
        Description = "Returns provinces matching the specified name search term",
        OperationId = "SearchProvincesByName"
    )]
    [SwaggerResponse(200, "Returns a list of matching provinces")]
    [SwaggerResponse(401, "Unauthorized - Invalid API Key")]
    [SwaggerResponse(500, "Internal server error")]
    public async Task<ActionResult<List<ProvinceResponse>>> SearchProvincesByName(
        [FromBody] [SwaggerParameter(Description = "Search request containing the name to search for")]
        SearchByNameRequest request)
    {
        // Kiểm tra dữ liệu đầu vào
        if (request == null || string.IsNullOrEmpty(request.Name))
        {
            return BadRequest("Name parameter is required");
        }

        var requestInfo = ProcessRequestInfo($"searchProvincesByName:{request.Name}");

        return await ExecuteWithErrorHandling(
            async () => {
                var provinces = await _provincesService.GetProvincesByNameAsync(request.Name);
                return provinces.Select(MapToProvinceResponse).ToList();
            },
            "search provinces by name");
    }

    /// <summary>
    /// Filters provinces by type
    /// </summary>
    /// <param name="type">Province type</param>
    [HttpGet("filter/type")]
    [SwaggerOperation(
        Summary = "Filter provinces by type",
        Description = "Returns provinces matching the specified type",
        OperationId = "FilterProvincesByType"
    )]
    [SwaggerResponse(200, "Returns a list of matching provinces")]
    [SwaggerResponse(401, "Unauthorized - Invalid API Key")]
    [SwaggerResponse(500, "Internal server error")]
    public async Task<ActionResult<List<ProvinceResponse>>> FilterProvincesByType(
        [FromQuery] [SwaggerParameter(Description = "Province type")]
        string type)
    {
        var requestInfo = ProcessRequestInfo($"filterProvincesByType:{type}");

        return await ExecuteWithErrorHandling(
            async () => {
                var provinces = await _provincesService.GetProvincesByTypeAsync(type);
                return provinces.Select(MapToProvinceResponse).ToList();
            },
            "filter provinces by type");
    }

    /// <summary>
    /// Clear all provinces cache
    /// </summary>
    [HttpPost("cache/clear")]
    [SwaggerOperation(
        Summary = "Clear provinces cache",
        Description = "Clears all cached province data to force fresh data to be fetched from database",
        OperationId = "ClearProvincesCache"
    )]
    [SwaggerResponse(200, "Cache cleared successfully")]
    [SwaggerResponse(401, "Unauthorized - Invalid API Key")]
    [SwaggerResponse(500, "Internal server error")]
    public ActionResult ClearProvincesCache()
    {
        var requestInfo = ProcessRequestInfo("clearProvincesCache");

        try
        {
            // Xóa toàn bộ cache của tỉnh/thành phố
            _provincesService.InvalidateAllProvinceCache();
            
            Logger.LogInformation("Provinces cache cleared successfully");
            return Ok(new { message = "Provinces cache cleared successfully" });
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error clearing provinces cache: {Message}", ex.Message);
            return StatusCode(500, "An error occurred while clearing provinces cache");
        }
    }

    /// <summary>
    /// Maps a ProvincesNew entity to a ProvinceResponse model
    /// </summary>
    private static ProvinceResponse MapToProvinceResponse(Entities.PostgresPlace.ProvincesNew province)
    {
        // Tạo đối tượng response với các thuộc tính cơ bản
        var response = new ProvinceResponse
        {
            Id = province.Id,
            Code = province.Code,
            Name = province.Name,
            Type = province.Type,
            OldCode = province.Oldcode,
            CenterLocation = province.CenterLocation,
            Processed = province.Processed
        };
        
        // Thêm thông tin các tỉnh cũ từ ProvincesBridge
        if (province.ProvincesBridges != null && province.ProvincesBridges.Any())
        {
            foreach (var bridge in province.ProvincesBridges)
            {
                // Kiểm tra xem ProvincesOldCodeNavigation có dữ liệu không
                if (bridge.ProvincesOldCodeNavigation != null)
                {
                    // Ánh xạ thông tin tỉnh cũ vào OldProvinceInfo
                    var oldProvinceInfo = new OldProvinceInfo
                    {
                        Id = bridge.ProvincesOldCodeNavigation.Id,
                        Code = bridge.ProvincesOldCodeNavigation.Code,
                        Name = bridge.ProvincesOldCodeNavigation.Name,
                        Type = bridge.ProvincesOldCodeNavigation.Type,
                        CenterLocation = bridge.ProvincesOldCodeNavigation.CenterLocation,
                        Processed = bridge.ProvincesOldCodeNavigation.Processed
                    };
                    
                    // Thêm vào danh sách tỉnh cũ
                    response.OldProvinces.Add(oldProvinceInfo);
                }
            }
        }
        
        return response;
    }
}