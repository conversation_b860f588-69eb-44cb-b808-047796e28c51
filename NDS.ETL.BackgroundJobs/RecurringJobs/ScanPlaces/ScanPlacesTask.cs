using System.Diagnostics; // Thư viện để đo thời gian thực thi.
using Hangfire; // Thư viện hỗ trợ quản lý và thực thi các công việc nền (background jobs).
using KnowledgeBase.Core.Extensions; // Thư viện mở rộng (extensions) cho các chức năng bổ sung.
using Microsoft.EntityFrameworkCore; // Thư viện ORM để làm việc với cơ sở dữ liệu.
using NDS.ETL.BackgroundJobs.Context; // Namespace chứa các lớp liên quan đến ngữ cảnh công việc nền.
// Namespace chứa các dịch vụ liên quan đến ElasticSearch.
using NDS.ETL.Entities.Context; // Namespace chứa các lớp ngữ cảnh cơ sở dữ liệu.
using NDS.ETL.Entities.PostgresPlace; // Namespace chứa các thực thể liên quan đến PostgresPlace.
using NDS.ETL.Infrastructure.Common; // Namespace chứa các tiện ích chung.
using System.Collections.Concurrent; // Thư viện hỗ trợ các cấu trúc dữ liệu đồng thời.
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.RecurringJobs.Model;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Infrastructure.Extensions;
using NDS.ETL.Infrastructure.Utils; // Namespace chứa các mô hình liên quan đến ElasticSearch.

namespace NDS.ETL.BackgroundJobs.RecurringJobs.ScanPlaces;

// Lớp này định nghĩa một công việc nền định kỳ để quét dữ liệu địa điểm và xử lý giữa cơ sở dữ liệu và ElasticSearch.
public class ScanPlacesTask : RecurringJobBase<PgPlaceContext>
{
    private readonly IConfiguration _configuration; // Cấu hình ứng dụng.
    private readonly ILogger<ScanPlacesTask> _logger; // Logger để ghi log.
    private readonly IESPlacesService _iesPlacesServiceService; // Dịch vụ ElasticSearch để làm việc với dữ liệu địa điểm.
    private readonly IScopedResolver<NpgPlaceContext> _dbContextResolver; // Resolver để quản lý ngữ cảnh cơ sở dữ liệu.
    private const int MainBatchSize = 50000; // Số lượng bản ghi tối đa xử lý trong một lô.
    private const int ProcessingBatchSize = 5000; // Số lượng bản ghi xử lý song song.

    // Hàm khởi tạo để khởi tạo các phụ thuộc.
    public ScanPlacesTask(IConfiguration configuration,
        ILogger<ScanPlacesTask> logger,
        IScopedResolver<NpgPlaceContext> dbContextResolver,
        IESPlacesService iesPlacesServiceService)
        : base(configuration, logger, null)
    {
        _logger = logger; // Gán logger.
        _dbContextResolver = dbContextResolver; // Gán resolver cơ sở dữ liệu.
        _iesPlacesServiceService = iesPlacesServiceService; // Gán dịch vụ ElasticSearch.
    }

    // Định danh duy nhất cho công việc.
    public override string JobId => "ScanPlaces";

    // Phần cấu hình cho công việc.
    public override string ConfigSection => "JobPgSyncPlace";

    public override async Task DoWorkAsync(CancellationToken stoppingToken)
    {
        var begin = Stopwatch.GetTimestamp(); // Bắt đầu đo thời gian thực thi
        _logger.LogInformation("ScanPlacesTask DoWorkAsync started");

        try
        {
            await _dbContextResolver.ResolveAsync(async dbContext =>
            {
                dbContext.Database.SetCommandTimeout(900); // Tăng timeout xử lý truy vấn DB nếu dữ liệu lớn

                // Lấy hàng đợi địa điểm đầu tiên cần xử lý (Status = 2)
                var placeQueue = await dbContext.PlacesQueues
                    .Where(x => x.Status == 2)
                    .OrderBy(x => x.CreateDate)
                    .FirstOrDefaultAsync(stoppingToken)
                    .ConfigureAwait(false);

                if (placeQueue == null)
                {
                    _logger.LogInformation("No place queue found with Status = 2");
                    return true; // Không có gì để xử lý
                }

                // Lấy hàng đợi liên quan trong bảng EsQueues có trạng thái cần xử lý (Status = 1)
                var esQueue = await dbContext.EsQueues
                    .Where(x => x.PlacesQueueId == placeQueue.Id && x.Status == 1)
                    .OrderBy(x => x.OrderNum)
                    .FirstOrDefaultAsync(stoppingToken)
                    .ConfigureAwait(false);

                if (esQueue == null)
                {
                    _logger.LogInformation("No ES queue found for PlaceQueueId {PlaceQueueId} with Status = 1", placeQueue.Id);
                    return true;
                }

                var jobId = Guid.NewGuid().ToString(); // Tạo jobId để đánh dấu phiên quét
                int districtId = (int)placeQueue.Districtid;

                // Lấy toàn bộ địa điểm từ DB trong quận
                var dbPlacesDict = new ConcurrentDictionary<string, Place>(
                    await dbContext.Places
                        .AsNoTracking()
                        .Where(x => x.DistrictId == districtId && x.Processed && !x.Removed)
                        .ToDictionaryAsync(x => x.PlaceId, stoppingToken)
                        .ConfigureAwait(false)
                );

                // Lấy toàn bộ dữ liệu từ Elasticsearch trong quận
                var esDistrictPlaces = await _iesPlacesServiceService.GetPlacesByDistrictId(
                    districtId,
                    false,
                    false,
                    cancellationToken: stoppingToken
                ).ConfigureAwait(false);

                // Biến chứa toàn bộ kết quả quét
                var scanResults = new ConcurrentBag<PlacesScanHistory>();
                _logger.LogInformation("Starting processing {0} trusted places and {1} places from ES, districtId {2}", dbPlacesDict.Count , esDistrictPlaces.Count , districtId);

                // Duyệt song song từng địa điểm từ DB
                await Parallel.ForEachAsync(dbPlacesDict.Values, new ParallelOptions
                {
                    MaxDegreeOfParallelism = Environment.ProcessorCount/2,
                    CancellationToken = stoppingToken
                },
                async (place, ct) =>
                {
                    try
                    {
                        // Nếu không có cấu hình hoặc không có địa điểm tương ứng trên ES thì bỏ qua
                        if (esQueue.ScanConfigDistance == null)
                            return;

                        // Dựa vào toàn bộ esDistrictPlaces đã lấy trước, lọc những địa điểm nằm trong bán kính cần scan
                        var listScan = esDistrictPlaces
                            .Where(x => x.PlaceId != place.PlaceId) // Bỏ qua chính nó
                            .Where(x => x.Location != null && place.Location != null)
                            .Select(x => new {
                                PlaceSearchResponses = new PlaceSearchResponse {
                                    PlaceId = x.PlaceId,
                                    Address = x.Address,
                                    ScanStatus = x.ScanStatus
                                },
                                Distance = GeoHelper.CalculateDistance(
                                    place.Location.Y, place.Location.X, // Lat, Lon
                                    x.Location.Lat, x.Location.Lon      // Lat, Lon
                                )
                            })
                            .Where(x => x.Distance <= esQueue.ScanConfigDistance) // Lọc theo khoảng cách
                            .OrderBy(x => x.Distance) // Sắp xếp theo khoảng cách gần nhất
                            .Take(esQueue.ScanConfigRange) // Chỉ lấy số lượng bản ghi theo cấu hình
                            .Select(x => x.PlaceSearchResponses)
                            .ToList();

                        // Tạo dict kết quả quét
                        var scanDataDict = listScan
                            .Where(x => x.ScanStatus != 1) // Chắc chắn chưa scan và nằm trong cùng 1 quận
                            .ToDictionary(
                                x => x.PlaceId,
                                x => new PlacesScanHistory
                                {
                                    Placeid = x.PlaceId,
                                    Fulladdress = x.Address, // Address từ els đã được chạy qua ConvertToSortAddress khi index.
                                    SourcePlaceid = place.PlaceId,
                                    SourceFulladdress = place.FullAddress.ConvertToSortAddress(),
                                    Type = 1,
                                    HangfireJobId = jobId
                                });

                        // Tính điểm tương đồng giữa địa chỉ
                        foreach (var x in listScan)
                        {
                            if (place.FullAddress.ConvertToSortAddress() != null && x.Address != null &&
                                scanDataDict.TryGetValue(x.PlaceId, out var item))
                            {
                                var score = place.FullAddress.ConvertToSortAddress()!.ToLower()
                                    .CalculateSimilarity(x.Address.ToLower());
                                item.CompareScore = (decimal)(score * 100);
                            }
                        }

                        foreach (var item in scanDataDict.Values)
                        {
                            scanResults.Add(item);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing place {PlaceId}", place.PlaceId);
                    }
                });
                
                _logger.LogInformation("Completed processing {TotalPlaces} places", scanResults.Count);

                // Lưu kết quả nếu có
                if (!scanResults.IsEmpty)
                {
                    var originalAutoDetectChanges = dbContext.ChangeTracker.AutoDetectChangesEnabled;
                    dbContext.ChangeTracker.AutoDetectChangesEnabled = false;
                    try
                    {
                        const int batchSize = 1000;
                        for (int i = 0; i < scanResults.Count; i += batchSize)
                        {
                            var currentBatch = scanResults.Skip(i).Take(batchSize).ToList();
                            await dbContext.PlacesScanHistories.AddRangeAsync(currentBatch, stoppingToken).ConfigureAwait(false);
                            await dbContext.SaveChangesAsync(stoppingToken).ConfigureAwait(false);
                            dbContext.ChangeTracker.Clear();
                            _logger.LogInformation("Saved batch {0} of {1} scan results districtId: {2} , hangfireJobId: {3}", i, scanResults.Count , districtId, jobId);
                        }
                    }
                    finally
                    {
                        // Khôi phục lại trạng thái ban đầu
                        dbContext.ChangeTracker.AutoDetectChangesEnabled = originalAutoDetectChanges;
                    }
                }

                var freshEsQueue = await dbContext.EsQueues.FindAsync(esQueue.Id, stoppingToken);
                if (freshEsQueue != null)
                {
                    freshEsQueue.HangfireJobId = jobId;
                    freshEsQueue.UpdateDate = DateTime.Now;
                    await dbContext.SaveChangesAsync(stoppingToken);
                }

                var end = Stopwatch.GetTimestamp(); // Ghi thời gian kết thúc
                _logger.LogInformation(
                    "Completed processing {TotalProcessed} places in {TotalTime} seconds",
                    dbPlacesDict.Count,
                    TimeSpan.FromTicks(end - begin).TotalSeconds);

                // Gọi công việc tiếp theo
                RecurringJob.TriggerJob("SyncDataScannedToEs");

                return true;
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred in ScanPlacesTask");
            throw;
        }
    }

    // Các phương thức chưa được triển khai để lập chỉ mục dữ liệu.
    public override Task IndexMany<T>(List<T> source, CancellationToken cancellationToken, int take = 1000)
    {
        throw new NotImplementedException();
    }

    public override Task IndexMany<T>(List<T> source, CancellationToken cancellationToken, string esIndex, int take = 1000)
    {
        throw new NotImplementedException();
    }
}