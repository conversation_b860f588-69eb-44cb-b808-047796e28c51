// <auto-generated />
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NDS.ETL.Entities.Context;
using NetTopologySuite.Geometries;
using NodaTime;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using NpgsqlTypes;

#nullable disable

namespace NDS.ETL.Entities.Migrations.NpgPlace
{
    [DbContext(typeof(NpgPlaceContext))]
    partial class NpgPlaceContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "sys", "dss_freq_enum", new[] { "YEARLY", "MONTHLY", "WEEKLY", "DAILY", "HOURLY", "MINUTELY", "SECONDLY" });
            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "sys", "dss_program_type_enum", new[] { "PLSQL_BLOCK", "STORED_PROCEDURE" });
            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "btree_gist");
            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "pg_catalog", "edb_dblink_libpq");
            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "pg_catalog", "edb_dblink_oci");
            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "pg_catalog", "edbspl");
            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "pg_stat_statements");
            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "pgstattuple");
            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "postgis");
            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "unaccent");
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.HasSequence("countries_id_seq");

            modelBuilder.HasSequence("hibernate_sequence");

            modelBuilder.HasSequence("public.user_booking_histories_SEQ");

            modelBuilder.HasSequence("user_booking_histories_seq");

            modelBuilder.HasSequence("user_booking_histories_SEQ");

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.ApiKey", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<Instant>("CreatedAt")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<Instant?>("ExpirationDate")
                        .HasColumnType("timestamp without time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "IsActive" }, "IX_ApiKeys_IsActive");

                    b.HasIndex(new[] { "Key" }, "IX_ApiKeys_Key")
                        .IsUnique();

                    b.ToTable("ApiKeys");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.ApiKeyPermission", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ApiKeyId")
                        .HasColumnType("uuid");

                    b.Property<int>("PermissionName")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "ApiKeyId" }, "IX_ApiKeyPermissions_ApiKeyId");

                    b.HasIndex(new[] { "PermissionName" }, "IX_ApiKeyPermissions_PermissionName");

                    b.ToTable("ApiKeyPermissions");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.BlockedPlace", b =>
                {
                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("createdAt");

                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("PlaceId")
                        .HasColumnType("text")
                        .HasColumnName("placeId");

                    b.ToTable("blocked_places");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.BookingHistoriesItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BookingHistoryId")
                        .HasColumnType("character varying")
                        .HasColumnName("booking_history_id");

                    b.Property<DateTime?>("BookingTime")
                        .HasColumnType("timestamp(0) without time zone")
                        .HasColumnName("booking_time");

                    b.Property<string>("CustomerPhone")
                        .HasColumnType("character varying")
                        .HasColumnName("customer_phone");

                    b.Property<DateTime?>("SearchTime")
                        .HasColumnType("timestamp(0) without time zone")
                        .HasColumnName("search_time");

                    b.HasKey("Id")
                        .HasName("booking_histories_item_pkey");

                    b.ToTable("booking_histories_item");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.BookingHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("character varying")
                        .HasColumnName("id");

                    b.Property<int?>("BookingCount")
                        .HasColumnType("integer")
                        .HasColumnName("booking_count");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp(0) without time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CustomerId")
                        .HasColumnType("character varying")
                        .HasColumnName("customer_id");

                    b.Property<string>("CustomerPhone")
                        .HasColumnType("character varying")
                        .HasColumnName("customer_phone");

                    b.Property<string>("DesAddress")
                        .HasColumnType("character varying")
                        .HasColumnName("des_address");

                    b.Property<string>("DesFullAddress")
                        .HasColumnType("character varying")
                        .HasColumnName("des_full_address");

                    b.Property<string>("DesPlaceId")
                        .HasColumnType("character varying")
                        .HasColumnName("des_place_id");

                    b.Property<DateTime?>("LastBookingAt")
                        .HasColumnType("timestamp(0) without time zone")
                        .HasColumnName("last_booking_at");

                    b.Property<DateTime?>("LastPaidAt")
                        .HasColumnType("timestamp(0) without time zone")
                        .HasColumnName("last_paid_at");

                    b.Property<DateTime?>("LastSearchAt")
                        .HasColumnType("timestamp(0) without time zone")
                        .HasColumnName("last_search_at");

                    b.Property<string>("OriginAddress")
                        .HasColumnType("character varying")
                        .HasColumnName("origin_address");

                    b.Property<string>("OriginFullAddress")
                        .HasColumnType("character varying")
                        .HasColumnName("origin_full_address");

                    b.Property<string>("OriginPlaceId")
                        .HasColumnType("character varying")
                        .HasColumnName("origin_place_id");

                    b.Property<int?>("SearchCount")
                        .HasColumnType("integer")
                        .HasColumnName("search_count");

                    b.HasKey("Id")
                        .HasName("booking_histories_pkey");

                    b.ToTable("booking_histories");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.BookingPlacePair", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("ArrivalPlaceId")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("arrivalPlaceId");

                    b.Property<int>("BookingCount")
                        .HasColumnType("integer")
                        .HasColumnName("bookingCount");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp(6) without time zone")
                        .HasColumnName("createdAt");

                    b.Property<string>("CustomerId")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("customerId");

                    b.Property<string>("CustomerPhone")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("customerPhone");

                    b.Property<double>("Distance")
                        .HasColumnType("double precision")
                        .HasColumnName("distance");

                    b.Property<long?>("EstimateAmount")
                        .HasColumnType("bigint")
                        .HasColumnName("estimateAmount");

                    b.Property<string>("FromAddress")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("fromAddress");

                    b.Property<string>("FromCity")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("fromCity");

                    b.Property<string>("FromFullAddress")
                        .HasMaxLength(10000)
                        .HasColumnType("character varying(10000)")
                        .HasColumnName("fromFullAddress");

                    b.Property<string>("Hash1")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("hash1");

                    b.Property<string>("Hash2")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("hash2");

                    b.Property<DateTime?>("LastBookingAt")
                        .HasColumnType("timestamp(6) without time zone")
                        .HasColumnName("lastBookingAt");

                    b.Property<string>("LastEstimateAmount")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("lastEstimateAmount");

                    b.Property<string>("LastNote")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("lastNote");

                    b.Property<DateTime?>("LastSearchAt")
                        .HasColumnType("timestamp(6) without time zone")
                        .HasColumnName("lastSearchAt");

                    b.Property<Point>("Location1")
                        .HasColumnType("geometry")
                        .HasColumnName("location1");

                    b.Property<Point>("Location2")
                        .HasColumnType("geometry")
                        .HasColumnName("location2");

                    b.Property<int>("PaidCount")
                        .HasColumnType("integer")
                        .HasColumnName("paidCount");

                    b.Property<string>("PickupPlaceId")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("pickupPlaceId");

                    b.Property<int>("SearchCount")
                        .HasColumnType("integer")
                        .HasColumnName("searchCount");

                    b.Property<string>("ToAddress")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("toAddress");

                    b.Property<string>("ToCity")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("toCity");

                    b.Property<string>("ToFullAddress")
                        .HasMaxLength(10000)
                        .HasColumnType("character varying(10000)")
                        .HasColumnName("toFullAddress");

                    b.Property<NpgsqlTsVector>("Ts")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("tsvector")
                        .HasColumnName("ts")
                        .HasComputedColumnSql("to_tsvector('english'::regconfig, (((((((COALESCE(\"fromAddress\", ''::character varying))::text || ' '::text) || (COALESCE(\"fromFullAddress\", ''::character varying))::text) || ' '::text) || (COALESCE(\"toAddress\", ''::character varying))::text) || ' '::text) || (COALESCE(\"toFullAddress\", ''::character varying))::text))", true);

                    b.Property<NpgsqlTsVector>("Ts2")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("tsvector")
                        .HasColumnName("ts2")
                        .HasComputedColumnSql("to_tsvector('english'::regconfig, regexp_replace(((COALESCE(vn_unaccent((\"fromFullAddress\")::text), ''::text) || ' '::text) || COALESCE(vn_unaccent((\"toFullAddress\")::text), ''::text)), '[^\\w]+'::text, ' '::text, 'g'::text))", true);

                    b.Property<DateTime>("UpdateAt")
                        .HasColumnType("timestamp(6) without time zone")
                        .HasColumnName("updateAt");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "ArrivalPlaceId" }, "IX_booking_place_pairs_arrivalPlaceId");

                    b.HasIndex(new[] { "BookingCount" }, "IX_booking_place_pairs_bookingCount");

                    b.HasIndex(new[] { "CustomerId" }, "IX_booking_place_pairs_customerId");

                    b.HasIndex(new[] { "CustomerPhone" }, "IX_booking_place_pairs_customerPhone");

                    b.HasIndex(new[] { "Hash1" }, "IX_booking_place_pairs_hash1");

                    b.HasIndex(new[] { "Hash2" }, "IX_booking_place_pairs_hash2");

                    b.HasIndex(new[] { "PaidCount" }, "IX_booking_place_pairs_paidCount");

                    b.HasIndex(new[] { "PickupPlaceId" }, "IX_booking_place_pairs_pickupPlaceId");

                    b.HasIndex(new[] { "SearchCount" }, "IX_booking_place_pairs_searchCount");

                    b.HasIndex(new[] { "Ts" }, "IX_booking_place_pairs_ts");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex(new[] { "Ts" }, "IX_booking_place_pairs_ts"), "gin");

                    b.HasIndex(new[] { "Ts2" }, "IX_booking_place_pairs_ts2");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex(new[] { "Ts2" }, "IX_booking_place_pairs_ts2"), "gin");

                    b.HasIndex(new[] { "Id", "CreatedAt" }, "booking_place_pairs_id_idx");

                    b.HasIndex(new[] { "CreatedAt" }, "created_at");

                    b.ToTable("booking_place_pairs");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.BuildingBoundary", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("Boundary")
                        .HasColumnType("jsonb")
                        .HasColumnName("boundary");

                    b.Property<string>("Code")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<string>("CreatorId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("creatorId");

                    b.Property<Polygon>("Geom")
                        .HasColumnType("geometry(Polygon,4326)")
                        .HasColumnName("geom");

                    b.Property<string>("LastUpdateUser")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("lastUpdateUser");

                    b.Property<string>("LastUpdateUserId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("lastUpdateUserId");

                    b.Property<double>("Latitude")
                        .HasColumnType("double precision")
                        .HasColumnName("latitude");

                    b.Property<double>("Longitude")
                        .HasColumnType("double precision")
                        .HasColumnName("longitude");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("name");

                    b.Property<string>("Note")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("note");

                    b.Property<int?>("Origin")
                        .HasColumnType("integer")
                        .HasColumnName("origin");

                    b.Property<long>("PlaceFkId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasDefaultValue(0L)
                        .HasColumnName("placeFkId");

                    b.Property<string>("PlaceMasterAddress")
                        .HasColumnType("text")
                        .HasColumnName("placeMasterAddress");

                    b.Property<int>("ProcessStatus")
                        .HasColumnType("integer")
                        .HasColumnName("processStatus");

                    b.Property<int?>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.HasKey("Id");

                    b.ToTable("building_boundary");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.BuildingBoundaryPlace", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<long>("BuildingBoundaryId")
                        .HasColumnType("bigint")
                        .HasColumnName("buildingBoundaryId");

                    b.Property<long>("PlaceFkId")
                        .HasColumnType("bigint")
                        .HasColumnName("placeFkId");

                    b.Property<string>("PlaceId")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("placeId");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "BuildingBoundaryId" }, "IX_building_boundary_place_buildingBoundaryId");

                    b.HasIndex(new[] { "PlaceFkId" }, "IX_building_boundary_place_placeFkId");

                    b.ToTable("building_boundary_place");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.Country", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Boundary")
                        .HasColumnType("text")
                        .HasColumnName("boundary");

                    b.Property<string>("BoundaryGeom")
                        .HasColumnType("text")
                        .HasColumnName("boundary_geom");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("countries_pkey");

                    b.ToTable("countries");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.Customer", b =>
                {
                    b.Property<string>("UserId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("userId");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("phoneNumber");

                    b.HasKey("UserId")
                        .HasName("customers_pkey");

                    b.HasIndex(new[] { "PhoneNumber" }, "phoneNumber")
                        .IsUnique();

                    b.HasIndex(new[] { "UserId" }, "userId")
                        .IsUnique();

                    b.ToTable("customers");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.Danhmuc", b =>
                {
                    b.Property<string>("ActiveAt")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("AreaCode")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("CancelAt")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ClosedCodeAt")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Code")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CreatedAt")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Creator")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("DistrictCodeDisable")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("DistrictCode(Disable)");

                    b.Property<string>("InssuanceAt")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ModifiedAt")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Name")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("OldCode")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ParentCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ProvinceCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Type")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("WardCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.ToTable("danhmuc");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.DefaultColumn", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<LocalDateTime>("CreateDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("create_date");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("Key")
                        .HasColumnType("text")
                        .HasColumnName("key");

                    b.Property<LocalDateTime>("UpdateDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("update_date");

                    b.Property<string>("Value")
                        .HasColumnType("text")
                        .HasColumnName("value");

                    b.HasKey("Id");

                    b.ToTable("default_columns");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.District", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("Boundary")
                        .HasColumnType("text")
                        .HasColumnName("boundary");

                    b.Property<string>("BoundaryGeom")
                        .HasColumnType("text")
                        .HasColumnName("boundary_geom");

                    b.Property<string>("CenterLocation")
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)")
                        .HasColumnName("center_location");

                    b.Property<string>("Code")
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)")
                        .HasColumnName("code");

                    b.Property<string>("Name")
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)")
                        .HasColumnName("name");

                    b.Property<string>("NoSpaceName")
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)")
                        .HasColumnName("no_space_name");

                    b.Property<bool?>("Processed")
                        .HasColumnType("boolean")
                        .HasColumnName("processed");

                    b.Property<string>("ProvinceCode")
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)")
                        .HasColumnName("provinceCode");

                    b.Property<string>("ProvinceCode1")
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)")
                        .HasColumnName("province_code");

                    b.Property<int?>("ProvinceId")
                        .HasColumnType("integer")
                        .HasColumnName("provinceId");

                    b.Property<int?>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.HasKey("Id")
                        .HasName("districts_pkey");

                    b.ToTable("districts");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.DistrictBoundary", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("_id");

                    b.Property<string>("Class")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("_class");

                    b.Property<LocalDateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Data")
                        .HasMaxLength(10485760)
                        .HasColumnType("character varying(10485760)")
                        .HasColumnName("data");

                    b.Property<int?>("Iddistrict")
                        .HasColumnType("integer")
                        .HasColumnName("iddistrict");

                    b.Property<string>("Location")
                        .HasMaxLength(10485760)
                        .HasColumnType("character varying(10485760)")
                        .HasColumnName("location");

                    b.Property<LocalDateTime?>("ModifiedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("modified_by");

                    b.Property<string>("Name")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("name");

                    b.Property<int?>("Postcode")
                        .HasColumnType("integer")
                        .HasColumnName("postcode");

                    b.Property<int?>("Province")
                        .HasColumnType("integer")
                        .HasColumnName("province");

                    b.Property<string>("Provinces")
                        .HasMaxLength(25)
                        .HasColumnType("character varying(25)")
                        .HasColumnName("provinces");

                    b.Property<int?>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.HasKey("Id")
                        .HasName("district_boundaries_pk");

                    b.ToTable("district_boundaries");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.EfmigrationsHistoryEsdbContext", b =>
                {
                    b.Property<string>("MigrationId")
                        .HasMaxLength(150)
                        .HasColumnType("character varying(150)");

                    b.Property<string>("ProductVersion")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("character varying(32)");

                    b.HasKey("MigrationId");

                    b.ToTable("__EFMigrationsHistory_ESDbContext");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.ElsQueryConfig", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("NameIndex")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("name_index");

                    b.Property<string>("QueryEn")
                        .IsRequired()
                        .HasMaxLength(10000)
                        .HasColumnType("character varying(10000)")
                        .HasColumnName("query_en");

                    b.Property<string>("QueryVi")
                        .IsRequired()
                        .HasMaxLength(10000)
                        .HasColumnType("character varying(10000)")
                        .HasColumnName("query_vi");

                    b.HasKey("Id")
                        .HasName("els_query_config_copy1_pkey");

                    b.ToTable("els_query_configs");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.EsQueue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreateDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("create_date")
                        .HasDefaultValueSql("current_timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("character varying")
                        .HasColumnName("description");

                    b.Property<string>("EsJobid")
                        .HasColumnType("character varying")
                        .HasColumnName("es_jobid");

                    b.Property<string>("HangfireJobId")
                        .HasColumnType("character varying")
                        .HasColumnName("hangfire_job_id");

                    b.Property<int>("OrderNum")
                        .HasColumnType("integer")
                        .HasColumnName("order_num");

                    b.Property<long>("PlacesQueueId")
                        .HasColumnType("bigint")
                        .HasColumnName("places_queue_id");

                    b.Property<int?>("ScanConfigDistance")
                        .HasColumnType("integer")
                        .HasColumnName("scan_config_distance");

                    b.Property<int>("ScanConfigRange")
                        .HasColumnType("integer")
                        .HasColumnName("scan_config_range");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<DateTime>("UpdateDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("update_date")
                        .HasDefaultValueSql("current_timestamp");

                    b.HasKey("Id");

                    b.ToTable("es_queue");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.FavoritePlace", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("boolean")
                        .HasColumnName("active");

                    b.Property<string>("Address")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("address");

                    b.Property<long>("BookingCount")
                        .HasColumnType("bigint")
                        .HasColumnName("bookingCount");

                    b.Property<string>("City")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("city");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp(6) without time zone")
                        .HasColumnName("createdAt");

                    b.Property<string>("DataFrom")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("dataFrom");

                    b.Property<string>("District")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("district");

                    b.Property<string>("FullAddress")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("fullAddress");

                    b.Property<string>("Keywords")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("keywords");

                    b.Property<Point>("Location")
                        .HasColumnType("geometry(Geometry,4326)")
                        .HasColumnName("location");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("name");

                    b.Property<string>("ParentAddress")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("parentAddress");

                    b.Property<string>("ParentId")
                        .HasColumnType("text")
                        .HasColumnName("parentId");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("phoneNumber");

                    b.Property<string>("PlaceDetailId")
                        .HasColumnType("text")
                        .HasColumnName("placeDetailId");

                    b.Property<string>("PlaceId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("placeId");

                    b.Property<bool>("Removed")
                        .HasColumnType("boolean")
                        .HasColumnName("removed");

                    b.Property<string>("Route")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("route");

                    b.Property<string>("StreetNumber")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("streetNumber");

                    b.Property<string>("TagInput")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("tagInput");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp(6) without time zone")
                        .HasColumnName("updatedAt");

                    b.Property<string>("UserId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("userId");

                    b.Property<string>("Ward")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("ward");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "PhoneNumber" }, "index_phonenumber");

                    b.HasIndex(new[] { "Type" }, "index_type");

                    b.HasIndex(new[] { "UserId" }, "index_userid");

                    b.ToTable("favorite_places");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.GisProvince", b =>
                {
                    b.Property<string>("Class")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("_class");

                    b.Property<string>("CreatedAt")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("created_at");

                    b.Property<int?>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("_id");

                    b.Property<string>("Location")
                        .HasMaxLength(32767)
                        .HasColumnType("character varying(32767)")
                        .HasColumnName("location");

                    b.Property<string>("ModifiedAt")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("modified_at");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("modified_by");

                    b.Property<string>("Name")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("name");

                    b.Property<string>("PostCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("postCode");

                    b.Property<int?>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.ToTable("gis_province");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.HanoiBoundary", b =>
                {
                    b.Property<string>("Addr")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("addr");

                    b.Property<string>("Amenity")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("amenity");

                    b.Property<string>("Boundary")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("boundary");

                    b.Property<string>("Building")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("building");

                    b.Property<string>("Name")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("name");

                    b.Property<string>("Operator")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("operator");

                    b.Property<int?>("OsmId")
                        .HasColumnType("integer")
                        .HasColumnName("osm_id");

                    b.Property<string>("StAstext")
                        .HasColumnType("character varying")
                        .HasColumnName("st_astext");

                    b.Property<float?>("WayArea")
                        .HasColumnType("real")
                        .HasColumnName("way_area");

                    b.ToTable("hanoi_boundary");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.HiddenPlace", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("boolean")
                        .HasColumnName("active");

                    b.Property<string>("Address")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("address");

                    b.Property<int>("BookingCount")
                        .HasColumnType("integer")
                        .HasColumnName("bookingCount");

                    b.Property<string>("City")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("city");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("createdAt");

                    b.Property<string>("CreatorId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("creatorId");

                    b.Property<string>("DataFrom")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("dataFrom");

                    b.Property<string>("District")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("district");

                    b.Property<int?>("DistrictId")
                        .HasColumnType("integer")
                        .HasColumnName("districtId");

                    b.Property<string>("FullAddress")
                        .IsRequired()
                        .HasMaxLength(10000)
                        .HasColumnType("character varying(10000)")
                        .HasColumnName("fullAddress");

                    b.Property<string>("FullAddressHash")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("fullAddressHash");

                    b.Property<bool?>("HasChild")
                        .HasColumnType("boolean")
                        .HasColumnName("hasChild");

                    b.Property<bool>("Hidden")
                        .HasColumnType("boolean")
                        .HasColumnName("hidden");

                    b.Property<string>("Keywords")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("keywords");

                    b.Property<string>("LastUpdateUser")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("lastUpdateUser");

                    b.Property<Point>("Location")
                        .HasColumnType("geometry(Point,4326)")
                        .HasColumnName("location");

                    b.Property<string>("LocationHash")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("locationHash");

                    b.Property<string>("LocationHash2")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("locationHash2");

                    b.Property<string>("LocationHash3")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("locationHash3");

                    b.Property<string>("Name")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("name");

                    b.Property<bool>("Pending")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("pending");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)")
                        .HasColumnName("phoneNumber");

                    b.Property<string>("PlaceDetailId")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)")
                        .HasColumnName("placeDetailId");

                    b.Property<string>("PlaceId")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("placeId");

                    b.Property<bool>("Popular")
                        .HasColumnType("boolean")
                        .HasColumnName("popular");

                    b.Property<bool>("Processed")
                        .HasColumnType("boolean")
                        .HasColumnName("processed");

                    b.Property<int?>("ProvinceId")
                        .HasColumnType("integer")
                        .HasColumnName("provinceId");

                    b.Property<int>("QuoteCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("quoteCount");

                    b.Property<bool>("Removed")
                        .HasColumnType("boolean")
                        .HasColumnName("removed");

                    b.Property<string>("Route")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("route");

                    b.Property<string>("StreetNumber")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("streetNumber");

                    b.Property<string>("TagInput")
                        .HasColumnType("text")
                        .HasColumnName("tagInput");

                    b.Property<List<string>>("Tags")
                        .HasColumnType("text[]")
                        .HasColumnName("tags");

                    b.Property<NpgsqlTsVector>("Ts")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("tsvector")
                        .HasColumnName("ts")
                        .HasComputedColumnSql("to_tsvector('english'::regconfig, (((((COALESCE(name, ''::character varying))::text || ' '::text) || (\"fullAddress\")::text) || ' '::text) || (COALESCE(keywords, ''::character varying))::text))", true);

                    b.Property<NpgsqlTsVector>("Ts2")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("tsvector")
                        .HasColumnName("ts2")
                        .HasComputedColumnSql("to_tsvector('english'::regconfig, regexp_replace(((((COALESCE(vn_unaccent((name)::text), ''::text) || ' '::text) || COALESCE(vn_unaccent((\"fullAddress\")::text), ''::text)) || ' '::text) || COALESCE(vn_unaccent((keywords)::text), ''::text)), '[^\\w]+'::text, ' '::text, 'g'::text))", true);

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updatedAt");

                    b.Property<string>("Ward")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("ward");

                    b.Property<int?>("WardId")
                        .HasColumnType("integer")
                        .HasColumnName("wardId");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "BookingCount" }, "IX_hidden_places_bookingCount");

                    b.HasIndex(new[] { "BookingCount", "QuoteCount" }, "IX_hidden_places_bookingCount_quoteCount");

                    b.HasIndex(new[] { "City" }, "IX_hidden_places_city");

                    b.HasIndex(new[] { "CreatedAt" }, "IX_hidden_places_createdAt");

                    b.HasIndex(new[] { "DataFrom" }, "IX_hidden_places_dataFrom");

                    b.HasIndex(new[] { "DistrictId" }, "IX_hidden_places_districtId");

                    b.HasIndex(new[] { "FullAddressHash" }, "IX_hidden_places_fullAddressHash");

                    b.HasIndex(new[] { "Hidden" }, "IX_hidden_places_hidden");

                    b.HasIndex(new[] { "Location" }, "IX_hidden_places_location");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex(new[] { "Location" }, "IX_hidden_places_location"), "gist");

                    b.HasIndex(new[] { "LocationHash" }, "IX_hidden_places_locationHash");

                    b.HasIndex(new[] { "LocationHash2" }, "IX_hidden_places_locationHash2");

                    b.HasIndex(new[] { "LocationHash3" }, "IX_hidden_places_locationHash3");

                    b.HasIndex(new[] { "Pending" }, "IX_hidden_places_pending");

                    b.HasIndex(new[] { "PlaceDetailId" }, "IX_hidden_places_placeDetailId");

                    b.HasIndex(new[] { "Popular" }, "IX_hidden_places_popular");

                    b.HasIndex(new[] { "Popular", "Processed", "Removed", "Hidden" }, "IX_hidden_places_popular_processed_removed_hidden");

                    b.HasIndex(new[] { "Processed" }, "IX_hidden_places_processed");

                    b.HasIndex(new[] { "Processed", "Pending" }, "IX_hidden_places_processed_pending");

                    b.HasIndex(new[] { "ProvinceId" }, "IX_hidden_places_provinceId");

                    b.HasIndex(new[] { "QuoteCount" }, "IX_hidden_places_quoteCount");

                    b.HasIndex(new[] { "Removed" }, "IX_hidden_places_removed");

                    b.HasIndex(new[] { "Removed", "Processed", "Pending" }, "IX_hidden_places_removed_processed_pending");

                    b.HasIndex(new[] { "Removed", "Processed", "Pending", "BookingCount", "QuoteCount" }, "IX_hidden_places_removed_processed_pending_bookingCount_quoteC~");

                    b.HasIndex(new[] { "Ts" }, "IX_hidden_places_ts");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex(new[] { "Ts" }, "IX_hidden_places_ts"), "gin");

                    b.HasIndex(new[] { "Ts2" }, "IX_hidden_places_ts2");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex(new[] { "Ts2" }, "IX_hidden_places_ts2"), "gin");

                    b.HasIndex(new[] { "WardId" }, "IX_hidden_places_wardId");

                    b.ToTable("hidden_places");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.JobConfig", b =>
                {
                    b.Property<double?>("BatchSize")
                        .HasColumnType("double precision")
                        .HasColumnName("batchSize");

                    b.Property<int?>("Enable")
                        .HasColumnType("integer")
                        .HasColumnName("enable");

                    b.Property<string>("Id")
                        .HasMaxLength(254)
                        .HasColumnType("character varying(254)")
                        .HasColumnName("id");

                    b.Property<long?>("Interval")
                        .HasColumnType("bigint")
                        .HasColumnName("interval");

                    b.Property<long?>("LastTime")
                        .HasColumnType("bigint")
                        .HasColumnName("lastTime");

                    b.Property<int?>("ThreadPoolSize")
                        .HasColumnType("integer")
                        .HasColumnName("threadPoolSize");

                    b.ToTable("job_config");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.JobConfigPlace", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime?>("CreateAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("createdAt");

                    b.Property<string>("CreatorUser")
                        .HasColumnType("text")
                        .HasColumnName("creatorUser");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("JobId")
                        .HasColumnType("text")
                        .HasColumnName("jobId");

                    b.Property<string>("KeyConfig")
                        .HasColumnType("text")
                        .HasColumnName("keyConfig");

                    b.Property<string>("LastUpdateUser")
                        .HasColumnType("text")
                        .HasColumnName("lastUpdateUser");

                    b.Property<DateTime?>("UpdateAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updatedAt");

                    b.Property<string>("ValueConfig")
                        .HasColumnType("text")
                        .HasColumnName("valueConfig");

                    b.HasKey("Id");

                    b.ToTable("job_config_place");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.JobLastUpdate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("lastUpdate");

                    b.Property<int?>("Origin")
                        .HasColumnType("integer")
                        .HasColumnName("origin");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "Origin" }, "job_last_update_origin_idx");

                    b.ToTable("job_last_update");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.JwtClient", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("ClientId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("clientId");

                    b.Property<string>("ClientSecretKey")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("clientSecretKey");

                    b.HasKey("Id")
                        .HasName("jwt_clients_pkey");

                    b.ToTable("jwt_clients");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.MigrationCheckpoint", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<LocalDateTime>("CheckpointTime")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("checkpoint_time");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)")
                        .HasColumnName("error_message");

                    b.Property<string>("LastProcessedId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("last_processed_id");

                    b.Property<string>("MigrationId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("migration_id");

                    b.Property<long>("ProcessedCount")
                        .HasColumnType("bigint")
                        .HasColumnName("processed_count");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("status");

                    b.Property<string>("ThreadId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("thread_id");

                    b.Property<long>("TotalCount")
                        .HasColumnType("bigint")
                        .HasColumnName("total_count");

                    b.HasKey("Id")
                        .HasName("migration_checkpoints_pkey");

                    b.ToTable("migration_checkpoints");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.Osm2pgsqlProperty", b =>
                {
                    b.Property<string>("Property")
                        .HasColumnType("text")
                        .HasColumnName("property");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("value");

                    b.HasKey("Property")
                        .HasName("osm2pgsql_properties_pkey");

                    b.ToTable("osm2pgsql_properties");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.Place", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("boolean")
                        .HasColumnName("active");

                    b.Property<string>("Address")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("address");

                    b.Property<int>("BookingCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("bookingCount");

                    b.Property<string>("City")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("city");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("createdAt")
                        .HasDefaultValueSql("'01-JAN-01 00:00:00'::timestamp without time zone");

                    b.Property<string>("CreatorId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("creatorId");

                    b.Property<string>("DataFrom")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("dataFrom");

                    b.Property<string>("District")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("district");

                    b.Property<int?>("DistrictId")
                        .HasColumnType("integer")
                        .HasColumnName("districtId");

                    b.Property<string>("FullAddress")
                        .IsRequired()
                        .HasMaxLength(10000)
                        .HasColumnType("character varying(10000)")
                        .HasColumnName("fullAddress");

                    b.Property<string>("FullAddressHash")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("fullAddressHash");

                    b.Property<string>("FullAddressNew")
                        .HasMaxLength(10000)
                        .HasColumnType("character varying(10000)")
                        .HasColumnName("fullAddressNew");

                    b.Property<bool>("HasChild")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("hasChild");

                    b.Property<bool>("Hidden")
                        .HasColumnType("boolean")
                        .HasColumnName("hidden");

                    b.Property<string>("Keywords")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("keywords");

                    b.Property<string>("LastUpdateUser")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("lastUpdateUser");

                    b.Property<Point>("Location")
                        .HasColumnType("geometry(Point,4326)")
                        .HasColumnName("location");

                    b.Property<string>("LocationHash")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("locationHash");

                    b.Property<string>("LocationHash2")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("locationHash2");

                    b.Property<string>("LocationHash3")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("locationHash3");

                    b.Property<string>("Name")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("name");

                    b.Property<bool>("Pending")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("pending");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)")
                        .HasColumnName("phoneNumber");

                    b.Property<string>("PlaceDetailId")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)")
                        .HasColumnName("placeDetailId");

                    b.Property<string>("PlaceId")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("placeId");

                    b.Property<bool>("Popular")
                        .HasColumnType("boolean")
                        .HasColumnName("popular");

                    b.Property<bool>("Processed")
                        .HasColumnType("boolean")
                        .HasColumnName("processed");

                    b.Property<int?>("ProvinceId")
                        .HasColumnType("integer")
                        .HasColumnName("provinceId");

                    b.Property<int>("QuoteCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("quoteCount");

                    b.Property<bool>("Removed")
                        .HasColumnType("boolean")
                        .HasColumnName("removed");

                    b.Property<string>("Route")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("route");

                    b.Property<string>("StreetNumber")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("streetNumber");

                    b.Property<string>("TagInput")
                        .HasColumnType("text")
                        .HasColumnName("tagInput");

                    b.Property<List<string>>("Tags")
                        .HasColumnType("text[]")
                        .HasColumnName("tags");

                    b.Property<NpgsqlTsVector>("Ts")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("tsvector")
                        .HasColumnName("ts")
                        .HasComputedColumnSql("to_tsvector('english'::regconfig, (((((COALESCE(name, ''::character varying))::text || ' '::text) || (\"fullAddress\")::text) || ' '::text) || (COALESCE(keywords, ''::character varying))::text))", true);

                    b.Property<NpgsqlTsVector>("Ts2")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("tsvector")
                        .HasColumnName("ts2")
                        .HasComputedColumnSql("to_tsvector('english'::regconfig, regexp_replace(((((COALESCE(vn_unaccent((name)::text), ''::text) || ' '::text) || COALESCE(vn_unaccent((\"fullAddress\")::text), ''::text)) || ' '::text) || COALESCE(vn_unaccent((keywords)::text), ''::text)), '[^\\w]+'::text, ' '::text, 'g'::text))", true);

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updatedAt")
                        .HasDefaultValueSql("'01-JAN-01 00:00:00'::timestamp without time zone");

                    b.Property<string>("Ward")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("ward");

                    b.Property<int?>("WardId")
                        .HasColumnType("integer")
                        .HasColumnName("wardId");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "DistrictId" }, "IX_districtId");

                    b.HasIndex(new[] { "BookingCount" }, "IX_places_bookingCount");

                    b.HasIndex(new[] { "BookingCount", "QuoteCount" }, "IX_places_bookingCount_quoteCount");

                    b.HasIndex(new[] { "City" }, "IX_places_city");

                    b.HasIndex(new[] { "DataFrom" }, "IX_places_dataFrom");

                    b.HasIndex(new[] { "FullAddressHash" }, "IX_places_fullAddressHash");

                    b.HasIndex(new[] { "Hidden" }, "IX_places_hidden");

                    b.HasIndex(new[] { "LastUpdateUser" }, "IX_places_lastUpdateUser");

                    b.HasIndex(new[] { "Location" }, "IX_places_location");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex(new[] { "Location" }, "IX_places_location"), "gist");

                    b.HasIndex(new[] { "LocationHash" }, "IX_places_locationHash");

                    b.HasIndex(new[] { "LocationHash2" }, "IX_places_locationHash2");

                    b.HasIndex(new[] { "LocationHash3" }, "IX_places_locationHash3");

                    b.HasIndex(new[] { "Pending" }, "IX_places_pending");

                    b.HasIndex(new[] { "PlaceDetailId" }, "IX_places_placeDetailId");

                    b.HasIndex(new[] { "PlaceId" }, "IX_places_placeId")
                        .IsUnique();

                    b.HasIndex(new[] { "Popular" }, "IX_places_popular");

                    b.HasIndex(new[] { "Processed" }, "IX_places_processed");

                    b.HasIndex(new[] { "Processed", "Pending" }, "IX_places_processed_pending");

                    b.HasIndex(new[] { "QuoteCount" }, "IX_places_quoteCount");

                    b.HasIndex(new[] { "Removed" }, "IX_places_removed");

                    b.HasIndex(new[] { "Removed", "Processed", "Pending" }, "IX_places_removed_processed_pending");

                    b.HasIndex(new[] { "Removed", "Processed", "Pending", "BookingCount", "QuoteCount" }, "IX_places_removed_processed_pending_bookingCount_quoteCount");

                    b.HasIndex(new[] { "Ts" }, "IX_places_ts");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex(new[] { "Ts" }, "IX_places_ts"), "gin");

                    b.HasIndex(new[] { "Ts2" }, "IX_places_ts2");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex(new[] { "Ts2" }, "IX_places_ts2"), "gin");

                    b.HasIndex(new[] { "UpdatedAt" }, "IX_places_updatedAt");

                    b.HasIndex(new[] { "ProvinceId" }, "IX_provinceId");

                    b.HasIndex(new[] { "WardId" }, "IX_wardId");

                    b.HasIndex(new[] { "CreatedAt" }, "ix_places_createdat");

                    b.ToTable("places");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.PlaceApprove", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("Address")
                        .HasColumnType("text")
                        .HasColumnName("address");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("CreatorUserId")
                        .HasColumnType("text");

                    b.Property<string>("DescriptionReasonApprove")
                        .HasColumnType("text")
                        .HasColumnName("descriptionReasonApprove");

                    b.Property<int?>("DetermineAddressLevel")
                        .HasColumnType("integer")
                        .HasColumnName("determineAddressLevel");

                    b.Property<int?>("DistrictId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("districtId");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("LastUpdateUser")
                        .HasColumnType("text")
                        .HasColumnName("lastUpdateUser");

                    b.Property<Point>("Location")
                        .HasColumnType("geometry(Point,4326)")
                        .HasColumnName("location");

                    b.Property<string>("LocationAddressDiffScore")
                        .HasColumnType("text")
                        .HasColumnName("locationAddressDiffScore");

                    b.Property<string>("Name")
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("Note")
                        .HasColumnType("text")
                        .HasColumnName("note");

                    b.Property<string>("PlaceId")
                        .HasColumnType("text")
                        .HasColumnName("placeId");

                    b.Property<int?>("ProvinceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("provinceId");

                    b.Property<string>("Reason")
                        .HasColumnType("text")
                        .HasColumnName("reason");

                    b.Property<List<int>>("ReasonApprove")
                        .HasColumnType("integer[]")
                        .HasColumnName("reasonApprove");

                    b.Property<List<int>>("ReasonOperations")
                        .HasColumnType("integer[]")
                        .HasColumnName("reasonOperations");

                    b.Property<int>("ScanProcessed")
                        .HasColumnType("integer")
                        .HasColumnName("scanProcessed");

                    b.Property<int>("StatusApprove")
                        .HasColumnType("integer")
                        .HasColumnName("statusApprove");

                    b.Property<int>("StatusCalculate")
                        .HasColumnType("integer")
                        .HasColumnName("statusCalculate");

                    b.Property<DateTime?>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp without time zone")
                        .HasDefaultValueSql("'-infinity'::timestamp without time zone");

                    b.Property<int?>("WardId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("wardId");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "PlaceId" }, "place_approve_placeid_idx");

                    b.ToTable("place_approve");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.PlaceChangeHistory", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("createdAt");

                    b.Property<string>("Creator")
                        .HasColumnType("text")
                        .HasColumnName("creator");

                    b.Property<string>("DataJson")
                        .HasColumnType("text");

                    b.Property<string>("LastUpdatedUser")
                        .HasColumnType("text")
                        .HasColumnName("lastUpdatedUser");

                    b.Property<long>("PlaceId")
                        .HasColumnType("bigint")
                        .HasColumnName("placeId");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updatedAt");

                    b.HasKey("Id");

                    b.ToTable("place_change_history");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.PlaceTag", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("Code")
                        .HasColumnType("text")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("createdAt");

                    b.Property<string>("Creator")
                        .HasColumnType("text")
                        .HasColumnName("creator");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("En")
                        .HasColumnType("text")
                        .HasColumnName("en");

                    b.Property<string>("UpdateUser")
                        .HasColumnType("text")
                        .HasColumnName("updateUser");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updatedAt");

                    b.Property<string>("Vi")
                        .HasColumnType("text")
                        .HasColumnName("vi");

                    b.HasKey("Id");

                    b.ToTable("place_tags");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.PlaceUatUpLive", b =>
                {
                    b.Property<string>("Placeid")
                        .HasColumnType("character varying")
                        .HasColumnName("placeid");

                    b.Property<int>("Type")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("type");

                    b.HasKey("Placeid")
                        .HasName("place_up_live_pk");

                    b.HasIndex(new[] { "Type" }, "place_uat_up_live_type_idx");

                    b.ToTable("place_uat_up_live");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.Placechangehistory1", b =>
                {
                    b.Property<string>("CreatedAt")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("createdAt");

                    b.Property<string>("Creator")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("creator");

                    b.Property<string>("DataJson")
                        .HasMaxLength(2048)
                        .HasColumnType("character varying(2048)");

                    b.Property<int?>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("LastUpdatedUser")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("lastUpdatedUser");

                    b.Property<int?>("PlaceId")
                        .HasColumnType("integer")
                        .HasColumnName("placeId");

                    b.Property<string>("UpdatedAt")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("updatedAt");

                    b.ToTable("placechangehistory");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.PlacesDetail", b =>
                {
                    b.Property<string>("AddressComponents")
                        .HasColumnType("jsonb")
                        .HasColumnName("address_components");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("createdAt");

                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("ImageUrl")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("imageUrl");

                    b.Property<long?>("PlaceFkId")
                        .HasColumnType("bigint")
                        .HasColumnName("placeFkId");

                    b.Property<string>("PlaceId")
                        .HasColumnType("text")
                        .HasColumnName("placeId");

                    b.Property<double?>("Rating")
                        .HasColumnType("double precision")
                        .HasColumnName("rating");

                    b.Property<int?>("ReviewCount")
                        .HasColumnType("integer")
                        .HasColumnName("reviewCount");

                    b.Property<string>("Types")
                        .HasColumnType("jsonb")
                        .HasColumnName("types");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updatedAt");

                    b.HasIndex(new[] { "AddressComponents" }, "IX_places_details_address_components");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex(new[] { "AddressComponents" }, "IX_places_details_address_components"), "gin");

                    b.HasIndex(new[] { "PlaceFkId" }, "IX_places_details_placeFkId");

                    b.HasIndex(new[] { "Rating" }, "IX_places_details_rating");

                    b.HasIndex(new[] { "ReviewCount" }, "IX_places_details_reviewCount");

                    b.HasIndex(new[] { "Types" }, "IX_places_details_types");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex(new[] { "Types" }, "IX_places_details_types"), "gin");

                    b.ToTable("places_details");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.PlacesQueue", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreateDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("create_date");

                    b.Property<string>("CreatorUserId")
                        .HasColumnType("text")
                        .HasColumnName("creatorUserId");

                    b.Property<long>("Districtid")
                        .HasColumnType("bigint")
                        .HasColumnName("districtid");

                    b.Property<string>("LastUpdateUser")
                        .HasColumnType("text")
                        .HasColumnName("lastUpdateUser");

                    b.Property<string>("PlaceType")
                        .HasColumnType("character varying")
                        .HasColumnName("place_type");

                    b.Property<long>("Provinceid")
                        .HasColumnType("bigint")
                        .HasColumnName("provinceid");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<DateTime>("UpdateDate")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("update_date");

                    b.HasKey("Id");

                    b.ToTable("places_queue");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.PlacesRemove", b =>
                {
                    b.Property<bool?>("Active")
                        .HasColumnType("boolean")
                        .HasColumnName("active");

                    b.Property<string>("Address")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("address");

                    b.Property<int?>("BookingCount")
                        .HasColumnType("integer")
                        .HasColumnName("bookingCount");

                    b.Property<string>("City")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("city");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("createdAt");

                    b.Property<string>("CreatorId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("creatorId");

                    b.Property<string>("DataFrom")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("dataFrom");

                    b.Property<string>("District")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("district");

                    b.Property<int?>("DistrictId")
                        .HasColumnType("integer")
                        .HasColumnName("districtId");

                    b.Property<string>("FullAddress")
                        .HasMaxLength(10000)
                        .HasColumnType("character varying(10000)")
                        .HasColumnName("fullAddress");

                    b.Property<string>("FullAddressHash")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("fullAddressHash");

                    b.Property<bool?>("HasChild")
                        .HasColumnType("boolean")
                        .HasColumnName("hasChild");

                    b.Property<bool?>("Hidden")
                        .HasColumnType("boolean")
                        .HasColumnName("hidden");

                    b.Property<long?>("Id")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("Keywords")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("keywords");

                    b.Property<string>("LastUpdateUser")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("lastUpdateUser");

                    b.Property<Point>("Location")
                        .HasColumnType("geometry(Point,4326)")
                        .HasColumnName("location");

                    b.Property<string>("LocationHash")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("locationHash");

                    b.Property<string>("LocationHash2")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("locationHash2");

                    b.Property<string>("LocationHash3")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("locationHash3");

                    b.Property<string>("Name")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("name");

                    b.Property<bool?>("Pending")
                        .HasColumnType("boolean")
                        .HasColumnName("pending");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)")
                        .HasColumnName("phoneNumber");

                    b.Property<string>("PlaceDetailId")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)")
                        .HasColumnName("placeDetailId");

                    b.Property<string>("PlaceId")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("placeId");

                    b.Property<bool?>("Popular")
                        .HasColumnType("boolean")
                        .HasColumnName("popular");

                    b.Property<bool?>("Processed")
                        .HasColumnType("boolean")
                        .HasColumnName("processed");

                    b.Property<int?>("ProvinceId")
                        .HasColumnType("integer")
                        .HasColumnName("provinceId");

                    b.Property<int?>("QuoteCount")
                        .HasColumnType("integer")
                        .HasColumnName("quoteCount");

                    b.Property<string>("Reason")
                        .HasColumnType("text")
                        .HasColumnName("reason");

                    b.Property<bool?>("Removed")
                        .HasColumnType("boolean")
                        .HasColumnName("removed");

                    b.Property<string>("Route")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("route");

                    b.Property<string>("StreetNumber")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("streetNumber");

                    b.Property<string>("TagInput")
                        .HasColumnType("text")
                        .HasColumnName("tagInput");

                    b.Property<List<string>>("Tags")
                        .HasColumnType("text[]")
                        .HasColumnName("tags");

                    b.Property<NpgsqlTsVector>("Ts")
                        .HasColumnType("tsvector")
                        .HasColumnName("ts");

                    b.Property<NpgsqlTsVector>("Ts2")
                        .HasColumnType("tsvector")
                        .HasColumnName("ts2");

                    b.Property<int?>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updatedAt");

                    b.Property<string>("Ward")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("ward");

                    b.Property<int?>("WardId")
                        .HasColumnType("integer")
                        .HasColumnName("wardId");

                    b.ToTable("places_remove");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.PlacesScanHistory", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<decimal?>("CompareScore")
                        .HasColumnType("numeric")
                        .HasColumnName("compare_score");

                    b.Property<DateTime>("Createat")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("createat")
                        .HasDefaultValueSql("current_timestamp");

                    b.Property<int>("Districtid")
                        .HasColumnType("integer")
                        .HasColumnName("districtid");

                    b.Property<string>("Fulladdress")
                        .HasMaxLength(10000)
                        .HasColumnType("character varying(10000)")
                        .HasColumnName("fulladdress");

                    b.Property<string>("HangfireJobId")
                        .HasColumnType("character varying")
                        .HasColumnName("hangfire_job_id");

                    b.Property<string>("Placeid")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("placeid");

                    b.Property<int>("Provinceid")
                        .HasColumnType("integer")
                        .HasColumnName("provinceid");

                    b.Property<string>("SourceFulladdress")
                        .HasMaxLength(10000)
                        .HasColumnType("character varying(10000)")
                        .HasColumnName("source_fulladdress");

                    b.Property<string>("SourcePlaceid")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("source_placeid");

                    b.Property<int?>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTime?>("Updateat")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updateat")
                        .HasDefaultValueSql("current_timestamp");

                    b.Property<int>("Wardid")
                        .HasColumnType("integer")
                        .HasColumnName("wardid");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "Placeid" }, "idx_places_scan_history_placeid");

                    b.HasIndex(new[] { "HangfireJobId" }, "idx_places_scan_history_source_hangfire_job_id");

                    b.HasIndex(new[] { "SourcePlaceid" }, "idx_places_scan_history_source_placeid");

                    b.ToTable("places_scan_history");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.PlacesScanResult", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<decimal>("CompareScoreMax")
                        .HasColumnType("numeric")
                        .HasColumnName("compare_score_max");

                    b.Property<DateTime>("CreateAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("create_at")
                        .HasDefaultValueSql("current_timestamp");

                    b.Property<string>("HangfireJobId")
                        .HasColumnType("character varying")
                        .HasColumnName("hangfire_job_id");

                    b.Property<string>("LastUpdateUser")
                        .HasColumnType("text")
                        .HasColumnName("lastUpdateUser");

                    b.Property<string>("Note")
                        .HasColumnType("text")
                        .HasColumnName("note");

                    b.Property<string>("Placeid")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("placeid");

                    b.Property<string>("Reason")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("reason");

                    b.Property<int[]>("ReasonOperations")
                        .HasColumnType("integer[]")
                        .HasColumnName("reason_operations");

                    b.Property<int?>("ScanProcessed")
                        .HasColumnType("integer")
                        .HasColumnName("scan_processed");

                    b.Property<string>("ScanType")
                        .HasColumnType("character varying")
                        .HasColumnName("scan_type");

                    b.Property<string>("SourcePlaceid")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("source_placeid");

                    b.Property<int>("SyncStatus")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("sync_status");

                    b.Property<int>("TotalScans")
                        .HasColumnType("integer")
                        .HasColumnName("total_scans");

                    b.Property<DateTime>("UpdateAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("update_at")
                        .HasDefaultValueSql("current_timestamp");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "HangfireJobId" }, "places_scan_result_hangfire_job_id_idx");

                    b.HasIndex(new[] { "Placeid" }, "places_scan_result_placeid_idx");

                    b.HasIndex(new[] { "ScanProcessed" }, "places_scan_result_scan_processed_idx");

                    b.HasIndex(new[] { "SyncStatus" }, "places_scan_result_sync_status_idx");

                    b.HasIndex(new[] { "Placeid" }, "places_scan_result_unique")
                        .IsUnique();

                    b.ToTable("places_scan_result");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.PlacesUatBak", b =>
                {
                    b.Property<bool?>("Active")
                        .HasColumnType("boolean")
                        .HasColumnName("active");

                    b.Property<string>("Address")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("address");

                    b.Property<int?>("BookingCount")
                        .HasColumnType("integer")
                        .HasColumnName("bookingCount");

                    b.Property<string>("City")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("city");

                    b.Property<LocalDateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("createdAt");

                    b.Property<string>("CreatorId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("creatorId");

                    b.Property<string>("DataFrom")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("dataFrom");

                    b.Property<string>("District")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("district");

                    b.Property<int?>("DistrictId")
                        .HasColumnType("integer")
                        .HasColumnName("districtId");

                    b.Property<string>("FullAddress")
                        .HasMaxLength(10000)
                        .HasColumnType("character varying(10000)")
                        .HasColumnName("fullAddress");

                    b.Property<string>("FullAddressHash")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("fullAddressHash");

                    b.Property<bool?>("HasChild")
                        .HasColumnType("boolean")
                        .HasColumnName("hasChild");

                    b.Property<bool?>("Hidden")
                        .HasColumnType("boolean")
                        .HasColumnName("hidden");

                    b.Property<long?>("Id")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("Keywords")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("keywords");

                    b.Property<string>("LastUpdateUser")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("lastUpdateUser");

                    b.Property<Point>("Location")
                        .HasColumnType("geometry(Point,4326)")
                        .HasColumnName("location");

                    b.Property<string>("LocationHash")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("locationHash");

                    b.Property<string>("LocationHash2")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("locationHash2");

                    b.Property<string>("LocationHash3")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("locationHash3");

                    b.Property<string>("Name")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("name");

                    b.Property<bool?>("Pending")
                        .HasColumnType("boolean")
                        .HasColumnName("pending");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)")
                        .HasColumnName("phoneNumber");

                    b.Property<string>("PlaceDetailId")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)")
                        .HasColumnName("placeDetailId");

                    b.Property<string>("PlaceId")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("placeId");

                    b.Property<bool?>("Popular")
                        .HasColumnType("boolean")
                        .HasColumnName("popular");

                    b.Property<bool?>("Processed")
                        .HasColumnType("boolean")
                        .HasColumnName("processed");

                    b.Property<int?>("ProvinceId")
                        .HasColumnType("integer")
                        .HasColumnName("provinceId");

                    b.Property<int?>("QuoteCount")
                        .HasColumnType("integer")
                        .HasColumnName("quoteCount");

                    b.Property<bool?>("Removed")
                        .HasColumnType("boolean")
                        .HasColumnName("removed");

                    b.Property<string>("Route")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("route");

                    b.Property<string>("StreetNumber")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("streetNumber");

                    b.Property<string>("TagInput")
                        .HasColumnType("text")
                        .HasColumnName("tagInput");

                    b.Property<List<string>>("Tags")
                        .HasColumnType("text[]")
                        .HasColumnName("tags");

                    b.Property<NpgsqlTsVector>("Ts")
                        .HasColumnType("tsvector")
                        .HasColumnName("ts");

                    b.Property<NpgsqlTsVector>("Ts2")
                        .HasColumnType("tsvector")
                        .HasColumnName("ts2");

                    b.Property<int?>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<LocalDateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updatedAt");

                    b.Property<string>("Ward")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("ward");

                    b.Property<int?>("WardId")
                        .HasColumnType("integer")
                        .HasColumnName("wardId");

                    b.ToTable("places_uat_bak");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.Placesdatae", b =>
                {
                    b.Property<string>("Address")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("CompareScore")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("District")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("LastUpdateUser")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("PlaceId")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("Province")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("ScanProcessed")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("UpdatedAt")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Ward")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.ToTable("placesdataes");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.PlanetOsmLine", b =>
                {
                    b.Property<string>("Access")
                        .HasColumnType("text")
                        .HasColumnName("access");

                    b.Property<string>("AddrHousename")
                        .HasColumnType("text")
                        .HasColumnName("addr:housename");

                    b.Property<string>("AddrHousenumber")
                        .HasColumnType("text")
                        .HasColumnName("addr:housenumber");

                    b.Property<string>("AddrInterpolation")
                        .HasColumnType("text")
                        .HasColumnName("addr:interpolation");

                    b.Property<string>("AdminLevel")
                        .HasColumnType("text")
                        .HasColumnName("admin_level");

                    b.Property<string>("Aerialway")
                        .HasColumnType("text")
                        .HasColumnName("aerialway");

                    b.Property<string>("Aeroway")
                        .HasColumnType("text")
                        .HasColumnName("aeroway");

                    b.Property<string>("Amenity")
                        .HasColumnType("text")
                        .HasColumnName("amenity");

                    b.Property<string>("Area")
                        .HasColumnType("text")
                        .HasColumnName("area");

                    b.Property<string>("Barrier")
                        .HasColumnType("text")
                        .HasColumnName("barrier");

                    b.Property<string>("Bicycle")
                        .HasColumnType("text")
                        .HasColumnName("bicycle");

                    b.Property<string>("Boundary")
                        .HasColumnType("text")
                        .HasColumnName("boundary");

                    b.Property<string>("Brand")
                        .HasColumnType("text")
                        .HasColumnName("brand");

                    b.Property<string>("Bridge")
                        .HasColumnType("text")
                        .HasColumnName("bridge");

                    b.Property<string>("Building")
                        .HasColumnType("text")
                        .HasColumnName("building");

                    b.Property<string>("Construction")
                        .HasColumnType("text")
                        .HasColumnName("construction");

                    b.Property<string>("Covered")
                        .HasColumnType("text")
                        .HasColumnName("covered");

                    b.Property<string>("Culvert")
                        .HasColumnType("text")
                        .HasColumnName("culvert");

                    b.Property<string>("Cutting")
                        .HasColumnType("text")
                        .HasColumnName("cutting");

                    b.Property<string>("Denomination")
                        .HasColumnType("text")
                        .HasColumnName("denomination");

                    b.Property<string>("Disused")
                        .HasColumnType("text")
                        .HasColumnName("disused");

                    b.Property<string>("Embankment")
                        .HasColumnType("text")
                        .HasColumnName("embankment");

                    b.Property<string>("Foot")
                        .HasColumnType("text")
                        .HasColumnName("foot");

                    b.Property<string>("GeneratorSource")
                        .HasColumnType("text")
                        .HasColumnName("generator:source");

                    b.Property<string>("Harbour")
                        .HasColumnType("text")
                        .HasColumnName("harbour");

                    b.Property<string>("Highway")
                        .HasColumnType("text")
                        .HasColumnName("highway");

                    b.Property<string>("Historic")
                        .HasColumnType("text")
                        .HasColumnName("historic");

                    b.Property<string>("Horse")
                        .HasColumnType("text")
                        .HasColumnName("horse");

                    b.Property<string>("Intermittent")
                        .HasColumnType("text")
                        .HasColumnName("intermittent");

                    b.Property<string>("Junction")
                        .HasColumnType("text")
                        .HasColumnName("junction");

                    b.Property<string>("Landuse")
                        .HasColumnType("text")
                        .HasColumnName("landuse");

                    b.Property<string>("Layer")
                        .HasColumnType("text")
                        .HasColumnName("layer");

                    b.Property<string>("Leisure")
                        .HasColumnType("text")
                        .HasColumnName("leisure");

                    b.Property<string>("Lock")
                        .HasColumnType("text")
                        .HasColumnName("lock");

                    b.Property<string>("ManMade")
                        .HasColumnType("text")
                        .HasColumnName("man_made");

                    b.Property<string>("Military")
                        .HasColumnType("text")
                        .HasColumnName("military");

                    b.Property<string>("Motorcar")
                        .HasColumnType("text")
                        .HasColumnName("motorcar");

                    b.Property<string>("Name")
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("Natural")
                        .HasColumnType("text")
                        .HasColumnName("natural");

                    b.Property<string>("Office")
                        .HasColumnType("text")
                        .HasColumnName("office");

                    b.Property<string>("Oneway")
                        .HasColumnType("text")
                        .HasColumnName("oneway");

                    b.Property<string>("Operator")
                        .HasColumnType("text")
                        .HasColumnName("operator");

                    b.Property<long?>("OsmId")
                        .HasColumnType("bigint")
                        .HasColumnName("osm_id");

                    b.Property<string>("Place")
                        .HasColumnType("text")
                        .HasColumnName("place");

                    b.Property<string>("Population")
                        .HasColumnType("text")
                        .HasColumnName("population");

                    b.Property<string>("Power")
                        .HasColumnType("text")
                        .HasColumnName("power");

                    b.Property<string>("PowerSource")
                        .HasColumnType("text")
                        .HasColumnName("power_source");

                    b.Property<string>("PublicTransport")
                        .HasColumnType("text")
                        .HasColumnName("public_transport");

                    b.Property<string>("Railway")
                        .HasColumnType("text")
                        .HasColumnName("railway");

                    b.Property<string>("Ref")
                        .HasColumnType("text")
                        .HasColumnName("ref");

                    b.Property<string>("Religion")
                        .HasColumnType("text")
                        .HasColumnName("religion");

                    b.Property<string>("Route")
                        .HasColumnType("text")
                        .HasColumnName("route");

                    b.Property<string>("Service")
                        .HasColumnType("text")
                        .HasColumnName("service");

                    b.Property<string>("Shop")
                        .HasColumnType("text")
                        .HasColumnName("shop");

                    b.Property<string>("Sport")
                        .HasColumnType("text")
                        .HasColumnName("sport");

                    b.Property<string>("Surface")
                        .HasColumnType("text")
                        .HasColumnName("surface");

                    b.Property<string>("Toll")
                        .HasColumnType("text")
                        .HasColumnName("toll");

                    b.Property<string>("Tourism")
                        .HasColumnType("text")
                        .HasColumnName("tourism");

                    b.Property<string>("TowerType")
                        .HasColumnType("text")
                        .HasColumnName("tower:type");

                    b.Property<string>("Tracktype")
                        .HasColumnType("text")
                        .HasColumnName("tracktype");

                    b.Property<string>("Tunnel")
                        .HasColumnType("text")
                        .HasColumnName("tunnel");

                    b.Property<string>("Water")
                        .HasColumnType("text")
                        .HasColumnName("water");

                    b.Property<string>("Waterway")
                        .HasColumnType("text")
                        .HasColumnName("waterway");

                    b.Property<LineString>("Way")
                        .HasColumnType("geometry(LineString,3857)")
                        .HasColumnName("way");

                    b.Property<float?>("WayArea")
                        .HasColumnType("real")
                        .HasColumnName("way_area");

                    b.Property<string>("Wetland")
                        .HasColumnType("text")
                        .HasColumnName("wetland");

                    b.Property<string>("Width")
                        .HasColumnType("text")
                        .HasColumnName("width");

                    b.Property<string>("Wood")
                        .HasColumnType("text")
                        .HasColumnName("wood");

                    b.Property<int?>("ZOrder")
                        .HasColumnType("integer")
                        .HasColumnName("z_order");

                    b.HasIndex(new[] { "Way" }, "planet_osm_line_way_idx")
                        .HasAnnotation("Npgsql:StorageParameter:fillfactor", "100");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex(new[] { "Way" }, "planet_osm_line_way_idx"), "gist");

                    b.ToTable("planet_osm_line");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.PlanetOsmNode", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<int>("Lat")
                        .HasColumnType("integer")
                        .HasColumnName("lat");

                    b.Property<int>("Lon")
                        .HasColumnType("integer")
                        .HasColumnName("lon");

                    b.Property<string>("Tags")
                        .HasColumnType("jsonb")
                        .HasColumnName("tags");

                    b.HasKey("Id")
                        .HasName("planet_osm_nodes_pkey");

                    b.ToTable("planet_osm_nodes");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.PlanetOsmPoint", b =>
                {
                    b.Property<string>("Access")
                        .HasColumnType("text")
                        .HasColumnName("access");

                    b.Property<string>("AddrHousename")
                        .HasColumnType("text")
                        .HasColumnName("addr:housename");

                    b.Property<string>("AddrHousenumber")
                        .HasColumnType("text")
                        .HasColumnName("addr:housenumber");

                    b.Property<string>("AddrInterpolation")
                        .HasColumnType("text")
                        .HasColumnName("addr:interpolation");

                    b.Property<string>("AdminLevel")
                        .HasColumnType("text")
                        .HasColumnName("admin_level");

                    b.Property<string>("Aerialway")
                        .HasColumnType("text")
                        .HasColumnName("aerialway");

                    b.Property<string>("Aeroway")
                        .HasColumnType("text")
                        .HasColumnName("aeroway");

                    b.Property<string>("Amenity")
                        .HasColumnType("text")
                        .HasColumnName("amenity");

                    b.Property<string>("Area")
                        .HasColumnType("text")
                        .HasColumnName("area");

                    b.Property<string>("Barrier")
                        .HasColumnType("text")
                        .HasColumnName("barrier");

                    b.Property<string>("Bicycle")
                        .HasColumnType("text")
                        .HasColumnName("bicycle");

                    b.Property<string>("Boundary")
                        .HasColumnType("text")
                        .HasColumnName("boundary");

                    b.Property<string>("Brand")
                        .HasColumnType("text")
                        .HasColumnName("brand");

                    b.Property<string>("Bridge")
                        .HasColumnType("text")
                        .HasColumnName("bridge");

                    b.Property<string>("Building")
                        .HasColumnType("text")
                        .HasColumnName("building");

                    b.Property<string>("Capital")
                        .HasColumnType("text")
                        .HasColumnName("capital");

                    b.Property<string>("Construction")
                        .HasColumnType("text")
                        .HasColumnName("construction");

                    b.Property<string>("Covered")
                        .HasColumnType("text")
                        .HasColumnName("covered");

                    b.Property<string>("Culvert")
                        .HasColumnType("text")
                        .HasColumnName("culvert");

                    b.Property<string>("Cutting")
                        .HasColumnType("text")
                        .HasColumnName("cutting");

                    b.Property<string>("Denomination")
                        .HasColumnType("text")
                        .HasColumnName("denomination");

                    b.Property<string>("Disused")
                        .HasColumnType("text")
                        .HasColumnName("disused");

                    b.Property<string>("Ele")
                        .HasColumnType("text")
                        .HasColumnName("ele");

                    b.Property<string>("Embankment")
                        .HasColumnType("text")
                        .HasColumnName("embankment");

                    b.Property<string>("Foot")
                        .HasColumnType("text")
                        .HasColumnName("foot");

                    b.Property<string>("GeneratorSource")
                        .HasColumnType("text")
                        .HasColumnName("generator:source");

                    b.Property<string>("Harbour")
                        .HasColumnType("text")
                        .HasColumnName("harbour");

                    b.Property<string>("Highway")
                        .HasColumnType("text")
                        .HasColumnName("highway");

                    b.Property<string>("Historic")
                        .HasColumnType("text")
                        .HasColumnName("historic");

                    b.Property<string>("Horse")
                        .HasColumnType("text")
                        .HasColumnName("horse");

                    b.Property<string>("Intermittent")
                        .HasColumnType("text")
                        .HasColumnName("intermittent");

                    b.Property<string>("Junction")
                        .HasColumnType("text")
                        .HasColumnName("junction");

                    b.Property<string>("Landuse")
                        .HasColumnType("text")
                        .HasColumnName("landuse");

                    b.Property<string>("Layer")
                        .HasColumnType("text")
                        .HasColumnName("layer");

                    b.Property<string>("Leisure")
                        .HasColumnType("text")
                        .HasColumnName("leisure");

                    b.Property<string>("Lock")
                        .HasColumnType("text")
                        .HasColumnName("lock");

                    b.Property<string>("ManMade")
                        .HasColumnType("text")
                        .HasColumnName("man_made");

                    b.Property<string>("Military")
                        .HasColumnType("text")
                        .HasColumnName("military");

                    b.Property<string>("Motorcar")
                        .HasColumnType("text")
                        .HasColumnName("motorcar");

                    b.Property<string>("Name")
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("Natural")
                        .HasColumnType("text")
                        .HasColumnName("natural");

                    b.Property<string>("Office")
                        .HasColumnType("text")
                        .HasColumnName("office");

                    b.Property<string>("Oneway")
                        .HasColumnType("text")
                        .HasColumnName("oneway");

                    b.Property<string>("Operator")
                        .HasColumnType("text")
                        .HasColumnName("operator");

                    b.Property<long?>("OsmId")
                        .HasColumnType("bigint")
                        .HasColumnName("osm_id");

                    b.Property<string>("Place")
                        .HasColumnType("text")
                        .HasColumnName("place");

                    b.Property<string>("Population")
                        .HasColumnType("text")
                        .HasColumnName("population");

                    b.Property<string>("Power")
                        .HasColumnType("text")
                        .HasColumnName("power");

                    b.Property<string>("PowerSource")
                        .HasColumnType("text")
                        .HasColumnName("power_source");

                    b.Property<string>("PublicTransport")
                        .HasColumnType("text")
                        .HasColumnName("public_transport");

                    b.Property<string>("Railway")
                        .HasColumnType("text")
                        .HasColumnName("railway");

                    b.Property<string>("Ref")
                        .HasColumnType("text")
                        .HasColumnName("ref");

                    b.Property<string>("Religion")
                        .HasColumnType("text")
                        .HasColumnName("religion");

                    b.Property<string>("Route")
                        .HasColumnType("text")
                        .HasColumnName("route");

                    b.Property<string>("Service")
                        .HasColumnType("text")
                        .HasColumnName("service");

                    b.Property<string>("Shop")
                        .HasColumnType("text")
                        .HasColumnName("shop");

                    b.Property<string>("Sport")
                        .HasColumnType("text")
                        .HasColumnName("sport");

                    b.Property<string>("Surface")
                        .HasColumnType("text")
                        .HasColumnName("surface");

                    b.Property<string>("Toll")
                        .HasColumnType("text")
                        .HasColumnName("toll");

                    b.Property<string>("Tourism")
                        .HasColumnType("text")
                        .HasColumnName("tourism");

                    b.Property<string>("TowerType")
                        .HasColumnType("text")
                        .HasColumnName("tower:type");

                    b.Property<string>("Tunnel")
                        .HasColumnType("text")
                        .HasColumnName("tunnel");

                    b.Property<string>("Water")
                        .HasColumnType("text")
                        .HasColumnName("water");

                    b.Property<string>("Waterway")
                        .HasColumnType("text")
                        .HasColumnName("waterway");

                    b.Property<Point>("Way")
                        .HasColumnType("geometry(Point,3857)")
                        .HasColumnName("way");

                    b.Property<string>("Wetland")
                        .HasColumnType("text")
                        .HasColumnName("wetland");

                    b.Property<string>("Width")
                        .HasColumnType("text")
                        .HasColumnName("width");

                    b.Property<string>("Wood")
                        .HasColumnType("text")
                        .HasColumnName("wood");

                    b.Property<int?>("ZOrder")
                        .HasColumnType("integer")
                        .HasColumnName("z_order");

                    b.HasIndex(new[] { "Way" }, "planet_osm_point_way_idx")
                        .HasAnnotation("Npgsql:StorageParameter:fillfactor", "100");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex(new[] { "Way" }, "planet_osm_point_way_idx"), "gist");

                    b.ToTable("planet_osm_point");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.PlanetOsmPolygon", b =>
                {
                    b.Property<string>("Access")
                        .HasColumnType("text")
                        .HasColumnName("access");

                    b.Property<string>("AddrHousename")
                        .HasColumnType("text")
                        .HasColumnName("addr:housename");

                    b.Property<string>("AddrHousenumber")
                        .HasColumnType("text")
                        .HasColumnName("addr:housenumber");

                    b.Property<string>("AddrInterpolation")
                        .HasColumnType("text")
                        .HasColumnName("addr:interpolation");

                    b.Property<string>("AdminLevel")
                        .HasColumnType("text")
                        .HasColumnName("admin_level");

                    b.Property<string>("Aerialway")
                        .HasColumnType("text")
                        .HasColumnName("aerialway");

                    b.Property<string>("Aeroway")
                        .HasColumnType("text")
                        .HasColumnName("aeroway");

                    b.Property<string>("Amenity")
                        .HasColumnType("text")
                        .HasColumnName("amenity");

                    b.Property<string>("Area")
                        .HasColumnType("text")
                        .HasColumnName("area");

                    b.Property<string>("Barrier")
                        .HasColumnType("text")
                        .HasColumnName("barrier");

                    b.Property<string>("Bicycle")
                        .HasColumnType("text")
                        .HasColumnName("bicycle");

                    b.Property<string>("Boundary")
                        .HasColumnType("text")
                        .HasColumnName("boundary");

                    b.Property<string>("Brand")
                        .HasColumnType("text")
                        .HasColumnName("brand");

                    b.Property<string>("Bridge")
                        .HasColumnType("text")
                        .HasColumnName("bridge");

                    b.Property<string>("Building")
                        .HasColumnType("text")
                        .HasColumnName("building");

                    b.Property<string>("Construction")
                        .HasColumnType("text")
                        .HasColumnName("construction");

                    b.Property<string>("Covered")
                        .HasColumnType("text")
                        .HasColumnName("covered");

                    b.Property<string>("Culvert")
                        .HasColumnType("text")
                        .HasColumnName("culvert");

                    b.Property<string>("Cutting")
                        .HasColumnType("text")
                        .HasColumnName("cutting");

                    b.Property<string>("Denomination")
                        .HasColumnType("text")
                        .HasColumnName("denomination");

                    b.Property<string>("Disused")
                        .HasColumnType("text")
                        .HasColumnName("disused");

                    b.Property<string>("Embankment")
                        .HasColumnType("text")
                        .HasColumnName("embankment");

                    b.Property<string>("Foot")
                        .HasColumnType("text")
                        .HasColumnName("foot");

                    b.Property<string>("GeneratorSource")
                        .HasColumnType("text")
                        .HasColumnName("generator:source");

                    b.Property<string>("Harbour")
                        .HasColumnType("text")
                        .HasColumnName("harbour");

                    b.Property<string>("Highway")
                        .HasColumnType("text")
                        .HasColumnName("highway");

                    b.Property<string>("Historic")
                        .HasColumnType("text")
                        .HasColumnName("historic");

                    b.Property<string>("Horse")
                        .HasColumnType("text")
                        .HasColumnName("horse");

                    b.Property<string>("Intermittent")
                        .HasColumnType("text")
                        .HasColumnName("intermittent");

                    b.Property<string>("Junction")
                        .HasColumnType("text")
                        .HasColumnName("junction");

                    b.Property<string>("Landuse")
                        .HasColumnType("text")
                        .HasColumnName("landuse");

                    b.Property<string>("Layer")
                        .HasColumnType("text")
                        .HasColumnName("layer");

                    b.Property<string>("Leisure")
                        .HasColumnType("text")
                        .HasColumnName("leisure");

                    b.Property<string>("Lock")
                        .HasColumnType("text")
                        .HasColumnName("lock");

                    b.Property<string>("ManMade")
                        .HasColumnType("text")
                        .HasColumnName("man_made");

                    b.Property<string>("Military")
                        .HasColumnType("text")
                        .HasColumnName("military");

                    b.Property<string>("Motorcar")
                        .HasColumnType("text")
                        .HasColumnName("motorcar");

                    b.Property<string>("Name")
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("Natural")
                        .HasColumnType("text")
                        .HasColumnName("natural");

                    b.Property<string>("Office")
                        .HasColumnType("text")
                        .HasColumnName("office");

                    b.Property<string>("Oneway")
                        .HasColumnType("text")
                        .HasColumnName("oneway");

                    b.Property<string>("Operator")
                        .HasColumnType("text")
                        .HasColumnName("operator");

                    b.Property<long?>("OsmId")
                        .HasColumnType("bigint")
                        .HasColumnName("osm_id");

                    b.Property<string>("Place")
                        .HasColumnType("text")
                        .HasColumnName("place");

                    b.Property<string>("Population")
                        .HasColumnType("text")
                        .HasColumnName("population");

                    b.Property<string>("Power")
                        .HasColumnType("text")
                        .HasColumnName("power");

                    b.Property<string>("PowerSource")
                        .HasColumnType("text")
                        .HasColumnName("power_source");

                    b.Property<string>("PublicTransport")
                        .HasColumnType("text")
                        .HasColumnName("public_transport");

                    b.Property<string>("Railway")
                        .HasColumnType("text")
                        .HasColumnName("railway");

                    b.Property<string>("Ref")
                        .HasColumnType("text")
                        .HasColumnName("ref");

                    b.Property<string>("Religion")
                        .HasColumnType("text")
                        .HasColumnName("religion");

                    b.Property<string>("Route")
                        .HasColumnType("text")
                        .HasColumnName("route");

                    b.Property<string>("Service")
                        .HasColumnType("text")
                        .HasColumnName("service");

                    b.Property<string>("Shop")
                        .HasColumnType("text")
                        .HasColumnName("shop");

                    b.Property<string>("Sport")
                        .HasColumnType("text")
                        .HasColumnName("sport");

                    b.Property<string>("Surface")
                        .HasColumnType("text")
                        .HasColumnName("surface");

                    b.Property<string>("Toll")
                        .HasColumnType("text")
                        .HasColumnName("toll");

                    b.Property<string>("Tourism")
                        .HasColumnType("text")
                        .HasColumnName("tourism");

                    b.Property<string>("TowerType")
                        .HasColumnType("text")
                        .HasColumnName("tower:type");

                    b.Property<string>("Tracktype")
                        .HasColumnType("text")
                        .HasColumnName("tracktype");

                    b.Property<string>("Tunnel")
                        .HasColumnType("text")
                        .HasColumnName("tunnel");

                    b.Property<string>("Water")
                        .HasColumnType("text")
                        .HasColumnName("water");

                    b.Property<string>("Waterway")
                        .HasColumnType("text")
                        .HasColumnName("waterway");

                    b.Property<Geometry>("Way")
                        .HasColumnType("geometry(Geometry,3857)")
                        .HasColumnName("way");

                    b.Property<float?>("WayArea")
                        .HasColumnType("real")
                        .HasColumnName("way_area");

                    b.Property<string>("Wetland")
                        .HasColumnType("text")
                        .HasColumnName("wetland");

                    b.Property<string>("Width")
                        .HasColumnType("text")
                        .HasColumnName("width");

                    b.Property<string>("Wood")
                        .HasColumnType("text")
                        .HasColumnName("wood");

                    b.Property<int?>("ZOrder")
                        .HasColumnType("integer")
                        .HasColumnName("z_order");

                    b.HasIndex(new[] { "Way" }, "planet_osm_polygon_way_idx")
                        .HasAnnotation("Npgsql:StorageParameter:fillfactor", "100");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex(new[] { "Way" }, "planet_osm_polygon_way_idx"), "gist");

                    b.ToTable("planet_osm_polygon");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.PlanetOsmRel", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("Members")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("members");

                    b.Property<string>("Tags")
                        .HasColumnType("jsonb")
                        .HasColumnName("tags");

                    b.HasKey("Id")
                        .HasName("planet_osm_rels_pkey");

                    b.ToTable("planet_osm_rels");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.PlanetOsmRoad", b =>
                {
                    b.Property<string>("Access")
                        .HasColumnType("text")
                        .HasColumnName("access");

                    b.Property<string>("AddrHousename")
                        .HasColumnType("text")
                        .HasColumnName("addr:housename");

                    b.Property<string>("AddrHousenumber")
                        .HasColumnType("text")
                        .HasColumnName("addr:housenumber");

                    b.Property<string>("AddrInterpolation")
                        .HasColumnType("text")
                        .HasColumnName("addr:interpolation");

                    b.Property<string>("AdminLevel")
                        .HasColumnType("text")
                        .HasColumnName("admin_level");

                    b.Property<string>("Aerialway")
                        .HasColumnType("text")
                        .HasColumnName("aerialway");

                    b.Property<string>("Aeroway")
                        .HasColumnType("text")
                        .HasColumnName("aeroway");

                    b.Property<string>("Amenity")
                        .HasColumnType("text")
                        .HasColumnName("amenity");

                    b.Property<string>("Area")
                        .HasColumnType("text")
                        .HasColumnName("area");

                    b.Property<string>("Barrier")
                        .HasColumnType("text")
                        .HasColumnName("barrier");

                    b.Property<string>("Bicycle")
                        .HasColumnType("text")
                        .HasColumnName("bicycle");

                    b.Property<string>("Boundary")
                        .HasColumnType("text")
                        .HasColumnName("boundary");

                    b.Property<string>("Brand")
                        .HasColumnType("text")
                        .HasColumnName("brand");

                    b.Property<string>("Bridge")
                        .HasColumnType("text")
                        .HasColumnName("bridge");

                    b.Property<string>("Building")
                        .HasColumnType("text")
                        .HasColumnName("building");

                    b.Property<string>("Construction")
                        .HasColumnType("text")
                        .HasColumnName("construction");

                    b.Property<string>("Covered")
                        .HasColumnType("text")
                        .HasColumnName("covered");

                    b.Property<string>("Culvert")
                        .HasColumnType("text")
                        .HasColumnName("culvert");

                    b.Property<string>("Cutting")
                        .HasColumnType("text")
                        .HasColumnName("cutting");

                    b.Property<string>("Denomination")
                        .HasColumnType("text")
                        .HasColumnName("denomination");

                    b.Property<string>("Disused")
                        .HasColumnType("text")
                        .HasColumnName("disused");

                    b.Property<string>("Embankment")
                        .HasColumnType("text")
                        .HasColumnName("embankment");

                    b.Property<string>("Foot")
                        .HasColumnType("text")
                        .HasColumnName("foot");

                    b.Property<string>("GeneratorSource")
                        .HasColumnType("text")
                        .HasColumnName("generator:source");

                    b.Property<string>("Harbour")
                        .HasColumnType("text")
                        .HasColumnName("harbour");

                    b.Property<string>("Highway")
                        .HasColumnType("text")
                        .HasColumnName("highway");

                    b.Property<string>("Historic")
                        .HasColumnType("text")
                        .HasColumnName("historic");

                    b.Property<string>("Horse")
                        .HasColumnType("text")
                        .HasColumnName("horse");

                    b.Property<string>("Intermittent")
                        .HasColumnType("text")
                        .HasColumnName("intermittent");

                    b.Property<string>("Junction")
                        .HasColumnType("text")
                        .HasColumnName("junction");

                    b.Property<string>("Landuse")
                        .HasColumnType("text")
                        .HasColumnName("landuse");

                    b.Property<string>("Layer")
                        .HasColumnType("text")
                        .HasColumnName("layer");

                    b.Property<string>("Leisure")
                        .HasColumnType("text")
                        .HasColumnName("leisure");

                    b.Property<string>("Lock")
                        .HasColumnType("text")
                        .HasColumnName("lock");

                    b.Property<string>("ManMade")
                        .HasColumnType("text")
                        .HasColumnName("man_made");

                    b.Property<string>("Military")
                        .HasColumnType("text")
                        .HasColumnName("military");

                    b.Property<string>("Motorcar")
                        .HasColumnType("text")
                        .HasColumnName("motorcar");

                    b.Property<string>("Name")
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("Natural")
                        .HasColumnType("text")
                        .HasColumnName("natural");

                    b.Property<string>("Office")
                        .HasColumnType("text")
                        .HasColumnName("office");

                    b.Property<string>("Oneway")
                        .HasColumnType("text")
                        .HasColumnName("oneway");

                    b.Property<string>("Operator")
                        .HasColumnType("text")
                        .HasColumnName("operator");

                    b.Property<long?>("OsmId")
                        .HasColumnType("bigint")
                        .HasColumnName("osm_id");

                    b.Property<string>("Place")
                        .HasColumnType("text")
                        .HasColumnName("place");

                    b.Property<string>("Population")
                        .HasColumnType("text")
                        .HasColumnName("population");

                    b.Property<string>("Power")
                        .HasColumnType("text")
                        .HasColumnName("power");

                    b.Property<string>("PowerSource")
                        .HasColumnType("text")
                        .HasColumnName("power_source");

                    b.Property<string>("PublicTransport")
                        .HasColumnType("text")
                        .HasColumnName("public_transport");

                    b.Property<string>("Railway")
                        .HasColumnType("text")
                        .HasColumnName("railway");

                    b.Property<string>("Ref")
                        .HasColumnType("text")
                        .HasColumnName("ref");

                    b.Property<string>("Religion")
                        .HasColumnType("text")
                        .HasColumnName("religion");

                    b.Property<string>("Route")
                        .HasColumnType("text")
                        .HasColumnName("route");

                    b.Property<string>("Service")
                        .HasColumnType("text")
                        .HasColumnName("service");

                    b.Property<string>("Shop")
                        .HasColumnType("text")
                        .HasColumnName("shop");

                    b.Property<string>("Sport")
                        .HasColumnType("text")
                        .HasColumnName("sport");

                    b.Property<string>("Surface")
                        .HasColumnType("text")
                        .HasColumnName("surface");

                    b.Property<string>("Toll")
                        .HasColumnType("text")
                        .HasColumnName("toll");

                    b.Property<string>("Tourism")
                        .HasColumnType("text")
                        .HasColumnName("tourism");

                    b.Property<string>("TowerType")
                        .HasColumnType("text")
                        .HasColumnName("tower:type");

                    b.Property<string>("Tracktype")
                        .HasColumnType("text")
                        .HasColumnName("tracktype");

                    b.Property<string>("Tunnel")
                        .HasColumnType("text")
                        .HasColumnName("tunnel");

                    b.Property<string>("Water")
                        .HasColumnType("text")
                        .HasColumnName("water");

                    b.Property<string>("Waterway")
                        .HasColumnType("text")
                        .HasColumnName("waterway");

                    b.Property<LineString>("Way")
                        .HasColumnType("geometry(LineString,3857)")
                        .HasColumnName("way");

                    b.Property<float?>("WayArea")
                        .HasColumnType("real")
                        .HasColumnName("way_area");

                    b.Property<string>("Wetland")
                        .HasColumnType("text")
                        .HasColumnName("wetland");

                    b.Property<string>("Width")
                        .HasColumnType("text")
                        .HasColumnName("width");

                    b.Property<string>("Wood")
                        .HasColumnType("text")
                        .HasColumnName("wood");

                    b.Property<int?>("ZOrder")
                        .HasColumnType("integer")
                        .HasColumnName("z_order");

                    b.HasIndex(new[] { "Way" }, "planet_osm_roads_way_idx")
                        .HasAnnotation("Npgsql:StorageParameter:fillfactor", "100");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex(new[] { "Way" }, "planet_osm_roads_way_idx"), "gist");

                    b.ToTable("planet_osm_roads");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.PlanetOsmWay", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<List<long>>("Nodes")
                        .IsRequired()
                        .HasColumnType("bigint[]")
                        .HasColumnName("nodes");

                    b.Property<string>("Tags")
                        .HasColumnType("jsonb")
                        .HasColumnName("tags");

                    b.HasKey("Id")
                        .HasName("planet_osm_ways_pkey");

                    b.ToTable("planet_osm_ways");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.Province", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<string>("Boundary")
                        .HasColumnType("text")
                        .HasColumnName("boundary");

                    b.Property<string>("BoundaryGeom")
                        .HasColumnType("text")
                        .HasColumnName("boundary_geom");

                    b.Property<string>("CenterLocation")
                        .HasColumnType("character varying")
                        .HasColumnName("center_location");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("character varying")
                        .HasColumnName("code");

                    b.Property<string>("Name")
                        .HasColumnType("character varying")
                        .HasColumnName("name");

                    b.Property<bool?>("Processed")
                        .HasColumnType("boolean")
                        .HasColumnName("processed");

                    b.Property<int?>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.HasKey("Id")
                        .HasName("pk_provinces");

                    b.HasIndex(new[] { "Code" }, "provinces_code_unique")
                        .IsUnique();

                    b.ToTable("provinces");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.ProvinceBoundary", b =>
                {
                    b.Property<string>("Class")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("_class");

                    b.Property<LocalDateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("_id");

                    b.Property<int?>("Idprovince")
                        .HasColumnType("integer")
                        .HasColumnName("idprovince");

                    b.Property<string>("Location")
                        .HasMaxLength(10485760)
                        .HasColumnType("character varying(10485760)")
                        .HasColumnName("location");

                    b.Property<LocalDateTime?>("ModifiedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("modified_by");

                    b.Property<string>("Name")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("name");

                    b.Property<int?>("Postcode")
                        .HasColumnType("integer")
                        .HasColumnName("postcode");

                    b.Property<int?>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.ToTable("province_boundaries");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.ProvincesBridge", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<int>("Id"));

                    b.Property<string>("ProvincesNewCode")
                        .IsRequired()
                        .HasColumnType("character varying")
                        .HasColumnName("provinces_new_code")
                        .HasComment("Mã tỉnh mới sâu 1/7/2025");

                    b.Property<string>("ProvincesOldCode")
                        .IsRequired()
                        .HasColumnType("character varying")
                        .HasColumnName("provinces_old_code")
                        .HasComment("Mã tỉnh cũ trước 1/7/2025");

                    b.HasKey("Id")
                        .HasName("provinces_pk");

                    b.HasIndex(new[] { "ProvincesNewCode" }, "provinces_provinces_new_code_idx");

                    b.HasIndex(new[] { "ProvincesOldCode" }, "provinces_provinces_old_code_idx");

                    b.ToTable("provinces_bridge");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.ProvincesNew", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id")
                        .HasDefaultValueSql("nextval('provinces__id_seq'::regclass)");

                    b.Property<string>("Boundary")
                        .HasColumnType("text")
                        .HasColumnName("boundary");

                    b.Property<string>("BoundaryGeom")
                        .HasColumnType("text")
                        .HasColumnName("boundary_geom");

                    b.Property<string>("CenterLocation")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("center_location");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<string>("Name")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("name");

                    b.Property<string>("Oldcode")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("oldcode");

                    b.Property<bool?>("Processed")
                        .HasColumnType("boolean")
                        .HasColumnName("processed");

                    b.Property<string>("Type")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("type");

                    b.HasKey("Id")
                        .HasName("provinces__pkey");

                    b.HasIndex(new[] { "Code" }, "provinces_new_code_unique")
                        .IsUnique();

                    b.ToTable("provinces_new");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.Sheet2", b =>
                {
                    b.Property<int?>("Column10")
                        .HasColumnType("integer")
                        .HasColumnName("Column1.0");

                    b.Property<int?>("Column11")
                        .HasColumnType("integer")
                        .HasColumnName("Column1.1");

                    b.Property<int?>("Column110")
                        .HasColumnType("integer")
                        .HasColumnName("Column1.10");

                    b.Property<int?>("Column111")
                        .HasColumnType("integer")
                        .HasColumnName("Column1.11");

                    b.Property<string>("Column112")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)")
                        .HasColumnName("Column1.12");

                    b.Property<int?>("Column113")
                        .HasColumnType("integer")
                        .HasColumnName("Column1.13");

                    b.Property<string>("Column12")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.2");

                    b.Property<string>("Column13")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.3");

                    b.Property<string>("Column14")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.4");

                    b.Property<string>("Column15")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.5");

                    b.Property<string>("Column16")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.6");

                    b.Property<int?>("Column17")
                        .HasColumnType("integer")
                        .HasColumnName("Column1.7");

                    b.Property<string>("Column18")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.8");

                    b.Property<string>("Column19")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("Column1.9");

                    b.Property<string>("Column1Cay")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.cay");

                    b.Property<string>("Column1Con")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.con");

                    b.Property<string>("Column1Dansonguoi")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.dansonguoi");

                    b.Property<int?>("Column1Dientichkm2")
                        .HasColumnType("integer")
                        .HasColumnName("Column1.dientichkm2");

                    b.Property<int?>("Column1Id")
                        .HasColumnType("integer")
                        .HasColumnName("Column1.id");

                    b.Property<int?>("Column1Kinhdo")
                        .HasColumnType("integer")
                        .HasColumnName("Column1.kinhdo");

                    b.Property<string>("Column1Loai")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.loai");

                    b.Property<string>("Column1Ma")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.ma");

                    b.Property<int?>("Column1Mahc")
                        .HasColumnType("integer")
                        .HasColumnName("Column1.mahc");

                    b.Property<string>("Column1Matinh")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.matinh");

                    b.Property<int?>("Column1Maxa")
                        .HasColumnType("integer")
                        .HasColumnName("Column1.maxa");

                    b.Property<string>("Column1Tenhc")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.tenhc");

                    b.Property<string>("Column1Tentinh")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.tentinh");

                    b.Property<string>("Column1Trungtamhc")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("Column1.trungtamhc");

                    b.Property<string>("Column1Truocsapnhap")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)")
                        .HasColumnName("Column1.truocsapnhap");

                    b.Property<int?>("Column1Vido")
                        .HasColumnType("integer")
                        .HasColumnName("Column1.vido");

                    b.ToTable("sheet2");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.Sheet3", b =>
                {
                    b.Property<int>("Column1Id")
                        .HasColumnType("integer")
                        .HasColumnName("Column1.id");

                    b.Property<int?>("Column10")
                        .HasColumnType("integer")
                        .HasColumnName("Column1.0");

                    b.Property<int?>("Column11")
                        .HasColumnType("integer")
                        .HasColumnName("Column1.1");

                    b.Property<string>("Column12")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.2");

                    b.Property<string>("Column13")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.3");

                    b.Property<string>("Column14")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.4");

                    b.Property<string>("Column15")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.5");

                    b.Property<int?>("Column16")
                        .HasColumnType("integer")
                        .HasColumnName("Column1.6");

                    b.Property<int?>("Column17")
                        .HasColumnType("integer")
                        .HasColumnName("Column1.7");

                    b.Property<string>("Column18")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("Column1.8");

                    b.Property<string>("Column19")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.9");

                    b.Property<string>("Column1Con")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.con");

                    b.Property<string>("Column1Dansonguoi")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.dansonguoi");

                    b.Property<string>("Column1Dientichkm2")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.dientichkm2");

                    b.Property<int?>("Column1Kinhdo")
                        .HasColumnType("integer")
                        .HasColumnName("Column1.kinhdo");

                    b.Property<string>("Column1Mahc")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.mahc");

                    b.Property<string>("Column1Tentinh")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.tentinh");

                    b.Property<string>("Column1Trungtamhc")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("Column1.trungtamhc");

                    b.Property<string>("Column1Truocsapnhap")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("Column1.truocsapnhap");

                    b.Property<int?>("Column1Vido")
                        .HasColumnType("integer")
                        .HasColumnName("Column1.vido");

                    b.HasKey("Column1Id")
                        .HasName("sheet3_pk");

                    b.ToTable("sheet3");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.SubPlace", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("boolean")
                        .HasColumnName("active");

                    b.Property<string>("Address")
                        .HasColumnType("character varying")
                        .HasColumnName("address");

                    b.Property<int>("BookingCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("bookingCount");

                    b.Property<string>("City")
                        .HasColumnType("character varying")
                        .HasColumnName("city");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("createdAt")
                        .HasDefaultValueSql("'01-JAN-01 00:00:00'::timestamp without time zone");

                    b.Property<string>("CreatorId")
                        .HasColumnType("character varying")
                        .HasColumnName("creatorId");

                    b.Property<string>("DataFrom")
                        .HasColumnType("character varying")
                        .HasColumnName("dataFrom");

                    b.Property<string>("District")
                        .HasColumnType("character varying")
                        .HasColumnName("district");

                    b.Property<int?>("DistrictId")
                        .HasColumnType("integer")
                        .HasColumnName("districtId");

                    b.Property<string>("FullAddress")
                        .IsRequired()
                        .HasColumnType("character varying")
                        .HasColumnName("fullAddress");

                    b.Property<string>("FullAddressHash")
                        .HasColumnType("character varying")
                        .HasColumnName("fullAddressHash");

                    b.Property<bool>("Hidden")
                        .HasColumnType("boolean")
                        .HasColumnName("hidden");

                    b.Property<string>("Keywords")
                        .HasColumnType("character varying")
                        .HasColumnName("keywords");

                    b.Property<string>("LastUpdateUser")
                        .HasColumnType("character varying")
                        .HasColumnName("lastUpdateUser");

                    b.Property<Point>("Location")
                        .HasColumnType("geometry(Point,4326)")
                        .HasColumnName("location");

                    b.Property<string>("LocationHash")
                        .HasColumnType("character varying")
                        .HasColumnName("locationHash");

                    b.Property<string>("LocationHash2")
                        .HasColumnType("character varying")
                        .HasColumnName("locationHash2");

                    b.Property<string>("LocationHash3")
                        .HasColumnType("character varying")
                        .HasColumnName("locationHash3");

                    b.Property<string>("Name")
                        .HasColumnType("character varying")
                        .HasColumnName("name");

                    b.Property<long?>("ParentId")
                        .HasColumnType("bigint")
                        .HasColumnName("parentId");

                    b.Property<string>("ParentPlaceId")
                        .HasColumnType("character varying")
                        .HasColumnName("parentPlaceId");

                    b.Property<bool>("Pending")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("pending");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("character varying")
                        .HasColumnName("phoneNumber");

                    b.Property<string>("PlaceDetailId")
                        .HasColumnType("character varying")
                        .HasColumnName("placeDetailId");

                    b.Property<string>("PlaceId")
                        .IsRequired()
                        .HasColumnType("character varying")
                        .HasColumnName("placeId");

                    b.Property<bool>("Popular")
                        .HasColumnType("boolean")
                        .HasColumnName("popular");

                    b.Property<bool>("Processed")
                        .HasColumnType("boolean")
                        .HasColumnName("processed");

                    b.Property<int?>("ProvinceId")
                        .HasColumnType("integer")
                        .HasColumnName("provinceId");

                    b.Property<int>("QuoteCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("quoteCount");

                    b.Property<bool>("Removed")
                        .HasColumnType("boolean")
                        .HasColumnName("removed");

                    b.Property<string>("Route")
                        .HasColumnType("character varying")
                        .HasColumnName("route");

                    b.Property<string>("StreetNumber")
                        .HasColumnType("character varying")
                        .HasColumnName("streetNumber");

                    b.Property<string>("TagInput")
                        .HasColumnType("text")
                        .HasColumnName("tagInput");

                    b.Property<List<string>>("Tags")
                        .HasColumnType("text[]")
                        .HasColumnName("tags");

                    b.Property<NpgsqlTsVector>("Ts")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("tsvector")
                        .HasColumnName("ts")
                        .HasComputedColumnSql("to_tsvector('english'::regconfig, (((((COALESCE(name, ''::character varying))::text || ' '::text) || (\"fullAddress\")::text) || ' '::text) || (COALESCE(keywords, ''::character varying))::text))", true);

                    b.Property<NpgsqlTsVector>("Ts2")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("tsvector")
                        .HasColumnName("ts2")
                        .HasComputedColumnSql("to_tsvector('english'::regconfig, regexp_replace(((((COALESCE(vn_unaccent((name)::text), ''::text) || ' '::text) || COALESCE(vn_unaccent((\"fullAddress\")::text), ''::text)) || ' '::text) || COALESCE(vn_unaccent((keywords)::text), ''::text)), '[^\\w]+'::text, ' '::text, 'g'::text))", true);

                    b.Property<NpgsqlTsVector>("Ts3")
                        .HasColumnType("tsvector")
                        .HasColumnName("ts3");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updatedAt")
                        .HasDefaultValueSql("'01-JAN-01 00:00:00'::timestamp without time zone");

                    b.Property<string>("Ward")
                        .HasColumnType("character varying")
                        .HasColumnName("ward");

                    b.Property<int?>("WardId")
                        .HasColumnType("integer")
                        .HasColumnName("wardId");

                    b.HasKey("Id");

                    b.HasIndex("ParentPlaceId");

                    b.HasIndex(new[] { "BookingCount" }, "IX_sub_places_bookingCount");

                    b.HasIndex(new[] { "BookingCount", "QuoteCount" }, "IX_sub_places_bookingCount_quoteCount");

                    b.HasIndex(new[] { "City" }, "IX_sub_places_city");

                    b.HasIndex(new[] { "DistrictId" }, "IX_sub_places_districtId");

                    b.HasIndex(new[] { "FullAddressHash" }, "IX_sub_places_fullAddressHash");

                    b.HasIndex(new[] { "Hidden" }, "IX_sub_places_hidden");

                    b.HasIndex(new[] { "Location" }, "IX_sub_places_location");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex(new[] { "Location" }, "IX_sub_places_location"), "gist");

                    b.HasIndex(new[] { "LocationHash" }, "IX_sub_places_locationHash");

                    b.HasIndex(new[] { "LocationHash2" }, "IX_sub_places_locationHash2");

                    b.HasIndex(new[] { "LocationHash3" }, "IX_sub_places_locationHash3");

                    b.HasIndex(new[] { "ParentId" }, "IX_sub_places_parentId");

                    b.HasIndex(new[] { "Pending" }, "IX_sub_places_pending");

                    b.HasIndex(new[] { "PlaceDetailId" }, "IX_sub_places_placeDetailId");

                    b.HasIndex(new[] { "PlaceId" }, "IX_sub_places_placeId")
                        .IsUnique();

                    b.HasIndex(new[] { "Popular" }, "IX_sub_places_popular");

                    b.HasIndex(new[] { "Popular", "Processed", "Removed", "Hidden" }, "IX_sub_places_popular_processed_removed_hidden");

                    b.HasIndex(new[] { "Processed" }, "IX_sub_places_processed");

                    b.HasIndex(new[] { "Processed", "Pending" }, "IX_sub_places_processed_pending");

                    b.HasIndex(new[] { "ProvinceId" }, "IX_sub_places_provinceId");

                    b.HasIndex(new[] { "QuoteCount" }, "IX_sub_places_quoteCount");

                    b.HasIndex(new[] { "Removed" }, "IX_sub_places_removed");

                    b.HasIndex(new[] { "Removed", "Processed", "Pending" }, "IX_sub_places_removed_processed_pending");

                    b.HasIndex(new[] { "Removed", "Processed", "Pending", "BookingCount", "QuoteCount" }, "IX_sub_places_removed_processed_pending_bookingCount_quoteCount");

                    b.HasIndex(new[] { "Ts" }, "IX_sub_places_ts");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex(new[] { "Ts" }, "IX_sub_places_ts"), "gin");

                    b.HasIndex(new[] { "Ts2" }, "IX_sub_places_ts2");

                    NpgsqlIndexBuilderExtensions.HasMethod(b.HasIndex(new[] { "Ts2" }, "IX_sub_places_ts2"), "gin");

                    b.HasIndex(new[] { "WardId" }, "IX_sub_places_wardId");

                    b.ToTable("sub_places");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.SubplaceChangeHistory", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("createdAt");

                    b.Property<string>("Creator")
                        .HasColumnType("text")
                        .HasColumnName("creator");

                    b.Property<string>("DataJson")
                        .HasColumnType("text")
                        .HasColumnName("dataJson");

                    b.Property<string>("LastUpdatedUser")
                        .HasColumnType("text")
                        .HasColumnName("lastUpdatedUser");

                    b.Property<long>("SubPlaceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasDefaultValue(0L)
                        .HasColumnName("subPlaceId");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updatedAt");

                    b.HasKey("Id");

                    b.ToTable("subplace_change_histories");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.SysNo", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("TableName")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("tableName");

                    b.Property<int>("TypeCode")
                        .HasColumnType("integer")
                        .HasColumnName("typeCode");

                    b.Property<long>("Value")
                        .HasColumnType("bigint")
                        .HasColumnName("value");

                    b.HasKey("Id");

                    b.ToTable("sys_no");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.TrustedPlace", b =>
                {
                    b.Property<bool>("Active")
                        .HasColumnType("boolean")
                        .HasColumnName("active");

                    b.Property<string>("Address")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("address");

                    b.Property<int>("BookingCount")
                        .HasColumnType("integer")
                        .HasColumnName("bookingCount");

                    b.Property<string>("City")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("city");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("createdAt");

                    b.Property<string>("CreatorId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("creatorId");

                    b.Property<string>("DataFrom")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("dataFrom");

                    b.Property<string>("DataSource")
                        .HasColumnType("text")
                        .HasColumnName("dataSource");

                    b.Property<string>("District")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("district");

                    b.Property<int?>("DistrictId")
                        .HasColumnType("integer")
                        .HasColumnName("districtId");

                    b.Property<string>("FullAddress")
                        .IsRequired()
                        .HasMaxLength(10000)
                        .HasColumnType("character varying(10000)")
                        .HasColumnName("fullAddress");

                    b.Property<string>("FullAddressHash")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("fullAddressHash");

                    b.Property<bool?>("HasChild")
                        .HasColumnType("boolean")
                        .HasColumnName("hasChild");

                    b.Property<bool>("Hidden")
                        .HasColumnType("boolean")
                        .HasColumnName("hidden");

                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("Keywords")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("keywords");

                    b.Property<string>("LastUpdateUser")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("lastUpdateUser");

                    b.Property<Point>("Location")
                        .HasColumnType("geometry(Point,4326)")
                        .HasColumnName("location");

                    b.Property<string>("LocationHash")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("locationHash");

                    b.Property<string>("LocationHash2")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("locationHash2");

                    b.Property<string>("LocationHash3")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("locationHash3");

                    b.Property<string>("Name")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("name");

                    b.Property<bool>("Pending")
                        .HasColumnType("boolean")
                        .HasColumnName("pending");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)")
                        .HasColumnName("phoneNumber");

                    b.Property<string>("PlaceDetailId")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)")
                        .HasColumnName("placeDetailId");

                    b.Property<string>("PlaceId")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("placeId");

                    b.Property<bool>("Popular")
                        .HasColumnType("boolean")
                        .HasColumnName("popular");

                    b.Property<bool>("Processed")
                        .HasColumnType("boolean")
                        .HasColumnName("processed");

                    b.Property<int?>("ProvinceId")
                        .HasColumnType("integer")
                        .HasColumnName("provinceId");

                    b.Property<int>("QuoteCount")
                        .HasColumnType("integer")
                        .HasColumnName("quoteCount");

                    b.Property<bool>("Removed")
                        .HasColumnType("boolean")
                        .HasColumnName("removed");

                    b.Property<string>("Route")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("route");

                    b.Property<string>("StreetNumber")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("streetNumber");

                    b.Property<string>("TagInput")
                        .HasColumnType("text")
                        .HasColumnName("tagInput");

                    b.Property<List<string>>("Tags")
                        .HasColumnType("text[]")
                        .HasColumnName("tags");

                    b.Property<NpgsqlTsVector>("Ts")
                        .HasColumnType("tsvector")
                        .HasColumnName("ts");

                    b.Property<NpgsqlTsVector>("Ts2")
                        .HasColumnType("tsvector")
                        .HasColumnName("ts2");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("updatedAt");

                    b.Property<string>("Ward")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("ward");

                    b.Property<int?>("WardId")
                        .HasColumnType("integer")
                        .HasColumnName("wardId");

                    b.ToTable("trusted_places");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.UatPlaceMigrate", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("Address")
                        .HasColumnType("character varying")
                        .HasColumnName("address");

                    b.Property<string>("AddressOld")
                        .HasColumnType("character varying")
                        .HasColumnName("address_old");

                    b.Property<int?>("Districtid")
                        .HasColumnType("integer")
                        .HasColumnName("districtid");

                    b.Property<Point>("Location")
                        .HasColumnType("geometry(Point,4326)")
                        .HasColumnName("location");

                    b.Property<Point>("LocationOld")
                        .HasColumnType("geometry(Point,4326)")
                        .HasColumnName("location_old");

                    b.Property<string>("Name")
                        .HasColumnType("character varying")
                        .HasColumnName("name");

                    b.Property<string>("NameOld")
                        .HasColumnType("character varying")
                        .HasColumnName("name_old");

                    b.Property<string>("PlaceId")
                        .IsRequired()
                        .HasMaxLength(5000)
                        .HasColumnType("character varying(5000)")
                        .HasColumnName("placeId");

                    b.Property<int?>("Provinceid")
                        .HasColumnType("integer")
                        .HasColumnName("provinceid");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("status");

                    b.Property<string>("UpdateUser")
                        .HasColumnType("character varying")
                        .HasColumnName("update_user");

                    b.Property<int?>("Wardid")
                        .HasColumnType("integer")
                        .HasColumnName("wardid");

                    b.HasKey("Id")
                        .HasName("uat_place_migrate_pk");

                    b.HasIndex(new[] { "PlaceId" }, "idx_uat_place_migrate_placeid");

                    b.ToTable("uat_place_migrate");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.UatPlaceMigrate2", b =>
                {
                    b.Property<string>("Address")
                        .HasColumnType("text")
                        .HasColumnName("address");

                    b.Property<string>("AddressOld")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("address_old");

                    b.Property<int?>("DistrictId")
                        .HasColumnType("integer")
                        .HasColumnName("districtId");

                    b.Property<Point>("Location")
                        .HasColumnType("geometry(Point,4326)")
                        .HasColumnName("location");

                    b.Property<Point>("LocationOld")
                        .HasColumnType("geometry(Point,4326)")
                        .HasColumnName("location_old");

                    b.Property<string>("Name")
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("NameOld")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("name_old");

                    b.Property<string>("PlaceId")
                        .HasColumnType("text")
                        .HasColumnName("placeId");

                    b.Property<int?>("ProvinceId")
                        .HasColumnType("integer")
                        .HasColumnName("provinceId");

                    b.Property<LocalDateTime?>("UpdateAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("update_at");

                    b.Property<string>("UpdateUser")
                        .HasColumnType("text")
                        .HasColumnName("update_user");

                    b.Property<int?>("WardId")
                        .HasColumnType("integer")
                        .HasColumnName("wardId");

                    b.HasIndex(new[] { "PlaceId" }, "uat_place_migrate2_placeid_idx")
                        .IsUnique();

                    b.HasIndex(new[] { "PlaceId" }, "uat_place_migrate2_unique")
                        .IsUnique();

                    b.ToTable("uat_place_migrate2");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.UserBookingHistory", b =>
                {
                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("createdAt");

                    b.Property<string>("CustomerPhone")
                        .HasColumnType("character varying")
                        .HasColumnName("customerPhone");

                    b.Property<string>("DesAddress")
                        .HasColumnType("character varying")
                        .HasColumnName("desAddress");

                    b.Property<string>("DesFullAddress")
                        .HasColumnType("character varying")
                        .HasColumnName("desFullAddress");

                    b.Property<Point>("DesLocation")
                        .HasColumnType("geometry(Point,4326)")
                        .HasColumnName("desLocation");

                    b.Property<string>("DesName")
                        .IsRequired()
                        .HasColumnType("character varying")
                        .HasColumnName("desName");

                    b.Property<string>("DesPlaceId")
                        .HasColumnType("character varying")
                        .HasColumnName("desPlaceId");

                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("OriginAddress")
                        .HasColumnType("character varying")
                        .HasColumnName("originAddress");

                    b.Property<string>("OriginFullAddress")
                        .HasColumnType("character varying")
                        .HasColumnName("originFullAddress");

                    b.Property<Point>("OriginLocation")
                        .HasColumnType("geometry(Point,4326)")
                        .HasColumnName("originLocation");

                    b.Property<string>("OriginName")
                        .IsRequired()
                        .HasColumnType("character varying")
                        .HasColumnName("originName");

                    b.Property<string>("OriginPlaceId")
                        .HasColumnType("character varying")
                        .HasColumnName("originPlaceId");

                    b.ToTable("user_booking_histories");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.Ward", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Boundary")
                        .HasColumnType("text")
                        .HasColumnName("boundary");

                    b.Property<string>("BoundaryGeom")
                        .HasColumnType("text")
                        .HasColumnName("boundary_geom");

                    b.Property<string>("CenterLocation")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("center_location");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("code");

                    b.Property<string>("DistrictCode")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("districtCode");

                    b.Property<string>("DistrictCode1")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("district_code");

                    b.Property<int?>("DistrictId")
                        .HasColumnType("integer")
                        .HasColumnName("districtId");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("name");

                    b.Property<string>("NoSpaceName")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("no_space_name");

                    b.Property<bool?>("Processed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("processed");

                    b.Property<string>("ProvinceCode")
                        .HasMaxLength(8)
                        .HasColumnType("character varying(8)")
                        .HasColumnName("province_code");

                    b.Property<int?>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.HasKey("Id")
                        .HasName("wards_pkey");

                    b.HasIndex(new[] { "Code" }, "wards_code_unique")
                        .IsUnique();

                    b.ToTable("wards");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.WardBoundary", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("_id");

                    b.Property<string>("Class")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("_class");

                    b.Property<LocalDateTime?>("CreatedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("created_at");

                    b.Property<int?>("District")
                        .HasColumnType("integer")
                        .HasColumnName("district");

                    b.Property<string>("Location")
                        .HasMaxLength(10485760)
                        .HasColumnType("character varying(10485760)")
                        .HasColumnName("location");

                    b.Property<LocalDateTime?>("ModifiedAt")
                        .HasColumnType("timestamp without time zone")
                        .HasColumnName("modified_at");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("modified_by");

                    b.Property<string>("Name")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("name");

                    b.Property<string>("Postcode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("postcode");

                    b.Property<int?>("Province")
                        .HasColumnType("integer")
                        .HasColumnName("province");

                    b.Property<string>("Provinces")
                        .HasMaxLength(25)
                        .HasColumnType("character varying(25)")
                        .HasColumnName("provinces");

                    b.Property<int?>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("WardType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("ward_type");

                    b.HasKey("Id")
                        .HasName("ward_boundaries_pk");

                    b.ToTable("ward_boundaries");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.WardNew", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id")
                        .HasDefaultValueSql("nextval('ward__id_seq'::regclass)");

                    b.Property<string>("Boundary")
                        .HasColumnType("text")
                        .HasColumnName("boundary");

                    b.Property<Polygon>("BoundaryGeom")
                        .HasColumnType("geometry(Polygon,4326)")
                        .HasColumnName("boundary_geom");

                    b.Property<string>("CenterLocation")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("center_location");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<string>("Name")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("name");

                    b.Property<string>("NoSpaceName")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("no_space_name");

                    b.Property<string>("Oldcode")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("oldcode");

                    b.Property<bool?>("Processed")
                        .HasColumnType("boolean")
                        .HasColumnName("processed");

                    b.Property<string>("Provincecode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("provincecode");

                    b.Property<string>("Type")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("type");

                    b.HasKey("Id")
                        .HasName("ward__pkey");

                    b.HasIndex(new[] { "Code" }, "ward_new_code_idx");

                    b.HasIndex(new[] { "Name" }, "ward_new_name_idx");

                    b.HasIndex(new[] { "Code" }, "ward_new_unique")
                        .IsUnique();

                    b.ToTable("ward_new");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.WardsBridge", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("WardsNewCode")
                        .IsRequired()
                        .HasColumnType("character varying")
                        .HasColumnName("wards_new_code")
                        .HasComment("wards code mới từ ngày 1/7/2025 từ bảng ward_new");

                    b.Property<string>("WardsOldCode")
                        .IsRequired()
                        .HasColumnType("character varying")
                        .HasColumnName("wards_old_code")
                        .HasComment("Ward Code cũ trước 1/7/2025");

                    b.HasKey("Id")
                        .HasName("wards_bridge_pk");

                    b.HasIndex(new[] { "WardsNewCode" }, "wards_bridge_wards_new_code_idx");

                    b.HasIndex(new[] { "WardsOldCode" }, "wards_bridge_wards_old_code_idx");

                    b.ToTable("wards_bridge");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.ApiKeyPermission", b =>
                {
                    b.HasOne("NDS.ETL.Entities.PostgresPlace.ApiKey", "ApiKey")
                        .WithMany("ApiKeyPermissions")
                        .HasForeignKey("ApiKeyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApiKey");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.BuildingBoundaryPlace", b =>
                {
                    b.HasOne("NDS.ETL.Entities.PostgresPlace.BuildingBoundary", "BuildingBoundary")
                        .WithMany("BuildingBoundaryPlaces")
                        .HasForeignKey("BuildingBoundaryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NDS.ETL.Entities.PostgresPlace.Place", "PlaceFk")
                        .WithMany("BuildingBoundaryPlaces")
                        .HasForeignKey("PlaceFkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BuildingBoundary");

                    b.Navigation("PlaceFk");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.ProvincesBridge", b =>
                {
                    b.HasOne("NDS.ETL.Entities.PostgresPlace.ProvincesNew", "ProvincesNewCodeNavigation")
                        .WithMany("ProvincesBridges")
                        .HasForeignKey("ProvincesNewCode")
                        .HasPrincipalKey("Code")
                        .IsRequired()
                        .HasConstraintName("provinces_bridge_provinces_new_fk");

                    b.HasOne("NDS.ETL.Entities.PostgresPlace.Province", "ProvincesOldCodeNavigation")
                        .WithMany("ProvincesBridges")
                        .HasForeignKey("ProvincesOldCode")
                        .HasPrincipalKey("Code")
                        .IsRequired()
                        .HasConstraintName("provinces_bridge_provinces_fk");

                    b.Navigation("ProvincesNewCodeNavigation");

                    b.Navigation("ProvincesOldCodeNavigation");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.SubPlace", b =>
                {
                    b.HasOne("NDS.ETL.Entities.PostgresPlace.Place", "ParentPlace")
                        .WithMany("SubPlaces")
                        .HasForeignKey("ParentPlaceId")
                        .HasPrincipalKey("PlaceId")
                        .HasConstraintName("fk_sub_places_places_parentplaceid");

                    b.HasOne("NDS.ETL.Entities.PostgresPlace.Ward", "WardNavigation")
                        .WithMany("SubPlaces")
                        .HasForeignKey("WardId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("ParentPlace");

                    b.Navigation("WardNavigation");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.WardsBridge", b =>
                {
                    b.HasOne("NDS.ETL.Entities.PostgresPlace.WardNew", "WardsNewCodeNavigation")
                        .WithMany("WardsBridges")
                        .HasForeignKey("WardsNewCode")
                        .HasPrincipalKey("Code")
                        .IsRequired()
                        .HasConstraintName("wards_bridge_ward_new_fk");

                    b.HasOne("NDS.ETL.Entities.PostgresPlace.Ward", "WardsOldCodeNavigation")
                        .WithMany("WardsBridges")
                        .HasForeignKey("WardsOldCode")
                        .HasPrincipalKey("Code")
                        .IsRequired()
                        .HasConstraintName("wards_bridge_wards_fk");

                    b.Navigation("WardsNewCodeNavigation");

                    b.Navigation("WardsOldCodeNavigation");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.ApiKey", b =>
                {
                    b.Navigation("ApiKeyPermissions");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.BuildingBoundary", b =>
                {
                    b.Navigation("BuildingBoundaryPlaces");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.Place", b =>
                {
                    b.Navigation("BuildingBoundaryPlaces");

                    b.Navigation("SubPlaces");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.Province", b =>
                {
                    b.Navigation("ProvincesBridges");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.ProvincesNew", b =>
                {
                    b.Navigation("ProvincesBridges");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.Ward", b =>
                {
                    b.Navigation("SubPlaces");

                    b.Navigation("WardsBridges");
                });

            modelBuilder.Entity("NDS.ETL.Entities.PostgresPlace.WardNew", b =>
                {
                    b.Navigation("WardsBridges");
                });
#pragma warning restore 612, 618
        }
    }
}
