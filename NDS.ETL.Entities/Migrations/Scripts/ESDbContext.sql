CREATE EXTENSION IF NOT EXISTS postgis;
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory_ESDbContext" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory_ESDbContext" PRIMARY KEY ("MigrationId")
);

START TRANSACTION;

CREATE EXTENSION IF NOT EXISTS postgis;

CREATE TABLE "ApiKeys" (
    "Id" uuid NOT NULL,
    "Key" character varying(256) NOT NULL,
    "Description" character varying(512) NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "ExpirationDate" timestamp with time zone,
    "IsActive" boolean NOT NULL,
    CONSTRAINT "PK_ApiKeys" PRIMARY KEY ("Id")
);

CREATE TABLE "ApiKeyPermissions" (
    "Id" uuid NOT NULL,
    "PermissionName" integer NOT NULL,
    "ApiKeyId" uuid NOT NULL,
    CONSTRAINT "PK_ApiKeyPermissions" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_ApiKeyPermissions_ApiKeys_ApiKeyId" FOREIGN KEY ("ApiKeyId") REFERENCES "ApiKeys" ("Id") ON DELETE CASCADE
);

CREATE INDEX "IX_ApiKeyPermissions_ApiKeyId" ON "ApiKeyPermissions" ("ApiKeyId");

CREATE INDEX "IX_ApiKeyPermissions_PermissionName" ON "ApiKeyPermissions" ("PermissionName");

CREATE INDEX "IX_ApiKeys_IsActive" ON "ApiKeys" ("IsActive");

CREATE UNIQUE INDEX "IX_ApiKeys_Key" ON "ApiKeys" ("Key");

INSERT INTO "__EFMigrationsHistory_ESDbContext" ("MigrationId", "ProductVersion")
VALUES ('20250423111110_Add_ApiKeys', '8.0.10');

COMMIT;

