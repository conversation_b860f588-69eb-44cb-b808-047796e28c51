########################################
########### Defind Variables ###########
########################################


variables:
  DOTNET_NUGET_SIGNATURE_VERIFICATION : 'false'
  OBJECTS_DIRECTORY: 'obj'
  NUGET_PACKAGES_DIRECTORY: '.nuget'
  SOURCE_CODE_PATH: '*/*/'
  IMAGE_NAME: nds/vntaxi/nds.etl.backgroundjobs
  LOCAL_REGISTRY: registry.vnpay.vn
  REGISTRY_USERNAME: nds
  REGISTRY_PASSWORD: RegistryNDS@2023!@#
  IMAGE_TAG: $CI_COMMIT_SHORT_SHA
cache:
  # Per-stage and per-branch caching.
  key: "$CI_JOB_STAGE-$CI_COMMIT_REF_SLUG"
  paths:
    # Specify three paths that should be cached:
    #
    # 1) Main JSON file holding information about package dependency tree, packages versions,
    # frameworks etc. It also holds information where to the dependencies were restored.
    - '$SOURCE_CODE_PATH$OBJECTS_DIRECTORY/project.assets.json'
    # 2) Other NuGet and MSBuild related files. Also needed.
    - '$SOURCE_CODE_PATH$OBJECTS_DIRECTORY/*.csproj.nuget.*'
    # 3) Path to the directory where restored dependencies are kept.
    - '$NUGET_PACKAGES_DIRECTORY'
  # 'pull-push' policy is the default cache policy, you do not have to specify it explicitly.
  policy: pull-push


########################################
############# Defind Stage #############
########################################
stages:
  - build
  - dockerize
  - publish
  - deploy
# Learn more about GitLab cache: https://docs.gitlab.com/ee/ci/caching/index.html
before_script:
  - 'dotnet restore NDS.ETL.BackgroundJobs/NDS.ETL.BackgroundJobs.csproj --packages $NUGET_PACKAGES_DIRECTORY --source https://artifact.vnpay.vn/nexus/repository/nuget-group/index.json --source https://artifact.vnpay.vn/nexus/repository/nuget.org-proxy/index.json'

build:
  image: bitnami/dotnet-sdk:8
  variables:
    DOTNET_NUGET_SIGNATURE_VERIFICATION : 'false'
  stage: build
  script:
    - 'dotnet publish NDS.ETL.BackgroundJobs/NDS.ETL.BackgroundJobs.csproj --no-restore -c Release -f net8.0 -r linux-x64 --output ./publish/production --self-contained=false -p:PublishSingleFile=false'
    - ls -lah ./publish/production
  
  artifacts:
    expire_in: 8h
    paths:
      - 'publish/production/*'
  only:
    refs:
      - dev
      - /^releases/(\d+.\d+.\d+)(-.*)?/
      - master
  tags: [shared]
  when: always

# Docker template and scan image########
.dockerize_template: &dockerize
  stage: dockerize
  image: registry.vnpay.vn/tools/docker:latest #docker:19.03.12
  script:
    - ls ./publish/production
    - echo building docker image ${IMAGE_NAME}:${IMAGE_TAG}
    - docker build --build-arg IMAGE_TAG_ARG=${IMAGE_TAG} -t ${IMAGE_NAME}:${IMAGE_TAG} --file=./publish/production/${DOCKERFILE_NAME} ./publish/production
    - docker tag ${IMAGE_NAME}:${IMAGE_TAG} $LOCAL_REGISTRY/$DEPLOY_ZONE/${IMAGE_NAME}:${IMAGE_TAG}
  tags: [shared]
  allow_failure: false

# On the master branch
# Dockerize
dockerize:deploy_test:
  <<: *dockerize
  variables:
    DEPLOY_ZONE: vnpay
  only:
    refs:
      - dev
  before_script:
    - export IMAGE_TAG=t-${CI_COMMIT_SHORT_SHA}
    - export DOCKERFILE_NAME=Dockerfile
# On the Master branch
# Dockerize
#---Tạm thời chưa cấu hình trên nhánh master vì trên đó chứa code Oracle---
dockerize:main:
  <<: *dockerize
  variables:
    DEPLOY_ZONE: gds-ocp
  only:
    refs:
      - master
  before_script:
    - export IMAGE_TAG=m-${CI_COMMIT_SHORT_SHA}
    - export DOCKERFILE_NAME=Dockerfile

# Docker push publish images template ########
.publish-image: &publish
  stage: publish
  dependencies: [] #To disable artifact passing, define the job with empty dependencies
  extends: .dockerize_template
  script:
    - echo "$REGISTRY_PASSWORD" | docker login $LOCAL_REGISTRY --username $REGISTRY_USERNAME --password-stdin
    #    - docker login "${LOCAL_REGISTRY}" -u "${REGISTRY_USERNAME}" -p "${REGISTRY_PASSWORD}"
    - docker push $LOCAL_REGISTRY/$DEPLOY_ZONE/${IMAGE_NAME}:${IMAGE_TAG}
  tags: [shared]
  allow_failure: false

publish:test:
  <<: *publish
  variables:
    # Define Site Deploy Application
    # vnpay -> K8S VNPAY
    # gds   -> K8S GDS
    # cloud -> K8S Cloud VNPAY
    # vnpay-ocp -> Openshift Vnpay
    # gds-ocp   -> Openshift GDS
    DEPLOY_ZONE: vnpay
  only:
    refs:
      - dev
  before_script:
    - export IMAGE_TAG=t-${CI_COMMIT_SHORT_SHA}

publish:main:
  <<: *publish
  variables:
    # Define Site Deploy Application
    # vnpay -> K8S VNPAY
    # gds   -> K8S GDS
    # cloud -> K8S Cloud VNPAY
    # vnpay-ocp -> Openshift Vnpay
    # gds-ocp   -> Openshift GDS
    DEPLOY_ZONE: gds-ocp
  only:
    refs:
      - main
  before_script:
    - export IMAGE_TAG=m-${CI_COMMIT_SHORT_SHA}

deploy:
  variables:
    GIT_STRATEGY: none
  dependencies: [] #To disable artifact passing, define the job with empty dependencies
  before_script:
    - export IMAGE_TAG=t-${CI_COMMIT_SHORT_SHA}
  stage: deploy
  script:
    - for i in $(docker images --format '{{.Repository}}:{{.Tag}}' | grep 'vnpay/nds/vntaxi/nds.etl.backgroundjobs' | grep -v '$IMAGE_TAG' | grep -v 'latest'); do docker rmi $i || true; done
    - docker rm -f nds.etl.backgroundjobs || true && docker run --restart=always -d -p 6008:8080 -m 4096m -e "ASPNETCORE_URLS=http://+:8080" -e "ASPNETCORE_ENVIRONMENT=Staging" --name nds.etl.backgroundjobs registry.vnpay.vn/vnpay/nds/vntaxi/nds.etl.backgroundjobs:$IMAGE_TAG
  allow_failure: false
  only:
    refs:
      - dev
  tags: ["161"]