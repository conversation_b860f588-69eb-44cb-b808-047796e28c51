using System;

namespace NDS.ETL.Infrastructure.Utils
{
    /// <summary>
    /// Helper class để xử lý các thao tác liên quan đến DateTime
    /// </summary>
    public static class DateTimeHelper
    {
        /// <summary>
        /// Chuyển đổi Unix timestamp (milliseconds) sang DateTime local
        /// </summary>
        /// <param name="timestampMs">Unix timestamp tính bằng milliseconds</param>
        /// <returns>DateTime trong múi giờ local</returns>
        public static DateTime FromUnixTimeMillisecondsToLocal(long timestampMs)
        {
            return DateTimeOffset.FromUnixTimeMilliseconds(timestampMs).ToLocalTime().DateTime;
        }

        /// <summary>
        /// Chuyển đổi Unix timestamp (milliseconds) sang DateTime UTC
        /// </summary>
        /// <param name="timestampMs">Unix timestamp tính bằng milliseconds</param>
        /// <returns>DateTime trong múi giờ UTC</returns>
        public static DateTime FromUnixTimeMillisecondsToUtc(long timestampMs)
        {
            return DateTimeOffset.FromUnixTimeMilliseconds(timestampMs).UtcDateTime;
        }

        /// <summary>
        /// Chuyển đổi Unix timestamp nullable (milliseconds) sang DateTime local
        /// </summary>
        /// <param name="timestampMs">Unix timestamp tính bằng milliseconds (có thể null)</param>
        /// <param name="defaultValue">Giá trị mặc định nếu timestamp là null</param>
        /// <returns>DateTime trong múi giờ local hoặc giá trị mặc định</returns>
        public static DateTime FromUnixTimeMillisecondsToLocal(long? timestampMs, DateTime defaultValue = default)
        {
            return timestampMs.HasValue 
                ? FromUnixTimeMillisecondsToLocal(timestampMs.Value) 
                : defaultValue;
        }

        /// <summary>
        /// Chuyển đổi Unix timestamp nullable (milliseconds) sang DateTime UTC
        /// </summary>
        /// <param name="timestampMs">Unix timestamp tính bằng milliseconds (có thể null)</param>
        /// <param name="defaultValue">Giá trị mặc định nếu timestamp là null</param>
        /// <returns>DateTime trong múi giờ UTC hoặc giá trị mặc định</returns>
        public static DateTime FromUnixTimeMillisecondsToUtc(long? timestampMs, DateTime defaultValue = default)
        {
            return timestampMs.HasValue 
                ? FromUnixTimeMillisecondsToUtc(timestampMs.Value) 
                : defaultValue;
        }

        /// <summary>
        /// Chuyển đổi DateTime sang Unix timestamp (milliseconds)
        /// </summary>
        /// <param name="dateTime">DateTime cần chuyển đổi</param>
        /// <returns>Unix timestamp tính bằng milliseconds</returns>
        public static long ToUnixTimeMilliseconds(DateTime dateTime)
        {
            return new DateTimeOffset(dateTime).ToUnixTimeMilliseconds();
        }
    }
} 