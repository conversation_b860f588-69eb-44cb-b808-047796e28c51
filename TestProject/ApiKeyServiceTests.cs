using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using NDS.ETL.BackgroundJobs.Authentication;
using NDS.ETL.BackgroundJobs.Controllers;
using NDS.ETL.BackgroundJobs.Model;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Services;
using NDS.ETL.Entities.Context;
using NDS.ETL.Entities.ESEntities;
using System.Security.Cryptography;
using System.Text;
using NDS.ETL.BackgroundJobs.Services.ES;

namespace TestProject
{
    [TestFixture]
    public class ApiKeyServiceTests
    {
        // Services to test
        private ApiKeyService _apiKeyService;
        private ESController _esController;
        private ESDbContext _actualDbContext;

        // Dependencies
        private Mock<IDbContextFactory<ESDbContext>> _mockDbContextFactory;
        private Mock<ESDbContext> _mockDbContext;
        private Mock<IMemoryCache> _mockMemoryCache;
        private Mock<ICacheManager> _mockCacheManager;
        private Mock<IApiKeyValidationService> _mockApiKeyValidationService;
        private Mock<IESAutocompleteService> _mockAutocompleteService;
        private Mock<IRequestInfoLogger> _mockRequestInfoLogger;
        private Mock<IConfiguration> _mockConfig;
        private Mock<HttpContext> _mockHttpContext;
        private Mock<HttpRequest> _mockRequest;
        private IConfiguration _actualConfig;
        private DbContextOptions<ESDbContext> _dbContextOptions;
        private Mock<IESNearbyService> _mockNearByService;
        private Mock<IESRecommendationService> _mockRecommendationService;
        private Mock<IESPlaceDetailsService> _eSPlaceDetailsService;

        [OneTimeSetUp]
        public void OneTimeSetup()
        {
            // Load actual configuration from appsettings.json
            _actualConfig = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json")
                .Build();

            // Get real connection string
            var connectionString = _actualConfig.GetConnectionString("PgPlace");

            // Configure DbContext options with real connection string
            _dbContextOptions = new DbContextOptionsBuilder<ESDbContext>()
                .UseNpgsql(connectionString, opts => opts.UseNetTopologySuite())
                .EnableSensitiveDataLogging()
                .Options;

            // Output connection string to verify the test is using the correct connection
            TestContext.Out.WriteLine($"Using connection string: {connectionString}");
        }

        [SetUp]
        public void Setup()
        {
            // Setup configurations
            _mockConfig = new Mock<IConfiguration>();
            _mockConfig.Setup(c => c["ApiKeySettings:SecretKey"]).Returns("ThisIsATestSecretKeyForUnitTests12345");
            _mockConfig.Setup(c => c["ApiKeySettings:ExpirationDays"]).Returns("30");

            // Setup DbContext for database access
            _mockDbContext = new Mock<ESDbContext>();
            _mockDbContextFactory = new Mock<IDbContextFactory<ESDbContext>>();
            _mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(_mockDbContext.Object);

            // Setup cache for API key validation
            _mockMemoryCache = new Mock<IMemoryCache>();
            _mockCacheManager = new Mock<ICacheManager>();

            // Setup validation service
            _mockApiKeyValidationService = new Mock<IApiKeyValidationService>();

            // Setup autocomplete service
            _mockAutocompleteService = new Mock<IESAutocompleteService>();

            _mockNearByService = new Mock<IESNearbyService>();

            _mockRecommendationService = new Mock<IESRecommendationService>();
            _eSPlaceDetailsService = new Mock<IESPlaceDetailsService>();

            // Setup request info logger
            _mockRequestInfoLogger = new Mock<IRequestInfoLogger>();
            _mockRequestInfoLogger.Setup(l => l.ExtractRequestInfo(It.IsAny<HttpRequest>()))
                .Returns(new RequestInfo());

            // Setup HTTP context for testing controller
            _mockRequest = new Mock<HttpRequest>();
            _mockHttpContext = new Mock<HttpContext>();
            _mockHttpContext.SetupGet(x => x.Request).Returns(_mockRequest.Object);

            // Create the service for API key generation
            _apiKeyService = new ApiKeyService(_mockConfig.Object);

            // Create the controller to test
            _esController = new ESController(
                _mockCacheManager.Object,
                _mockApiKeyValidationService.Object,
                _mockRequestInfoLogger.Object,
                _mockAutocompleteService.Object,
                _mockNearByService.Object,
                _mockRecommendationService.Object,
                _eSPlaceDetailsService.Object,
                Mock.Of<ILogger<ESController>>());

            // Setup controller context
            var controllerContext = new ControllerContext
            {
                HttpContext = _mockHttpContext.Object
            };
            _esController.ControllerContext = controllerContext;

            // Create a real instance of ESDbContext for DB operations
            try
            {
                _actualDbContext = new ESDbContext(_dbContextOptions);
            }
            catch (Exception ex)
            {
                TestContext.Out.WriteLine($"Error creating DbContext: {ex.Message}");
                throw;
            }
        }

        [TearDown]
        public void Teardown()
        {
            _actualDbContext?.Dispose();
        }

        #region Existing Tests

        [Test]
        public void GenerateApiKey_WithValidPartner_ReturnsValidKey()
        {
            // Arrange
            string partnerName = "vnpaytaxi";
            PermissionName[] permissions = [PermissionName.Autocomplete, PermissionName.Geocoding];

            // Act
            var apiKey = _apiKeyService.GenerateApiKey(partnerName, permissions);

            // Assert
            Assert.Multiple(() =>
            {
                Assert.That(apiKey, Is.Not.Null);
                Assert.That(apiKey.Length, Is.GreaterThan(20), "API key should be long enough for security");
                Assert.That(apiKey, Does.StartWith(partnerName), "API key should start with partner name");
                Assert.That(apiKey, Does.Contain("."), "API key should contain separator");
            });
        }

        [Test]
        public void GenerateApiKey_WithAllPermission_ReturnsValidKey()
        {
            // Arrange
            string partnerName = "vnpaytaxi";
            PermissionName[] permissions = [PermissionName.All];

            // Act
            var apiKey = _apiKeyService.GenerateApiKey(partnerName, permissions);

            // Assert
            Assert.That(apiKey, Is.Not.Null);

            // Store generated key for later tests
            TestContext.Out.WriteLine($"Generated API key with All permission: {apiKey}");
        }

        [Test]
        public void GenerateApiKey_SamePartnerDifferentPermissions_ReturnsDifferentKeys()
        {
            // Arrange
            string partnerName = "vnpaytaxi";

            // Act
            var keyWithAutocomplete = _apiKeyService.GenerateApiKey(partnerName, [PermissionName.Autocomplete]);
            var keyWithGeocoding = _apiKeyService.GenerateApiKey(partnerName, [PermissionName.Geocoding]);

            // Assert
            Assert.That(keyWithAutocomplete, Is.Not.EqualTo(keyWithGeocoding), "Different permissions should generate different keys");
        }

        [Test]
        public async Task Autocomplete_WithValidApiKey_ReturnsOkResult()
        {
            // Arrange
            string apiKey = _apiKeyService.GenerateApiKey("vnpaytaxi", [PermissionName.All]);

            // Setup request headers
            var headers = new HeaderDictionary { { "X-API-Key", apiKey } };
            _mockRequest.Setup(r => r.Headers).Returns(headers);

            // Setup API key validation
            _mockApiKeyValidationService.Setup(s => s.ValidateApiKeyAsync(apiKey))
                .ReturnsAsync((true, new List<PermissionName> { PermissionName.Autocomplete }));

            // Setup autocomplete service to return results
            var expectedResults = new List<PlacesEs>
            {
                new PlacesEs { Name = "Test Place 1" },
                new PlacesEs { Name = "Test Place 2" }
            };
            _mockAutocompleteService.Setup(s => s.SearchAutocompleteAsync(
                    It.IsAny<string>(),It.IsAny<string>(), It.IsAny<string>(), It.IsAny<double>(), It.IsAny<double>(), It.IsAny<int>(), 10, false))
                .ReturnsAsync(expectedResults);

            // Setup filter context
            var apiKeyFilterAttribute = new ApiKeyAttribute(nameof(PermissionName.Autocomplete));
            var authFilterContext = new AuthorizationFilterContext(
                new ActionContext()
                {
                    HttpContext = _mockHttpContext.Object,
                    RouteData = new Microsoft.AspNetCore.Routing.RouteData(),
                    ActionDescriptor = new Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor()
                    {
                        EndpointMetadata = new[] { apiKeyFilterAttribute }
                    }
                },
                new[] { new Mock<IFilterMetadata>().Object });

            // Create filter with mock services
            var apiKeyFilter = new ApiKeyAuthenticationFilter(
                _mockApiKeyValidationService.Object,
                Mock.Of<ILogger<ApiKeyAuthenticationFilter>>());

            // Act
            await apiKeyFilter.OnAuthorizationAsync(authFilterContext);
            var result = await _esController.Autocomplete("coffee", 21.02, 105.8);

            // Assert
            Assert.That(authFilterContext.Result, Is.Null, "No authentication error result should be set");
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>(), "Should return OK result");

            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult, Is.Not.Null, "OK result should not be null");
            Assert.That(okResult.Value, Is.InstanceOf<List<PlacesEs>>(), "Result value should be a list of PlacesEs");
            Assert.That(((List<PlacesEs>)okResult.Value).Count, Is.EqualTo(2), "Should return 2 test places");
        }

        [Test]
        public async Task Autocomplete_WithInvalidApiKey_ReturnsUnauthorized()
        {
            // Arrange
            string invalidApiKey = "InvalidKey";

            // Setup request headers
            var headers = new HeaderDictionary { { "X-API-Key", invalidApiKey } };
            _mockRequest.Setup(r => r.Headers).Returns(headers);

            // Setup API key validation to fail
            _mockApiKeyValidationService.Setup(s => s.ValidateApiKeyAsync(invalidApiKey))
                .ReturnsAsync((false, Array.Empty<PermissionName>()));

            // Setup filter context
            var apiKeyFilterAttribute = new ApiKeyAttribute(nameof(PermissionName.Autocomplete));
            var authFilterContext = new AuthorizationFilterContext(
                new ActionContext()
                {
                    HttpContext = _mockHttpContext.Object,
                    RouteData = new Microsoft.AspNetCore.Routing.RouteData(),
                    ActionDescriptor = new Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor()
                    {
                        EndpointMetadata = new[] { apiKeyFilterAttribute }
                    }
                },
                new[] { new Mock<IFilterMetadata>().Object });

            // Create filter with mock services
            var apiKeyFilter = new ApiKeyAuthenticationFilter(
                _mockApiKeyValidationService.Object,
                Mock.Of<ILogger<ApiKeyAuthenticationFilter>>());

            // Act
            await apiKeyFilter.OnAuthorizationAsync(authFilterContext);

            // Assert
            Assert.That(authFilterContext.Result, Is.InstanceOf<UnauthorizedObjectResult>(),
                "Authorization filter should return Unauthorized for invalid API key");
        }

        [Test]
        public async Task NearBy_WithAllPermission_ReturnsOkResult()
        {
            // Arrange
            string apiKey = _apiKeyService.GenerateApiKey("vnpaytaxi", [PermissionName.All]);

            // Setup request headers
            var headers = new HeaderDictionary { { "X-API-Key", apiKey } };
            _mockRequest.Setup(r => r.Headers).Returns(headers);

            // Setup API key validation with All permission
            _mockApiKeyValidationService.Setup(s => s.ValidateApiKeyAsync(apiKey))
                .ReturnsAsync((true, new[] { PermissionName.All }));

            // Setup filter context
            var apiKeyFilterAttribute = new ApiKeyAttribute(nameof(PermissionName.NearBy));
            var authFilterContext = new AuthorizationFilterContext(
                new ActionContext()
                {
                    HttpContext = _mockHttpContext.Object,
                    RouteData = new Microsoft.AspNetCore.Routing.RouteData(),
                    ActionDescriptor = new Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor()
                    {
                        EndpointMetadata = new[] { apiKeyFilterAttribute }
                    }
                },
                new[] { new Mock<IFilterMetadata>().Object });

            // Create filter with mock services
            var apiKeyFilter = new ApiKeyAuthenticationFilter(
                _mockApiKeyValidationService.Object,
                Mock.Of<ILogger<ApiKeyAuthenticationFilter>>());

            // Act
            await apiKeyFilter.OnAuthorizationAsync(authFilterContext);
            var result = _esController.NearBy(21.02, 105.8, 1000);

            // Assert
            Assert.That(authFilterContext.Result, Is.Null, "No authentication error result should be set");
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>(), "Should return OK result");
        }

        [Test]
        public async Task Geocoding_WithWrongPermission_ReturnsForbidden()
        {
            // Arrange
            // Generate an API key with NearBy permission only (not Geocoding)
            string apiKey = _apiKeyService.GenerateApiKey("vnpaytaxi", [PermissionName.NearBy]);

            // Setup request headers
            var headers = new HeaderDictionary { { "X-API-Key", apiKey } };
            _mockRequest.Setup(r => r.Headers).Returns(headers);

            // Setup API key validation to return NearBy permission only
            _mockApiKeyValidationService.Setup(s => s.ValidateApiKeyAsync(apiKey))
                .ReturnsAsync((true, new[] { PermissionName.NearBy }));

            // Setup filter context for Geocoding (which requires Geocoding permission)
            var apiKeyFilterAttribute = new ApiKeyAttribute(nameof(PermissionName.Geocoding));
            var authFilterContext = new AuthorizationFilterContext(
                new ActionContext()
                {
                    HttpContext = _mockHttpContext.Object,
                    RouteData = new Microsoft.AspNetCore.Routing.RouteData(),
                    ActionDescriptor = new Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor()
                    {
                        EndpointMetadata = new[] { apiKeyFilterAttribute }
                    }
                },
                new[] { new Mock<IFilterMetadata>().Object });

            // Create filter with mock services
            var apiKeyFilter = new ApiKeyAuthenticationFilter(
                _mockApiKeyValidationService.Object,
                Mock.Of<ILogger<ApiKeyAuthenticationFilter>>());

            // Act
            await apiKeyFilter.OnAuthorizationAsync(authFilterContext);

            // Assert
            Assert.That(authFilterContext.Result, Is.InstanceOf<ForbidResult>(),
                "Authorization filter should return Forbidden when API key doesn't have the required permission");
        }

        [Test]
        public void ClearApiKeysCache_ReturnsOkResult()
        {
            // Act
            var result = _esController.ClearApiKeysCache();

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>(), "Should return OK result");
            _mockApiKeyValidationService.Verify(s => s.ClearApiKeysCache(), Times.Once,
                "Should call ClearApiKeysCache on the service");
        }

        #endregion

        #region New DB Tests

        [Test]
        [Category("DatabaseIntegration")]
        public async Task GenerateAndSaveApiKey_WithValidPartnerAndPermissions_InsertsToDatabase()
        {
            // Arrange
            string partnerName = $"vnpaytaxi_{Guid.NewGuid():N}"; // Ensure uniqueness
            var permissions = new[] { PermissionName.Autocomplete, PermissionName.Geocoding };
            string generatedKey = _apiKeyService.GenerateApiKey(partnerName, permissions);

            try
            {
                // Act
                var apiKey = new ApiKey
                {
                    Key = generatedKey,
                    Description = $"Test API key for {partnerName}",
                    CreatedAt = DateTime.UtcNow,
                    ExpirationDate = DateTime.UtcNow.AddDays(30),
                    IsActive = true,
                    Permissions = permissions.Select(p => new ApiKeyPermission
                    {
                        PermissionName = p
                    }).ToList()
                };

                // Save to database
                _actualDbContext.ApiKeys.Add(apiKey);
                await _actualDbContext.SaveChangesAsync();

                // Assert
                // Retrieve from database to confirm insertion
                var savedKey = await _actualDbContext.ApiKeys
                    .Include(k => k.Permissions)
                    .FirstOrDefaultAsync(k => k.Key == generatedKey);

                Assert.Multiple(() =>
                {
                    Assert.That(savedKey, Is.Not.Null, "API key should be saved to database");
                    Assert.That(savedKey.Key, Is.EqualTo(generatedKey), "Saved key should match generated key");
                    Assert.That(savedKey.Description, Does.Contain(partnerName), "Description should contain partner name");
                    Assert.That(savedKey.IsActive, Is.True, "Key should be active");
                    Assert.That(savedKey.Permissions, Has.Count.EqualTo(permissions.Length), "Should have correct number of permissions");
                    Assert.That(savedKey.Permissions.Select(p => p.PermissionName),
                        Is.EquivalentTo(permissions), "Saved permissions should match generated permissions");
                });

                // Log success
                TestContext.Out.WriteLine($"Successfully saved API key to database: {generatedKey}");
            }
            catch (Exception ex)
            {
                TestContext.Out.WriteLine($"Error during test: {ex.Message}");
                throw;
            }
            finally
            {
                // Clean up - remove the test key from database
                try
                {
                    var keyToRemove = await _actualDbContext.ApiKeys
                        .FirstOrDefaultAsync(k => k.Key == generatedKey);

                    if (keyToRemove != null)
                    {
                        _actualDbContext.ApiKeys.Remove(keyToRemove);
                        await _actualDbContext.SaveChangesAsync();
                        TestContext.Out.WriteLine("Test cleanup completed successfully");
                    }
                }
                catch (Exception ex)
                {
                    TestContext.Out.WriteLine($"Error during cleanup: {ex.Message}");
                }
            }
        }

        [Test]
        [Category("DatabaseIntegration")]
        public async Task GenerateApiKey_WithAllPermission_SavesAndValidatesInDatabase()
        {
            // Arrange
            string partnerName = $"AllAccess_{Guid.NewGuid():N}"; // Ensure uniqueness
            var permissions = new[] { PermissionName.All };
            string generatedKey = _apiKeyService.GenerateApiKey(partnerName, permissions);

            try
            {
                // Act
                var apiKey = new ApiKey
                {
                    Key = generatedKey,
                    Description = $"All access API key for {partnerName}",
                    CreatedAt = DateTime.UtcNow,
                    ExpirationDate = DateTime.UtcNow.AddDays(90), // 90 days expiration for All access
                    IsActive = true,
                    Permissions = permissions.Select(p => new ApiKeyPermission
                    {
                        PermissionName = p
                    }).ToList()
                };

                // Save to database
                _actualDbContext.ApiKeys.Add(apiKey);
                await _actualDbContext.SaveChangesAsync();

                // Assert - Check if saved correctly
                var savedKey = await _actualDbContext.ApiKeys
                    .Include(k => k.Permissions)
                    .FirstOrDefaultAsync(k => k.Key == generatedKey);

                Assert.Multiple(() =>
                {
                    Assert.That(savedKey, Is.Not.Null, "API key should be saved to database");
                    Assert.That(savedKey.Key, Is.EqualTo(generatedKey), "Saved key should match generated key");
                    Assert.That(savedKey.Permissions.First().PermissionName, Is.EqualTo(PermissionName.All),
                        "Should have All permission");
                });

                // Create a real validation service to test the key
                var realCacheManager = new Mock<ICacheManager>();
                var apiKeyValidationService = new ApiKeyValidationService(
                    new DbContextFactory(_dbContextOptions),
                    _mockMemoryCache.Object,
                    _actualConfig,
                    Mock.Of<ILogger<ApiKeyValidationService>>(),
                    realCacheManager.Object);

                // Validate the API key
                var (isValid, validationPermissions) = await apiKeyValidationService.ValidateApiKeyAsync(generatedKey);

                // Assert validation results
                Assert.Multiple(() =>
                {
                    Assert.That(isValid, Is.True, "Generated API key should be valid");
                    Assert.That(validationPermissions, Contains.Item(PermissionName.All), "Validation should return All permission");
                });

                TestContext.Out.WriteLine($"Successfully validated all-access API key: {generatedKey}");
            }
            catch (Exception ex)
            {
                TestContext.Out.WriteLine($"Error during test: {ex.Message}");
                throw;
            }
            finally
            {
                // Clean up - remove the test key
                try
                {
                    var keyToRemove = await _actualDbContext.ApiKeys
                        .FirstOrDefaultAsync(k => k.Key == generatedKey);

                    if (keyToRemove != null)
                    {
                        _actualDbContext.ApiKeys.Remove(keyToRemove);
                        await _actualDbContext.SaveChangesAsync();
                    }
                }
                catch (Exception ex)
                {
                    TestContext.Out.WriteLine($"Error during cleanup: {ex.Message}");
                }
            }
        }

        [Test]
        [Category("DatabaseIntegration")]
        public async Task BulkGenerateApiKeys_ForDifferentPartners_SucceedsAndValidates()
        {
            // Arrange - create multiple partners with different permission sets
            var partners = new[]
            {
                ("MobileApp", new[] { PermissionName.Autocomplete, PermissionName.NearBy }),
                ("AdminPanel", new[] { PermissionName.All }),
                ("ThirdPartyX", new[] { PermissionName.Geocoding }),
                ("Monitoring", new[] { PermissionName.Geocoding, PermissionName.NearBy })
            };

            var generatedKeys = new Dictionary<string, string>();
            var createdApiKeys = new List<ApiKey>();

            try
            {
                // Act - Generate and save all keys
                foreach (var (partnerName, permissions) in partners)
                {
                    var uniquePartnerName = $"{partnerName}_{Guid.NewGuid():N}";
                    var key = _apiKeyService.GenerateApiKey(uniquePartnerName, permissions);
                    generatedKeys[uniquePartnerName] = key;

                    var apiKey = new ApiKey
                    {
                        Key = key,
                        Description = $"API key for {uniquePartnerName}",
                        CreatedAt = DateTime.UtcNow,
                        ExpirationDate = DateTime.UtcNow.AddDays(30),
                        IsActive = true,
                        Permissions = permissions.Select(p => new ApiKeyPermission
                        {
                            PermissionName = p
                        }).ToList()
                    };

                    _actualDbContext.ApiKeys.Add(apiKey);
                    createdApiKeys.Add(apiKey);
                }

                await _actualDbContext.SaveChangesAsync();

                // Assert - Verify all keys were saved correctly
                foreach (var (partnerName, key) in generatedKeys)
                {
                    var savedKey = await _actualDbContext.ApiKeys
                        .Include(k => k.Permissions)
                        .FirstOrDefaultAsync(k => k.Key == key);

                    Assert.That(savedKey, Is.Not.Null, $"API key for {partnerName} should be saved");
                    TestContext.Out.WriteLine($"Successfully saved and verified API key for {partnerName}: {key}");
                }

                // Verify all keys can be found in a single query
                var allKeys = await _actualDbContext.ApiKeys
                    .Where(k => generatedKeys.Values.Contains(k.Key))
                    .ToListAsync();

                Assert.That(allKeys.Count, Is.EqualTo(partners.Length), "All API keys should be retrievable at once");
            }
            catch (Exception ex)
            {
                TestContext.Out.WriteLine($"Error during bulk key generation: {ex.Message}");
                throw;
            }
            finally
            {
                // Clean up all created keys
                try
                {
                    foreach (var key in createdApiKeys)
                    {
                        _actualDbContext.ApiKeys.Remove(key);
                    }

                    await _actualDbContext.SaveChangesAsync();
                    TestContext.Out.WriteLine($"Successfully cleaned up {createdApiKeys.Count} test API keys");
                }
                catch (Exception ex)
                {
                    TestContext.Out.WriteLine($"Error during cleanup: {ex.Message}");
                }
            }
        }

        #endregion

        /// <summary>
        /// Service for generating API keys
        /// </summary>
        public class ApiKeyService
        {
            private readonly IConfiguration _configuration;

            public ApiKeyService(IConfiguration configuration)
            {
                _configuration = configuration;
            }

            /// <summary>
            /// Generate a new API key for a third-party partner
            /// </summary>
            /// <param name="partnerName">Name of the partner</param>
            /// <param name="permissions">Permissions to grant to this key</param>
            /// <returns>A secure API key in the format partnerName.hash</returns>
            public string GenerateApiKey(string partnerName, IEnumerable<PermissionName> permissions)
            {
                if (string.IsNullOrEmpty(partnerName))
                    throw new ArgumentException("Partner name cannot be empty", nameof(partnerName));

                if (permissions == null || !permissions.Any())
                    throw new ArgumentException("At least one permission must be specified", nameof(permissions));

                // Get the secret key from configuration
                string secretKey = _configuration["ApiKeySettings:SecretKey"] ??
                                   throw new InvalidOperationException("ApiKeySettings:SecretKey is not configured");

                // Convert permissions to string for hashing
                string permissionsString = string.Join(",", permissions.Select(p => p.ToString()));

                // Create unique value to hash: partnerName + permissions + timestamp
                string valueToHash = $"{partnerName}:{permissionsString}:{DateTime.UtcNow.Ticks}";

                // Generate hash using HMACSHA256
                using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secretKey));
                byte[] hashBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(valueToHash));

                // Convert to Base64 string and remove characters that might cause issues in URLs
                string hashString = Convert.ToBase64String(hashBytes)
                    .Replace("/", "_")
                    .Replace("+", "-")
                    .Replace("=", "");

                // Format: partnerName.hash
                return $"{partnerName}.{hashString}";
            }
        }

        /// <summary>
        /// Simple factory implementation for creating ESDbContext instances
        /// </summary>
        public class DbContextFactory : IDbContextFactory<ESDbContext>
        {
            private readonly DbContextOptions<ESDbContext> _options;

            public DbContextFactory(DbContextOptions<ESDbContext> options)
            {
                _options = options;
            }

            public ESDbContext CreateDbContext()
            {
                return new ESDbContext(_options);
            }

            public Task<ESDbContext> CreateDbContextAsync(CancellationToken cancellationToken = default)
            {
                return Task.FromResult(CreateDbContext());
            }
        }
    }
}