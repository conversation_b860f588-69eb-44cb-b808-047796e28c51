namespace NDS.ETL.BackgroundJobs.Model.KafkaSyncModel;

public class KafkaPlaceModel
{
    public long Id { get; set; }
    public string? PlaceId { get; set; }
    
    public string? PlaceDetailId { get; set; }
    public List<double>? Location { get; set; }
    public string? Name { get; set; }
    public string? PhoneNumber { get; set; }
    public string? Address { get; set; }
    public string? FullAddress { get; set; }
    public string? StreetNumber { get; set; }
    public string? Route { get; set; }
    public string? Ward { get; set; }
    public string? District { get; set; }
    public string? City { get; set; }
    public int Type { get; set; }
    public List<string>? Tags { get; set; }
    public string? TagInput { get; set; }
    public string? CreatorId { get; set; }
    public long? UpdatedAt { get; set; }
    public long? CreatedAt { get; set; }
    public double Score { get; set; }
    public int BookingCount { get; set; }
    public int QuoteCount { get; set; }
    public bool Pending { get; set; }
    public bool Active { get; set; }
    public bool Popular { get; set; }
    public bool Processed { get; set; }
    public bool Removed { get; set; }
    public bool Hidden { get; set; }
    public bool InVnLocation { get; set; }
    public List<object>? SubPlaces { get; set; }
}