using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Table("customers")]
[Index("PhoneNumber", Name = "phoneNumber", IsUnique = true)]
[Index("UserId", Name = "userId", IsUnique = true)]
public partial class Customer
{
    [Key]
    [Column("userId")]
    [StringLength(255)]
    public string UserId { get; set; } = null!;

    [Column("phoneNumber")]
    [StringLength(255)]
    public string? PhoneNumber { get; set; }
}
