variables:
  DOTNET_NUGET_SIGNATURE_VERIFICATION: 'false'
  OBJECTS_DIRECTORY: obj
  NUGET_PACKAGES_DIRECTORY: .nuget
  SOURCE_CODE_PATH: '*/*/'
  IMAGE_NAME: nds/vntaxi/es-backend
  LOCAL_REGISTRY: registry.vnpay.vn
  PROJECT_NAME: vntaxi
  DOCKERFILE_NAME: NDS.ETL.BackgroundJobs/Dockerfile
  TAG_RUNNER: NDS
cache:
  key: $CI_JOB_STAGE-$CI_COMMIT_REF_SLUG
  paths:
  - $SOURCE_CODE_PATH$OBJECTS_DIRECTORY/project.assets.json
  - $SOURCE_CODE_PATH$OBJECTS_DIRECTORY/*.csproj.nuget.*
  - $NUGET_PACKAGES_DIRECTORY
  policy: pull-push
include:
- project: qtud/ci-template
  ref: master
  file: template-ci-vnpay.yaml
  path: null
stages:
- scan
- build
- dockerize
- publish
- deploy
build:
  image: bitnami/dotnet-sdk:8
  variables:
    DOTNET_NUGET_SIGNATURE_VERIFICATION: 'false'
  stage: build
  script:
  - dotnet restore NDS.ETL.BackgroundJobs/NDS.ETL.BackgroundJobs.csproj --packages
    $NUGET_PACKAGES_DIRECTORY --source https://artifact.vnpay.vn/nexus/repository/nuget-group/index.json
    --source https://artifact.vnpay.vn/nexus/repository/nuget.org-proxy/index.json
  - dotnet publish NDS.ETL.BackgroundJobs/NDS.ETL.BackgroundJobs.csproj --no-restore
    -c Release -f net8.0 -r linux-x64 --output ./publish/production --self-contained=false
    -p:PublishSingleFile=false
  - ls -lah ./publish/production
  artifacts:
    expire_in: 8h
    paths:
    - publish/production/*
  only:
    refs:
    - dev
    - /^releases/(\d+.\d+.\d+)(-.*)?/
    - master
  tags:
  - shared
  when: always
deploy:
  variables:
    GIT_STRATEGY: none
  dependencies: []
  before_script:
  - export IMAGE_TAG=t-${CI_COMMIT_SHORT_SHA}
  stage: deploy
  script:
  - for i in $(docker images --format '{{.Repository}}:{{.Tag}}' | grep '$PROJECT_NAME/$IMAGE_NAME'
    | grep -v '$IMAGE_TAG' | grep -v 'latest'); do docker rmi $i || true; done
  - docker rm -f es-backend || true && docker run --restart=always -d -p 6011:8080
    -m 4g -e "ASPNETCORE_URLS=http://+:8080" -e "ASPNETCORE_ENVIRONMENT=Staging" --name
    es-backend $LOCAL_REGISTRY/$PROJECT_NAME/$IMAGE_NAME:$IMAGE_TAG
  allow_failure: false
  only:
    refs:
    - dev
  tags:
  - '161'
  when: always
