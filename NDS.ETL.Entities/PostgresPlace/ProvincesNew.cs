using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("provinces_new")]
[Index("Code", Name = "provinces_new_code_unique", IsUnique = true)]
public partial class ProvincesNew
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("code")]
    [StringLength(50)]
    public string Code { get; set; } = null!;

    [Column("name")]
    [StringLength(1000)]
    public string? Name { get; set; }

    [Column("type")]
    [StringLength(50)]
    public string? Type { get; set; }

    [Column("oldcode")]
    [StringLength(1000)]
    public string? Oldcode { get; set; }

    [Column("boundary")]
    public string? Boundary { get; set; }

    [Column("center_location")]
    [StringLength(255)]
    public string? CenterLocation { get; set; }

    [Column("processed")]
    public bool? Processed { get; set; }

    [Column("boundary_geom")]
    public string? BoundaryGeom { get; set; }

    [InverseProperty("ProvincesNewCodeNavigation")]
    public virtual ICollection<ProvincesBridge> ProvincesBridges { get; set; } = new List<ProvincesBridge>();
}
