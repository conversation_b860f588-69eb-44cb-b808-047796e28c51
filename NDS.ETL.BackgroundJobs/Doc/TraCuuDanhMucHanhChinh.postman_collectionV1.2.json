{"info": {"_postman_id": "48490b4a-cb31-47e7-b1c7-37dc7d24ebef", "name": "TrCuuDanhMucHanhChinh", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "30525556"}, "item": [{"name": "Province - Get List", "request": {"method": "GET", "header": [{"key": "accept", "value": "text/plain"}, {"key": "X-Api-Key", "value": "{{api-key}}"}], "url": {"raw": "{{uri}}/api/admin/provinces", "host": ["{{uri}}"], "path": ["api", "admin", "provinces"]}}, "response": []}, {"name": "Province - Get Province By Code", "request": {"method": "GET", "header": [{"key": "accept", "value": "text/plain"}, {"key": "X-Api-Key", "value": "{{api-key}}"}], "url": {"raw": "{{uri}}/api/admin/provinces/code/01", "host": ["{{uri}}"], "path": ["api", "admin", "provinces", "code", "01"]}}, "response": []}, {"name": "Province - Search by Name", "request": {"method": "POST", "header": [{"key": "accept", "value": "text/plain"}, {"key": "X-Api-Key", "value": "{{api-key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"<PERSON><PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{uri}}/api/admin/provinces/search/name", "host": ["{{uri}}"], "path": ["api", "admin", "provinces", "search", "name"]}, "description": "Generated from cURL: curl -X 'POST' \\\r\n  'https://localhost:7205/api/admin/provinces/search/name' \\\r\n  -H 'accept: text/plain' \\\r\n  -H 'X-Api-Key: vnpaytaxi_ffbe7d151ce849b3b576b71437a65c33.a9j__W-TZIuMR9oIQIE37oHPFPUIXxirLBDeccXFf6M' \\\r\n  -H 'Content-Type: application/json' \\\r\n  -d '{\r\n  \"name\": \"Hà Nội\"\r\n}'"}, "response": []}, {"name": "Ward - Get List", "request": {"method": "GET", "header": [{"key": "accept", "value": "text/plain"}, {"key": "X-Api-Key", "value": "{{api-key}}"}], "url": {"raw": "{{uri}}/api/admin/wards?pageNumber=1&pageSize=10", "host": ["{{uri}}"], "path": ["api", "admin", "wards"], "query": [{"key": "pageNumber", "value": "1"}, {"key": "pageSize", "value": "10"}]}}, "response": []}, {"name": "Ward - Get by Code", "request": {"method": "GET", "header": [{"key": "accept", "value": "text/plain"}, {"key": "X-Api-Key", "value": "{{api-key}}"}], "url": {"raw": "{{uri}}/api/admin/wards/code/31093", "host": ["{{uri}}"], "path": ["api", "admin", "wards", "code", "31093"]}}, "response": []}, {"name": "Ward - Search by Name", "request": {"method": "POST", "header": [{"key": "accept", "value": "text/plain"}, {"key": "X-Api-Key", "value": "{{api-key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"<PERSON><PERSON>\",\r\n  \"pageNumber\": 0,\r\n  \"pageSize\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{uri}}/api/admin/wards/search/name", "host": ["{{uri}}"], "path": ["api", "admin", "wards", "search", "name"]}, "description": "Generated from cURL: curl -X 'POST' \\\r\n  'https://localhost:7205/api/admin/wards/search/name' \\\r\n  -H 'accept: text/plain' \\\r\n  -H 'X-Api-Key: vnpaytaxi_ffbe7d151ce849b3b576b71437a65c33.a9j__W-TZIuMR9oIQIE37oHPFPUIXxirLBDeccXFf6M' \\\r\n  -H 'Content-Type: application/json' \\\r\n  -d '{\r\n  \"name\": \"Trung Chính\",\r\n  \"pageNumber\": 0,\r\n  \"pageSize\": 1\r\n}'"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "uri", "value": "https://mbcms.vnpaytest.vn/backgroundjob", "type": "string"}, {"key": "api-key", "value": "vnpaytaxi_ffbe7d151ce849b3b576b71437a65c33.a9j__W-TZIuMR9oIQIE37oHPFPUIXxirLBDeccXFf6M", "type": "string"}]}