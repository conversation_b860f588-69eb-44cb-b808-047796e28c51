using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Table("places_scan_history")]
[Index("Placeid", Name = "idx_places_scan_history_placeid")]
[Index("HangfireJobId", Name = "idx_places_scan_history_source_hangfire_job_id")]
[Index("SourcePlaceid", Name = "idx_places_scan_history_source_placeid")]
public partial class PlacesScanHistory
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("placeid")]
    [StringLength(500)]
    public string? Placeid { get; set; }

    [Column("fulladdress")]
    [StringLength(10000)]
    public string? Fulladdress { get; set; }

    [Column("type")]
    public int? Type { get; set; }

    [Column("compare_score")]
    public decimal? CompareScore { get; set; }

    [Column("hangfire_job_id", TypeName = "character varying")]
    public string? HangfireJobId { get; set; }

    [Column("createat", TypeName = "timestamp without time zone")]
    public DateTime Createat { get; set; }

    [Column("updateat", TypeName = "timestamp without time zone")]
    public DateTime? Updateat { get; set; }

    [Column("source_placeid")]
    [StringLength(500)]
    public string? SourcePlaceid { get; set; }

    [Column("source_fulladdress")]
    [StringLength(10000)]
    public string? SourceFulladdress { get; set; }

    [Column("districtid")]
    public int Districtid { get; set; }

    [Column("provinceid")]
    public int Provinceid { get; set; }

    [Column("wardid")]
    public int Wardid { get; set; }
}
