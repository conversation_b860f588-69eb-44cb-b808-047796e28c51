using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using NDS.ETL.Entities.ESEntities;
using System.Reflection;

namespace NDS.ETL.BackgroundJobs.Authentication
{
    /// <summary>
    /// Attribute to require API key authentication for endpoints
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
    public class ApiKeyAttribute : Attribute, IAsyncActionFilter
    {
        private readonly string _permission;

        /// <summary>
        /// Gets the permission required for this endpoint
        /// </summary>
        public string Permission => _permission;

        /// <summary>
        /// Constructor with optional permission parameter
        /// </summary>
        /// <param name="permission">The specific permission required, or null if any valid API key is sufficient</param>
        public ApiKeyAttribute(string? permission = null)
        {
            _permission = permission ?? string.Empty;
            
            // Validate that the permission is a valid enum value if provided
            if (!string.IsNullOrEmpty(_permission) && 
                !Enum.TryParse<PermissionName>(_permission, out _))
            {
                throw new ArgumentException($"Invalid permission name: {_permission}. " +
                                           $"Must be one of: {string.Join(", ", Enum.GetNames(typeof(PermissionName)))}");
            }
        }

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            // The actual validation logic is in the ApiKeyAuthenticationFilter
            // This attribute just marks the endpoints that require API key authentication
            
            // Note: Permission checking happens in ApiKeyAuthenticationFilter.cs which:
            // 1. Validates the API key from the header
            // 2. Retrieves the permissions associated with the key
            // 3. Checks if the key has the specific permission required (_permission)
            // 4. Handles the All permission special case
            
            await next();
        }
    }
}
