using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Keyless]
[Table("user_booking_histories")]
public partial class UserBookingHistory
{
    [Column("id")]
    public long Id { get; set; }

    [Column("customerPhone", TypeName = "character varying")]
    public string? CustomerPhone { get; set; }

    [Column("originPlaceId", TypeName = "character varying")]
    public string? OriginPlaceId { get; set; }

    [Column("originName", TypeName = "character varying")]
    public string OriginName { get; set; } = null!;

    [Column("originLocation", TypeName = "geometry(Point,4326)")]
    public Point? OriginLocation { get; set; }

    [Column("originFullAddress", TypeName = "character varying")]
    public string? OriginFullAddress { get; set; }

    [Column("originAddress", TypeName = "character varying")]
    public string? OriginAddress { get; set; }

    [Column("desPlaceId", TypeName = "character varying")]
    public string? DesPlaceId { get; set; }

    [Column("desName", TypeName = "character varying")]
    public string DesName { get; set; } = null!;

    [Column("desLocation", TypeName = "geometry(Point,4326)")]
    public Point? DesLocation { get; set; }

    [Column("desFullAddress", TypeName = "character varying")]
    public string? DesFullAddress { get; set; }

    [Column("desAddress", TypeName = "character varying")]
    public string? DesAddress { get; set; }

    [Column("createdAt", TypeName = "timestamp without time zone")]
    public DateTime? CreatedAt { get; set; }
}
