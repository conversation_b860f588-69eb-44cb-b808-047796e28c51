using Elastic.Clients.Elasticsearch.Mapping;
using KnowledgeBase.Core.Extensions;
using NDS.ETL.Entities.PostgresPlace;
using NDS.ETL.Infrastructure.Extensions;
using System.Linq.Expressions;

namespace NDS.ETL.BackgroundJobs.Model.ElasticSearch;

public class PlacesEs : IElasticSearchModel
{
    public PlacesEs()
    {
    }

    public PlacesEs(SubPlacesEs item)
    {
        // Gán các thuộc tính từ SubPlacesEs sang PlacesEs
        Id = item.Id;
        PlaceDetailId = item.PlaceDetailId;
        Location = item.Location;
        PhoneNumber = item.PhoneNumber;
        Name = item.Name;
        PlaceId = item.PlaceId;
        Address = item.Address;
        FullAddress = item.FullAddress;
        StreetNumber = item.StreetNumber;
        Active = item.Active;
        Tags = item.Tags;
        TagInput = item.TagInput;
        Keywords = item.Keywords;
        Route = item.Route;
        Ward = item.Ward;
        WardId = item.WardId;
        District = item.District;
        DistrictId = item.DistrictId;
        City = item.City;
        Type = item.Type;
        DataFrom = item.DataFrom;
        CreatedAt = item.CreatedAt;
        UpdatedAt = item.UpdatedAt;
        LastUpdateUser = item.LastUpdateUser;
        Pending = item.Pending;
        ProvinceId = item.ProvinceId;
        QuoteCount = item.QuoteCount;
        BookingCount = item.BookingCount;
        Popular = item.Popular;
        Processed = item.Processed;
        DetermineAddressLevel = AddressProcessor.DetermineAddressLevel(item.FullAddress);
        SubPlaces = new List<SubPlacesEs>(); // Khởi tạo danh sách rỗng
    }

    public PlacesEs(PlacePairEs item)
    {
        // Gán các thuộc tính từ PlacePairEs sang PlacesEs
        PlaceId = item.PlaceId;
        Location = item.Location;
        FullAddress = item.FullAddress;
        BookingCount = item.BookingCount;
        Type = item.Type;
        Keywords = item.Keywords;

        // Thiết lập các thuộc tính mặc định cho các trường bắt buộc
        Active = true;
        Processed = true;

        // Thiết lập thời gian tạo và cập nhật
        CreatedAt = item.CreatedAt;
        UpdatedAt = item.UpdateAt;
    }

    public static Expression<Func<Place, PlacesEs>> Selector => t => new PlacesEs
    {
        Id = t.Id,
        PlaceDetailId = t.PlaceDetailId,
        Location = new Location
        {
            Lat = t.Location.Y,
            Lon = t.Location.X
        },
        PhoneNumber = t.PhoneNumber,
        Name = t.Name,
        PlaceId = t.PlaceId,
        Address = t.Address, // Không cần format lại Address nữa 
        FullAddress = t.FullAddress,
        StreetNumber = t.StreetNumber,
        Active = t.Active,
        Tags = t.Tags,
        TagInput = t.TagInput,
        Keywords = t.Keywords,
        Route = t.Route,
        Ward = t.Ward,
        WardId = t.WardId,
        District = t.District,
        DistrictId = t.DistrictId,
        City = t.City,
        Type = t.Type,
        DataFrom = t.DataFrom,
        CreatedAt = t.CreatedAt,
        UpdatedAt = t.UpdatedAt,
        LastUpdateUser = t.LastUpdateUser,
        HasChild = t.HasChild,
        Pending = t.Pending,
        ProvinceId = t.ProvinceId,
        QuoteCount = t.QuoteCount,
        BookingCount = t.BookingCount,
        Popular = t.Popular,
        Processed = t.Processed,
        DetermineAddressLevel = AddressProcessor.DetermineAddressLevel(t.FullAddress),
        SubPlaces = t.SubPlaces.AsQueryable().Select(SubPlacesEs.Selector).ToList()
    };

    public void ProcessKeywords()
    {
        string? keywords = Keywords;
        string fullAddress = FullAddress;
        if (string.IsNullOrEmpty(keywords)) return;

        var keywordList = keywords.Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(k => k.Trim())
            .Where(k => !string.IsNullOrEmpty(k))
            .ToList();

        // Loại bỏ các từ khóa trùng lặp không dấu
        var uniqueKeywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
        var result = new List<string>();

        foreach (var keyword in keywordList)
        {
            var unsignedKeyword = keyword.ToUnsign().ToLower();
            if (!uniqueKeywords.Contains(unsignedKeyword) &&
                !string.IsNullOrEmpty(fullAddress) &&
                !fullAddress.ToUnsign().ToLower().Contains(unsignedKeyword))
            {
                uniqueKeywords.Add(unsignedKeyword);
                result.Add(keyword);
            }
        }

        Keywords = result.Any() ? string.Join(",", result) : null;
    }

    public void CalculateMasterAddressAndPermuted()
    {
        MasterAddress = FullAddress;
        if (!string.IsNullOrWhiteSpace(Name) && !MasterAddress.ToUnsign().Contains(Name.ToUnsign()))
        {
            MasterAddress = Name + ", " + MasterAddress;
        }

        AddressPermuted = AddressPermutator.GenerateOptimizedPermutations(MasterAddress, 15);

        var insertItems = VietnameseAddressPermutator.GenerateOptimizedPermutations(MasterAddress, 3);
        AddressPermuted.InsertRange(0, insertItems);

        if (SubPlaces?.Any() == true)
        {
            var subPlaceStr = string.Join(",", SubPlaces.Select(x => x.Name));
            AddressPermuted.AddRange(VietnameseAddressPermutator.GenerateOptimizedPermutations(subPlaceStr, 3));
            AddressPermuted.AddRange(AddressPermutator.GenerateOptimizedPermutations(subPlaceStr, 10));
        }
    }

    public string MasterAddress { get; set; }

    public List<string> AddressPermuted { get; set; }

    public long Id { get; set; }
    public string? PlaceDetailId { get; set; }
    public Location Location { get; set; }
    public string? PhoneNumber { get; set; }
    public string PlaceId { get; set; } = null!;
    public string? Name { get; set; }
    public string FullAddress { get; set; } = null!;
    public string? Address { get; set; }
    public string? StreetNumber { get; set; }
    public string? Route { get; set; }
    public string? Ward { get; set; }
    public string? District { get; set; }
    public string? City { get; set; }
    public int Type { get; set; }
    public bool Active { get; set; }
    public List<string>? Tags { get; set; }
    public string? TagInput { get; set; }
    public bool Popular { get; set; }
    public bool Processed { get; set; }
    public int BookingCount { get; set; }
    public bool Removed { get; set; }
    public bool Hidden { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string? CreatorId { get; set; }
    public string? LastUpdateUser { get; set; }
    public string? DataFrom { get; set; }
    public bool HasChild { get; set; }
    public int? DistrictId { get; set; }
    public int? ProvinceId { get; set; }
    public int? WardId { get; set; }
    public bool Pending { get; set; }
    public decimal? CompareScoreMax { get; set; }
    public int? TotalScans { get; set; }
    public int? ScanType { get; set; }
    public int? DetermineAddressLevel { get; set; }
    public string? JobScanId { get; set; }
    public string SourcePlaceId { get; set; } = null!;
    public List<int>? ReasonOperations { get; set; }
    public int ScanProcessed { get; set; }
    public int ScanStatus { get; set; }
    public string Reason { get; set; }
    public string Note { get; set; }
    public string? Keywords { get; set; }
    public int QuoteCount { get; set; }
    public List<SubPlacesEs> SubPlaces { get; set; }
    public int? StatusApprovePlace { get; set; }
    public string? ReasonApprove { get; set; }
    public double? Score { get; set; }
    public double Distance { get; set; }

    public static void MappingProperties(PropertiesDescriptor<PlacesEs> d)
    {
        d.LongNumber(k => k.Id)
            .Boolean(k => k.Active)
            .Text(t => t.Name,
                t =>
                    t.Analyzer("icu")
                        .Fields(ff => ff
                            .Keyword("keyword",
                                st => st.IgnoreAbove(256))
                        ))
            .Text(t => t.Address,
                t =>
                    t.Analyzer("icu")
                        .Fields(ff => ff
                            .Keyword("keyword",
                                st => st.IgnoreAbove(256))
                        ))
            .LongNumber(l => l.QuoteCount)
            .LongNumber(l => l.BookingCount)
            .Text(t => t.City,
                t =>
                    t.Analyzer("icu")
                        .Fields(ff => ff
                            .Keyword("keyword",
                                st => st.IgnoreAbove(256))
                        ))
            .Keyword(k => k.PlaceId)
            .Text(t => t.FullAddress,
                t =>
                    t.Analyzer("icu")
                        .Fields(ff => ff
                            .Keyword("keyword",
                                st => st.IgnoreAbove(256))
                        ))
            .GeoPoint(g => g.Location)
            .Boolean(k => k.Popular)
            .Boolean(k => k.Processed)
            .IntegerNumber(k => k.ProvinceId)
            .IntegerNumber(k => k.DistrictId)
            .IntegerNumber(k => k.WardId)
            .Keyword(k => k.Reason)
            .Keyword(k => k.Note)
            .Keyword(k => k.ReasonApprove)
            .Keyword(k => k.ScanStatus)
            .Keyword(k => k.SourcePlaceId)
            .Keyword(k => k.CreatorId)
            .Keyword(k => k.LastUpdateUser)
            .Keyword(k => k.JobScanId)
            .Date(k => k.CreatedAt)
            .Date(k => k.UpdatedAt);
    }
}

public class Location
{
    public double Lat { get; set; }
    public double Lon { get; set; }
}