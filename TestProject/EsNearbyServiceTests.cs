using Elastic.Clients.Elasticsearch;
using Elastic.Transport;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using System.Diagnostics;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Entities.Context;
using NDS.ETL.Infrastructure.Common;
using NDS.ETL.Infrastructure.Utils;
using NUnit.Framework.Legacy;

namespace TestProject
{
    [TestFixture]
    public class EsNearbyServiceTests
    {
        private Mock<ILogger<ESNearbyService>> _mockLogger;
        private ElasticsearchClient _elasticClient;
        private ESNearbyService _service;
        private ElasticSearchConfig _elasticSearchConfig;
        private Mock<IESClientFactory> _mockElasticSearchClientFactory;
        private string _testIndex = "place-sit";

        [SetUp]
        public void Setup()
        {
            // Load configuration from appsettings.json
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            // Bind ElasticSearchConfig from appsettings.json
            _elasticSearchConfig = configuration.GetSection("ElasticSearchConfig").Get<ElasticSearchConfig>()!;

            var url = _elasticSearchConfig!.Url.First();
            var settings = new ElasticsearchClientSettings(new Uri(url))
                .DefaultIndex(_testIndex)
                .Authentication(new BasicAuthentication(_elasticSearchConfig!.UserName!, _elasticSearchConfig.Password!))
                .ServerCertificateValidationCallback((sender, cert, chain, errors) => true)
                .EnableDebugMode()
                .DisableDirectStreaming()
                .RequestTimeout(TimeSpan.FromSeconds(30));

            _elasticClient = new ElasticsearchClient(settings);

            // Setup mock factory to return the ElasticsearchClient and index names
            _mockElasticSearchClientFactory = new Mock<IESClientFactory>();
            _mockElasticSearchClientFactory.Setup(f => f.GetClient()).Returns(_elasticClient);
            _mockElasticSearchClientFactory.Setup(f => f.GetPlaceIndexName()).Returns(_testIndex);
            _mockElasticSearchClientFactory.Setup(f => f.GetPlacePairIndexName()).Returns("place-pair-sit");
            _mockElasticSearchClientFactory.Setup(f => f.GetPlaceFavoriteIndexName()).Returns("fav-place-sit");

            // Mock logger
            _mockLogger = new Mock<ILogger<ESNearbyService>>();

            // Mock ESPlacesService
            var mockPlacesService = new Mock<IESPlaceDetailsService>(
                _elasticClient,
                configuration,
                Mock.Of<IScopedResolver<NpgPlaceContext>>(),
                Mock.Of<ISearchConfigService>(),
                Mock.Of<ILogger<IESPlaceDetailsService>>()
            );

            // Create service instance using the mock factory instead of configuration
            _service = new ESNearbyService(_mockElasticSearchClientFactory.Object, mockPlacesService.Object, _mockLogger.Object);
        }

        [Test]
        [TestCase("","0984086431", 21.021673, 105.803715, 9, 5, 150)] // H3 resolution 9 ~ bán kính 174m
        [TestCase("","0984086431", 21.000103, 105.841003, 8, 3, 300)] // H3 resolution 8 ~ bán kính 461m
        [TestCase("","0984086431", 21.007831, 105.845657, 10, 4, 50)] // H3 resolution 10 ~ bán kính 65m
        [TestCase("test-user","0984086431", 21.031219, 105.853027, 9, 5, 200)] // H3 resolution 9 ~ bán kính 174m
        [TestCase("test-user","0984086431", 20.991324, 105.804276, 7, 10, 1000)] // H3 resolution 7 ~ bán kính 1220m
        public async Task SearchNearbyAsync_ReturnsExpectedResults(string userId, string phoneNumber, double latitude, double longitude, int h3Resolution, int limit, double? radiusMeters = null)
        {
            // Arrange & Act
            // Đo thời gian xử lý tổng thể
            var overallStopwatch = Stopwatch.StartNew();

            // Đo thời gian thực thi phương thức tìm kiếm
            var searchStopwatch = Stopwatch.StartNew();
            // Thực hiện tìm kiếm
            var results = await _service.SearchNearbyAsync(userId, phoneNumber, latitude, longitude, h3Resolution, limit, null, radiusMeters);
            searchStopwatch.Stop();

            // Assert
            Assert.That(results, Is.Not.Null);

            // Ghi log kết quả để debug
            Console.WriteLine($"Tìm kiếm gần đây tại ({latitude}, {longitude}) với H3 resolution {h3Resolution} trả về {results.Count} kết quả:");
            Console.WriteLine($"Thời gian xử lý: {searchStopwatch.ElapsedMilliseconds}ms cho phương thức SearchNearbyAsync");

            foreach (var item in results)
            {
                Console.WriteLine($" - {item.Name} ({item.DataFrom}): {item.FullAddress} - {item.Distance:##0.##}m - Score: {item.Score:##0.##}");
                
                // Log địa điểm con nếu có
                if (item.SubPlaces?.Any() == true)
                {
                    Console.WriteLine($"   Có {item.SubPlaces.Count} địa điểm con:");
                    foreach (var subplace in item.SubPlaces.OrderBy(x => x.Id))
                    {
                        Console.WriteLine($"    * {subplace.Name}");
                    }
                }
            }

            // Kiểm tra số lượng kết quả không vượt quá giới hạn
            Assert.That(results.Count, Is.LessThanOrEqualTo(limit));

            // Dừng đo thời gian tổng thể và ghi log
            overallStopwatch.Stop();
            Console.WriteLine($"Tổng thời gian thực thi: {overallStopwatch.ElapsedMilliseconds}ms");
            Console.WriteLine($"Hoạt động tìm kiếm mất {searchStopwatch.ElapsedMilliseconds}ms ({(double)searchStopwatch.ElapsedMilliseconds / overallStopwatch.ElapsedMilliseconds:P2} tổng thời gian test)");
        }

        [Test]
        public async Task SearchNearbyAsync_WithInvalidCoordinates_HandlesGracefully()
        {
            // Act - vĩ độ ngoài phạm vi hợp lệ
            var results = await _service.SearchNearbyAsync("", "0984086431", 200.0, 105.803715, 9, 5); // H3 resolution 9 ~ bán kính 174m

            // Assert
            Assert.That(results, Is.Not.Null);
            Assert.That(results, Is.Empty);
        }

        [Test]
        public async Task SearchNearbyAsync_WithInvalidH3Resolution_UsesDefaultH3Resolution()
        {
            // Act
            var results = await _service.SearchNearbyAsync("", "0984086431", 21.021673, 105.803715, 0, 5); // H3 resolution 0 (không hợp lệ, sẽ sử dụng giá trị mặc định)

            // Assert
            Assert.That(results, Is.Not.Null);
            
            // Dịch vụ nên xử lý một cách hợp lý và có thể sử dụng H3 resolution mặc định
            if (results.Any())
            {
                Assert.Pass("Dịch vụ đã xử lý H3 resolution không hợp lệ một cách hợp lý");
            }
        }

        [Test]
        public async Task SearchNearbyAsync_WithZeroH3Resolution_UsesDefaultH3Resolution()
        {
            // Act
            var results = await _service.SearchNearbyAsync("", "0984086431", 21.021673, 105.803715, 0, 5);

            // Assert
            Assert.That(results, Is.Not.Null);

            // Kiểm tra xem dịch vụ có trả về kết quả khi H3 resolution là 0 hay không
            if (results.Any())
            {
                Assert.Pass("Dịch vụ đã xử lý H3 resolution 0 bằng cách sử dụng giá trị mặc định");
            }
        }

        [Test]
        public async Task SearchNearbyAsync_SortsResultsByProperCriteria()
        {
            // Act
            // Thực hiện tìm kiếm với tọa độ tại Hà Nội
            // H3 resolution 7 ~ bán kính 1220m
            var results = await _service.SearchNearbyAsync("", "0984086431", 21.021673, 105.803715, 7, 10);

            // Assert
            if (results.Count >= 2)
            {
                // Kiểm tra thứ tự sắp xếp ưu tiên
                var expectedOrder = results
                    .OrderByDescending(p => p.Popular)
                    .ThenByDescending(p => p.BookingCount)
                    .ThenBy(p => p.Distance)
                    .ToList();

                CollectionAssert.AreEqual(
                    expectedOrder.Select(p => p.PlaceId).ToList(),
                    results.Select(p => p.PlaceId).ToList(),
                    "Kết quả không được sắp xếp theo thứ tự ưu tiên mong muốn (Popular > BookingCount > Distance)");
            }
            else
            {
                Console.WriteLine("Không đủ kết quả để kiểm tra logic sắp xếp");
                Assert.Pass();
            }
        }

        [Test]
        public async Task SearchNearbyAsync_WithDistanceCalculation_ReturnsCorrectDistances()
        {
            // Tọa độ trung tâm Hà Nội
            double latitude = 21.021673;
            double longitude = 105.803715;

            // Act
            // H3 resolution 7 ~ bán kính 1220m
            var results = await _service.SearchNearbyAsync("", "0984086431", latitude, longitude, 7, 5);

            // Assert
            Assert.That(results, Is.Not.Null);

            foreach (var place in results)
            {
                // Kiểm tra nếu có tọa độ
                if (place.Location != null)
                {
                    // Tính khoảng cách sử dụng công thức Haversine
                    double lat1 = latitude * Math.PI / 180.0;
                    double lon1 = longitude * Math.PI / 180.0;
                    double lat2 = place.Location.Lat * Math.PI / 180.0;
                    double lon2 = place.Location.Lon * Math.PI / 180.0;

                    double dlon = lon2 - lon1;
                    double dlat = lat2 - lat1;

                    double a = Math.Sin(dlat / 2) * Math.Sin(dlat / 2) + 
                              Math.Cos(lat1) * Math.Cos(lat2) * 
                              Math.Sin(dlon / 2) * Math.Sin(dlon / 2);
                    double c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
                    const double EARTH_RADIUS = 6371000; // Bán kính trái đất tính bằng mét
                    double calculatedDistance = EARTH_RADIUS * c;

                    // In ra thông tin để debug
                    Console.WriteLine($"Địa điểm: {place.Name}, Khoảng cách trả về: {place.Distance}m, Khoảng cách tính toán: {calculatedDistance:F0}m");

                    // Chấp nhận sai số nhỏ khoảng 5%
                    double acceptableError = 0.05 * calculatedDistance;
                    Assert.That(
                        Math.Abs(place.Distance - calculatedDistance),
                        Is.LessThanOrEqualTo(acceptableError),
                        $"Khoảng cách {place.Distance}m khác nhiều so với khoảng cách tính toán {calculatedDistance:F0}m"
                    );
                }
            }
        }

        [Test]
        public async Task SearchNearbyAsync_UsesConcurrentCollectionsForThreadSafety()
        {
            // Kiểm tra rằng nhiều tìm kiếm đồng thời không gây ra ngoại lệ

            // Act - Chạy nhiều tìm kiếm đồng thời
            var tasks = new List<Task<List<PlacesEs>>>
            {
                _service.SearchNearbyAsync("", "0984086431", 21.021673, 105.803715, 10, 5), // H3 resolution 10 ~ bán kính 65m
                _service.SearchNearbyAsync("", "0984086431", 21.000103, 105.841003, 8, 3),  // H3 resolution 8 ~ bán kính 461m
                _service.SearchNearbyAsync("", "0984086431", 21.007831, 105.845657, 9, 4),  // H3 resolution 9 ~ bán kính 174m
                _service.SearchNearbyAsync("", "0984086431", 21.031219, 105.853027, 9, 5),  // H3 resolution 9 ~ bán kính 174m
                _service.SearchNearbyAsync("", "0984086431", 20.991324, 105.804276, 7, 5)   // H3 resolution 7 ~ bán kính 1220m
            };

            // Assert - Không nên có ngoại lệ nào được ném ra
            await Task.WhenAll(tasks);
            Assert.Pass("Các tìm kiếm đồng thời đã hoàn thành mà không có ngoại lệ");
        }

        [Test]
        public async Task SearchNearbyAsync_WithPriorityResults_ReturnsPriorityResultsFirst()
        {
            // Thực hiện tìm kiếm có userId để kích hoạt tìm kiếm địa điểm yêu thích
            string userId = "test-user";
            string phoneNumber = "0984086431";
            double latitude = 21.021673;
            double longitude = 105.803715;
            int h3Resolution = 7; // H3 resolution 7 ~ bán kính 1220m
            int limit = 10;

            // Act
            var results = await _service.SearchNearbyAsync(userId, phoneNumber, latitude, longitude, h3Resolution, limit);

            // Assert
            Assert.That(results, Is.Not.Null);

            // Kiểm tra xem kết quả ưu tiên có được sắp xếp trước không
            var priorityResults = results.Where(p => p.DataFrom != null && p.DataFrom.Contains("Priority")).ToList();
            if (priorityResults.Any())
            {
                Console.WriteLine($"Tìm thấy {priorityResults.Count} kết quả ưu tiên");
                
                // Kiểm tra vị trí của kết quả ưu tiên (chúng nên đứng đầu danh sách)
                foreach (var priorityResult in priorityResults)
                {
                    int index = results.IndexOf(priorityResult);
                    Console.WriteLine($"Kết quả ưu tiên '{priorityResult.Name}' ở vị trí {index + 1}/{results.Count}");
                }
                
                // Đếm số kết quả ưu tiên ở nửa đầu danh sách
                int priorityInFirstHalf = priorityResults.Count(p => results.IndexOf(p) < results.Count / 2);
                
                // Đảm bảo phần lớn kết quả ưu tiên nằm ở nửa đầu danh sách
                Assert.That(priorityInFirstHalf, Is.GreaterThanOrEqualTo(priorityResults.Count / 2),
                    "Các kết quả ưu tiên nên xuất hiện chủ yếu ở nửa đầu danh sách kết quả");
            }
            else
            {
                Console.WriteLine("Không tìm thấy kết quả ưu tiên trong môi trường test này");
                Assert.Pass();
            }
        }
    }
} 