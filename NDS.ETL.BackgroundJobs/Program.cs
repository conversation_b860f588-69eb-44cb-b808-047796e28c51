using System.Text;
using Confluent.Kafka;
using Elastic.Clients.Elasticsearch;
using Hangfire;
using Hangfire.Dashboard;
using Hangfire.Dashboard.BasicAuthorization;
using Hangfire.MemoryStorage;
using Hangfire.PostgreSql;
using HealthChecks.ApplicationStatus.DependencyInjection;
using KnowledgeBase.Core.Extensions;
using Microsoft.AspNetCore.Authentication;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using NDS.ETL.BackgroundJobs.Authentication;
using NDS.ETL.BackgroundJobs.Extensions;
using NDS.ETL.BackgroundJobs.Filters;
using NDS.ETL.BackgroundJobs.Registrations;
using NDS.ETL.BackgroundJobs.Services;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Entities.Context;
using NDS.ETL.Infrastructure.Common;
using NDS.ETL.Infrastructure.Utils;
using Npgsql;
using StackExchange.Redis;

try
{
    Console.WriteLine($"BackgroundJob is starting!");
    var builder = WebApplication.CreateBuilder(args);
    builder.Logging.ClearProviders();
    builder.Logging.AddSimpleConsole(opts =>
    {
        opts.SingleLine = true;
        opts.TimestampFormat = "yyyy-MM-dd HH:mm:ss.fff ";
    });

    builder.Services
        .AddHealthChecks()
        .AddApplicationStatus();

    builder.Services.AddAuthentication("BasicAuthentication")
        .AddScheme<AuthenticationSchemeOptions, BasicAuthenticationHandler>("BasicAuthentication", null);

    builder.Services
        .AddAuthorization();

    builder.Services.AddControllers();

    // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
    builder.Services.AddEndpointsApiExplorer();

    builder.Services.AddSwaggerGen(options =>
    {
        // Add API key security definition
        options.AddSecurityDefinition("ApiKey", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
        {
            Name = "X-Api-Key",
            Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
            In = Microsoft.OpenApi.Models.ParameterLocation.Header,
            Description = "API Key needed to access the endpoints"
        });

        // Add security requirement for API key
        options.AddSecurityRequirement(new Microsoft.OpenApi.Models.OpenApiSecurityRequirement
        {
            {
                new Microsoft.OpenApi.Models.OpenApiSecurityScheme
                {
                    Reference = new Microsoft.OpenApi.Models.OpenApiReference
                    {
                        Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                        Id = "ApiKey"
                    }
                },
                Array.Empty<string>()
            }
        });
    });
    builder.Services.AddSingleton(typeof(IScopedResolver<>), typeof(ScopedResolver<>));
    builder.Services.AddMemoryCache();
    builder.Services.AddScoped<ICacheManager, CacheManager>();
    builder.Services.AddScoped<IRequestInfoLogger, RequestInfoLogger>();

    // Đăng ký Redis và dịch vụ khóa phân tán
    var redisConfiguration = builder.Configuration.GetConnectionString("Redis");
    if (!string.IsNullOrEmpty(redisConfiguration))
    {
        builder.Services.AddSingleton<IConnectionMultiplexer>(sp =>
            ConnectionMultiplexer.Connect(redisConfiguration));
        builder.Services.AddSingleton<IDistributedLock, RedisDistributedLock>();
    }
    else
    {
        // Đăng ký mock/in-memory implementation nếu không có Redis
        builder.Services.AddSingleton<IDistributedLock, InMemoryDistributedLock>();
    }


    var services = builder.Services;
    IConfiguration _configuration = builder.Configuration;

    var elsSetting = builder.Configuration.Bind<ElasticSearchConfig>(nameof(ElasticSearchConfig));
    services.AddSingleton(elsSetting);

    services.AddElasticsearchServices();

    var env = builder.Environment;
    Console.WriteLine($"BackgroundJob is starting with Environment Name!" + env.EnvironmentName);
    Console.WriteLine($"Test Config: " + elsSetting.UserName);
    await services.CreateElasticSearchIndices(elsSetting, env);
    Console.WriteLine($"BackgroundJob is starting! Index: " + elsSetting.Index.FirstOrDefault());
    services.AddSingleton<ESPlacesService>();

    services.AddSingleton(typeof(IESPlaceDetailsService), typeof(ESPlaceDetailsService));
    services.AddSingleton<IESNearbyService, ESNearbyService>();
    services.AddSingleton<IESAutocompleteService, ESAutocompleteService>();
    services.AddSingleton<IESPlacePairs, ESPlacePairsService>();
    services.AddSingleton<IESRecommendationService, ESRecommendationService>();
    services.AddScoped<IProvincesService, ProvincesService>();
    services.AddScoped<IWardsService, WardsService>();
    if (env.IsDevelopment())
    {
        builder.Services.AddHangfireServer();
        services.AddHangfire((s, cfg) =>
        {
            var options = new MemoryStorageOptions
            {
                FetchNextJobTimeout = TimeSpan.FromDays(1)
            };
            GlobalConfiguration.Configuration.UseMemoryStorage(options);
            cfg
                .UseSimpleAssemblyNameTypeSerializer()
                .UseRecommendedSerializerSettings()
                .UseMemoryStorage(options);
            cfg.UseFilter(new AutomaticRetryAttribute
            {
                Attempts = 5,
            });
            cfg.UseActivator(new ServiceProviderActivator(s));
        });
    }
    else
    {
        services.AddHangfire((s, cfg) =>
        {
            var connectionString = _configuration["ConnectionStrings:PgPlace"];

            // Enhanced PostgreSQL options
            var postgresOptions = new PostgreSqlStorageOptions
            {
                SchemaName = _configuration["ConnectionStrings:HangfireSchema"],
                // Performance tuning
                QueuePollInterval = TimeSpan.FromSeconds(15), // How often to poll for new jobs
                JobExpirationCheckInterval = TimeSpan.FromHours(1), // Cleanup old jobs
                CountersAggregateInterval = TimeSpan.FromMinutes(5), // Statistics aggregation
                // Schema management
                PrepareSchemaIfNecessary = false, //Turn Off Auto-create schema & tables
                // Connection settings
                TransactionSynchronisationTimeout = TimeSpan.FromSeconds(30),
                InvisibilityTimeout = TimeSpan.FromMinutes(30), // Job lock timeout
                // Enable distributed locks (important for multiple instances)
                UseNativeDatabaseTransactions = true
            };

            cfg
                .UseSimpleAssemblyNameTypeSerializer()
                .UseRecommendedSerializerSettings()
                .UsePostgreSqlStorage(opt =>
                {
                    opt.UseNpgsqlConnection(connectionString);
                }, postgresOptions);

            // Enhanced retry policy
            cfg.UseFilter(new AutomaticRetryAttribute
            {
                Attempts = 5,
                DelaysInSeconds = new[] { 60, 300, 900, 1800, 3600 }, // 1min, 5min, 15min, 30min, 1hr
                //OnAttemptsExceeded = AttemptsExceededAction.Delete // Delete failed jobs after max attempts
            });

            // Custom filters
            cfg.UseActivator(new ServiceProviderActivator(s));
        });
        services.AddHangfireServer(options =>
        {
            options.ServerName = Environment.MachineName + "-" + Environment.ProcessId;
            options.WorkerCount = Math.Max(Environment.ProcessorCount, 20); // Adjust based on your needs
            options.Queues = new[] { "default", "critical", "background" }; // Multiple queues
            options.ServerTimeout = TimeSpan.FromMinutes(30);
            options.SchedulePollingInterval = TimeSpan.FromSeconds(15);
        });
    }

    if (_configuration["KafkaConfig:Disable"] == "false")
    {
        var config = new ConsumerConfig
        {
            BootstrapServers = _configuration["KafkaConfig:Connection"],
            GroupId = _configuration["KafkaConfig:GroupSyncPlaces"],
            EnableAutoOffsetStore = false,
            EnableAutoCommit = false,
            StatisticsIntervalMs = 5000,
            SessionTimeoutMs = 6000,
            AutoOffsetReset = AutoOffsetReset.Latest,
            EnablePartitionEof = true,
            PartitionAssignmentStrategy = PartitionAssignmentStrategy.CooperativeSticky
        };
        services.AddSingleton(new ConsumerBuilder<Ignore, string>(config).Build());
        services.AddHostedService<KafkaConsumerService>();
    }

    services.AddHangfireServer();
    services.AddHostedService<RecurringJobService>();
    services.AddSingleton(typeof(ISearchConfigService), typeof(SearchConfigService));
    services.AddSingleton(typeof(IESPlacesService), typeof(ESPlacesService));

    // Register the API key validation service with memory cache
    builder.Services.AddMemoryCache();
    builder.Services.AddScoped<IApiKeyValidationService, ApiKeyValidationService>();

    // Register the API key authentication filter
    builder.Services.AddScoped<ApiKeyAuthenticationFilter>();

    // Configure MVC with the API key authentication filter
    builder.Services.AddMvc(options => { options.Filters.AddService<ApiKeyAuthenticationFilter>(); });

    var connectionString = builder.Configuration.GetConnectionString("PgPlace");


    AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
    AppContext.SetSwitch("Npgsql.DisableDateTimeInfinityConversions", false);
    var dataSourceBuilder = new NpgsqlDataSourceBuilder(connectionString);
    dataSourceBuilder.UseNetTopologySuite();
    dataSourceBuilder.UseNodaTime();
    dataSourceBuilder.EnableDynamicJson();
    dataSourceBuilder.EnableUnmappedTypes();
    //dataSourceBuilder.EnableParameterLogging();
    var dataSource = dataSourceBuilder.Build();

    builder.Services.AddDbContextPool<NpgPlaceContext>(options =>
            options.UseNpgsql(dataSource, o =>
            {
                o.MigrationsAssembly(typeof(NpgPlaceContext).Assembly.FullName);
                o.UseNetTopologySuite();
                o.UseNodaTime();
            }),
        poolSize: 128);
    builder.Services.AddDbContextPool<NpgPlaceDevContext>(options =>
            options.UseNpgsql(dataSource, o =>
            {
                o.MigrationsAssembly(typeof(NpgPlaceDevContext).Assembly.FullName);
                o.UseNetTopologySuite();
                o.UseNodaTime();
            }),
        poolSize: 128);

    // Add the DbContextFactory registration
    builder.Services.AddDbContextFactory<ESDbContext>(options =>
        options.UseNpgsql(connectionString, x => x.UseNetTopologySuite()));

    var mongoConnectionString = _configuration["MongoDbSettings:ConnectionString"];
    var mongoDbName = _configuration["MongoDbSettings:DatabaseName"];

    var client = new MongoClient(mongoConnectionString);
    var database = client.GetDatabase(mongoDbName);
    services.AddSingleton(database);

    var serviceType = typeof(IRecurringJob);
    foreach (var implType in typeof(Program).Assembly.GetTypes()
                 .Where(x => x.IsAssignableTo(serviceType) && x.IsClass && !x.IsAbstract))
    {
        services.AddSingleton(implType);
        services.AddSingleton(serviceType, s => s.GetRequiredService(implType));
    }

    var app = builder.Build();
    app.UseHealthChecks("/status");
    var pathBase = builder.Configuration["PathBase"];
    Console.WriteLine($"UsePathBase: {pathBase}");
    if (!string.IsNullOrWhiteSpace(pathBase))
    {
        app.UsePathBase(new PathString(pathBase));
    }
   
    var loggerFactory = app.Services.GetRequiredService<ILoggerFactory>();
    var log4netConfig = builder.Environment.IsStaging() ? "log4net.config" : "log4net.Production.config";
    loggerFactory.AddLog4Net(log4netConfig, true);


    //ILog logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
    //GlobalContext.Properties["component"] = "send";
    //logger.Debug("this Debug msg " + DateTime.Now.ToShortTimeString());

    var conf = app.Services.GetRequiredService<IConfiguration>();
    app.UseHangfireDashboard("/hangfire", new DashboardOptions
    {
        Authorization = new[]
        {
            new BasicAuthAuthorizationFilter(new BasicAuthAuthorizationFilterOptions
            {
                RequireSsl = false,
                SslRedirect = false,
                LoginCaseSensitive = true,
                Users = new[]
                {
                    new BasicAuthAuthorizationUser
                    {
                        Login = conf["BasicAuth:UserName"],
                        PasswordClear = conf["BasicAuth:Password"]
                    }
                }
            })
        }
    });
    app.MapHangfireDashboard();

    // ✅ Bảo vệ Swagger UI bằng `[Authorize]`
    app.UseWhen(context => context.Request.Path.StartsWithSegments("/swagger"), appBuilder =>
    {
        appBuilder.Use(async (context, next) =>
        {
            string authHeader = context.Request.Headers["Authorization"];

            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Basic "))
            {
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                context.Response.Headers["WWW-Authenticate"] = "Basic";
                await context.Response.WriteAsync("Unauthorized");
                return;
            }

            var encodedCredentials = authHeader.Substring("Basic ".Length).Trim();
            var decodedCredentials = Encoding.UTF8.GetString(Convert.FromBase64String(encodedCredentials));
            var credentials = decodedCredentials.Split(':', 2);

            var conf = app.Services.GetRequiredService<IConfiguration>();
            if (credentials.Length != 2 || credentials[0] != conf["BasicAuth:UserName"] ||
                credentials[1] != conf["BasicAuth:Password"])
            {
                context.Response.StatusCode = StatusCodes.Status403Forbidden;
                await context.Response.WriteAsync("Forbidden");
                return;
            }

            await next();
        });
    });

    // Configure the HTTP request pipeline.
    // if (app.Environment.IsDevelopment())
    // {
    //     app.UseSwagger();
    //     app.UseSwaggerUI();
    // }
    // if (app.Environment.IsDevelopment())
    // {
    app.UseSwagger();
    app.UseSwaggerUI(options =>
    {
        // Add Swagger UI configuration for API key
        options.SwaggerEndpoint("/swagger/v1/swagger.json", "API V1");
        options.DefaultModelsExpandDepth(-1); // Disable schema expansion
        options.DisplayRequestDuration(); // Show request duration
        options.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion
            .List); // Expand the operations list by default
        options.EnableFilter(); // Enable filtering operations
        options.EnableDeepLinking(); // Enable deep linking for easy sharing
        options.DefaultModelRendering(Swashbuckle.AspNetCore.SwaggerUI.ModelRendering.Model); // Change model rendering
    });
    // }
    app.UseRouting();
    app.UseAuthorization();
    app.MapControllers();
    app.Run();
}
catch (Exception ex)
{
    Console.WriteLine(ex);
}

internal class DashboardNoAuthorizationFilter : IDashboardAuthorizationFilter
{
    public bool Authorize(DashboardContext context)
    {
        // var httpContext = context.GetHttpContext();

        // Allow all authenticated users to see the Dashboard (potentially dangerous).
        // return httpContext.User.Identity?.IsAuthenticated ?? false;
        return true;
    }
}