using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Keyless]
[Table("job_config")]
public partial class JobConfig
{
    [Column("id")]
    [StringLength(254)]
    public string? Id { get; set; }

    [Column("lastTime")]
    public long? LastTime { get; set; }

    [Column("interval")]
    public long? Interval { get; set; }

    [Column("threadPoolSize")]
    public int? ThreadPoolSize { get; set; }

    [Column("enable")]
    public int? Enable { get; set; }

    [Column("batchSize")]
    public double? BatchSize { get; set; }
}
