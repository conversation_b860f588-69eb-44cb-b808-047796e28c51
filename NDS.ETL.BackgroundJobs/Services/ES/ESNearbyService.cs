using Elastic.Clients.Elasticsearch;
using NDS.ETL.BackgroundJobs.Extensions;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.Infrastructure.Extensions;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text.Json;

namespace NDS.ETL.BackgroundJobs.Services.ES
{
    /// <summary>
    /// Dịch vụ tìm kiếm địa điểm gần đây sử dụng Elasticsearch
    /// </summary>
    public class ESNearbyService : IESNearbyService
    {
        #region Private Fields

        private readonly ElasticsearchClient _elasticClient;
        private string _placeIdxName;
        private string _placePairIdxName;
        private string _placeFavIdxName;
        private readonly IESPlaceDetailsService _placeDetailsService;
        private readonly ILogger<ESNearbyService> _logger;

        #endregion

        #region Constructor        
        /// <summary>
        /// Khởi tạo dịch vụ tìm kiếm địa điểm gần đây
        /// </summary>
        public ESNearbyService(
            IESClientFactory iesClientFactory,
            IESPlaceDetailsService placeDetailsService,
            ILogger<ESNearbyService> logger)
        {
            _placeDetailsService = placeDetailsService;
            _logger = logger;

            // Lấy client và thông tin index từ factory
            _elasticClient = iesClientFactory.GetClient();
            _placeIdxName = iesClientFactory.GetPlaceIndexName();
            _placePairIdxName = iesClientFactory.GetPlacePairIndexName();
            _placeFavIdxName = iesClientFactory.GetPlaceFavoriteIndexName();
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Tìm kiếm địa điểm gần đây dựa trên tọa độ và độ phân giải H3
        /// </summary>
        public async Task<List<PlacesEs>> SearchNearbyAsync(string userId,
            string customerPhone,
            double latitude,
            double longitude,
            int? h3Resolution = null,
            int limit = 5,
            bool? popular = null,
            double? maxRadiusInMeters = null,
            double? minRadiusMeters = null
        )
        {
            var stopwatch = Stopwatch.StartNew();
            _logger.LogInformation("Searching nearby places at ({Lat}, {Lon}) with H3 resolution {Resolution}, popular filter: {Popular}, radius: {RadiusInMeters}m",
                latitude, longitude, h3Resolution, popular, maxRadiusInMeters);

            try
            {
                h3Resolution ??= maxRadiusInMeters > 0 ? maxRadiusInMeters.Value.GetH3ResolutionFromRadiusInMeters() : 8; // Mặc định là độ phân giải 8 nếu không được cung cấp bán kính ~460 m

                // Tạo đối tượng vị trí từ tọa độ
                var currentLocation = CreateGeoLocation(latitude, longitude);

                // Tính toán bán kính tìm kiếm dựa trên độ phân giải H3 nếu maxRadiusInMeters không được cung cấp
                double calculatedRadiusInMeters = maxRadiusInMeters ?? (h3Resolution!.Value.GetRadiusKmFromH3Res() * 1000);

                _logger.LogDebug("Using search radius of {RadiusInMeters}m from H3 resolution {Resolution}",
                    calculatedRadiusInMeters, h3Resolution);

                // Dictionary để lưu kết quả gần đây
                var nearbyResultDict = new ConcurrentDictionary<long, PlacesEs>();

                // Dictionary để lưu các kết quả ưu tiên (yêu thích, lịch sử)
                var priorityResultDict = new ConcurrentDictionary<string, PlacesEs>();

                // Thực hiện các task tìm kiếm song song
                await ExecuteSearchTasks(
                    userId,
                    customerPhone,
                    currentLocation,
                    h3Resolution.Value,
                    limit,
                    nearbyResultDict,
                    priorityResultDict,
                    popular,
                    minRadiusMeters);

                // Kết hợp kết quả, ưu tiên các địa điểm đã lưu
                var combinedResults = await CombineResults(
                    nearbyResultDict,
                    priorityResultDict,
                    limit,
                    latitude,
                    longitude,
                    calculatedRadiusInMeters);

                _logger.LogInformation("Found {Count} nearby places in {ElapsedMs}ms",
                    combinedResults.Count, stopwatch.ElapsedMilliseconds);

                return combinedResults;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching nearby places: {ErrorMessage}", ex.Message);
                return new List<PlacesEs>();
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Tạo đối tượng GeoLocation từ tọa độ
        /// </summary>
        private GeoLocation CreateGeoLocation(double latitude, double longitude)
        {
            return GeoLocation.LatitudeLongitude(
                new LatLonGeoLocation { Lat = latitude, Lon = longitude });
        }

        /// <summary>
        /// Thực hiện các task tìm kiếm đồng thời
        /// </summary>
        private async Task ExecuteSearchTasks(string userId,
            string customerPhone,
            GeoLocation currentLocation,
            int h3Resolution,
            int limit,
            ConcurrentDictionary<long, PlacesEs> nearbyResultDict,
            ConcurrentDictionary<string, PlacesEs> priorityResultDict,
            bool? popular = null, double? minRadiusMeters = null)
        {
            // Tính toán bán kính tìm kiếm dựa trên độ phân giải H3
            double radiusInMeters = h3Resolution.GetRadiusKmFromH3Res() * 1000;

            // Tạo các task tìm kiếm song song
            var searchTasks = CreateNearbySearchTasks(
                userId,
                customerPhone,
                currentLocation,
                radiusInMeters, // Vẫn sử dụng radius để khả năng tương thích với các phương thức hiện tại
                limit,
                nearbyResultDict,
                priorityResultDict,
                popular,
                minRadiusMeters);

            // Chờ tất cả các task hoàn thành
            await Task.WhenAll(searchTasks);
        }

        /// <summary>
        /// Tạo các task tìm kiếm địa điểm gần đây song song
        /// </summary>
        private Task[] CreateNearbySearchTasks(string userId,
            string customerPhone,
            GeoLocation currentLocation,
            double radiusInMeters, // Đơn vị meters
            int limit,
            ConcurrentDictionary<long, PlacesEs> nearbyResultDict,
            ConcurrentDictionary<string, PlacesEs> priorityResultDict,
            bool? popular = null, double? minRadiusMeters = null
        )
        {
            List<Task> searchTasks = new List<Task>();

            // Lấy tọa độ làm điểm gốc cho tính toán khoảng cách
            currentLocation.TryGetLatitudeLongitude(out var latLng);
            var lat1 = latLng.Lat;
            var lon1 = latLng.Lon;

            var forUser = !string.IsNullOrWhiteSpace(userId) || !string.IsNullOrWhiteSpace(customerPhone);
            if (popular != true || !forUser)
            {
                // Thêm task tìm kiếm địa điểm gần đây
                searchTasks.Add(SearchNearbyPlacesTask(
                    currentLocation,
                    radiusInMeters,
                    limit,
                    nearbyResultDict,
                    popular,
                    minRadiusMeters));
            }

            // Thêm các task tìm kiếm địa điểm yêu thích và lịch sử nếu có userId hoặc customerPhone
            if (forUser)
            {
                // Task tìm kiếm địa điểm yêu thích
                searchTasks.Add(SearchFavoritePlacesTask(
                    userId,
                    customerPhone,
                    currentLocation,
                    radiusInMeters,
                    limit,
                    priorityResultDict,
                    lat1,
                    lon1,
                    minRadiusMeters));

                // Task tìm kiếm địa điểm lịch sử
                searchTasks.Add(SearchHistoryPlacesTask(
                    userId,
                    customerPhone,
                    currentLocation,
                    radiusInMeters,
                    limit,
                    priorityResultDict,
                    lat1,
                    lon1,
                    popular,
                    minRadiusMeters));
            }

            return searchTasks.ToArray();
        }

        /// <summary>
        /// Task tìm kiếm địa điểm gần đây
        /// </summary>
        private Task SearchNearbyPlacesTask(GeoLocation currentLocation,
            double radiusInMeters,
            int limit,
            ConcurrentDictionary<long, PlacesEs> nearbyResultDict,
            bool? popular = null,
            double? minRadiusMeters = null)
        {
            return Task.Run(async () =>
            {
                try
                {
                    // Chuyển đổi từ radiusInMeters sang h3Resolution để sử dụng tương thích
                    int h3Resolution = radiusInMeters.GetH3ResolutionFromRadiusInMeters();

                    // Tạo truy vấn tìm kiếm gần đây
                    var searchRequest = _elasticClient.CreateNearbyQuery(
                        currentLocation,
                        h3Resolution,
                        limit * 2, // Lấy nhiều hơn để có thể lọc sau
                        _placeIdxName,
                        popular,
                        minRadiusMeters);

                    // Thực hiện tìm kiếm
                    var response = await _elasticClient.SearchAsync(searchRequest);

                    if (response.IsValidResponse)
                    {
                        ProcessNearbySearchResults(response, nearbyResultDict);
                    }
                    else
                    {
                        _logger.LogError("Lỗi khi thực hiện tìm kiếm địa điểm gần đây: {ErrorMessage}",
                            response.DebugInformation);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi tìm kiếm địa điểm gần đây");
                }
            });
        }

        /// <summary>
        /// Xử lý kết quả tìm kiếm địa điểm gần đây
        /// </summary>
        private void ProcessNearbySearchResults(
            SearchResponse<PlacesEs> response,
            ConcurrentDictionary<long, PlacesEs> nearbyResultDict)
        {
            // Xử lý kết quả tìm kiếm song song
            Parallel.ForEach(response.Hits, hit =>
            {
                try
                {
                    // Tính khoảng cách nếu có
                    double distanceInMeters = 0;
                    if (hit.Fields != null &&
                        hit.Fields.TryGetValue("distance", out var distanceValue))
                    {
                        // Chuyển đổi và làm tròn khoảng cách (vẫn giữ đơn vị meters)
                        distanceInMeters = ((JsonElement)distanceValue).GetDistance();
                    }

                    // Tạo đối tượng PlacesEs từ kết quả
                    var place = hit.Source;
                    if (place != null)
                    {
                        place.Distance = distanceInMeters; // Lưu khoảng cách theo meters
                        place.Score = 1; // Score thấp nhất cho kết quả nearby

                        // Xóa các trường không cần thiết để giảm dung lượng
                        place.Keywords = null;
                        place.MasterAddress = string.Empty;
                        place.AddressPermuted?.Clear();

                        nearbyResultDict.TryAdd(place.Id, place);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi xử lý kết quả tìm kiếm gần đây với ID: {Id}", hit.Id);
                }
            });
        }

        /// <summary>
        /// Task tìm kiếm địa điểm yêu thích
        /// </summary>
        private Task SearchFavoritePlacesTask(string userId,
            string customerPhone,
            GeoLocation currentLocation,
            double radiusInMeters,
            int limit,
            ConcurrentDictionary<string, PlacesEs> priorityResultDict,
            double lat1,
            double lon1,
            double? minRadiusMeters = null)
        {
            return Task.Run(async () =>
            {
                try
                {
                    // Chuyển đổi từ radiusInMeters sang h3Resolution để sử dụng tương thích
                    int h3Resolution = radiusInMeters.GetH3ResolutionFromRadiusInMeters();

                    var favSearchRequest = _elasticClient.CreateFavPlaceNearbyQuery(
                        userId,
                        customerPhone,
                        currentLocation,
                        h3Resolution,
                        limit,
                        _placeFavIdxName,
                        minRadiusMeters
                        );

                    var response = await _elasticClient.SearchAsync(favSearchRequest);

                    if (response.IsValidResponse)
                    {
                        ProcessFavoriteResults(response.Documents, priorityResultDict, lat1, lon1);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi tìm kiếm địa điểm yêu thích");
                }
            });
        }

        /// <summary>
        /// Task tìm kiếm địa điểm lịch sử
        /// </summary>
        private Task SearchHistoryPlacesTask(string userId,
            string customerPhone,
            GeoLocation currentLocation,
            double radiusInMeters,
            int limit,
            ConcurrentDictionary<string, PlacesEs> priorityResultDict,
            double lat1,
            double lon1,
            bool? popular = null,
            double? minRadiusMeters = null)
        {
            return Task.Run(async () =>
            {
                try
                {
                    // Chuyển đổi từ radiusInMeters sang h3Resolution để sử dụng tương thích
                    int h3Resolution = radiusInMeters.GetH3ResolutionFromRadiusInMeters();

                    var pairsSearchRequest = _elasticClient.CreatePlacePairNearbyQuery(
                        userId,
                        customerPhone,
                        currentLocation,
                        h3Resolution,
                        limit,
                        _placePairIdxName,
                        popular,
                        minRadiusMeters);


                    var response = await _elasticClient.SearchAsync(pairsSearchRequest);

                    if (response.IsValidResponse)
                    {
                        ProcessHistoryResults(response.Documents, priorityResultDict, lat1, lon1);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi tìm kiếm địa điểm lịch sử");
                }
            });
        }

        /// <summary>
        /// Xử lý kết quả tìm kiếm địa điểm yêu thích
        /// </summary>
        private void ProcessFavoriteResults(
            IReadOnlyCollection<FavPlaceEs> documents,
            ConcurrentDictionary<string, PlacesEs> priorityResultDict,
            double lat1,
            double lon1)
        {
            ProcessPriorityResults(documents, priorityResultDict, lat1, lon1, "Favorite", 3);
        }

        /// <summary>
        /// Xử lý kết quả tìm kiếm địa điểm lịch sử
        /// </summary>
        private void ProcessHistoryResults(
            IReadOnlyCollection<PlacePairEs> documents,
            ConcurrentDictionary<string, PlacesEs> priorityResultDict,
            double lat1,
            double lon1)
        {
            ProcessPriorityResults(documents, priorityResultDict, lat1, lon1, "History", 2);
        }

        /// <summary>
        /// Xử lý kết quả tìm kiếm địa điểm ưu tiên (yêu thích hoặc lịch sử)
        /// </summary>
        private void ProcessPriorityResults<T>(
            IReadOnlyCollection<T> documents,
            ConcurrentDictionary<string, PlacesEs> priorityResultDict,
            double lat1,
            double lon1,
            string tag,
            int score) where T : class, IPlaceEsConvertible
        {
            Parallel.ForEach(documents, item =>
            {
                var placesEs = item.ToPlacesEs();
                placesEs.Score = score;
                placesEs.TagInput = tag;

                var lat2 = placesEs.Location.Lat;
                var lon2 = placesEs.Location.Lon;

                // Tính khoảng cách Haversine và chuyển đổi sang meters
                var distanceInMeters = H3Helper.CalculateDistance(lat1, lon1, lat2, lon2);
                placesEs.Distance = distanceInMeters;

                priorityResultDict.TryAdd(placesEs.PlaceId!, placesEs);
            });
        }

        /// <summary>
        /// Kết hợp và sắp xếp kết quả
        /// </summary>
        private async Task<List<PlacesEs>> CombineResults(
            ConcurrentDictionary<long, PlacesEs> nearbyResultDict,
            ConcurrentDictionary<string, PlacesEs> priorityResultDict,
            int limit,
            double userLat,
            double userLon,
            double radiusInMeters)
        {
            // Tổng hợp và sắp xếp kết quả
            var results = new ConcurrentBag<PlacesEs>();

            // Thêm các kết quả ưu tiên (yêu thích, lịch sử) và thông thường song song
            Parallel.Invoke(
                () =>
                {
                    Parallel.ForEach(priorityResultDict.Values, item =>
                    {
                        item.DataFrom = $"{item.TagInput} - Priority";
                        results.Add(item);
                    });
                },
                () =>
                {
                    Parallel.ForEach(nearbyResultDict.Values, item =>
                    {
                        item.DataFrom = "Nearby";
                        results.Add(item);
                    });
                }
            );

            // Loại bỏ các kết quả trùng lặp và giới hạn số lượng
            var distinctResults = results
                .AsParallel()
                .GroupBy(p => p.PlaceId)
                .Select(g =>
                {
                    var placesEs = g.OrderByDescending(p => p.Score).First();
                    placesEs.Distance = H3Helper.CalculateDistance(userLat, userLon, placesEs.Location.Lat, placesEs.Location.Lon);
                    return placesEs;
                })
                .Where(x =>
                {
                    var matchDistance = x.Distance <= radiusInMeters;
                    if (!string.IsNullOrWhiteSpace(x.Name) && x.FullAddress.StartsWith(x.Name))
                        x.Name = null;

                    // Kiểm tra điều kiện địa chỉ có đủ 3 phần tử sau khi loại bỏ "vietnam"
                    var hasValidAddressParts = CheckValidAddressParts(x);

                    return matchDistance && hasValidAddressParts;
                })
                // Ưu tiên kết quả từ favorite -> history -> nearby
                .OrderByDescending(p => p.Score) // Sắp xếp theo độ ưu tiên
                .ThenByDescending(p => p.Popular) // Sắp xếp theo độ phổ biến
                .ThenByDescending(p => p.BookingCount) // Sau đó theo số lần đặt
                .ThenBy(p => p.Distance) // Cuối cùng theo khoảng cách
                .Take(limit)
                .ToList();

            // Filter all distinctResults with tagInput == "History"
            var historyResults = distinctResults
                .AsParallel()
                .Select((item, idx) => new { Item = item, Index = idx })
                .Where(x => x.Item.DataFrom?.ToLower()?.Contains("priority") == true || x.Item.TagInput == "History")
                .ToList();

            if (historyResults.Count > 0)
            {
                // Collect PlaceIds for those with tagInput == "History"
                var tokenPlaceIds = historyResults
                    .AsParallel()
                    .Select(x => x.Item.PlaceId)
                    .Where(id => !string.IsNullOrEmpty(id))
                    .ToList();

                // Fetch details from the search service
                var detailedPlaces = await _placeDetailsService.GetPlacesDetailsByIdsAsync(tokenPlaceIds!);

                // Create a lookup for fast replacement
                var detailedPlacesDict = detailedPlaces.ToDictionary(p => p.PlaceId);

                // Replace the corresponding entries in distinctResults at the correct indices using parallel processing
                var lockObj = new object();

                foreach (var history in historyResults.AsParallel())
                {
                    var item = distinctResults[history.Index];
                    if (!detailedPlacesDict.TryGetValue(history.Item.PlaceId, out var detailedPlace))
                    {
                        detailedPlace = detailedPlacesDict.Values.FirstOrDefault(x => history.Item.PlaceId.StartsWith(x.PlaceId));
                    }
                    if (detailedPlace != null)
                    {
                        // Xóa các trường không cần thiết để giảm dung lượng
                        detailedPlace.Keywords = null;
                        detailedPlace.MasterAddress = string.Empty;
                        detailedPlace.AddressPermuted?.Clear();

                        // Tính lại khoảng cách dựa trên vị trí người dùng hiện tại
                        var lat2 = detailedPlace.Location.Lat;
                        var lon2 = detailedPlace.Location.Lon;
                        var distanceInMeters = H3Helper.CalculateDistance(userLat, userLon, lat2, lon2);


                        detailedPlace.TagInput = item.TagInput;
                        detailedPlace.DataFrom = item.DataFrom;
                        detailedPlace.Distance = distanceInMeters;

                        // Thay đổi distinctResults đòi hỏi đồng bộ hóa
                        lock (lockObj)
                        {
                            distinctResults[history.Index] = detailedPlace;
                        }
                    }
                }
            }
            return distinctResults
                .Where(x => x.Distance <= radiusInMeters)
                .OrderBy(x => x.Distance)
                .ToList();
        }

        /// <summary>
        /// Kiểm tra địa chỉ có đủ 3 phần tử sau khi loại bỏ "vietnam" và chuẩn hóa
        /// Có thể xử lý và gán lại thuộc tính cho object place
        /// </summary>
        private bool CheckValidAddressParts(PlacesEs place)
        {
            try
            {
                var existName = !string.IsNullOrWhiteSpace(place.Name) && !place.FullAddress.Contains(place.Name);
                // Tạo địa chỉ đầy đủ để kiểm tra
                var checkFullAddress = existName ? $"{place.Name}, {place.FullAddress}" : place.FullAddress;

                // Tách địa chỉ theo dấu phẩy
                var addressParts = checkFullAddress.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(part => part.Trim())
                    .Where(part => !string.IsNullOrWhiteSpace(part))
                    .ToList();

                // Chuẩn hóa và loại bỏ "vietnam"
                var filteredParts = addressParts
                    .Select(VietnameseAddressPermutator.RemoveVietnameseDiacritics)
                    .Where(normalizedPart =>
                    {
                        var compareTxt = normalizedPart.ToLower().Replace(" ", "");
                        return !compareTxt.Equals("vietnam") && !compareTxt.Equals("vn");
                    })
                    .ToList();

                // Kiểm tra thêm nếu placeName rỗng và phần tử đầu tiên không chứa số nhà
                bool hasSpecificAddress = true;
                //if (string.IsNullOrWhiteSpace(place.Name) && filteredParts.Count > 0)
                //{
                //    var firstPart = filteredParts[0];
                //    // Kiểm tra xem phần đầu tiên có chứa số nhà/địa chỉ cụ thể không
                //    hasSpecificAddress = System.Text.RegularExpressions.Regex.IsMatch(firstPart, @"\d");
                //}


                if (existName && place.FullAddress.StartsWith(place.Name!) && hasSpecificAddress)
                {
                    place.Name = null; // Đặt lại Name nếu nó đã được bao gồm trong FullAddress và phần tử đầu của dịa chỉ là số
                }
                // Có thể thêm logic xử lý và gán lại thuộc tính cho place ở đây
                // Ví dụ: chuẩn hóa địa chỉ, gán lại Name nếu cần, etc.

                // Kiểm tra có đủ 3 phần tử không
                return filteredParts.Count >= 3 && hasSpecificAddress;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Lỗi khi kiểm tra địa chỉ: PlaceId={PlaceId}, PlaceName={PlaceName}, FullAddress={FullAddress}",
                    place.PlaceId, place.Name, place.FullAddress);
                return true; // Trả về true để không loại bỏ kết quả nếu có lỗi
            }
        }

        #endregion
    }
}