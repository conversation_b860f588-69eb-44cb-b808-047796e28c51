using Microsoft.AspNetCore.Http;
using NDS.ETL.BackgroundJobs.Model;
using System.Globalization;

namespace NDS.ETL.BackgroundJobs.Services
{
    public interface IRequestInfoLogger
    {
        RequestInfo ExtractRequestInfo(HttpRequest request);
        void LogRequestInfo(RequestInfo requestInfo, string endpoint);
    }

    public class RequestInfoLogger(ILogger<RequestInfoLogger> logger) : IRequestInfoLogger
    {
        private static readonly Dictionary<string, string> HeaderMapping = new()
        {
            { "X-Request-Id", "RequestId" },
            { "X-Version", "Version" },
            { "X-Device-Type", "DeviceType" },
            { "X-Imei", "Imei" },
            { "X-Is-Root", "IsRoot" },
            { "X-Request-Time", "RequestTime" },
            { "X-Device-Model", "DeviceModel" },
            { "X-Screen-Size", "ScreenSize" },
            { "X-SDK-Version", "SdkVersion" },
            { "X-Taxi-Version", "TaxiVersion" },
            { "X-OS-Version", "OsVersion" },
            { "X-Lang", "Lang" },
            { "X-Username", "UserName" },
            { "X-Session-Id", "SessionId" },
            { "X-Client-Id", "ClientId" },
            { "X-Client-Ip", "ClientIp" },
            { "X-Region", "Region" },
            { "X-Latitude", "Lat" },
            { "X-Longitude", "Lng" },
            { "X-Tag", "Tag" },
            { "X-Phone-Number", "PhoneNumber" },
            { "X-Nonce", "Nonce" },
            { "X-Bank-Code", "BankCode" },
            { "X-User-Id", "UserId" }
        };

        /// <summary>
        /// Extracts request information from HTTP headers
        /// </summary>
        public RequestInfo ExtractRequestInfo(HttpRequest request)
        {
            var info = new RequestInfo();
            
            // Extract information from headers using reflection
            foreach (var (headerKey, propName) in HeaderMapping)
            {
                if (request.Headers.TryGetValue(headerKey, out var value))
                {
                    var stringValue = value.ToString();
                    var prop = typeof(RequestInfo).GetProperty(propName);
                    
                    if (prop != null && !string.IsNullOrEmpty(stringValue))
                    {
                        if (prop.PropertyType == typeof(double?))
                        {
                            // Parse double values for lat/lng
                            if (double.TryParse(stringValue, NumberStyles.Any, CultureInfo.InvariantCulture, out var doubleValue))
                            {
                                prop.SetValue(info, doubleValue);
                            }
                        }
                        else
                        {
                            // Set string value
                            prop.SetValue(info, stringValue);
                        }
                    }
                }
            }
            
            // Get client IP if not already set from headers
            if (string.IsNullOrEmpty(info.ClientIp))
            {
                info.ClientIp = request.HttpContext.Connection.RemoteIpAddress?.ToString();
            }
            
            return info;
        }

        /// <summary>
        /// Logs the request information with the endpoint that was accessed
        /// </summary>
        public void LogRequestInfo(RequestInfo requestInfo, string endpoint)
        {
            logger.LogInformation(
                "Request to {Endpoint} - {RequestInfo}",
                endpoint,
                requestInfo.ToString());
            
            // Log additional details at debug level
            logger.LogInformation(
                "Detailed request info - RequestId: {RequestId}, User: {User}, Device: {DeviceType}/{DeviceModel}, " +
                "SessionId: {SessionId}, ClientId: {ClientId}, ClientIp: {ClientIp}, Region: {Region}, " +
                "Location: [{Lat}, {Lng}]",
                requestInfo.RequestId,
                requestInfo.UserName,
                requestInfo.DeviceType,
                requestInfo.DeviceModel,
                requestInfo.SessionId,
                requestInfo.ClientId,
                requestInfo.ClientIp,
                requestInfo.Region,
                requestInfo.Lat,
                requestInfo.Lng);
        }
    }
}
