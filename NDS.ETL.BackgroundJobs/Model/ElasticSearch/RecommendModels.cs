using System.Text.Json.Serialization;

namespace NDS.ETL.BackgroundJobs.Model.ElasticSearch;

/// <summary>
/// <PERSON>ô hình dữ liệu cho địa điểm được gợi ý
/// </summary>
public class RecommendedPlace
{

    public string PlaceId { get; set; } = string.Empty;
    public string? Name { get; set; } = string.Empty;
    public string FullAddress { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public LocationDto Location { get; set; } = new LocationDto();
    public bool Active { get; set; } = true;
    public bool Popular { get; set; } = false;
    public bool IsFavorite { get; set; } = false;
    public double? Distance { get; set; } = 0;
    public int? Seq { get; set; }
    public string Note { get; set; } = string.Empty;
    public string Desc { get; set; } = string.Empty;
    public int QuoteCount { get; set; }
    public int BookingCount { get; set; }
    public DateTime? LastSearchAt { get; set; }

    /// <summary>
    /// <PERSON><PERSON><PERSON><PERSON> đổi từ PlacesEs sang RecommendedPlace
    /// </summary>
    public static RecommendedPlace FromPlacesEs(PlacesEs place)
    {
        return new RecommendedPlace
        {
            PlaceId = place.PlaceId,
            Name = place.Name,
            FullAddress = place.FullAddress,
            Address = place.Address ?? string.Empty,
            PhoneNumber = place.PhoneNumber ?? string.Empty,
            Location = new LocationDto
            {
                Lat = place.Location?.Lat ?? 0,
                Lon = place.Location?.Lon ?? 0
            },
            Active = place.Active,
            Popular = place.Popular,
            QuoteCount = place.QuoteCount,
            BookingCount = place.BookingCount,
            LastSearchAt = place.UpdatedAt
        };
    }

    public RecommendedPlace()
    {
        
    }

    public RecommendedPlace(RecommendedPlaceLocation location)
    {
        PlaceId = location.PlaceId;
        FullAddress = location.FullAddress;
        Address = location.Address;
        Location = location.Location;
        Distance = location.Distance;
        Active = true;
        Popular = false;
    }

    /// <summary>
    /// Chuyển đổi từ RecommendedPlaceLocation sang RecommendedPlace
    /// </summary>
    public static RecommendedPlace FromPlaceLocation(RecommendedPlaceLocation location)
    {
        return new RecommendedPlace(location);
    }
}

/// <summary>
/// Mô hình dữ liệu cho gợi ý tìm kiếm
/// </summary>
public class RecommendSearchItem
{
    public string Title { get; set; } = string.Empty;
    public List<RecommendedPlace> Places { get; set; } = new List<RecommendedPlace>();
}

/// <summary>
/// Mô hình dữ liệu cho vị trí sử dụng trong gợi ý lộ trình
/// </summary>
public class RecommendedPlaceLocation
{
    public string PlaceId { get; set; } = string.Empty;
    public LocationDto Location { get; set; } = new LocationDto();
    public string Address { get; set; } = string.Empty;
    public string FullAddress { get; set; } = string.Empty;
    public double Distance { get; set; }

    /// <summary>
    /// Chuyển đổi từ PlacesEs sang RecommendedPlaceLocation
    /// </summary>
    public static RecommendedPlaceLocation FromPlacesEs(PlacesEs place)
    {
        return new RecommendedPlaceLocation
        {
            PlaceId = place.PlaceId,
            Location = new LocationDto
            {
                Lat = place.Location?.Lat ?? 0,
                Lon = place.Location?.Lon ?? 0
            },
            Address = !string.IsNullOrWhiteSpace(place.Name) ? place.Name : place.Address ?? string.Empty,
            FullAddress = place.FullAddress,
            Distance = place.Distance
        };
    }
}

/// <summary>
/// Mô hình dữ liệu cho lộ trình được gợi ý
/// </summary>
public class RecommendedRoute
{
    public RecommendedPlaceLocation Origin { get; set; } = new RecommendedPlaceLocation();
    public RecommendedPlaceLocation Destination { get; set; } = new RecommendedPlaceLocation();
    public double? Distance { get; set; }
    public string EstimateAmount { get; set; } = string.Empty;
    public string Note { get; set; } = string.Empty;
    public string Desc { get; set; } = string.Empty;

    // Trường để sắp xếp thứ tự ưu tiên của lộ trình
    // Không trả về cho client
    [JsonIgnore]
    public int Seq { get; set; } = 0;
}

/// <summary>
/// DTO cho vị trí (latitude, longitude)
/// </summary>
public class LocationDto
{
    public double Lat { get; set; }
    public double Lon { get; set; }
}