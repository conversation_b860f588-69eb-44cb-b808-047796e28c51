namespace NDS.ETL.Infrastructure.Common
{
    /// <summary>
    /// Interface để thực hiện phân tán khóa giữa các instance
    /// </summary>
    public interface IDistributedLock
    {
        /// <summary>
        /// Cố gắng lấy khóa với thời gian hết hạn xác định
        /// </summary>
        /// <param name="key">Khóa duy nhất để xác định resource</param>
        /// <param name="expiry">Thời gian khóa tự động giải phóng nếu không gọi Release</param>
        /// <returns>True nếu lấy được khóa, false nếu đã có instance khác giữ khóa</returns>
        Task<bool> TryAcquireLockAsync(string key, TimeSpan expiry);

        /// <summary>
        /// Giải phóng khóa đã lấy trước đó
        /// </summary>
        /// <param name="key"><PERSON><PERSON><PERSON><PERSON> cần giải phóng</param>
        Task ReleaseLockAsync(string key);

        /// <summary>
        /// Giải phóng khóa bất kể owner là ai (sử dụng trong trường hợp khẩn cấp)
        /// </summary>
        /// <param name="key">Khóa cần giải phóng</param>
        Task ForceReleaseLockAsync(string key);

        /// <summary>
        /// Lấy thông tin về khóa hiện tại
        /// </summary>
        /// <param name="key">Khóa cần kiểm tra</param>
        /// <returns>Thông tin về khóa hoặc null nếu khóa không tồn tại</returns>
        Task<LockInfo> GetLockInfoAsync(string key);
    }

    /// <summary>
    /// Thông tin về khóa
    /// </summary>
    public class LockInfo
    {
        /// <summary>
        /// ID của instance sở hữu khóa
        /// </summary>
        public string OwnerId { get; set; }

        /// <summary>
        /// Thời gian tạo khóa
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Thời gian hết hạn khóa
        /// </summary>
        public DateTime ExpiryTime { get; set; }
    }
} 