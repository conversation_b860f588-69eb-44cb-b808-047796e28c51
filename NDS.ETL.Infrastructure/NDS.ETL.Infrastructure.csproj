<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Confluent.Kafka" Version="2.6.1" />
      <PackageReference Include="Elastic.Clients.Elasticsearch" Version="8.17.4" />
      <PackageReference Include="KnowledgeBase.Core" Version="1.0.12.1" />
      <PackageReference Include="NetTopologySuite.IO.GeoJSON" Version="4.0.0" />
      <PackageReference Include="NetTopologySuite.IO.PostGis" Version="2.1.0" />
      <PackageReference Include="pocketken.H3" Version="4.0.0" />
      <PackageReference Include="StackExchange.Redis" Version="2.8.37" />
    </ItemGroup>

</Project>
