using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Keyless]
[Table("danhmuc")]
public partial class Danhmuc
{
    [StringLength(50)]
    public string? Code { get; set; }

    [StringLength(1000)]
    public string? Name { get; set; }

    [StringLength(50)]
    public string? ParentCode { get; set; }

    [StringLength(1000)]
    public string? Description { get; set; }

    [StringLength(50)]
    public string? ClosedCodeAt { get; set; }

    [StringLength(50)]
    public string? Type { get; set; }

    [StringLength(1000)]
    public string? OldCode { get; set; }

    [Column("DistrictCode(Disable)")]
    [StringLength(1000)]
    public string? DistrictCodeDisable { get; set; }

    [StringLength(50)]
    public string? ProvinceCode { get; set; }

    [StringLength(1000)]
    public string? AreaCode { get; set; }

    [StringLength(50)]
    public string? WardCode { get; set; }

    [StringLength(50)]
    public string? ActiveAt { get; set; }

    [StringLength(50)]
    public string? CancelAt { get; set; }

    [StringLength(50)]
    public string? ModifiedAt { get; set; }

    [StringLength(50)]
    public string? CreatedAt { get; set; }

    [StringLength(50)]
    public string? InssuanceAt { get; set; }

    [StringLength(1000)]
    public string? Creator { get; set; }

    public int? Status { get; set; }
}
