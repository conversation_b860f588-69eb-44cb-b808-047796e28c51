{
  "PathBase": "/backgroundjob",
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore.Hosting.Diagnostics": "Warning",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft": "Warning",
      "Hangfire": "Warning",
      "Microsoft.Hosting.Lifetime": "Warning",
      "Microsoft.EntityFrameworkCore": "Warning",
      "Microsoft.EntityFrameworkCore.Database.Command": "Warning",
      "Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker": "Warning"
    }
  },
  "ElasticSearchConfig": {
    "Index": [
      //"place-dev",
      //"fav-place-dev",
      //"user-place-dev",
      //"place-pair-dev"

      "place-sit2",
      "fav-place-sit",
      "user-place-sit",
      "place-pair-sit"

      //"place-uat2",
      //"fav-place-uat",
      //"user-place-uat",
      //"place-pair-uat"
    ],
    "LogIndex": "place-log-2",
    "Url": [
      //"https://*************:9200/" //SIT
      "http://localhost:9201/" //UAT
      //"http://localhost:9300/" //LOCAL
    ],
    "UserName": "elastic",
    "Password": "" //UAT
    //"Password": "Exg9m+v9QvEFf4MoczWw" //SIT
    //"Password": "nopass" //LOCAL
  },
  "KafkaConfig": {
    "Disable": "true",
    "Connection": "************:6091,************:6092,************:6093",
    "GroupSyncPlaces": "sync_es_places_gr",
    "TopicSyncPlaces": "sync_es_places"
  }
}