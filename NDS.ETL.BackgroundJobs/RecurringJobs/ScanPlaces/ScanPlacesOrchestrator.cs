using System.Diagnostics;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Context;
using NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.ESTools;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Entities.Context;
using NDS.ETL.Infrastructure.Common;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.ScanPlaces;

public class ScanPlacesOrchestrator : RecurringJobBase<PgPlaceContext>
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ScanPlacesOrchestrator> _logger;
    private readonly IScopedResolver<NpgPlaceContext> _dbContextResolver;
    private readonly IServiceProvider _serviceProvider;

    public ScanPlacesOrchestrator(IConfiguration configuration,
        ILogger<ScanPlacesOrchestrator> logger,
        IScopedResolver<NpgPlaceContext> dbContextResolver,
        IServiceProvider serviceProvider)
        : base(configuration, logger, null)
    {
        _logger = logger;
        _dbContextResolver = dbContextResolver;
        _serviceProvider = serviceProvider;
    }

    public override string JobId => "ScanPlacesOrchestrator";

    public override string ConfigSection => "JobPgSyncPlace";

    public override async Task DoWorkAsync(CancellationToken stoppingToken)
    {
        var begin = Stopwatch.GetTimestamp();
        _logger.LogInformation("ScanPlacesOrchestrator started - Beginning sequential job execution");

        try
        {
            await ExecuteJobSequence(stoppingToken);

            var end = Stopwatch.GetTimestamp();
            _logger.LogInformation(
                "ScanPlacesOrchestrator completed successfully in {TotalTime:F2} seconds",
                TimeSpan.FromTicks(end - begin).TotalSeconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred in ScanPlacesOrchestrator");
            throw;
        }
    }

    private async Task ExecuteJobSequence(CancellationToken stoppingToken)
    {
        // Step 1: Execute ScanPlaces and SyncDataScannedToEs loop until no more records
        await ExecuteScanningLoop(stoppingToken);
        
        // Step 2: Execute remaining jobs sequentially
        var remainingJobs = new[]
        {
            ("InsertPlacesScanResult", typeof(InsertPlacesScanResultTask)),
            ("SyncPlaceScanResult2", typeof(SyncPlaceScanResultJobs2)),
            ("ProcessDuplicateAddress", typeof(ProcessDuplicateAddressTask))
        };

        foreach (var (jobName, jobType) in remainingJobs)
        {
            if (stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("Cancellation requested, stopping job sequence");
                break;
            }

            var jobStopwatch = Stopwatch.StartNew();
            _logger.LogInformation("Starting job: {JobName}", jobName);

            try
            {
                if (jobType is Type type)
                {
                    await ExecuteJobByType(type, stoppingToken);
                }
                jobStopwatch.Stop();
                _logger.LogInformation(
                    "Job {JobName} completed successfully in {ElapsedSeconds:F2} seconds",
                    jobName, jobStopwatch.Elapsed.TotalSeconds);
            }
            catch (Exception ex)
            {
                jobStopwatch.Stop();
                _logger.LogError(ex, 
                    "Job {JobName} failed after {ElapsedSeconds:F2} seconds. Stopping orchestrator.",
                    jobName, jobStopwatch.Elapsed.TotalSeconds);
                throw;
            }
        }
    }

    private async Task ExecuteScanningLoop(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Starting ScanPlaces and SyncDataScannedToEs loop");
        
        int loopIteration = 0;
        const int maxIterations = 100;
        
        while (loopIteration < maxIterations)
        {
            if (stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("Cancellation requested, stopping scanning loop");
                break;
            }

            loopIteration++;
            _logger.LogInformation("Scanning loop iteration {Iteration}", loopIteration);

            bool hasRecordsToProcess = await CheckForRecordsToProcess(stoppingToken);
            if (!hasRecordsToProcess)
            {
                _logger.LogInformation("No more records to process. Scanning loop completed after {Iterations} iterations.", loopIteration - 1);
                break;
            }

            var scanStopwatch = Stopwatch.StartNew();
            _logger.LogInformation("Executing ScanPlaces (iteration {Iteration})", loopIteration);
            
            try
            {
                await ExecuteJobByType(typeof(ScanPlacesTask), stoppingToken);
                scanStopwatch.Stop();
                _logger.LogInformation("ScanPlaces completed in {ElapsedSeconds:F2} seconds", scanStopwatch.Elapsed.TotalSeconds);
            }
            catch (Exception ex)
            {
                scanStopwatch.Stop();
                _logger.LogError(ex, "ScanPlaces failed in iteration {Iteration} after {ElapsedSeconds:F2} seconds", 
                    loopIteration, scanStopwatch.Elapsed.TotalSeconds);
                throw;
            }

            var syncStopwatch = Stopwatch.StartNew();
            _logger.LogInformation("Executing SyncDataScannedToEs (iteration {Iteration})", loopIteration);
            
            try
            {
                await ExecuteJobByType(typeof(SyncDataScannedToEsTask), stoppingToken);
                syncStopwatch.Stop();
                _logger.LogInformation("SyncDataScannedToEs completed in {ElapsedSeconds:F2} seconds", syncStopwatch.Elapsed.TotalSeconds);
            }
            catch (Exception ex)
            {
                syncStopwatch.Stop();
                _logger.LogError(ex, "SyncDataScannedToEs failed in iteration {Iteration} after {ElapsedSeconds:F2} seconds", 
                    loopIteration, syncStopwatch.Elapsed.TotalSeconds);
                throw;
            }
        }

        if (loopIteration >= maxIterations)
        {
            _logger.LogWarning("Scanning loop reached maximum iterations ({MaxIterations}). Stopping to prevent infinite loop.", maxIterations);
        }
    }

    private async Task<bool> CheckForRecordsToProcess(CancellationToken stoppingToken)
    {
        try
        {
            return await _dbContextResolver.ResolveAsync(async dbContext =>
            {
                var hasRecords = await dbContext.PlacesQueues
                    .Where(x => x.Status == 2)
                    .AnyAsync(stoppingToken);

                if (hasRecords)
                {
                    var placeQueueIds = await dbContext.PlacesQueues
                        .Where(x => x.Status == 2)
                        .Select(x => x.Id)
                        .ToListAsync(stoppingToken);

                    hasRecords = await dbContext.EsQueues
                        .Where(x => placeQueueIds.Contains(x.PlacesQueueId) && x.Status == 1)
                        .AnyAsync(stoppingToken);
                }

                return hasRecords;
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking for records to process");
            return false;
        }
    }

    private async Task ExecuteJobByType(Type jobType, CancellationToken stoppingToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var job = scope.ServiceProvider.GetRequiredService(jobType);
        
        if (job is RecurringJobBase<PgPlaceContext> recurringJob)
        {
            await recurringJob.DoWorkAsync(stoppingToken);
        }
        else if (job is RecurringJobBaseElastic elasticJob)
        {
            await elasticJob.DoWorkAsync(stoppingToken);
        }
        else
        {
            throw new InvalidOperationException($"Job type {jobType.Name} is not a valid RecurringJobBase");
        }
    }

    private async Task ExecuteHangfireJob(string jobId, CancellationToken stoppingToken)
    {
        _logger.LogInformation("Triggering Hangfire job: {JobId}", jobId);
        
        BackgroundJob.Enqueue(() => RecurringJob.TriggerJob(jobId));
        
        await WaitForHangfireJobCompletion(jobId, stoppingToken);
    }

    private async Task WaitForHangfireJobCompletion(string jobId, CancellationToken stoppingToken)
    {
        const int maxWaitMinutes = 60;
        const int checkIntervalSeconds = 30;
        var maxWaitTime = TimeSpan.FromMinutes(maxWaitMinutes);
        var checkInterval = TimeSpan.FromSeconds(checkIntervalSeconds);
        var startTime = DateTime.UtcNow;

        _logger.LogInformation("Waiting for Hangfire job {JobId} to complete (max wait: {MaxWaitMinutes} minutes)", 
            jobId, maxWaitMinutes);

        while (DateTime.UtcNow - startTime < maxWaitTime)
        {
            if (stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("Cancellation requested while waiting for job {JobId}", jobId);
                break;
            }

            await Task.Delay(checkInterval, stoppingToken);
            
            var isJobRunning = await IsHangfireJobRunning(jobId);
            if (!isJobRunning)
            {
                _logger.LogInformation("Hangfire job {JobId} has completed", jobId);
                return;
            }
        }

        _logger.LogWarning("Hangfire job {JobId} did not complete within {MaxWaitMinutes} minutes", 
            jobId, maxWaitMinutes);
    }

    private async Task<bool> IsHangfireJobRunning(string jobId)
    {
        try
        {
            return await _dbContextResolver.ResolveAsync(async dbContext =>
            {
                var runningJobs = await dbContext.Database
                    .SqlQueryRaw<string>("SELECT data FROM hangfire.job WHERE statename = 'Processing'")
                    .ToListAsync();

                return runningJobs.Any(jobData => jobData.Contains(jobId));
            });
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Could not check Hangfire job status for {JobId}", jobId);
            return false;
        }
    }

    public override Task IndexMany<T>(List<T> source, CancellationToken cancellationToken, int take = 1000)
    {
        return Task.CompletedTask;
    }

    public override Task IndexMany<T>(List<T> source, CancellationToken cancellationToken, string esIndex, int take = 1000)
    {
        return Task.CompletedTask;
    }
} 