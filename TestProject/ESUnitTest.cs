using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Core.Search;
using Elastic.Transport;
using NDS.ETL.BackgroundJobs.Extensions;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.Infrastructure.Extensions;
using System.Diagnostics;
using System.Text.Json;

namespace TestProject
{
    public class ESUnitTest
    {
        private ElasticsearchClient _elasticClient;

        private string _testIndex = "place-sit";
        // private string _testIndex = "place-dev";
        private ElasticSearchTestSetup _testSetup;

        // Thông tin xác thực ElasticSearch
        //LOCAL
        //private readonly string _elasticSearchUrl = "http://localhost:9300"; // Đổi port từ 9200 sang 9300
        //private readonly string _elasticSearchUsername = "elastic"; // Tên đăng nhập
        //private readonly string _elasticSearchPassword = "nopass"; // Mật khẩu

        // UAT-SIT
        private readonly string _elasticSearchUrl = "https://*************:9200"; // Đổi port từ 9200 sang 9300
        private readonly string _elasticSearchUsername = "elastic"; // Tên đăng nhập
        private readonly string _elasticSearchPassword = "Exg9m+v9QvEFf4MoczWw"; // Mật khẩu

        [OneTimeSetUp]
        public async Task OneTimeSetUp()
        {
            // Thiết lập kết nối ElasticSearch dùng cho test với Basic Authentication
            var settings = new ElasticsearchClientSettings(new Uri(_elasticSearchUrl))
                .DefaultIndex(_testIndex)
                // Thêm Basic Authentication
                .Authentication(new BasicAuthentication(_elasticSearchUsername, _elasticSearchPassword))
                // Bỏ qua xác thực SSL nếu sử dụng chứng chỉ tự ký
                .ServerCertificateValidationCallback((sender, cert, chain, errors) => true)
                // Cấu hình debug mode cho việc kiểm tra lỗi
                .EnableDebugMode()
                .DisableDirectStreaming()
                .RequestTimeout(TimeSpan.FromMinutes(2));

            _elasticClient = new ElasticsearchClient(settings);

            // Thiết lập môi trường test
            _testSetup = new ElasticSearchTestSetup(_elasticClient, _testIndex);
            Console.WriteLine("Đang thiết lập môi trường test...");
            //var success = await _testSetup.SetupTestEnvironment(); //Không chạy nếu không muốn xoá index

            //if (!success)
            //{
            //    Assert.Fail("Không thể thiết lập môi trường test cho ElasticSearch");
            //}

            Console.WriteLine("Đã thiết lập môi trường test thành công");
        }

        [SetUp]
        public void Setup()
        {
            // Các thiết lập riêng cho từng test
        }

        /// <summary>
        /// Test chức năng autocomplete tìm kiếm địa chỉ
        /// </summary>
        [Test]
        public async Task TestAutocompleteSearch()
        {
            if (!await TestConnection()) return;

            // // Tọa độ tham khảo (Hà Nội)
            var currentLocation = GeoLocation.LatitudeLongitude(new LatLonGeoLocation { Lat = 21.021673, Lon = 105.803715 });
            // Tọa độ tham khảo (HCM)
            //var currentLocation = GeoLocation.LatitudeLongitude(new LatLonGeoLocation { Lat = 10.834121, Lon = 106.649323 });
            // Normalize search terms
            var searchTerms = new[]
            {
                // AddressPermutator.NormalizeVietnamese("nguyen thai son"), // Đường Nguyễn Thái Sơn
                // AddressPermutator.NormalizeVietnamese("ba thang 2"), // Đường Ba Tháng Hai
                // AddressPermutator.NormalizeVietnamese("quan 10"), // Quận 10
                // AddressPermutator.NormalizeVietnamese("lang ha"), // Láng Hạ
                // AddressPermutator.NormalizeVietnamese("coffee") // Tìm địa điểm cà phê
                //AddressPermutator.NormalizeVietnamese("22 lang ha"),
                 //AddressPermutator.NormalizeVietnamese("Sân Bay Quốc Tế Nội Bài (HAN)"),
                AddressPermutator.NormalizeVietnamese("lăng bác") 
                //AddressPermutator.NormalizeVietnamese("house coffee"), // Tìm địa điểm ở Hồ Chí Minh
            };

            foreach (var term in searchTerms)
            {
                Console.WriteLine($"\n===== Tìm kiếm autocomplete cho từ khóa: '{term}' =====");

                try
                {
                    //await AutoCompleteMethod1(currentLocation, term);


                    await AutoCompleteMethod2(currentLocation, term);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Lỗi ngoại lệ: {ex.Message}");
                    Console.WriteLine(ex.StackTrace);
                }
            }

            Assert.Pass("Test autocomplete đã hoàn thành");
        }

        private async Task AutoCompleteMethod2(GeoLocation currentLocation, string term)
        {
            // 2. Sử dụng Extension method
            Console.WriteLine("\n--- Phương pháp 2: Extension Method ---");
            var stopwatch = Stopwatch.StartNew(); // Bắt đầu đo thời gian
            var searchRequest = _elasticClient.CreateAutocompleteQuery(1, currentLocation, term, 5, _testIndex);
            var response = await _elasticClient.SearchAsync<PlacesEs>(searchRequest);
            stopwatch.Stop(); // Dừng đo thời gian
            Console.WriteLine($"Thời gian xử lý: {stopwatch.ElapsedMilliseconds} ms");

            if (response.IsValidResponse)
            {
                IReadOnlyCollection<ISuggest>? suggestions = null;
                response.Suggest?.TryGetValue("suggest", out suggestions);
                var totalSuggestCount = suggestions?.Any() != null ? ((CompletionSuggest<PlacesEs>)suggestions.First()).Options.Count : 0;

                Console.WriteLine($"Tìm thấy {response.Hits.Count} kết quả trực tiếp và " +
                                  $"{totalSuggestCount} gợi ý:");

                if (totalSuggestCount > 0)
                {
                    foreach (var suggestion in ((IList<CompletionSuggest<PlacesEs>>)suggestions)!)
                    {
                        foreach (var option in suggestion.Options)
                        {
                            if (option.Source != null)
                            {
                                Console.WriteLine($"- (Gợi ý) {option.Source.MasterAddress} ({GetDistanceStr((JsonElement)option.Fields!["distance"])} km)");
                                PrintSubPlace(option.Source.SubPlaces);
                            }
                        }
                    }
                }

                foreach (var hit in response.Hits)
                {
                    Console.WriteLine($"- {hit.Source.MasterAddress} ({GetDistanceStr((JsonElement)hit.Fields!["distance"])} km)");
                    PrintSubPlace(hit.Source.SubPlaces);
                }
            }
            Console.WriteLine($"Debug tìm kiếm extension: {response.DebugInformation}");
        }

        private static void PrintSubPlace(List<SubPlacesEs> items)
        {
            if (items?.Any() == true)
            {
                items.ForEach(es =>
                {
                    Console.WriteLine($"  *** {es.Name})");
                });
            }
        }

        public static string GetDistanceStr(JsonElement jsonElement)
        {
            var distArr = jsonElement.EnumerateArray();
            var distanceStr = string.Join(" ", distArr.Select(d => $"{d.GetDouble():0.###}"));
            return distanceStr;
        }


        private async Task<SearchResponse<PlacesEs>> AutoCompleteMethod1(GeoLocation currentLocation, string term)
        {
            // Sử dụng ba cách tìm kiếm autocomplete khác nhau từ ví dụ
            // 1. Sử dụng API Fluent (Direct usage)
            Console.WriteLine("\n--- Phương pháp 1: API Fluent ---");
            var stopwatch = Stopwatch.StartNew(); // Bắt đầu đo thời gian

            var response = await _elasticClient.SearchAsync<PlacesEs>(s => s
                .Index(_testIndex)
                .Size(5)
            #region Suggest
                .Suggest(s =>
                    s.Text(term)
                        .Suggesters(g =>
                        {
                            return g.Add("suggest", sg =>
                            {
                                sg.Completion(c => c
                                    .Field(f => f.AddressPermuted.Suffix("completion"))
                                    .Analyzer("address_search")
                                    .Size(5)
                                    .SkipDuplicates(true)
                                    .Fuzzy(f => f
                                        .Fuzziness(new Fuzziness("AUTO"))
                                        .MinLength(5)
                                        .PrefixLength(1)
                                        .Transpositions(true)
                                        .UnicodeAware(true)
                                    )
                                //.Contexts(c => c
                                //    .Add(new Field("city"),
                                //        new List<CompletionContext>
                                //        {
                                //            new()
                                //            {
                                //                Context = new Context("ha noi")
                                //            }
                                //        })
                                //)
                                );
                            });
                        })
                )
            #endregion
                //.Query(q => q
                //    .Bool(b => b
                //        .Should(
                //            sh => sh.Match(m => m
                //                .Field(f => f.AddressPermuted)
                //                .Query(term)
                //                .Analyzer("address_search")
                //            ),
                //            sh => sh.Prefix(p => p
                //                .Field(f => f.AddressPermuted)
                //                .Value(term)
                //            ),
                //            sh => sh.MatchPhrasePrefix(mp => mp
                //                .Field(f => f.AddressPermuted)
                //                .Query(term)
                //            )
                //        )
                //        .Filter(f => f
                //            .GeoDistance(g => g
                //                .Field(f => f.Location)
                //                .Distance("50km")
                //                .Location(currentLocation)
                //            )
                //        )
                //    )
                //)
                .Sort(so => so
                        .GeoDistance(g => g
                            .Field(f => f.Location)
                            .Location(new[] { currentLocation })
                            .Order(SortOrder.Asc)
                        )
                        .Field(p => p.Processed, _ => _.Order(SortOrder.Desc))
                )
            );
            stopwatch.Stop(); // Dừng đo thời gian
            Console.WriteLine($"Thời gian xử lý: {stopwatch.ElapsedMilliseconds} ms");

            if (response.IsValidResponse)
            {
                Console.WriteLine($"Tìm thấy {response.Hits.Count} kết quả trực tiếp và " +
                                  $"{(response.Suggest != null ? response.Suggest.Values.Count() : 0)} gợi ý:");

                foreach (var hit in response.Hits)
                {
                    Console.WriteLine($"- {hit.Source.MasterAddress}");
                }

                if (response.Suggest != null && response.Suggest.TryGetValue("suggest", out var suggestions))
                {
                    foreach (var suggestion in (IList<CompletionSuggest<PlacesEs>>)suggestions)
                    {
                        foreach (var option in suggestion.Options)
                        {
                            if (option.Source != null)
                            {
                                Console.WriteLine($"- (Gợi ý) {option.Source.MasterAddress}");
                            }
                        }
                    }
                }
            }
            //else
            //{
            Console.WriteLine($"Debug tìm kiếm: {response.DebugInformation}");
            //}

            return response;
        }


        private async Task<bool> TestConnection()
        {
            // Kiểm tra kết nối ElasticSearch
            var pingResult = await _elasticClient.PingAsync();
            if (!pingResult.IsValidResponse)
            {
                Assert.Fail("Không thể kết nối đến ElasticSearch server. Chi tiết lỗi: " + pingResult.DebugInformation);
                return false;
            }

            Console.WriteLine("\nĐã kết nối đến ElasticSearch thành công");
            return true;
        }

    }
}