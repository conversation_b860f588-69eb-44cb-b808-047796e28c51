using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("sys_no")]
public partial class SysNo
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("typeCode")]
    public int TypeCode { get; set; }

    [Column("value")]
    public long Value { get; set; }

    [Column("tableName")]
    [StringLength(50)]
    public string? TableName { get; set; }
}
