using Elastic.Clients.Elasticsearch.Mapping;
using KnowledgeBase.Core.Extensions;
using NDS.ETL.Entities.TaxiMongoDB;
using NDS.ETL.Infrastructure.Extensions;

namespace NDS.ETL.BackgroundJobs.Model.ElasticSearch;

public class UserSearchHistoryEs : IElasticSearchModel
{
    public string Id { get; set; }

    public string CustomerMobileNo { get; set; }

    public string Keyword { get; set; }

    public string PlaceId { get; set; }

    public string Name { get; set; }

    public string Address { get; set; }

    public string FullAddress { get; set; }

    public string MasterAddress
    {
        get
        {
            string str = Address;
            if (string.IsNullOrWhiteSpace(str))
            {
                str = FullAddress;
            }
            if (!string.IsNullOrWhiteSpace(Name) && !str.ToUnsign().Contains(Name.ToUnsign()))
            {
                str = Name + ", " + str;
            }

            AddressPermuted = AddressPermutator.GenerateOptimizedPermutations(str, 15);
            return str;
        }
    }

    public List<string> AddressPermuted { get; set; }

    public string Keywords { get; set; }

    public Location? Location { get; set; }

    public DateTime CreatedAt { get; set; }

    public static void MappingProperties(PropertiesDescriptor<UserSearchHistoryEs> d)
    {
        d.Keyword(k => k.Id)
            .Text(t => t.CustomerMobileNo,
                t =>
                    t.Analyzer("icu")
                        .Fields(ff => ff
                            .Keyword("keyword",
                                st => st.IgnoreAbove(256))
                        ))
            .Keyword(k => k.Keyword)
            .Keyword(k => k.PlaceId)
            .Keyword(k => k.Name)
            .Text(t => t.Address,
                t =>
                    t.Analyzer("icu")
                        .Fields(ff => ff
                            .Keyword("keyword",
                                st => st.IgnoreAbove(256))
                        ))
            .Text(t => t.FullAddress,
                t =>
                    t.Analyzer("icu")
                        .Fields(ff => ff
                            .Keyword("keyword",
                                st => st.IgnoreAbove(256))
                        ))
            .GeoPoint(g => g.Location)
            .Date(k => k.CreatedAt);
    }

    public static List<UserSearchHistoryEs> MapToEsDocuments(List<UserSearchHistory> data)
    {
        return data.Select(t => new UserSearchHistoryEs
        {
            Id = t.Id.ToString(),
            Name = t.Name,
            PlaceId = t.PlaceId,
            Address = t.Address,
            FullAddress = t.FullAddress,
            CreatedAt = t.CreatedAt.ToLocalTime(),
            CustomerMobileNo = t.CustomerMobileNo,
            Keyword = t.Keyword,
            Location = new Location
            {
                Lat = t.PlaceLocation?.Lat ?? 0.0,
                Lon = t.PlaceLocation?.Lng ?? 0.0
            }
        }).ToList();
    }
}
