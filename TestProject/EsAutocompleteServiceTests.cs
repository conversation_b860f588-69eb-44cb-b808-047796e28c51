using Confluent.Kafka;
using Elastic.Clients.Elasticsearch;
using Elastic.Transport;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Entities.Context;
using NDS.ETL.Infrastructure.Common;
using NDS.ETL.Infrastructure.Extensions; // Add this using directive for Stopwatch
using NDS.ETL.Infrastructure.Utils;
using Newtonsoft.Json; // Add this using directive for Stopwatch
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Linq;
using System.Text;

namespace TestProject
{
    [TestFixture]
    public class EsAutocompleteServiceTests
    {
        private Mock<IConfiguration> _mockConfig;
        private Mock<IConfigurationSection> _mockConfigSection;
        private Mock<ILogger<ESAutocompleteService>> _mockLogger;
        private ElasticSearchTestSetup _testSetup;
        private ElasticsearchClient _elasticClient;
        private ESAutocompleteService _service;
        private ElasticSearchConfig _elasticSearchConfig;
        private Mock<IESClientFactory> _mockElasticSearchClientFactory;
        private string _testIndex = "place-sit";

        //[OneTimeSetUp]
        //public async Task OneTimeSetUp()
        //{
        //    // Setup test environment with sample data
        //    // Bỏ qua nếu không muốn xoá dữ liệu
        //    //_testSetup = new ElasticSearchTestSetup(_elasticClient, _testIndex);
        //    //var success = await _testSetup.SetupTestEnvironment();
        //    //if (!success)
        //    //{
        //    //    Assert.Fail("Failed to set up test environment for ElasticSearch");
        //    //}
        //}

        [SetUp]
        public void Setup()
        {
            // Load configuration from appsettings.json
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            // Bind ElasticSearchConfig from appsettings.json
            _elasticSearchConfig = configuration.GetSection("ElasticSearchConfig").Get<ElasticSearchConfig>()!;

            var url = _elasticSearchConfig!.Url.First();
            var settings = new ElasticsearchClientSettings(new Uri(url))
                .DefaultIndex(_testIndex)
                .Authentication(new BasicAuthentication(_elasticSearchConfig!.UserName!, _elasticSearchConfig.Password!))
                .ServerCertificateValidationCallback((sender, cert, chain, errors) => true)
                .EnableDebugMode()
                .DisableDirectStreaming()
                .RequestTimeout(TimeSpan.FromSeconds(30));

            _elasticClient = new ElasticsearchClient(settings);

            // Setup mock factory to return the ElasticsearchClient and index names
            _mockElasticSearchClientFactory = new Mock<IESClientFactory>();
            _mockElasticSearchClientFactory.Setup(f => f.GetClient()).Returns(_elasticClient);
            _mockElasticSearchClientFactory.Setup(f => f.GetPlaceIndexName()).Returns(_testIndex);
            _mockElasticSearchClientFactory.Setup(f => f.GetPlacePairIndexName()).Returns(_elasticSearchConfig.Index.First(x => x.StartsWith("place-pair")));
            _mockElasticSearchClientFactory.Setup(f => f.GetPlaceFavoriteIndexName()).Returns(_elasticSearchConfig.Index.First(x => x.StartsWith("fav-place")));

            // Configure logging for tests
            var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder
                    .AddConsole()
                    .AddDebug()
                    .SetMinimumLevel(LogLevel.Information);
            });
            // Mock logger
            _mockLogger = new Mock<ILogger<ESAutocompleteService>>();
            var consoleLogger = loggerFactory.CreateLogger<ESAutocompleteService>();

            // Mock ESPlacesService với logger đúng type
            var mockPlacesService = new Mock<ESPlacesService>(
                _elasticClient,
                configuration,
                Mock.Of<IScopedResolver<NpgPlaceContext>>(),
                Mock.Of<ISearchConfigService>(),
                loggerFactory.CreateLogger<ESPlacesService>()
            );

            // Mock ESPlacesService với logger đúng type
            var mockPlacesDetailsService = new Mock<ESPlaceDetailsService>(
                mockPlacesService.Object,
                loggerFactory.CreateLogger<ESPlaceDetailsService>()
            );

            // Tạo service instance sử dụng mock factory thay vì configuration
            _service = new ESAutocompleteService(_mockElasticSearchClientFactory.Object, mockPlacesDetailsService.Object, consoleLogger);
        }

        [Test]
        //[TestCase("", "0984086431", "Từ khóa test", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "12 trang", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "20 ngõ 100 Trần", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "20/100 Trần", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "3 ngách 12 ngõ 105 Xuân", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "3/12/105 xuân", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "52/3 tây", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Hồ g", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Hồ guo", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "hồ gươm", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Phố cổ", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Phố cổ Hà", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Lăng", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Lăng Bác", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Lăng chủ tịch", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Quốc tự giám", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Văn Miếu", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "keangnam", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "72 landmark", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "lotte", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "lotte center", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "vincom bà", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "hadico", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "handico tower", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "toyata mỹ", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "T5 time", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "time", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "royal", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "vinhome gardenia", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "linh đàm", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "HH5 linh", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Mipec long biên", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "reiverside long biên", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "vincom", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Nguyễn văn cừ", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Chợ đồng", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Bến xe", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Giáp bát", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Mỹ đình", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Chợ long biên", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Bệnh viện", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Bạch mai", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Khách sạn", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Khách sạn daewoo", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Nhà hàng quán", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Nhà hàng quán ăn ngon", 21.021800211406582, 105.80387974060552, 50, 10)]


        [TestCase("", "0984086431", "!30 trần", 21.021800211406582, 105.80387974060552, null, 10)] // H3 resolution 10 ~ bán kính 65m
        [TestCase("", "0984086431", "22 L", 21.021800211406582, 105.80387974060552, null, 10)] // H3 resolution 10 ~ bán kính 65m
        [TestCase("", "0984086431", "22 Lê", 21.021800211406582, 105.80387974060552, null, 10)] // H3 resolution 10 ~ bán kính 65m
        [TestCase("", "0984086431", "22 Láng", 21.021800211406582, 105.80387974060552, null, 10)] // H3 resolution 10 ~ bán kính 65m
        [TestCase("", "0984086431", "30 Tr", 21.021800211406582, 105.80387974060552, null, 10)] // H3 resolution 10 ~ bán kính 65m
        [TestCase("", "0984086431", "Iris", 21.021800211406582, 105.80387974060552, null, 10)] // H3 resolution 10 ~ bán kính 65m
        [TestCase("", "0984086431", "22 Làng", 21.021800211406582, 105.80387974060552, null, 10)] // H3 resolution 10 ~ bán kính 65m
        [TestCase("", "0984086431", "Hạ Láng", 21.021800211406582, 105.80387974060552, null, 10)] // H3 resolution 10 ~ bán kính 65m
        [TestCase("", "0984086431", "12 thái", 21.021800211406582, 105.80387974060552, null, 10)] // H3 resolution 10 ~ bán kính 65m
        [TestCase("", "0984086431", "Sân vận động", 21.021800211406582, 105.80387974060552, null, 10)] // H3 resolution 10 ~ bán kính 65m
        [TestCase("", "0984086431", "Sân bay nọi bài", 21.021800211406582, 105.80387974060552, null, 10)] // H3 resolution 10 ~ bán kính 65m
        [TestCase("", "0984086431", "san bay noi bai", 21.021800211406582, 105.80387974060552, null, 10)] // H3 resolution 10 ~ bán kính 65m
        [TestCase("", "0984086431", "22Láng", 21.021800211406582, 105.80387974060552, null, 10)] // H3 resolution 10 ~ bán kính 65m
        [TestCase("", "0984086431", "Số 21, Ngách 51/128C Đại La", 21.021800211406582, 105.80387974060552, null, 10)] // H3 resolution 10 ~ bán kính 65m
        public async Task CallMapsServiceApi_ShouldReturnSuccessStatusCode(string userId, string phoneNumber, string query, double latitude, double longitude, int? h3Resolution, int limit)
        {
            var _client = new HttpClient();
            _client.DefaultRequestHeaders.Add("Client-Secret", "48c4b99727972be6eb7ae2558e94b36b");
            _client.DefaultRequestHeaders.Add("Client", "vntaxi");
            // Arrange
            var url = "http://************:6004/maps-service/api/maps/v2/places";

            var json = $@"{{
                ""requestId"": ""1742787955650.087891B787"",
                ""version"": ""2.36.1-25"",
                ""deviceType"": ""iOS"",
                ""imei"": ""658A5A14-EF26-4752-9F53-A49CDF44A01A"",
                ""requestTime"": ""1742787955645"",
                ""deviceModel"": ""iPhone"",
                ""screenSize"": ""414*736"",
                ""sdkVersion"": ""6.13.10"",
                ""taxiVersion"": ""6.13.10"",
                ""osVersion"": ""15.8.3"",
                ""lang"": ""vi"",
                ""userName"": ""0964696043"",
                ""sessionId"": ""7fbb344c7ae3433997c90be29a8296fb"",
                ""clientId"": ""BACKEND"",
                ""clientIp"": ""***********"",
                ""region"": ""hanoi"",
                ""lat"": {latitude},
                ""lng"": {longitude},
                ""phoneNumber"": ""{phoneNumber}"",
                ""autoComplete"": true,
                ""keyword"": ""{query}"",
                ""type"": 4,
                ""location"": {{
                    ""lat"": {latitude},
                    ""lng"": {longitude}
                }},
                ""h3Resolution"": {h3Resolution}, // H3 resolution thay cho distance
                ""pageIndex"": 0,
                ""pageSize"": 20
            }}";


            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync(url, content);

            // Assert
            response.EnsureSuccessStatusCode(); // throws if not 2xx
            var responseBody = await response.Content.ReadAsStringAsync();

            var places = JsonConvert.DeserializeObject<List<PlaceTestDto>>(responseBody);
            //
            foreach (var item in places)
            {
                //Console.WriteLine($" - {item.Address + " - "+  item.FullAddress} - ({item.DataFrom}) - (Score: {item.Score})");
                Console.WriteLine($" - {item.Address + " - " + item.FullAddress} - ({item.KindOfPlace}) - (Score: {item.Score})");
                // Log subplaces if they exist
                if (item.SubPlaces?.Any() == true)
                {
                    Console.WriteLine($"   Found {item.SubPlaces.Count} subplaces:");
                    foreach (var subplace in item.SubPlaces)
                    {
                        Console.WriteLine($"    * {subplace.Address + " - " + item.FullAddress}");
                    }
                }
            }
            // Optional: log response
            System.Console.WriteLine(responseBody);
        }

        public class LocationDto
        {
            public double Lat { get; set; }
            public double Lng { get; set; }
        }

        public class PlaceTestDto
        {
            public string PlaceId { get; set; }
            public string Address { get; set; }
            public string FullAddress { get; set; }
            public LocationDto Location { get; set; }
            public int Type { get; set; }
            public double Score { get; set; }
            public double RatingScore { get; set; }
            public double Distance { get; set; }
            public bool Popular { get; set; }
            public bool Processed { get; set; }
            public string DataFrom { get; set; }
            public bool Saved { get; set; }
            public double TmpScore { get; set; }
            public string KindOfPlace { get; set; }
            public int BookingCount { get; set; }
            public int QuoteCount { get; set; }
            public List<PlaceTestDto> SubPlaces { get; set; } // bạn có thể thay object bằng kiểu cụ thể nếu có schema subPlace
        }

        [Test]
        //[TestCase("", "0984086431", "Từ khóa test", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "12 trang", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "20 ngõ 100 Trần", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "20/100 Trần", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "3 ngách 12 ngõ 105 Xuân", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "3/12/105 xuân", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "52/3 tây", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Hồ g", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Hồ guo", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "hồ gươm", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Phố cổ", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Phố cổ Hà", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Lăng", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Lăng Bác", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Lăng chủ tịch", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Quốc tự giám", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Văn Miếu", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "keangnam", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "72 landmark", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "lotte", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "lotte center", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "vincom bà", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "hadico", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "handico tower", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "toyata mỹ", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "T5 time", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "time", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "royal", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "vinhome gardenia", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "linh đàm", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "HH5 linh", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Mipec long biên", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "reiverside long biên", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "vincom", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Nguyễn văn cừ", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Chợ đồng", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Bến xe", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Giáp bát", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Mỹ đình", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Chợ long biên", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Bệnh viện", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Bạch mai", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Khách sạn", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Khách sạn daewoo", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Nhà hàng quán", 21.021800211406582, 105.80387974060552, 50, 10)]
        //[TestCase("", "0984086431", "Nhà hàng quán ăn ngon", 21.021800211406582, 105.80387974060552, 50, 10)]


        //[TestCase("", "089222158", "ga ha", 21.021800211406582, 105.80387974060552, null, 10)]
        //[TestCase("", "089222158", "39 truong chinh", 21.021800211406582, 105.80387974060552, null, 10)]
        //[TestCase("", "089222158", "39 đ. trường chinh", 21.021800211406582, 105.80387974060552, null, 10)]
        //[TestCase("", "089222158", "39 đường trường chinh", 21.021800211406582, 105.80387974060552, null, 10)]
        //[TestCase("", "089222158", "số 39 trường chinh", 21.021800211406582, 105.80387974060552, null, 10)]
        //[TestCase("", "089222158", "39 trường chinh", 21.021800211406582, 105.80387974060552, null, 10)]
        //[TestCase("", "089222158", "chùa bái đính ninh bình", 21.021800211406582, 105.80387974060552, null, 10)]
        //[TestCase("", "089222158", "sân bay nội bài", 21.021800211406582, 105.80387974060552, null, 10)]
        //[TestCase("", "089222158", "dinh độc lập", 21.021800211406582, 105.80387974060552, null, 10)]
        //[TestCase("", "089222158", "dinh doc lap", 21.021800211406582, 105.80387974060552, null, 10)]
        //[TestCase("", "089222158", "nui ba den tay ninh", 21.021800211406582, 105.80387974060552, null, 10)]
        //[TestCase("", "089222158", "tttm, trường học, bệnh viện", 21.021800211406582, 105.80387974060552, null, 10)]
        [TestCase("", "0869222158", "sân bay gia lâm", 21.021800211406582, 105.80387974060552, null, 10)]
        [TestCase("", "0869222158", "bến xe mỹ đình", 20.648732, 105.839256,null, 10)]
        [TestCase("", "0869222158", "chùa tam chúc", 20.648732, 105.839256,null, 10)]
        [TestCase("", "0869222158", "sân bay nội bài", 20.648732, 105.839256,null, 10)]
        [TestCase("", "0869222158", "sân bay nội bài hà nội", 20.648732, 105.839256,null, 10)]
        [TestCase("", "0869222158", "135 trần", 20.648732, 105.839256,null, 10)]
        [TestCase("", "0362670380", "san bay noi bai", 21.021800211406582, 105.80387974060552, null, 10)] 
        [TestCase("", "0984086431", "noi bai san bay", 21.021800211406582, 105.80387974060552, null, 10)]
        [TestCase("", "0984086431", "san bay tan son nhat", 21.021800211406582, 105.80387974060552, null, 10)]
        [TestCase("", "0984086431", "sân bay tân sơn nhất", 21.021800211406582, 105.80387974060552, null, 10)]
        [TestCase("", "0984086431", "noi bai", 21.021800211406582, 105.80387974060552, null, 10)] 
        [TestCase("", "0984086431", "nội bài", 21.021800211406582, 105.80387974060552, null, 10)] 
        [TestCase("", "0362670380", "san bay", 21.021800211406582, 105.80387974060552, null, 10)] 
        [TestCase("", "0984086431", "22Láng", 21.021800211406582, 105.80387974060552, null, 10)]
        [TestCase("", "0984086431", "22ĐườngLáng", 21.021800211406582, 105.80387974060552, null, 10)]
        [TestCase("", "0984086431", "640/8 Láng", 21.021800211406582, 105.80387974060552, null, 10)]
        [TestCase("", "0984086431", "Số 21, Ngách 51/128C Đại La", 21.021800211406582, 105.80387974060552, null, 10)]
        [TestCase("", "0984086431", "Ngách 51/128C Đại La", 21.021800211406582, 105.80387974060552, null, 10)]
        [TestCase("", "0984086431", "51/128C Đại La", 21.021800211406582, 105.80387974060552, null, 10)]
        [TestCase("", "0984086431", "51 Đại La", 21.021800211406582, 105.80387974060552, null, 10)]
        [TestCase("", "0984086431", "99/110/32 định công hạ", 21.021800211406582, 105.80387974060552, null, 10)]
        [TestCase("", "0984086431", "Số 22 Láng Hạ", 21.021800211406582, 105.80387974060552, null, 10)]
        [TestCase("", "0984086431", "22 Láng Hạ", 21.021800211406582, 105.80387974060552, null, 10)]
        [TestCase("", "0984086431", "22LángHạ", 21.021800211406582, 105.80387974060552, null, 10)]
        [TestCase("", "0984086431", "Số 22LángHạ", 21.021800211406582, 105.80387974060552, null, 10)]
        [TestCase("", "0362670380", "22 thượng", 21.021800211406582, 105.80387974060552, null, 10)]
        [TestCase("", "0362670380", "Tét1", 20.648732, 105.839256, null, 10)]
        [TestCase("", "0362670380", "22 Lê", 20.648732, 105.839256, null, 10)]
        [TestCase("", "0362670380", "35 tràng", 20.648732, 105.839256, null, 10)]
        [TestCase("", "0362670380", "38 Nguyên Hồng", 20.648732, 105.839256, null, 10)]
        [TestCase("", "0362670380", "38 P. Nguyên Hồng", 20.648732, 105.839256, null, 10)]
        [TestCase("", "0362670380", "38 Đ. Nguyên Hồng", 20.648732, 105.839256, null, 10)]
        public async Task SearchAutocompleteAsync_ReturnsExpectedResults(string userId, string phoneNumber, string query, double latitude, double longitude, int? h3Resolution, int limit)
        {
            // Setup overall timing
            var overallStopwatch = Stopwatch.StartNew();

            // Measure just the search method execution time
            var searchStopwatch = Stopwatch.StartNew();
            // Act
            var results = await _service.SearchAutocompleteAsync(userId, phoneNumber, query, latitude, longitude, h3Resolution, limit, false);
            searchStopwatch.Stop();

            // Assert
            Assert.That(results, Is.Not.Null);

            // Log the results for debugging
            Console.WriteLine($"Search for '{query}' returned {results.Count} results:");
            Console.WriteLine($"Processing time: {searchStopwatch.ElapsedMilliseconds}ms for SearchAutocompleteAsync method call");

            foreach (var item in results)
            {
                Console.WriteLine(!string.IsNullOrWhiteSpace(item.TagInput) ?
                    $" - ({item.TagInput} - {item.DataFrom}) {(!string.IsNullOrWhiteSpace(item.Name) ? item.Name + " " : "")}{item.FullAddress} - {item.Distance:##0.##}km - Score: {item.Score:##0.##}" :
                    $" - ({item.DataFrom}) {(!string.IsNullOrWhiteSpace(item.Name) ? item.Name + " " : "")}{item.FullAddress} - {item.Distance:###.##}km - Score: {item.Score:##0.##}");
                // Log subplaces if they exist
                if (item.SubPlaces?.Any() == true)
                {
                    Console.WriteLine($"   Found {item.SubPlaces.Count} subplaces:");
                    foreach (var subplace in item.SubPlaces.OrderBy(x => x.Id))
                    {
                        Console.WriteLine($"    * {subplace.Name} - {subplace.PlaceId}");
                    }
                }
            }

            // Verify results are limited by count
            Assert.That(results.Count, Is.LessThanOrEqualTo(limit));

            // If searching for known items, verify they're found
            if (query.Contains("coffee", StringComparison.OrdinalIgnoreCase))
            {
                Assert.That(results.Any(p => p.Name!.NormalizeVietnamese().Contains("coffee".NormalizeVietnamese(), StringComparison.OrdinalIgnoreCase)), Is.True);
            }
            else if (query.Contains("bệnh viện", StringComparison.OrdinalIgnoreCase))
            {
                Assert.That(results?.Any(p => p.Name!.NormalizeVietnamese().Contains("Bệnh viện".NormalizeVietnamese(), StringComparison.OrdinalIgnoreCase)), Is.True);
            }

            // Stop overall timing and log total test execution time
            overallStopwatch.Stop();
            Console.WriteLine($"Total test execution time: {overallStopwatch.ElapsedMilliseconds}ms for query: '{query}'");
            Console.WriteLine($"Search operation took {searchStopwatch.ElapsedMilliseconds}ms ({(double)searchStopwatch.ElapsedMilliseconds / overallStopwatch.ElapsedMilliseconds:P2} of total test time)");
        }

        [Test]
        public async Task SearchAutocompleteAsync_WithEmptyQuery_ReturnsEmptyList()
        {
            // Act
            var results = await _service.SearchAutocompleteAsync("", "", "", 21.021673, 105.803715, 10, 5); // H3 resolution 10 ~ bán kính 65m

            // Assert
            Assert.That(results, Is.Not.Null);
            Assert.That(results, Is.Empty);
        }

        [Test]
        public async Task SearchAutocompleteAsync_WithInvalidCoordinates_HandlesGracefully()
        {
            // Act - latitude outside valid range
            var results = await _service.SearchAutocompleteAsync("", "", "coffee", 200.0, 105.803715, 10, 5); // H3 resolution 10 ~ bán kính 65m

            // Assert
            Assert.That(results, Is.Not.Null);
            Assert.That(results, Is.Empty);
        }

        [Test]
        public async Task SearchAutocompleteAsync_WithInvalidH3Resolution_UsesDefaultH3Resolution()
        {
            // Act
            var results = await _service.SearchAutocompleteAsync("", "", "coffee", 21.021673, 105.803715, 0, 5); // H3 resolution 0 (không hợp lệ, sẽ sử dụng giá trị mặc định)

            // Assert
            Assert.That(results, Is.Not.Null);

            // The service should handle this gracefully and potentially use default H3 resolution
            if (results.Any(p => p.Name.Contains("Coffee", StringComparison.OrdinalIgnoreCase)))
            {
                Assert.Pass("Service handled invalid H3 resolution gracefully");
            }
        }

        [Test]
        public async Task SearchAutocompleteAsync_SortsResultsCorrectly()
        {
            // Act
            // Get results with a query that should match multiple places
            var results = await _service.SearchAutocompleteAsync("", "", "hà nội", 21.021673, 105.803715, 10, 10); // H3 resolution 10 ~ bán kính 65m

            // Assert
            if (results.Count >= 2)
            {
                // Check that popular items come first
                var popularFirst = results
                    .Select((item, index) => new { Item = item, Index = index })
                    .GroupBy(x => x.Item.Popular)
                    .OrderByDescending(g => g.Key)
                    .SelectMany(g => g.OrderBy(x => x.Index).Select(x => x.Item))
                    .ToList();

                Assert.That(results, Is.EqualTo(popularFirst));
            }
            else
            {
                Console.WriteLine("Not enough results to test sorting logic");
                Assert.Pass();
            }
        }

        [Test]
        public async Task SearchAutocompleteAsync_UsesConcurrentDictionaryForThreadSafety()
        {
            // This test verifies that multiple concurrent searches don't throw exceptions

            // Act - Run multiple searches concurrently
            var tasks = new List<Task<List<PlacesEs>>>
            {
                _service.SearchAutocompleteAsync("","","coffee", 21.021673, 105.803715, 10, 5), // H3 resolution 10 ~ bán kính 65m
                _service.SearchAutocompleteAsync("","","bệnh viện", 21.000103, 105.841003, 9, 3), // H3 resolution 9 ~ bán kính 174m
                _service.SearchAutocompleteAsync("","","trường học", 21.007831, 105.845657, 8, 4), // H3 resolution 8 ~ bán kính 461m
                _service.SearchAutocompleteAsync("","","siêu thị", 21.019125, 105.810983, 9, 6), // H3 resolution 9 ~ bán kính 174m
                _service.SearchAutocompleteAsync("","","nhà hàng", 21.031219, 105.853027, 7, 7) // H3 resolution 7 ~ bán kính 1220m
            };

            // Assert - No exceptions should be thrown
            await Task.WhenAll(tasks);
            Assert.Pass("Concurrent searches completed without exceptions");
        }

        [Test]
        public async Task SearchAutocompleteAsync_WithSubPlaces_ReturnsSubPlacesInResults()
        {
            // Arrange
            // Search for a term that should find results with subplaces
            // Using "siêu thị" (supermarket) as an example since it commonly has departments/sections
            string searchTerm = "sân bay nội bài";
            double latitude = 21.021673; // Example coordinates in Hanoi
            double longitude = 105.803715;
            int h3Resolution = 10; // H3 resolution 10 ~ bán kính 65m
            int limit = 10;

            // Act
            var results = await _service.SearchAutocompleteAsync("", "", searchTerm, latitude, longitude, h3Resolution, limit);

            // Assert
            Assert.That(results, Is.Not.Null);

            // Log the results for debugging
            Console.WriteLine($"Search for '{searchTerm}' returned {results.Count} results:");
            foreach (var item in results)
            {
                Console.WriteLine($" - {item.Name} {item.FullAddress}");

                // Log subplaces if they exist
                if (item.SubPlaces?.Any() == true)
                {
                    Console.WriteLine($"   Found {item.SubPlaces.Count} subplaces:");
                    foreach (var subplace in item.SubPlaces)
                    {
                        Console.WriteLine($"    * {subplace.Name}");
                    }
                }
            }

            // Find any results that have subplaces
            var placesWithSubplaces = results.Where(p => p.SubPlaces?.Any() == true).ToList();

            // Test passes in two scenarios:
            // 1. We found at least one place with subplaces - this directly verifies the functionality
            // 2. We found places matching our search but none have subplaces - the functionality works but our test data doesn't have subplaces
            if (results.Any())
            {
                if (placesWithSubplaces.Any())
                {
                    // Verify we found places with subplaces
                    Assert.That(placesWithSubplaces, Is.Not.Empty, "No places with subplaces were found");

                    // Verify the subplaces have expected properties
                    var firstPlaceWithSubplaces = placesWithSubplaces.First();
                    var firstSubplace = firstPlaceWithSubplaces.SubPlaces.First();

                    Assert.Multiple(() =>
                    {
                        Assert.That(firstSubplace.Name, Is.Not.Null.Or.Empty, "Subplace should have a name");
                        // Check HasChild flag on parent is set correctly
                        Assert.That(firstPlaceWithSubplaces.SubPlaces.Count > 0, Is.True, "Parent place should have HasChild=true");
                    });
                }
                else
                {
                    // If we found matches but no subplaces, we'll mark the test as inconclusive
                    // This means the search functionality works but we don't have test data with subplaces
                    Assert.Inconclusive("Test found matching places but none contained subplaces. " +
                                       "This means the search works but cannot verify subplaces without appropriate test data.");
                }
            }
            else
            {
                // If we didn't find any places for this search term, try another common term
                Assert.Inconclusive($"No places found matching '{searchTerm}'. " +
                                   "Try modifying the test to use a different search term that exists in your test data.");
            }
        }

        [Test]
        public async Task SearchAutocompleteAsync_ReturnsExpectedResults_WithPriorityResults()
        {
            // Arrange
            string query = "coffee";
            double latitude = 21.021673;
            double longitude = 105.803715;
            int h3Resolution = 10; // H3 resolution 10 ~ bán kính 65m
            int limit = 10;

            // Act
            var results = await _service.SearchAutocompleteAsync("", "", query, latitude, longitude, h3Resolution, limit);

            // Assert
            Assert.That(results, Is.Not.Null);
            Assert.That(results.Count, Is.LessThanOrEqualTo(limit));

            // Verify priority results are included and appear first
            var priorityResults = results.Where(r => r.TagInput == "Priority").ToList();
            Assert.That(priorityResults, Is.Not.Empty, "Priority results should be included.");
            Assert.That(results.Take(priorityResults.Count), Is.EqualTo(priorityResults), "Priority results should appear first.");
        }

        [Test]
        public async Task SearchAutocompleteAsync_HandlesPriorityResultsIntegration()
        {
            // Arrange
            string query = "hospital";
            double latitude = 21.000103;
            double longitude = 105.841003;
            int h3Resolution = 10; // H3 resolution 10 ~ bán kính 65m
            int limit = 10;

            // Act
            var results = await _service.SearchAutocompleteAsync("", "", query, latitude, longitude, h3Resolution, limit);

            // Assert
            Assert.That(results, Is.Not.Null);

            // Verify priority results are integrated correctly
            var priorityResults = results.Where(r => r.TagInput == "Priority").ToList();
            var nonPriorityResults = results.Where(r => r.TagInput != "Priority").ToList();

            Assert.Multiple(() =>
            {
                Assert.That(priorityResults, Is.Not.Empty, "Priority results should be included.");
                Assert.That(nonPriorityResults, Is.Not.Empty, "Non-priority results should also be included.");
                Assert.That(results.Take(priorityResults.Count), Is.EqualTo(priorityResults), "Priority results should appear first.");
            });
        }

        [Test]
        public async Task LoadTest_AutocompleteService_MeasurePerformanceMetrics()
        {
            // Arrange
            int concurrentRequests = 50; // Number of concurrent requests to simulate
            int iterations = 5; // Repeat test this many times to get more data points
            
            // Define a set of realistic test queries
            var testQueries = new[] {
                "coffee",
                "bệnh viện",
                "sân bay nội bài",
                "lotte center",
                "vincom",
                "landmark",
                "mỹ đình",
                "nhà hàng",
                "khách sạn",
                "30 trần",
                "22 láng",
                "trường học",
                "chợ",
            };
            
            // Test coordinates (Hanoi)
            var testCoordinates = new[] {
                new { Lat = 21.021800, Lng = 105.803879 }, // Hanoi center
                new { Lat = 21.031219, Lng = 105.853027 }, // Different area
                new { Lat = 20.986839, Lng = 105.828629 }, // Another area
            };
            
            // Prepare the container to collect metrics
            var responseTimes = new ConcurrentBag<double>(); // Store in milliseconds
            var totalRequests = concurrentRequests * testQueries.Length * iterations;
            
            // Act
            Console.WriteLine($"Starting load test with {concurrentRequests} concurrent users × {testQueries.Length} queries × {iterations} iterations = {totalRequests} total requests");
            
            var overallStopwatch = Stopwatch.StartNew();
            Random random = new Random();
            
            for (int iteration = 0; iteration < iterations; iteration++)
            {
                var iterationTasks = new List<Task>();
                
                foreach (var query in testQueries)
                {
                    for (int i = 0; i < concurrentRequests; i++)
                    {
                        // Pick random coordinates from test set
                        var coords = testCoordinates[random.Next(testCoordinates.Length)];
                        
                        iterationTasks.Add(Task.Run(async () => {
                            var taskStopwatch = Stopwatch.StartNew();
                            
                            try
                            {
                                // Use random h3Resolution and limit
                                int h3Resolution = random.Next(9, 12); // 9-11 are good values for autocomplete
                                int limit = random.Next(5, 21); // 5-20 results
                                
                                // Execute service call
                                var results = await _service.SearchAutocompleteAsync("", 
                                    $"098{random.Next(1000000, 9999999)}", // Random phone number
                                    query, 
                                    coords.Lat, 
                                    coords.Lng, 
                                    h3Resolution, 
                                    limit);
                                
                                // Record timing info regardless of result count
                                taskStopwatch.Stop();
                                responseTimes.Add(taskStopwatch.Elapsed.TotalMilliseconds);
                                
                                // Minimal validation to ensure responses are coming back
                                Assert.That(results, Is.Not.Null);
                            }
                            catch (Exception ex)
                            {
                                // Log but don't fail entire test
                                taskStopwatch.Stop();
                                Console.WriteLine($"ERROR: Error in request for query {query}: {ex.Message}");
                                responseTimes.Add(taskStopwatch.Elapsed.TotalMilliseconds);
                            }
                        }));
                    }
                }
                
                // Wait for this iteration to complete
                await Task.WhenAll(iterationTasks);
                
                // Log progress
                Console.WriteLine($"Completed iteration {iteration + 1} of {iterations}");
            }
            
            overallStopwatch.Stop();
            
            // Process and report metrics
            var totalDurationSeconds = overallStopwatch.Elapsed.TotalSeconds;
            var throughput = totalRequests / totalDurationSeconds;
            
            // Calculate percentiles
            var sortedResponseTimes = responseTimes.OrderBy(t => t).ToArray();
            var count = sortedResponseTimes.Length;
            
            var min = sortedResponseTimes.First();
            var max = sortedResponseTimes.Last();
            var avg = sortedResponseTimes.Average();
            var median = count % 2 == 0 
                ? (sortedResponseTimes[count / 2 - 1] + sortedResponseTimes[count / 2]) / 2 
                : sortedResponseTimes[count / 2];
            var p95 = sortedResponseTimes[(int)(count * 0.95)];
            var p99 = sortedResponseTimes[(int)(count * 0.99)];
            
            // Assert and log results
            Assert.That(count, Is.EqualTo(totalRequests), $"Expected {totalRequests} results, but got {count}");
            
            Console.WriteLine("=== AUTOCOMPLETE SERVICE LOAD TEST RESULTS ===");
            Console.WriteLine($"Total Requests: {count}");
            Console.WriteLine($"Total Duration: {totalDurationSeconds:F2} seconds");
            Console.WriteLine($"Throughput: {throughput:F2} requests/second");
            Console.WriteLine($"Response Time (min): {min:F2} ms");
            Console.WriteLine($"Response Time (avg): {avg:F2} ms");
            Console.WriteLine($"Response Time (median): {median:F2} ms");
            Console.WriteLine($"Response Time (p95): {p95:F2} ms");
            Console.WriteLine($"Response Time (p99): {p99:F2} ms");
            Console.WriteLine($"Response Time (max): {max:F2} ms");
            
            // Console output for easier reading during test runs
            Console.WriteLine("=== AUTOCOMPLETE SERVICE LOAD TEST RESULTS ===");
            Console.WriteLine($"Total Requests: {count}");
            Console.WriteLine($"Total Duration: {totalDurationSeconds:F2} seconds");
            Console.WriteLine($"Throughput: {throughput:F2} requests/second");
            Console.WriteLine($"Response Time (min): {min:F2} ms");
            Console.WriteLine($"Response Time (avg): {avg:F2} ms");
            Console.WriteLine($"Response Time (median): {median:F2} ms");
            Console.WriteLine($"Response Time (p95): {p95:F2} ms");
            Console.WriteLine($"Response Time (p99): {p99:F2} ms");
            Console.WriteLine($"Response Time (max): {max:F2} ms");
            
            // Histogram distribution (optional)
            var histogram = new Dictionary<string, int>();
            var bucketSize = 50; // 50ms buckets
            foreach (var responseTime in sortedResponseTimes)
            {
                var bucket = ((int)(responseTime / bucketSize) * bucketSize).ToString();
                if (!histogram.ContainsKey(bucket))
                    histogram[bucket] = 0;
                histogram[bucket]++;
            }
            
            Console.WriteLine("\n=== RESPONSE TIME DISTRIBUTION ===");
            foreach (var bucket in histogram.OrderBy(b => int.Parse(b.Key)))
            {
                var percentage = (double)bucket.Value / count * 100;
                Console.WriteLine($"{bucket.Key}-{int.Parse(bucket.Key) + bucketSize}ms: {bucket.Value} requests ({percentage:F1}%)");
            }
            
            // A simple chart using ASCII characters
            Console.WriteLine("\n=== RESPONSE TIME HISTOGRAM ===");
            var maxBucketCount = histogram.Max(b => b.Value);
            var chartWidth = 50; // characters
            foreach (var bucket in histogram.OrderBy(b => int.Parse(b.Key)))
            {
                var barLength = (int)Math.Round((double)bucket.Value / maxBucketCount * chartWidth);
                var bar = new string('#', barLength);
                Console.WriteLine($"{bucket.Key}-{int.Parse(bucket.Key) + bucketSize}ms: {bar} ({bucket.Value})");
            }
            
            // Export results to CSV (optional)
            //File.WriteAllLines("autocomplete_perf_results.csv", 
            //    new[] { "ResponseTime_ms" }.Concat(
            //        sortedResponseTimes.Select(t => t.ToString("F2"))
            //    ));
        }

        [Test]
        public async Task LoadTest_ElasticsearchDirectQuery_MeasurePerformanceMetrics()
        {
            // Arrange
            int concurrentRequests = 5; // Số lượng request đồng thời để mô phỏng
            int iterations = 1; // Lặp lại test này nhiều lần để có nhiều điểm dữ liệu hơn
            
            // Định nghĩa một tập các từ khóa tìm kiếm thực tế
            var testQueries = new[] {
                "sân bay nội bài",
                "bến xe mỹ đình",
                "chùa tam chúc",
                "lotte center",
                "vincom",
                "landmark 72",
                "trường học",
                "bệnh viện",
                "nhà hàng",
                "khách sạn",
                "22 láng hạ",
                "30 trần hưng đạo",
                "chợ đồng xuân"
            };
            
            // Tọa độ test (Hà Nội)
            var testCoordinates = new[] {
                new { Lat = 21.021800, Lng = 105.803879 }, // Trung tâm Hà Nội
                new { Lat = 21.031219, Lng = 105.853027 }, // Khu vực khác
                new { Lat = 20.986839, Lng = 105.828629 }, // Khu vực khác nữa
            };
            
            // Chuẩn bị container để thu thập số liệu
            var responseTimes = new ConcurrentBag<double>(); // Lưu trữ theo milliseconds
            var totalRequests = concurrentRequests * testQueries.Length * iterations;
            
            // Act
            Console.WriteLine($"Bắt đầu test tải với {concurrentRequests} người dùng đồng thời × {testQueries.Length} truy vấn × {iterations} lần lặp = {totalRequests} tổng số request");
            
            var overallStopwatch = Stopwatch.StartNew();
            Random random = new Random();
            
            // Các mã H3 cố định cho filter
            var h3Cells = new[] {
                "84415cbffffffff",
                "84415ddffffffff",
                "8441437ffffffff",
                "8441435ffffffff"
            };
            
            // Các mã H3 cố định cho context suggest
            var h3ContextCells = new[] {
                "85415cb7fffffff",
                "85415ca7fffffff",
                "8541436bfffffff",
                "8541437bfffffff"
            };
            
            // Gọi API Elasticsearch trực tiếp qua Kibana proxy
            var url = "http://localhost:8200/place-sit/_search";
            //var url = "https://kibana-vntaxi.backendoffice.vn/api/console/proxy?path=place2%2F_search&method=GET";

            // Sử dụng một HttpClient duy nhất cho tất cả các request
            using var httpClient = new HttpClient();
            
            // Thiết lập headers cho requests
            httpClient.DefaultRequestHeaders.Add("Accept", "*/*");
            httpClient.DefaultRequestHeaders.Add("Accept-Language", "en-US,en;q=0.9,vi;q=0.8,zh-TW;q=0.7,zh-CN;q=0.6,zh;q=0.5");
            httpClient.DefaultRequestHeaders.Add("Authorization", "Basic YWRtaW46cXR1ZEAxMjM=");
            httpClient.DefaultRequestHeaders.Add("Connection", "keep-alive");
            httpClient.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            httpClient.DefaultRequestHeaders.Add("kbn-build-number", "76711");
            httpClient.DefaultRequestHeaders.Add("kbn-version", "8.15.3");
            httpClient.DefaultRequestHeaders.Add("sec-ch-ua", "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"");
            httpClient.DefaultRequestHeaders.Add("sec-ch-ua-mobile", "?0");
            httpClient.DefaultRequestHeaders.Add("sec-ch-ua-platform", "\"Windows\"");
            httpClient.DefaultRequestHeaders.Add("Sec-Fetch-Dest", "empty");
            httpClient.DefaultRequestHeaders.Add("Sec-Fetch-Mode", "cors");
            httpClient.DefaultRequestHeaders.Add("Sec-Fetch-Site", "same-origin");
            //httpClient.DefaultRequestHeaders.Add("Origin", "https://kibana-vntaxi.backendoffice.vn");
            //httpClient.DefaultRequestHeaders.Add("x-elastic-internal-origin", "Kibana");
            //httpClient.DefaultRequestHeaders.Add("x-kbn-context", "%7B%22type%22%3A%22application%22%2C%22name%22%3A%22management%22%2C%22url%22%3A%22%2Fapp%2Fmanagement%2Fdata%2Findex_management%2Findices%22%2C%22page%22%3A%22indexManagementIndicesTab%22%7D");
            
            // Thiết lập Cookie
            //httpClient.DefaultRequestHeaders.Add("Cookie", "rl_page_init_referrer=RudderEncrypt%3AU2FsdGVkX1%2B15nvAlna2RNPoQnbSg4Lb9XkKUyQtw3E%3D; rl_page_init_referring_domain=RudderEncrypt%3AU2FsdGVkX185uwLrzYww3jXUZb%2Bba%2F3YeB4dy3%2F0CJ8%3D; rl_anonymous_id=RudderEncrypt%3AU2FsdGVkX1%2BQE8ODRsbjUtSsbjZ7DYlEJEhvvmdYR29eMJ%2FKhIYCyqDKlyOtjCXFDufFINLpaSmA9fp34AO9zA%3D%3D; rl_user_id=RudderEncrypt%3AU2FsdGVkX1%2BVHOigkDFHaxBrNB%2FsRdmU4Zo9P3eObCfyDXe89qiuzwNNcIV9%2F842Q3dQSG0e3MrUPG6HlqTmjoVgEpf3vyk%2Fo7bRWM2oXaKXZ%2Fdq46KLeVAcgMxxEjcdTrjLtYsudYF7wKOPEpHJiZcJk2yLzb7WGl3D4IlGK28%3D; rl_trait=RudderEncrypt%3AU2FsdGVkX19Y1AKpTgmCwZohbGdUy%2B%2FtfiAmywhnZyS%2F4YlOz1PsX3RmuGQq5fKK%2B6qK1hQidVFiAUv0Vmptg%2FZ%2FBYxSD8qn2Icj0pg2Zv4gEsZq2nJ88l6zY0u12dcfkueeLAqOGkCx%2BcaKJ9MXFg%3D%3D; rl_session=RudderEncrypt%3AU2FsdGVkX184PcpBpHbeDhLl8SH9De3EzhiYup8dneC9Z3Cwfrb4rS1uzBWMMFrvJM%2FH2j5Hvhx1HLbosx6As0N6omOa5bJ%2BnTewWPKil3YBEz%2FckpsGwlNDGZNRAZmu0HQAL3OaxlVq%2FjRlAvUcqw%3D%3D");
            
            for (int iteration = 0; iteration < iterations; iteration++)
            {
                var iterationTasks = new List<Task>();
                
                foreach (var query in testQueries)
                {
                    for (int i = 0; i < concurrentRequests; i++)
                    {
                        // Chọn ngẫu nhiên tọa độ từ tập test
                        var coords = testCoordinates[random.Next(testCoordinates.Length)];
                        
                        iterationTasks.Add(Task.Run(async () => {
                            var taskStopwatch = Stopwatch.StartNew();
                            
                            try
                            {
                                // Tạo JSON payload với các tham số ngẫu nhiên
                                var payload = CreateElasticsearchPayload(
                                    query,
                                    coords.Lat,
                                    coords.Lng,
                                    h3Cells,
                                    h3ContextCells,
                                    random.Next(5, 21) // Kích thước kết quả ngẫu nhiên từ 5-20
                                );
                                
                                var content = new StringContent(payload, Encoding.UTF8, "application/json");
                                
                                // Thực hiện request
                                var response = await httpClient.PostAsync(url, content);
                                
                                // Ghi nhận thời gian xử lý
                                taskStopwatch.Stop();
                                responseTimes.Add(taskStopwatch.Elapsed.TotalMilliseconds);
                                
                                // Kiểm tra kết quả
                                if (response.IsSuccessStatusCode)
                                {
                                    var responseJson = await response.Content.ReadAsStringAsync();
                                    var responseObj = JsonConvert.DeserializeObject<dynamic>(responseJson);
                                    
                                    // Xác minh rằng phản hồi có cấu trúc hợp lệ
                                    Assert.That(responseObj, Is.Not.Null);
                                }
                                else
                                {
                                    // Ghi lại lỗi nếu request không thành công
                                    Console.WriteLine($"ERROR: Request failed with status code {response.StatusCode} for query '{query}'");
                                    var errorContent = await response.Content.ReadAsStringAsync();
                                    Console.WriteLine($"ERROR: Error details: {errorContent}");
                                }
                            }
                            catch (Exception ex)
                            {
                                // Ghi lại lỗi nhưng không làm hỏng toàn bộ test
                                taskStopwatch.Stop();
                                Console.WriteLine($"ERROR: Error in request for query {query}: {ex.Message}");
                                responseTimes.Add(taskStopwatch.Elapsed.TotalMilliseconds);
                            }
                        }));
                    }
                }
                
                // Đợi cho lần lặp này hoàn thành
                await Task.WhenAll(iterationTasks);
                
                // Ghi lại tiến trình
                Console.WriteLine($"Hoàn thành lần lặp {iteration + 1} của {iterations}");
            }
            
            overallStopwatch.Stop();
            
            // Xử lý và báo cáo số liệu
            var totalDurationSeconds = overallStopwatch.Elapsed.TotalSeconds;
            var throughput = totalRequests / totalDurationSeconds;
            
            // Tính toán các phân vị
            var sortedResponseTimes = responseTimes.OrderBy(t => t).ToArray();
            var count = sortedResponseTimes.Length;
            
            var min = sortedResponseTimes.First();
            var max = sortedResponseTimes.Last();
            var avg = sortedResponseTimes.Average();
            var median = count % 2 == 0 
                ? (sortedResponseTimes[count / 2 - 1] + sortedResponseTimes[count / 2]) / 2 
                : sortedResponseTimes[count / 2];
            var p95 = sortedResponseTimes[(int)(count * 0.95)];
            var p99 = sortedResponseTimes[(int)(count * 0.99)];
            
            // Kiểm tra và ghi lại kết quả
            Assert.That(count, Is.EqualTo(totalRequests), $"Dự kiến {totalRequests} kết quả, nhưng nhận được {count}");
            
            Console.WriteLine("=== KẾT QUẢ TEST TẢI TRUY VẤN ELASTICSEARCH TRỰC TIẾP ===");
            Console.WriteLine($"Total Requests: {count}");
            Console.WriteLine($"Total Duration: {totalDurationSeconds:F2} seconds");
            Console.WriteLine($"Throughput: {throughput:F2} requests/second");
            Console.WriteLine($"Response Time (min): {min:F2} ms");
            Console.WriteLine($"Response Time (avg): {avg:F2} ms");
            Console.WriteLine($"Response Time (median): {median:F2} ms");
            Console.WriteLine($"Response Time (p95): {p95:F2} ms");
            Console.WriteLine($"Response Time (p99): {p99:F2} ms");
            Console.WriteLine($"Response Time (max): {max:F2} ms");
            
            // Phân phối biểu đồ tần suất
            var histogram = new Dictionary<string, int>();
            var bucketSize = 50; // 50ms buckets
            foreach (var responseTime in sortedResponseTimes)
            {
                var bucket = ((int)(responseTime / bucketSize) * bucketSize).ToString();
                if (!histogram.ContainsKey(bucket))
                    histogram[bucket] = 0;
                histogram[bucket]++;
            }
            
            Console.WriteLine("\n=== PHÂN PHỐI THỜI GIAN PHẢN HỒI ===");
            foreach (var bucket in histogram.OrderBy(b => int.Parse(b.Key)))
            {
                var percentage = (double)bucket.Value / count * 100;
                Console.WriteLine($"{bucket.Key}-{int.Parse(bucket.Key) + bucketSize}ms: {bucket.Value} requests ({percentage:F1}%)");
            }
            
            // Biểu đồ đơn giản sử dụng ký tự ASCII
            Console.WriteLine("\n=== BIỂU ĐỒ THỜI GIAN PHẢN HỒI ===");
            var maxBucketCount = histogram.Max(b => b.Value);
            var chartWidth = 50; // ký tự
            foreach (var bucket in histogram.OrderBy(b => int.Parse(b.Key)))
            {
                var barLength = (int)Math.Round((double)bucket.Value / maxBucketCount * chartWidth);
                var bar = new string('#', barLength);
                Console.WriteLine($"{bucket.Key}-{int.Parse(bucket.Key) + bucketSize}ms: {bar} ({bucket.Value})");
            }
        }

        /// <summary>
        /// Tạo payload JSON cho truy vấn Elasticsearch
        /// </summary>
        private string CreateElasticsearchPayload(string query, double lat, double lon, string[] h3Cells, string[] h3ContextCells, int size)
        {
            // Xây dựng JSON payload cho truy vấn Elasticsearch dựa vào curl đã cho
            var h3CellsString = string.Join("\", \"", h3Cells);
            var h3ContextString = string.Join("\", \"", h3ContextCells);
            
            // Sử dụng StringBuilder để tránh vấn đề với chuỗi lớn
            var jsonBuilder = new StringBuilder();
            
            jsonBuilder.Append("{\n");
            jsonBuilder.Append("  \"collapse\": {\n");
            jsonBuilder.Append("    \"field\": \"fullAddress.keyword\"\n");
            jsonBuilder.Append("  },\n");
            jsonBuilder.Append("  \"query\": {\n");
            jsonBuilder.Append("    \"bool\": {\n");
            jsonBuilder.Append("      \"filter\": {\n");
            jsonBuilder.Append("        \"bool\": {\n");
            jsonBuilder.Append("          \"must\": [\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"terms\": {\n");
            jsonBuilder.Append("                \"h3.4.keyword\": [\n");
            jsonBuilder.Append($"                  \"{h3CellsString}\"\n");
            jsonBuilder.Append("                ]\n");
            jsonBuilder.Append("              }\n");
            jsonBuilder.Append("            },\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"geo_distance\": {\n");
            jsonBuilder.Append("                \"location\": {\n");
            jsonBuilder.Append($"                  \"lat\": {lat},\n");
            jsonBuilder.Append($"                  \"lon\": {lon}\n");
            jsonBuilder.Append("                },\n");
            jsonBuilder.Append("                \"distance\": \"50km\"\n");
            jsonBuilder.Append("              }\n");
            jsonBuilder.Append("            }\n");
            jsonBuilder.Append("          ]\n");
            jsonBuilder.Append("        }\n");
            jsonBuilder.Append("      },\n");
            jsonBuilder.Append("      \"must\": {\n");
            jsonBuilder.Append("        \"bool\": {\n");
            jsonBuilder.Append("          \"minimum_should_match\": 1,\n");
            jsonBuilder.Append("          \"should\": [\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"bool\": {\n");
            jsonBuilder.Append("                \"minimum_should_match\": 1,\n");
            jsonBuilder.Append("                \"should\": [\n");
            jsonBuilder.Append("                  {\n");
            jsonBuilder.Append("                    \"match_phrase_prefix\": {\n");
            jsonBuilder.Append("                      \"name\": {\n");
            jsonBuilder.Append("                        \"boost\": 30,\n");
            jsonBuilder.Append($"                        \"query\": \"{query}\"\n");
            jsonBuilder.Append("                      }\n");
            jsonBuilder.Append("                    }\n");
            jsonBuilder.Append("                  },\n");
            jsonBuilder.Append("                  {\n");
            jsonBuilder.Append("                    \"match_phrase\": {\n");
            jsonBuilder.Append("                      \"name\": {\n");
            jsonBuilder.Append("                        \"boost\": 25,\n");
            jsonBuilder.Append($"                        \"query\": \"{query}\"\n");
            jsonBuilder.Append("                      }\n");
            jsonBuilder.Append("                    }\n");
            jsonBuilder.Append("                  },\n");
            jsonBuilder.Append("                  {\n");
            jsonBuilder.Append("                    \"prefix\": {\n");
            jsonBuilder.Append("                      \"keywords.raw\": {\n");
            jsonBuilder.Append("                        \"boost\": 20,\n");
            jsonBuilder.Append($"                        \"value\": \"{query}\"\n");
            jsonBuilder.Append("                      }\n");
            jsonBuilder.Append("                    }\n");
            jsonBuilder.Append("                  },\n");
            jsonBuilder.Append("                  {\n");
            jsonBuilder.Append("                    \"match_phrase_prefix\": {\n");
            jsonBuilder.Append("                      \"keywords\": {\n");
            jsonBuilder.Append("                        \"boost\": 10,\n");
            jsonBuilder.Append($"                        \"query\": \"{query}\"\n");
            jsonBuilder.Append("                      }\n");
            jsonBuilder.Append("                    }\n");
            jsonBuilder.Append("                  },\n");
            jsonBuilder.Append("                  {\n");
            jsonBuilder.Append("                    \"match_phrase\": {\n");
            jsonBuilder.Append("                      \"keywords.raw\": {\n");
            jsonBuilder.Append("                        \"boost\": 5,\n");
            jsonBuilder.Append($"                        \"query\": \"{query}\"\n");
            jsonBuilder.Append("                      }\n");
            jsonBuilder.Append("                    }\n");
            jsonBuilder.Append("                  },\n");
            jsonBuilder.Append("                  {\n");
            jsonBuilder.Append("                    \"match\": {\n");
            jsonBuilder.Append("                      \"keywords.raw\": {\n");
            jsonBuilder.Append("                        \"boost\": 1,\n");
            jsonBuilder.Append($"                        \"query\": \"{query}\"\n");
            jsonBuilder.Append("                      }\n");
            jsonBuilder.Append("                    }\n");
            jsonBuilder.Append("                  }\n");
            jsonBuilder.Append("                ]\n");
            jsonBuilder.Append("              }\n");
            jsonBuilder.Append("            },\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"multi_match\": {\n");
            jsonBuilder.Append("                \"boost\": 5,\n");
            jsonBuilder.Append("                \"fields\": [\n");
            jsonBuilder.Append("                  \"addressPermuted^3\",\n");
            jsonBuilder.Append("                  \"addressPermuted.suggest^2\",\n");
            jsonBuilder.Append("                  \"keywords.suggest^4\"\n");
            jsonBuilder.Append("                ],\n");
            jsonBuilder.Append("                \"minimum_should_match\": \"75%\",\n");
            jsonBuilder.Append("                \"operator\": \"and\",\n");
            jsonBuilder.Append($"                \"query\": \"{query}\",\n");
            jsonBuilder.Append("                \"tie_breaker\": 0.3,\n");
            jsonBuilder.Append("                \"type\": \"most_fields\"\n");
            jsonBuilder.Append("              }\n");
            jsonBuilder.Append("            },\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"multi_match\": {\n");
            jsonBuilder.Append("                \"boost\": 1,\n");
            jsonBuilder.Append("                \"fields\": [\n");
            jsonBuilder.Append("                  \"addressPermuted^3\",\n");
            jsonBuilder.Append("                  \"addressPermuted.suggest^2\",\n");
            jsonBuilder.Append("                  \"keywords.suggest^4\"\n");
            jsonBuilder.Append("                ],\n");
            jsonBuilder.Append("                \"minimum_should_match\": \"75%\",\n");
            jsonBuilder.Append("                \"operator\": \"and\",\n");
            jsonBuilder.Append($"                \"query\": \"{query}\",\n");
            jsonBuilder.Append("                \"tie_breaker\": 0.3,\n");
            jsonBuilder.Append("                \"type\": \"most_fields\"\n");
            jsonBuilder.Append("              }\n");
            jsonBuilder.Append("            },\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"multi_match\": {\n");
            jsonBuilder.Append("                \"fields\": [\n");
            jsonBuilder.Append("                  \"addressPermuted^3\",\n");
            jsonBuilder.Append("                  \"addressPermuted.suggest^2\",\n");
            jsonBuilder.Append("                  \"keywords.suggest^4\"\n");
            jsonBuilder.Append("                ],\n");
            jsonBuilder.Append("                \"minimum_should_match\": \"100%\",\n");
            jsonBuilder.Append("                \"operator\": \"and\",\n");
            jsonBuilder.Append($"                \"query\": \"{query}\",\n");
            jsonBuilder.Append("                \"slop\": 0,\n");
            jsonBuilder.Append("                \"tie_breaker\": 0.3,\n");
            jsonBuilder.Append("                \"type\": \"phrase_prefix\"\n");
            jsonBuilder.Append("              }\n");
            jsonBuilder.Append("            },\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"nested\": {\n");
            jsonBuilder.Append("                \"path\": \"subPlaces\",\n");
            jsonBuilder.Append("                \"query\": {\n");
            jsonBuilder.Append("                  \"multi_match\": {\n");
            jsonBuilder.Append("                    \"fields\": [\n");
            jsonBuilder.Append("                      \"subPlaces.addressPermuted^3\",\n");
            jsonBuilder.Append("                      \"subPlaces.addressPermuted.suggest^1\",\n");
            jsonBuilder.Append("                      \"subPlaces.keywords^2\"\n");
            jsonBuilder.Append("                    ],\n");
            jsonBuilder.Append("                    \"minimum_should_match\": \"75%\",\n");
            jsonBuilder.Append("                    \"operator\": \"and\",\n");
            jsonBuilder.Append($"                    \"query\": \"{query}\",\n");
            jsonBuilder.Append("                    \"tie_breaker\": 0.3,\n");
            jsonBuilder.Append("                    \"type\": \"most_fields\"\n");
            jsonBuilder.Append("                  }\n");
            jsonBuilder.Append("                },\n");
            jsonBuilder.Append("                \"score_mode\": \"avg\"\n");
            jsonBuilder.Append("              }\n");
            jsonBuilder.Append("            }\n");
            jsonBuilder.Append("          ]\n");
            jsonBuilder.Append("        }\n");
            jsonBuilder.Append("      }\n");
            jsonBuilder.Append("    }\n");
            jsonBuilder.Append("  },\n");
            jsonBuilder.Append("  \"script_fields\": {\n");
            jsonBuilder.Append("    \"distance\": {\n");
            jsonBuilder.Append("      \"script\": {\n");
            jsonBuilder.Append("        \"params\": {\n");
            jsonBuilder.Append($"          \"lat\": {lat},\n");
            jsonBuilder.Append($"          \"lon\": {lon}\n");
            jsonBuilder.Append("        },\n");
            jsonBuilder.Append("        \"source\": \"doc['location'].arcDistance(params.lat, params.lon)/1000\"\n");
            jsonBuilder.Append("      }\n");
            jsonBuilder.Append("    }\n");
            jsonBuilder.Append("  },\n");
            jsonBuilder.Append($"  \"size\": {size},\n");
            jsonBuilder.Append("  \"sort\": [\n");
            jsonBuilder.Append("    {\n");
            jsonBuilder.Append("      \"popular\": {\n");
            jsonBuilder.Append("        \"order\": \"desc\"\n");
            jsonBuilder.Append("      }\n");
            jsonBuilder.Append("    },\n");
            jsonBuilder.Append("    {\n");
            jsonBuilder.Append("      \"processed\": {\n");
            jsonBuilder.Append("        \"order\": \"desc\"\n");
            jsonBuilder.Append("      }\n");
            jsonBuilder.Append("    },\n");
            jsonBuilder.Append("    {\n");
            jsonBuilder.Append("      \"_score\": {\n");
            jsonBuilder.Append("        \"order\": \"desc\"\n");
            jsonBuilder.Append("      }\n");
            jsonBuilder.Append("    },\n");
            jsonBuilder.Append("    {\n");
            jsonBuilder.Append("      \"_geo_distance\": {\n");
            jsonBuilder.Append("        \"location\": [\n");
            jsonBuilder.Append("          {\n");
            jsonBuilder.Append($"            \"lat\": {lat},\n");
            jsonBuilder.Append($"            \"lon\": {lon}\n");
            jsonBuilder.Append("          }\n");
            jsonBuilder.Append("        ],\n");
            jsonBuilder.Append("        \"distance_type\": \"plane\",\n");
            jsonBuilder.Append("        \"order\": \"asc\",\n");
            jsonBuilder.Append("        \"unit\": \"km\"\n");
            jsonBuilder.Append("      }\n");
            jsonBuilder.Append("    }\n");
            jsonBuilder.Append("  ],\n");
            jsonBuilder.Append("  \"_source\": true,\n");
            jsonBuilder.Append("  \"suggest\": {\n");
            jsonBuilder.Append($"    \"text\": \"{query}\",\n");
            jsonBuilder.Append("    \"suggest\": {\n");
            jsonBuilder.Append("      \"completion\": {\n");
            jsonBuilder.Append("        \"analyzer\": \"address_search\",\n");
            jsonBuilder.Append("        \"contexts\": {\n");
            jsonBuilder.Append("          \"location_50km\": [\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"boost\": 3,\n");
            jsonBuilder.Append($"              \"context\": \"{h3ContextCells[0]}\"\n");
            jsonBuilder.Append("            },\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"boost\": 3,\n");
            jsonBuilder.Append($"              \"context\": \"{h3ContextCells[1]}\"\n");
            jsonBuilder.Append("            },\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"boost\": 3,\n");
            jsonBuilder.Append($"              \"context\": \"{h3ContextCells[2]}\"\n");
            jsonBuilder.Append("            },\n");
            jsonBuilder.Append("            {\n");
            jsonBuilder.Append("              \"boost\": 3,\n");
            jsonBuilder.Append($"              \"context\": \"{h3ContextCells[3]}\"\n");
            jsonBuilder.Append("            }\n");
            jsonBuilder.Append("          ]\n");
            jsonBuilder.Append("        },\n");
            jsonBuilder.Append("        \"field\": \"addressPermuted.completion_with_location\",\n");
            jsonBuilder.Append("        \"fuzzy\": {\n");
            jsonBuilder.Append("          \"fuzziness\": \"AUTO\",\n");
            jsonBuilder.Append("          \"min_length\": 5,\n");
            jsonBuilder.Append("          \"prefix_length\": 3,\n");
            jsonBuilder.Append("          \"transpositions\": true,\n");
            jsonBuilder.Append("          \"unicode_aware\": true\n");
            jsonBuilder.Append("        },\n");
            jsonBuilder.Append("        \"size\": 3,\n");
            jsonBuilder.Append("        \"skip_duplicates\": true\n");
            jsonBuilder.Append("      }\n");
            jsonBuilder.Append("    }\n");
            jsonBuilder.Append("  }\n");
            jsonBuilder.Append("}");
            
            return jsonBuilder.ToString();
        }
    }
}
