using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Keyless]
[Table("placesdataes")]
public partial class Placesdatae
{
    [StringLength(512)]
    public string? PlaceId { get; set; }

    [StringLength(255)]
    public string? Name { get; set; }

    [StringLength(512)]
    public string? Address { get; set; }

    [StringLength(255)]
    public string? Province { get; set; }

    [StringLength(255)]
    public string? District { get; set; }

    [StringLength(255)]
    public string? Ward { get; set; }

    [StringLength(50)]
    public string? ScanProcessed { get; set; }

    [StringLength(255)]
    public string? UpdatedAt { get; set; }

    [StringLength(255)]
    public string? LastUpdateUser { get; set; }

    [StringLength(50)]
    public string? CompareScore { get; set; }
}
