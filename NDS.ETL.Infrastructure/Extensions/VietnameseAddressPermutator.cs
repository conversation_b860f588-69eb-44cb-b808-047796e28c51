using System.Text;
using System.Text.RegularExpressions;

namespace NDS.ETL.Infrastructure.Extensions;

public static class VietnameseAddressPermutator
{
    // Expanded Vietnamese stop words
    private static readonly string[] StopWords = {
        "và", "của", "cho", "tại", "từ", "đến", "trong", "ngoài", "trên", "dưới",
        "với", "bởi", "vì", "nên", "mà", "thì", "là", "có", "không", "được",
        "cùng", "giữa", "sau", "trước", "khi", "vừa", "theo", "về", "gần", "xa"
    };
    
    // Vietnamese specific address prefixes
    private static readonly string[] Prefixes = { 
        "số ", "ngõ ", "ngách ", "hẻm ", "kiệt ",
        "đường ", "phố ", "tổ ", "khu phố ", "ấp " 
    };

    // Vietnam-specific province/city shortcuts
    private static readonly Dictionary<string, string> ProvinceShortcuts = new(StringComparer.OrdinalIgnoreCase)
    {
        { "hcm", "hồ chí minh" },
        { "tphcm", "thành phố hồ chí minh" },
        { "hn", "hà nội" },
        { "hp", "hải phòng" },
        { "đn", "đà nẵng" },
        { "sg", "sài gòn" },
        { "ct", "cần thơ" }
    };

    public static List<string> GenerateOptimizedPermutations(string address, int maxPermutations = 15)
    {
        if (string.IsNullOrWhiteSpace(address)) return new List<string>();
        
        // Keep original Vietnamese while normalizing for permutation
        string normalized = NormalizeVietnameseAddress(address);
        var parts = SplitAddressParts(normalized);
        var permutations = new HashSet<string>();

        string joined = string.Join(" ", parts);
        permutations.Add(joined);
        
        // Generate Vietnamese-specific permutations
        permutations.Add(Cleanup(joined));
        permutations.Add(RemovePostalCode(joined));
        permutations.Add(RemovePrefixes(joined));
        permutations.Add(RemoveStopWords(joined));
        permutations.Add(ExpandProvinceShortcuts(joined));
        
        
        AddReversedPermutations(parts, permutations);
        AddDistrictFirstPermutations(parts, permutations);
        AddStandardPermutations(parts, permutations, maxPermutations);

        return permutations.Take(maxPermutations).ToList();
    }

    private static List<string> SplitAddressParts(string address)
    {
        var parts = address.Split(new[] { ',', '-', '|', ';' }, StringSplitOptions.RemoveEmptyEntries)
            .Select(p => p.Trim())
            .Where(p => !string.IsNullOrWhiteSpace(p))
            .ToList();

        return parts.Count <= 1 ? SmartChunkVietnameseAddress(address) : parts;
    }

    private static void AddReversedPermutations(List<string> parts, HashSet<string> permutations)
    {
        if (parts.Count < 2) return;

        // Full reverse (province first)
        var reversed = new List<string>(parts);
        reversed.Reverse();
        permutations.Add(string.Join(" ", reversed));
        
        // Common Vietnamese pattern: start with province or district
        for (int i = parts.Count - 1; i >= Math.Max(0, parts.Count - 3); i--)
        {
            var provinceLed = new List<string> { parts[i] };
            provinceLed.AddRange(parts.Where((_, j) => j != i));
            permutations.Add(string.Join(" ", provinceLed));
        }
    }
    
    private static void AddDistrictFirstPermutations(List<string> parts, HashSet<string> permutations)
    {
        if (parts.Count < 3) return;
        
        // Find parts that might be district names
        var districtKeywords = new[] { "quận", "huyện", "quân", "huyen" };
        
        for (int i = 0; i < parts.Count; i++)
        {
            if (districtKeywords.Any(k => parts[i].Contains(k, StringComparison.OrdinalIgnoreCase)))
            {
                var districtFirst = new List<string> { parts[i] };
                districtFirst.AddRange(parts.Where((_, j) => j != i));
                permutations.Add(string.Join(" ", districtFirst));
                break;
            }
        }
    }

    private static void AddStandardPermutations(List<string> parts, HashSet<string> permutations, int max)
    {
        if (parts.Count <= 3)
        {
            foreach (var perm in GetPermutations(parts, Math.Min(10, max)))
            {
                if (permutations.Count >= max) break;
                permutations.Add(string.Join(" ", perm));
            }
        }
        else
        {
            // For longer addresses, focus on meaningful permutations rather than all combinations
            for (int i = 0; i < Math.Min(parts.Count, 3); i++)
            {
                if (permutations.Count >= max) break;
                
                var first = parts[i];
                var rest = parts.Where((_, j) => j != i).ToList();
                
                permutations.Add(string.Join(" ", new[] { first }.Concat(rest)));
                
                // Add some common Vietnamese address patterns (specific part + rest)
                if (rest.Count > 1)
                {
                    permutations.Add(string.Join(" ", new[] { first, rest.Last() }.Concat(rest.Take(rest.Count - 1))));
                }
            }
        }
    }

    private static IEnumerable<List<string>> GetPermutations(List<string> list, int max)
    {
        return Permute(list, 0).Take(max);
    }

    private static IEnumerable<List<string>> Permute(List<string> list, int start)
    {
        if (start == list.Count - 1)
        {
            yield return new List<string>(list);
        }
        else
        {
            for (int i = start; i < list.Count; i++)
            {
                Swap(list, start, i);
                foreach (var perm in Permute(list, start + 1))
                    yield return perm;
                Swap(list, start, i);
            }
        }
    }

    private static void Swap(List<string> list, int i, int j)
    {
        if (i != j)
        {
            (list[i], list[j]) = (list[j], list[i]);
        }
    }

    public static string NormalizeVietnameseAddress(string input)
    {
        if (string.IsNullOrEmpty(input)) return input;
        
        string cleaned = RemoveDuplicateParentheses(input.ToLowerInvariant());
        
        // Expand province shortcuts before other processing
        foreach (var shortcut in ProvinceShortcuts)
        {
            cleaned = Regex.Replace(cleaned, $@"\b{Regex.Escape(shortcut.Key)}\b", shortcut.Value, RegexOptions.IgnoreCase);
        }

        // Vietnamese specific cleanups
        cleaned = cleaned
            .Replace(" /", "/")
            .Replace("/ ", "/")
            .Replace(" - ", ", ")
            .Replace(" | ", ", ");

        // Remove country name as it's redundant in most Vietnamese addresses
        cleaned = Regex.Replace(cleaned, @"\b(việt\s?nam)\b", "", RegexOptions.IgnoreCase);
        
        // Remove postal codes (common formats in Vietnam)
        cleaned = Regex.Replace(cleaned, @"\b\d{5,6}\b", "");

        // Clean up extra spaces
        cleaned = Regex.Replace(cleaned, @"\s{2,}", " ").Trim();

        // Split by commas, remove duplicates and rejoin
        var parts = cleaned.Split(',')
            .Select(p => p.Trim())
            .Where(p => !string.IsNullOrEmpty(p))
            .Distinct()
            .ToList();

        return string.Join(", ", parts).Trim();
    }

    public static string RemoveVietnameseDiacritics(string text)
    {
        if (string.IsNullOrEmpty(text)) return text;
        
        var normalized = text.Normalize(NormalizationForm.FormD);
        var result = Regex.Replace(normalized, @"\p{IsCombiningDiacriticalMarks}+", "");
        
        // Additional replacements for Vietnamese characters
        return result
            .Replace("Đ", "D")
            .Replace("đ", "d");
    }

    private static string Cleanup(string part)
    {
        if (string.IsNullOrEmpty(part)) return part;
        return Regex.Replace(part, @"[,\.;:""'\[\]\(\)]", " ")
            .Trim()
            .Replace("  ", " ");
    }

    private static string RemoveStopWords(string input)
    {
        if (string.IsNullOrEmpty(input)) return input;
        return string.Join(" ", input.Split(' ')
            .Where(w => !StopWords.Contains(w, StringComparer.OrdinalIgnoreCase)));
    }

    private static string RemovePostalCode(string input)
    {
        if (string.IsNullOrEmpty(input)) return input;
        return Regex.Replace(input, @"\b\d{5,6}\b", "").Trim();
    }

    private static string RemovePrefixes(string input)
    {
        if (string.IsNullOrEmpty(input)) return input;
        
        string result = input;
        foreach (var prefix in Prefixes)
        {
            result = Regex.Replace(result, $@"(^|\s){Regex.Escape(prefix)}\s*\d+", " ");
        }
        return Regex.Replace(result, @"\s+", " ").Trim();
    }

    
    private static string ExpandProvinceShortcuts(string input)
    {
        if (string.IsNullOrEmpty(input)) return input;
        
        string result = input;
        foreach (var shortcut in ProvinceShortcuts)
        {
            result = Regex.Replace(result, $@"\b{Regex.Escape(shortcut.Key)}\b", shortcut.Value, RegexOptions.IgnoreCase);
        }
        
        return result;
    }

    private static string RemoveDuplicateParentheses(string input)
    {
        if (string.IsNullOrEmpty(input)) return input;
        
        return Regex.Replace(input, @"\(([^()]+)\)", match =>
        {
            var content = match.Groups[1].Value.Trim();
            return input.Contains(content, StringComparison.OrdinalIgnoreCase) &&
                   !match.Value.Contains(content, StringComparison.OrdinalIgnoreCase) ? 
                   "" : match.Value;
        }).Replace(" ,", ",").Trim();
    }

    private static List<string> SmartChunkVietnameseAddress(string input)
    {
        if (string.IsNullOrEmpty(input)) return new List<string>();
        
        var parts = new List<string>();
        
        // Match Vietnamese street address pattern
        var streetMatch = Regex.Match(
            input, 
            @"^(.*?(số|ngõ|ngách|hẻm|kiệt|đường|phố|đ\.|đg\.|ng\.|ngh\.)[\w\s\/\.\-]*)\s+(phường|quận|huyện|tỉnh|thành phố|tp|tx|tt|xã|p\.|q\.|h\.|x\.)", 
            RegexOptions.IgnoreCase
        );

        if (streetMatch.Success)
        {
            parts.Add(streetMatch.Groups[1].Value.Trim());
            input = input[streetMatch.Groups[1].Value.Length..].Trim();
        }

        // Key administrative units in Vietnamese addresses
        var adminUnits = new[] {
            "phường", "quận", "huyện", "thành phố", "tp", "tỉnh", "xã", "thị xã", "tx", "thị trấn", "tt", "phố", "đường"
        };

        // Find and extract admin units
        foreach (var unit in adminUnits)
        {
            var index = input.IndexOf(unit, StringComparison.OrdinalIgnoreCase);
            if (index > 0)
            {
                // Look for the previous unit chunk
                var prevPart = input[..index].Trim();
                if (!string.IsNullOrEmpty(prevPart) && !parts.Contains(prevPart))
                {
                    parts.Add(prevPart);
                }
                
                // Find the complete admin unit chunk
                var startIndex = index;
                var endIndex = FindNextAdminUnitIndex(input, startIndex + unit.Length, adminUnits);
                
                if (endIndex > startIndex)
                {
                    var adminUnitPart = input[startIndex..endIndex].Trim();
                    if (!string.IsNullOrEmpty(adminUnitPart) && !parts.Contains(adminUnitPart))
                    {
                        parts.Add(adminUnitPart);
                    }
                    input = input[endIndex..].Trim();
                }
                else
                {
                    // This is the last admin unit
                    var adminUnitPart = input[startIndex..].Trim();
                    if (!string.IsNullOrEmpty(adminUnitPart) && !parts.Contains(adminUnitPart))
                    {
                        parts.Add(adminUnitPart);
                    }
                    input = "";
                    break;
                }
            }
        }

        // Add any remaining part
        if (!string.IsNullOrWhiteSpace(input) && !parts.Contains(input))
        {
            parts.Add(input);
        }

        return parts;
    }
    
    private static int FindNextAdminUnitIndex(string input, int startIndex, string[] adminUnits)
    {
        int minIndex = input.Length;
        
        foreach (var unit in adminUnits)
        {
            int index = input.IndexOf(unit, startIndex, StringComparison.OrdinalIgnoreCase);
            if (index > 0 && index < minIndex)
            {
                minIndex = index;
            }
        }
        
        return minIndex;
    }
}
