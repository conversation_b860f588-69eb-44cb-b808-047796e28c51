using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Core.Bulk;
using Microsoft.Extensions.Logging;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.SearchSync;

/// <summary>
/// Dịch vụ thao tác với dữ liệu lịch sử tìm kiếm trong Elasticsearch
/// </summary>
public class ElasticsearchDataService
{
    private readonly ILogger _logger;
    private readonly ElasticsearchClient _elasticClient;
    private readonly string _indexName;
    private const int FULL_SYNC_DAYS_THRESHOLD = 7; // Số ngày kể từ lần sync cuối để trigger full sync

    public ElasticsearchDataService(
        ILogger logger,
        ElasticsearchClient elasticClient,
        string indexName)
    {
        _logger = logger;
        _elasticClient = elasticClient;
        _indexName = indexName;
    }

    /// <summary>
    /// L<PERSON>y giá trị ID lớn nhất từ Elasticsearch
    /// </summary>
    public async Task<string?> GetMaxIdFromElasticsearch(CancellationToken stoppingToken)
    {
        try
        {
            // Kiểm tra xem index có tồn tại hay không
            var indexExistsResponse = await _elasticClient.Indices.ExistsAsync(_indexName, stoppingToken);
            if (!indexExistsResponse.Exists)
            {
                _logger.LogWarning("ES index '{IndexName}' doesn't exist", _indexName);
                return null;
            }

            // Tìm giá trị ID lớn nhất sử dụng max aggregation
            var searchResponse = await _elasticClient.SearchAsync<UserSearchHistoryEs>(s => s
                .Index(_indexName)
                .Size(0)
                .Aggregations(aggs => aggs
                    .Add("max_id", d =>
                    {
                        d.Max(x =>
                        {
                            x.Field(f => f.Id);
                        });
                    })
                ), stoppingToken);

            if (!searchResponse.IsValidResponse)
            {
                _logger.LogError("Error retrieving max ID from ES: {Error}", searchResponse.DebugInformation);
                return null;
            }

            if (searchResponse.Aggregations == null)
            {
                _logger.LogWarning("No aggregations returned from ES");
                return null;
            }

            var maxAgg = searchResponse.Aggregations.GetMax("max_id");
            if (maxAgg == null || maxAgg.Value == null)
            {
                _logger.LogWarning("No max aggregation results found");
                return null;
            }

            // Vì Id là string, nên không thể dùng max aggregation trực tiếp, có thể cần sửa lại logic này nếu cần
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving max ID from ES");
            return null;
        }
    }

    /// <summary>
    /// Lấy tổng số lượng documents trong index
    /// </summary>
    public async Task<long> GetTotalCountAsync(CancellationToken stoppingToken)
    {
        try
        {
            var response = await _elasticClient.CountAsync<UserSearchHistoryEs>(_indexName, stoppingToken);
            if (response.IsValidResponse)
            {
                return response.Count;
            }
            _logger.LogError("Error getting count from Elasticsearch: {Error}", response.DebugInformation);
            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting total count from Elasticsearch");
            throw;
        }
    }

    /// <summary>
    /// Thực hiện upsert (update or insert) các documents vào Elasticsearch
    /// </summary>
    public async Task UpsertDocumentsAsync(List<UserSearchHistoryEs> documents, CancellationToken stoppingToken)
    {
        try
        {
            if (!documents.Any())
                return;

            // Thực hiện upsert theo batch để tối ưu hiệu suất
            foreach (var batch in documents.Chunk(10000))
            {
                var bulkRequest = new BulkRequest(_indexName)
                {
                    Operations = new List<IBulkOperation>()
                };

                foreach (var doc in batch)
                {
                    bulkRequest.Operations.Add(new BulkUpdateOperation<UserSearchHistoryEs, object>(doc.Id)
                    {
                        Doc = doc,
                        DocAsUpsert = true,
                        RetryOnConflict = 3
                    });
                }

                var response = await _elasticClient.BulkAsync(bulkRequest, stoppingToken);

                if (!response.IsValidResponse)
                {
                    _logger.LogError("Error upserting documents to ES: {Error}", response.DebugInformation);
                }
                else if (response.Errors)
                {
                    // Log các lỗi cụ thể nếu có
                    foreach (var item in response.Items.Where(i => i.Error != null))
                    {
                        _logger.LogError("Upsert error for id {Id}: {Reason}", item.Id, item.Error?.Reason);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error upserting documents to ES");
            throw;
        }
    }

    /// <summary>
    /// Xóa lịch sử tìm kiếm khỏi Elasticsearch
    /// </summary>
    public async Task<bool> DeleteAsync(string id, CancellationToken stoppingToken)
    {
        try
        {
            var response = await _elasticClient.DeleteAsync<UserSearchHistoryEs>(id, d => d.Index(_indexName), stoppingToken);
            if (response.IsValidResponse)
            {
                _logger.LogInformation("Successfully deleted search history {Id} from Elasticsearch", id);
                return true;
            }
            else
            {
                _logger.LogError("Failed to delete search history {Id}: {Error}", id, response.DebugInformation);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting search history {Id} from Elasticsearch", id);
            throw;
        }
    }

    /// <summary>
    /// Kiểm tra xem lịch sử tìm kiếm có tồn tại trong Elasticsearch không
    /// </summary>
    public async Task<bool> ExistsAsync(string id, CancellationToken stoppingToken)
    {
        try
        {
            var response = await _elasticClient.ExistsAsync<UserSearchHistoryEs>(id, d => d.Index(_indexName), stoppingToken);
            return response.Exists;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if search history {Id} exists in Elasticsearch", id);
            return false;
        }
    }

    /// <summary>
    /// Lấy lịch sử tìm kiếm từ Elasticsearch theo ID
    /// </summary>
    public async Task<UserSearchHistoryEs?> GetByIdAsync(string id, CancellationToken stoppingToken)
    {
        try
        {
            var response = await _elasticClient.GetAsync<UserSearchHistoryEs>(id, g => g.Index(_indexName), stoppingToken);
            if (response.IsValidResponse && response.Found)
            {
                return response.Source;
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving search history {Id} from Elasticsearch", id);
            throw;
        }
    }

    /// <summary>
    /// Lấy CreatedAt lớn nhất từ Elasticsearch
    /// </summary>
    public async Task<DateTime?> GetLatestCreatedAtAsync(CancellationToken stoppingToken)
    {
        try
        {
            // Kiểm tra xem index có tồn tại hay không
            var indexExistsResponse = await _elasticClient.Indices.ExistsAsync(_indexName, stoppingToken);
            if (!indexExistsResponse.Exists)
            {
                _logger.LogWarning("ES index '{IndexName}' doesn't exist", _indexName);
                return null;
            }

            // Sử dụng sort để lấy document có CreatedAt lớn nhất
            var searchResponse = await _elasticClient.SearchAsync<UserSearchHistoryEs>(s => s
                .Index(_indexName)
                .Size(1)
                .Sort(srt =>
                {
                    srt.Field(f => f.CreatedAt, f => f.Order(SortOrder.Desc));
                })
            , stoppingToken);

            if (!searchResponse.IsValidResponse)
            {
                _logger.LogError("Error retrieving latest CreatedAt from ES: {Error}", searchResponse.DebugInformation);
                return null;
            }

            var doc = searchResponse.Documents.FirstOrDefault();
            return doc?.CreatedAt;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving latest CreatedAt from ES");
            return null;
        }
    }
}
