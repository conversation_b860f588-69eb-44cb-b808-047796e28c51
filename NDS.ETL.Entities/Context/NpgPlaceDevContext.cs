using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using NDS.ETL.Entities.PostgresPlaceDev;

namespace NDS.ETL.Entities.Context;

public partial class NpgPlaceDevContext : DbContext
{
    public NpgPlaceDevContext()
    {
    }

    public NpgPlaceDevContext(DbContextOptions<NpgPlaceDevContext> options)
        : base(options)
    {
    }

    public virtual DbSet<BlockedPlace> BlockedPlaces { get; set; }

    public virtual DbSet<BookingHistoriesItem> BookingHistoriesItems { get; set; }

    public virtual DbSet<BookingHistory> BookingHistories { get; set; }

    public virtual DbSet<BookingPlacePair> BookingPlacePairs { get; set; }

    public virtual DbSet<BuildingBoundary> BuildingBoundaries { get; set; }

    public virtual DbSet<CombinationOsmWay> CombinationOsmWays { get; set; }

    public virtual DbSet<CombinationOsmWaysCopy1> CombinationOsmWaysCopy1s { get; set; }

    public virtual DbSet<Country> Countries { get; set; }

    public virtual DbSet<Customer> Customers { get; set; }

    public virtual DbSet<District> Districts { get; set; }

    public virtual DbSet<Districts1> Districts1s { get; set; }

    public virtual DbSet<EsQueue> EsQueues { get; set; }

    public virtual DbSet<FavoritePlace> FavoritePlaces { get; set; }

    public virtual DbSet<Geometry> Geometries { get; set; }

    public virtual DbSet<HiddenPlace> HiddenPlaces { get; set; }

    public virtual DbSet<HiddenPlacesBak> HiddenPlacesBaks { get; set; }

    public virtual DbSet<JobConfig> JobConfigs { get; set; }

    public virtual DbSet<JwtClient> JwtClients { get; set; }

    public virtual DbSet<JwtClientsTest> JwtClientsTests { get; set; }

    public virtual DbSet<OsmWayBatch> OsmWayBatches { get; set; }

    public virtual DbSet<Place> Places { get; set; }

    public virtual DbSet<PlaceChangeHistory> PlaceChangeHistories { get; set; }

    public virtual DbSet<PlacesDetail> PlacesDetails { get; set; }

    public virtual DbSet<PlacesOrg> PlacesOrgs { get; set; }

    public virtual DbSet<PlacesProcessingCluster> PlacesProcessingClusters { get; set; }

    public virtual DbSet<PlacesProcessingClustersCopy1> PlacesProcessingClustersCopy1s { get; set; }

    public virtual DbSet<PlacesQueue> PlacesQueues { get; set; }

    public virtual DbSet<PlacesRemove> PlacesRemoves { get; set; }

    public virtual DbSet<PlacesScan2> PlacesScan2s { get; set; }

    public virtual DbSet<PlacesScanHistory> PlacesScanHistories { get; set; }

    public virtual DbSet<PlacesScanResult> PlacesScanResults { get; set; }

    public virtual DbSet<PlanetOsmWay> PlanetOsmWays { get; set; }

    public virtual DbSet<Province> Provinces { get; set; }

    public virtual DbSet<ProvincesBak> ProvincesBaks { get; set; }

    public virtual DbSet<RecommendPlaceConfig> RecommendPlaceConfigs { get; set; }

    public virtual DbSet<SubPlace> SubPlaces { get; set; }

    public virtual DbSet<SubplaceChangeHistory> SubplaceChangeHistories { get; set; }

    public virtual DbSet<TrustedPlace> TrustedPlaces { get; set; }

    public virtual DbSet<UserBookingHistory> UserBookingHistories { get; set; }

    public virtual DbSet<VwPlace> VwPlaces { get; set; }

    public virtual DbSet<Ward> Wards { get; set; }

    public virtual DbSet<WardsBak> WardsBaks { get; set; }
   
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder
            .HasPostgresExtension("postgis")
            .HasPostgresExtension("unaccent");

        modelBuilder.Entity<BookingHistoriesItem>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("booking_histories_item_pkey");
        });

        modelBuilder.Entity<BookingHistory>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("booking_histories_pkey");
        });

        modelBuilder.Entity<BookingPlacePair>(entity =>
        {
            entity.HasIndex(e => e.Ts, "IX_booking_place_pairs_ts").HasMethod("gin");

            entity.HasIndex(e => e.Ts2, "IX_booking_place_pairs_ts2").HasMethod("gin");

            entity.Property(e => e.Ts).HasComputedColumnSql("to_tsvector('english'::regconfig, (((((((COALESCE(\"fromAddress\", ''::character varying))::text || ' '::text) || (COALESCE(\"fromFullAddress\", ''::character varying))::text) || ' '::text) || (COALESCE(\"toAddress\", ''::character varying))::text) || ' '::text) || (COALESCE(\"toFullAddress\", ''::character varying))::text))", true);
            entity.Property(e => e.Ts2).HasComputedColumnSql("to_tsvector('english'::regconfig, regexp_replace(((COALESCE(vn_unaccent((\"fromFullAddress\")::text), ''::text) || ' '::text) || COALESCE(vn_unaccent((\"toFullAddress\")::text), ''::text)), '[^\\w]+'::text, ' '::text, 'g'::text))", true);
        });

        modelBuilder.Entity<BuildingBoundary>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedOnAdd();
        });

        modelBuilder.Entity<CombinationOsmWay>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("processed_osm_ways_copy1_pkey");

            entity.Property(e => e.Oneway).HasDefaultValue(false);
            entity.Property(e => e.Processed).HasDefaultValue(false);
            entity.Property(e => e.SameAdministration).HasDefaultValue(false);
        });

        modelBuilder.Entity<CombinationOsmWaysCopy1>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("combination_osm_ways_copy1_pkey");

            entity.Property(e => e.Oneway).HasDefaultValue(false);
            entity.Property(e => e.Processed).HasDefaultValue(false);
            entity.Property(e => e.SameAdministration).HasDefaultValue(false);
        });

        modelBuilder.Entity<Country>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("countries_pkey");
        });

        modelBuilder.Entity<Customer>(entity =>
        {
            entity.HasKey(e => e.UserId).HasName("customers_pkey");
        });

        modelBuilder.Entity<District>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("districts_pkey");

            entity.Property(e => e.SameStreetName).HasDefaultValue(false);
        });

        modelBuilder.Entity<Districts1>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("districts1_pkey");

            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<EsQueue>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("es_queue_pk");

            entity.Property(e => e.CreateDate).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdateDate).HasDefaultValueSql("CURRENT_TIMESTAMP");
        });

        modelBuilder.Entity<HiddenPlace>(entity =>
        {
            entity.HasIndex(e => e.Location, "IX_hidden_places_location").HasMethod("gist");

            entity.HasIndex(e => e.Ts, "IX_hidden_places_ts").HasMethod("gin");

            entity.HasIndex(e => e.Ts2, "IX_hidden_places_ts2").HasMethod("gin");

            entity.Property(e => e.Pending).HasDefaultValue(false);
            entity.Property(e => e.QuoteCount).HasDefaultValue(0);
            entity.Property(e => e.Ts).HasComputedColumnSql("to_tsvector('english'::regconfig, (((((COALESCE(name, ''::character varying))::text || ' '::text) || (\"fullAddress\")::text) || ' '::text) || (COALESCE(keywords, ''::character varying))::text))", true);
            entity.Property(e => e.Ts2).HasComputedColumnSql("to_tsvector('english'::regconfig, regexp_replace(((((COALESCE(vn_unaccent((name)::text), ''::text) || ' '::text) || COALESCE(vn_unaccent((\"fullAddress\")::text), ''::text)) || ' '::text) || COALESCE(vn_unaccent((keywords)::text), ''::text)), '[^\\w]+'::text, ' '::text, 'g'::text))", true);
        });

        modelBuilder.Entity<JwtClient>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("jwt_clients_pkey");
        });

        modelBuilder.Entity<JwtClientsTest>(entity =>
        {
            entity.Property(e => e.Id).HasDefaultValueSql("nextval('jwt_clients_id_seq'::regclass)");
        });

        modelBuilder.Entity<OsmWayBatch>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("osm_way_batches_pkey");

            entity.Property(e => e.SameAdministration).HasDefaultValue(false);
        });

        modelBuilder.Entity<Place>(entity =>
        {
            entity.HasIndex(e => e.Location, "IX_places_location").HasMethod("gist");

            entity.HasIndex(e => e.Ts, "IX_places_ts").HasMethod("gin");

            entity.HasIndex(e => e.Ts2, "IX_places_ts2").HasMethod("gin");

            entity.Property(e => e.BookingCount).HasDefaultValue(0);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("'0001-01-01 00:00:00'::timestamp without time zone");
            entity.Property(e => e.HasChild).HasDefaultValue(false);
            entity.Property(e => e.Pending).HasDefaultValue(false);
            entity.Property(e => e.QuoteCount).HasDefaultValue(0);
            entity.Property(e => e.Ts).HasComputedColumnSql("to_tsvector('english'::regconfig, (((((COALESCE(name, ''::character varying))::text || ' '::text) || (\"fullAddress\")::text) || ' '::text) || (COALESCE(keywords, ''::character varying))::text))", true);
            entity.Property(e => e.Ts2).HasComputedColumnSql("to_tsvector('english'::regconfig, regexp_replace(((((COALESCE(vn_unaccent((name)::text), ''::text) || ' '::text) || COALESCE(vn_unaccent((\"fullAddress\")::text), ''::text)) || ' '::text) || COALESCE(vn_unaccent((keywords)::text), ''::text)), '[^\\w]+'::text, ' '::text, 'g'::text))", true);
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("'0001-01-01 00:00:00'::timestamp without time zone");
        });

        modelBuilder.Entity<PlaceChangeHistory>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedOnAdd();
            entity.Property(e => e.OldId).HasDefaultValue(0L);
        });

        modelBuilder.Entity<PlacesDetail>(entity =>
        {
            entity.HasIndex(e => e.AddressComponents, "IX_places_details_address_components").HasMethod("gin");

            entity.HasIndex(e => e.Types, "IX_places_details_types").HasMethod("gin");
        });

        modelBuilder.Entity<PlacesOrg>(entity =>
        {
            entity.Property(e => e.BookingCount).HasDefaultValue(0);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("'0001-01-01 00:00:00'::timestamp without time zone");
            entity.Property(e => e.HasChild).HasDefaultValue(false);
            entity.Property(e => e.Id).ValueGeneratedOnAdd();
            entity.Property(e => e.IsVietnam).HasDefaultValue(true);
            entity.Property(e => e.Pending).HasDefaultValue(false);
            entity.Property(e => e.QuoteCount).HasDefaultValue(0);
            entity.Property(e => e.Ts).HasComputedColumnSql("to_tsvector('english'::regconfig, (((((COALESCE(name, ''::character varying))::text || ' '::text) || (\"fullAddress\")::text) || ' '::text) || (COALESCE(keywords, ''::character varying))::text))", true);
            entity.Property(e => e.Ts2).HasComputedColumnSql("to_tsvector('english'::regconfig, regexp_replace(((((COALESCE(vn_unaccent((name)::text), ''::text) || ' '::text) || COALESCE(vn_unaccent((\"fullAddress\")::text), ''::text)) || ' '::text) || COALESCE(vn_unaccent((keywords)::text), ''::text)), '[^\\w]+'::text, ' '::text, 'g'::text))", true);
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("'0001-01-01 00:00:00'::timestamp without time zone");
        });

        modelBuilder.Entity<PlacesProcessingCluster>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedOnAdd();
        });

        modelBuilder.Entity<PlacesProcessingClustersCopy1>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedOnAdd();
        });

        modelBuilder.Entity<PlacesQueue>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("places_queue_pk");

            entity.Property(e => e.CreateDate).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.Status).HasDefaultValue(1L);
            entity.Property(e => e.UpdateDate).HasDefaultValueSql("CURRENT_TIMESTAMP");
        });

        modelBuilder.Entity<PlacesScan2>(entity =>
        {
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("'0001-01-01 00:00:00'::timestamp without time zone");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("'0001-01-01 00:00:00'::timestamp without time zone");
        });

        modelBuilder.Entity<Province>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedOnAdd();
            entity.Property(e => e.SameStreetName).HasDefaultValue(false);
            entity.Property(e => e.Type).HasDefaultValue(0);
        });

        modelBuilder.Entity<SubPlace>(entity =>
        {
            entity.HasIndex(e => e.Location, "IX_sub_places_location").HasMethod("gist");

            entity.HasIndex(e => e.Ts, "IX_sub_places_ts").HasMethod("gin");

            entity.HasIndex(e => e.Ts2, "IX_sub_places_ts2").HasMethod("gin");

            entity.Property(e => e.BookingCount).HasDefaultValue(0);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("'0001-01-01 00:00:00'::timestamp without time zone");
            entity.Property(e => e.Pending).HasDefaultValue(false);
            entity.Property(e => e.QuoteCount).HasDefaultValue(0);
            entity.Property(e => e.Ts).HasComputedColumnSql("to_tsvector('english'::regconfig, (((((COALESCE(name, ''::character varying))::text || ' '::text) || (\"fullAddress\")::text) || ' '::text) || (COALESCE(keywords, ''::character varying))::text))", true);
            entity.Property(e => e.Ts2).HasComputedColumnSql("to_tsvector('english'::regconfig, regexp_replace(((((COALESCE(vn_unaccent((name)::text), ''::text) || ' '::text) || COALESCE(vn_unaccent((\"fullAddress\")::text), ''::text)) || ' '::text) || COALESCE(vn_unaccent((keywords)::text), ''::text)), '[^\\w]+'::text, ' '::text, 'g'::text))", true);
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("'0001-01-01 00:00:00'::timestamp without time zone");

            entity.HasOne(d => d.Parent).WithMany(p => p.SubPlaces).OnDelete(DeleteBehavior.Restrict);
        });

        modelBuilder.Entity<SubplaceChangeHistory>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedOnAdd();
            entity.Property(e => e.OldSubId).HasDefaultValue(0L);
        });

        modelBuilder.Entity<TrustedPlace>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedOnAdd();
        });

        modelBuilder.Entity<UserBookingHistory>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedOnAdd();
        });

        modelBuilder.Entity<VwPlace>(entity =>
        {
            entity.ToView("vw_places");
        });

        modelBuilder.Entity<Ward>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("wards_pkey");

            entity.Property(e => e.Processed).HasDefaultValue(false);
            entity.Property(e => e.SameStreetName).HasDefaultValue(false);
        });
        modelBuilder.HasSequence("countries_id_seq");
        modelBuilder.HasSequence("hibernate_sequence");
        modelBuilder.HasSequence("public.user_booking_histories_SEQ");
        modelBuilder.HasSequence("user_booking_histories_seq");
        modelBuilder.HasSequence("user_booking_histories_SEQ");

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
