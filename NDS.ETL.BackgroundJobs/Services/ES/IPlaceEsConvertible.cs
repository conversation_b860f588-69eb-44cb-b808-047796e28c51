using NDS.ETL.BackgroundJobs.Model.ElasticSearch;

namespace NDS.ETL.BackgroundJobs.Services.ES;

/// <summary>
/// Interface cho các loại dữ liệu có thể chuyển đổi thành PlacesEs
/// </summary>
public interface IPlaceEsConvertible
{
    /// <summary>
    /// Chuyển đổi đối tượng thành PlacesEs
    /// </summary>
    /// <returns>Đối tượng PlacesEs</returns>
    PlacesEs ToPlacesEs();

    /// <summary>
    /// ID của địa điểm
    /// </summary>
    string? PlaceId { get; }
}