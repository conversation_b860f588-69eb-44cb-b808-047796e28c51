using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Threading.Tasks;

namespace NDS.ETL.Infrastructure.Common
{
    /// <summary>
    /// Triển khai khóa phân tán trong bộ nhớ (chỉ có hiệu lực trong cùng một tiến trình)
    /// </summary>
    public class InMemoryDistributedLock : IDistributedLock
    {
        private readonly ILogger<InMemoryDistributedLock> _logger;
        private readonly ConcurrentDictionary<string, InMemoryLockInfo> _locks = new();
        private readonly string _instanceId;

        public InMemoryDistributedLock(ILogger<InMemoryDistributedLock> logger)
        {
            _logger = logger;
            _instanceId = $"{Environment.MachineName}:{Guid.NewGuid():N}";
            
            _logger.LogWarning("Using InMemoryDistributedLock - this is only effective within a single process and should not be used in a distributed environment!");
        }

        /// <summary>
        /// C<PERSON> gắng lấy khóa in-memory
        /// </summary>
        public Task<bool> TryAcquireLockAsync(string key, TimeSpan expiry)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentNullException(nameof(key));

            var now = DateTime.UtcNow;
            var expiryTime = now.Add(expiry);
            
            // Tạo thông tin khóa mới
            var newLock = new InMemoryLockInfo
            {
                InstanceId = _instanceId,
                CreatedAt = now,
                ExpiryTime = expiryTime
            };

            // Cố gắng thêm khóa mới hoặc cập nhật nếu khóa hiện tại đã hết hạn
            bool acquired = _locks.AddOrUpdate(
                key,
                // Thêm mới nếu khóa chưa tồn tại
                _ => newLock,
                // Cập nhật nếu khóa đã tồn tại và đã hết hạn
                (_, existingLock) =>
                {
                    if (existingLock.ExpiryTime <= now)
                    {
                        return newLock; // Khóa đã hết hạn, thay thế bằng khóa mới
                    }
                    return existingLock; // Khóa vẫn còn hiệu lực, giữ nguyên
                }
            ).InstanceId == _instanceId;

            if (acquired)
            {
                _logger.LogDebug($"Acquired in-memory lock '{key}' for instance {_instanceId}");
            }
            else
            {
                _logger.LogDebug($"Failed to acquire in-memory lock '{key}', already held by another caller");
            }

            return Task.FromResult(acquired);
        }

        /// <summary>
        /// Giải phóng khóa in-memory
        /// </summary>
        public Task ReleaseLockAsync(string key)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentNullException(nameof(key));

            if (_locks.TryGetValue(key, out var lockInfo))
            {
                if (lockInfo.InstanceId == _instanceId)
                {
                    _locks.TryRemove(key, out _);
                    _logger.LogDebug($"Released in-memory lock '{key}' for instance {_instanceId}");
                }
                else
                {
                    _logger.LogWarning($"Cannot release in-memory lock '{key}' as it is owned by another instance. Current: {lockInfo.InstanceId}, Ours: {_instanceId}");
                }
            }
            else
            {
                _logger.LogDebug($"Lock '{key}' not found when trying to release");
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// Giải phóng khóa bất kể owner là ai
        /// </summary>
        public Task ForceReleaseLockAsync(string key)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentNullException(nameof(key));

            if (_locks.TryGetValue(key, out var lockInfo))
            {
                _locks.TryRemove(key, out _);
                _logger.LogWarning($"Force released in-memory lock '{key}' owned by {lockInfo.InstanceId} (our instance: {_instanceId})");
            }
            else
            {
                _logger.LogDebug($"Force release not needed for '{key}', lock does not exist");
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// Lấy thông tin về khóa hiện tại
        /// </summary>
        public Task<LockInfo> GetLockInfoAsync(string key)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentNullException(nameof(key));

            if (_locks.TryGetValue(key, out var inMemoryLockInfo))
            {
                // Kiểm tra xem khóa có hết hạn không
                if (inMemoryLockInfo.ExpiryTime <= DateTime.UtcNow)
                {
                    // Khóa đã hết hạn, xóa nó
                    _locks.TryRemove(key, out _);
                    return Task.FromResult<LockInfo>(null);
                }

                // Chuyển đổi từ InMemoryLockInfo sang LockInfo
                var lockInfo = new LockInfo
                {
                    OwnerId = inMemoryLockInfo.InstanceId,
                    CreatedAt = inMemoryLockInfo.CreatedAt,
                    ExpiryTime = inMemoryLockInfo.ExpiryTime
                };

                return Task.FromResult(lockInfo);
            }

            return Task.FromResult<LockInfo>(null);
        }

        /// <summary>
        /// Class để lưu trữ thông tin về khóa
        /// </summary>
        private class InMemoryLockInfo
        {
            public string InstanceId { get; set; }
            public DateTime CreatedAt { get; set; }
            public DateTime ExpiryTime { get; set; }
        }
    }
} 