<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<RuntimeIdentifiers>win-x64;linux-x64</RuntimeIdentifiers>
		<SatelliteResourceLanguages>en-US;en</SatelliteResourceLanguages>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="Elastic.Clients.Elasticsearch" Version="8.17.4" />
		<PackageReference Include="Hangfire" Version="1.8.15" />
		<PackageReference Include="Hangfire.Core" Version="1.8.15" />
		<PackageReference Include="Hangfire.Dashboard.BasicAuthorization" Version="1.0.2" />
		<PackageReference Include="Hangfire.MemoryStorage" Version="1.8.1.1" />
		<PackageReference Include="Hangfire.PostgreSql" Version="1.20.12" />
		<PackageReference Include="Hangfire.Storage.SQLite" Version="0.4.2" />
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.2" />
		<PackageReference Include="StackExchange.Redis" Version="2.8.37" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
		<PackageReference Include="AspNetCore.HealthChecks.ApplicationStatus" Version="8.0.0" />
		<PackageReference Include="pocketken.H3" Version="4.0.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="8.1.1" />
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL.NodaTime" Version="8.0.10" />
		
		<PackageReference Include="Microsoft.Extensions.Logging.Log4Net.AspNetCore" Version="8.0.0" />
		<PackageReference Include="Gelf4Net" Version="3.0.1.2" />
	</ItemGroup>

	<ItemGroup>
	  <Content Include="..\..\.dockerignore">
		<Link>.dockerignore</Link>
	  </Content>
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\log4net.Kafka.Appender\log4net.Kafka.Appender.csproj" />
	  <ProjectReference Include="..\NDS.ETL.Entities\NDS.ETL.Entities.csproj" />
	  <ProjectReference Include="..\NDS.ETL.Infrastructure\NDS.ETL.Infrastructure.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Logs\" />
	</ItemGroup>

</Project>
