using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Core.Search;
using Elastic.Clients.Elasticsearch.QueryDsl;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.Infrastructure.Extensions;
using System.Text.Json;
using System.Text.RegularExpressions;
using RegexOptions = System.Text.RegularExpressions.RegexOptions;

namespace NDS.ETL.BackgroundJobs.Extensions
{
    public static class ESPlacesExtensions
    {
        // Định nghĩa các regex pattern tĩnh để tránh khởi tạo lặp lại
        private static readonly Regex AddressNumberPattern = new(@"^(\d+(?:\/|\\|,|\.|\/\d+|\\\d+|,\d+|\.\d+|\d+)*[a-zA-Z]?)\s+(.+)$", RegexOptions.Compiled);
        private static readonly Regex AlleyPattern = new(@"^(ngõ|ngách|hẻm|ngach|hem|ngo)\s+(\d+(?:\/|\\|,|\.|\/\d+|\\\d+|,\d+|\.\d+|\d+)*[a-zA-Z]?)[\/\\,\.]?(\d+[a-zA-Z]?)?\s+(.+)$", RegexOptions.IgnoreCase | RegexOptions.Compiled);
        private static readonly Regex StreetIndicatorPattern = new(@"^(đ\.|d\.|đường|duong)\s", RegexOptions.IgnoreCase | RegexOptions.Compiled);
        private static readonly Regex CleanAddressPattern = new(@"\b(đ\.|d\.|p\.|phố\.|pho\.|đường|duong|phường|phuong)\s", RegexOptions.IgnoreCase | RegexOptions.Compiled);
        private static readonly Regex FirstNumberPattern = new(@"^(\d+[a-zA-Z]?)", RegexOptions.Compiled);
        private static readonly Regex DigitTextPattern = new(@"(\d+)(?=[A-Za-zÀ-ỹ])", RegexOptions.Compiled);

        /// <summary>
        /// Tạo các điều kiện tìm kiếm cho truy vấn autocomplete
        /// </summary>
        /// <param name="keyword">Từ khóa tìm kiếm gốc</param>
        /// <param name="normalizedKeyword">Từ khóa đã được chuẩn hóa</param>
        /// <param name="forceSearch">Search hỗ trợ sai chính tả</param>
        /// <returns>Danh sách các điều kiện tìm kiếm</returns>
        public static QueryDescriptor<PlacesEs> GetSearchConditions(string keyword, string normalizedKeyword, QueryDescriptor<PlacesEs> query, bool forceSearch = false)
        {
            var hasVietnameseDiacritics = keyword.HasVietnameseDiacritics();
            // Xử lý từ khóa và kiểm tra xem từ khóa có bắt đầu bằng số không
            var searchTerms = ProcessKeyword(keyword, hasVietnameseDiacritics);

            var shouldConditions = new List<Action<QueryDescriptor<PlacesEs>>>
            {
                // Điều kiện 1: Tìm kiếm khớp chính xác trong trường Keywords
                s => CreateShortTermQuery(keyword, hasVietnameseDiacritics, searchTerms, forceSearch)(s),
            };

            // Điều kiện 4: Thêm MultiMatchQuery cho từng search term nếu có
            if (searchTerms?.Any() == true)
            {
                foreach (var searchTerm in searchTerms)
                {
                    var boost = 2.0f;
                    if(searchTerm == keyword) boost = 5.0f; // Tăng boost cho từ khóa gốc
                    if(searchTerm == normalizedKeyword) boost = 1.0f; // Giảm boost cho từ khóa đã chuẩn hóa

                    shouldConditions.Add(s =>
                    {
                        CreateMultiMatchQuery(searchTerm, boost)(s);
                    });
                }
            }

            return query.Bool(b => b
                .Should(shouldConditions.ToArray())
                .MinimumShouldMatch(MinimumShouldMatch.Fixed(1))
            );

            // Bạn có thể dễ dàng thêm hoặc bỏ các điều kiện tìm kiếm ở đây
        }

        /// <summary>
        /// Tạo truy vấn khớp chính xác cho từ khóa
        /// </summary>
        private static Func<QueryDescriptor<PlacesEs>, QueryDescriptor<PlacesEs>> CreateShortTermQuery(string keyword, bool hasVietnameseDiacritics, List<string>? searchTerms = null, bool forceSearch = false)
        {

            if (!forceSearch)
            {
                if (hasVietnameseDiacritics)
                {
                    var conditions = GetDiacriticsSearchConditions(keyword, searchTerms);
                    return q => q
                        .Bool(b => b
                            .Should(conditions)
                            .MinimumShouldMatch(1)
                        );
                }
            }

            var normalConditions = GetNormalSearchConditions(keyword, searchTerms);
            return q => q
                .Bool(b => b
                    .Should(normalConditions)
                    .MinimumShouldMatch(forceSearch ? 1 : 2)
                );
        }

        /// <summary>
        /// Tạo các biến thể số từ chuỗi số có ký tự đặc biệt
        /// </summary>
        private static List<string> CreateNumberVariants(string number)
        {
            var variants = new List<string> { number };

            if (HasSingleSpecialCharacter(number) &&
                (number.Contains('/') || number.Contains('\\') || number.Contains(',') || number.Contains('.')))
            {
                var firstPart = FirstNumberPattern.Match(number).Groups[1].Value;
                if (!string.IsNullOrEmpty(firstPart) && firstPart != number)
                {
                    variants.Add(firstPart);
                }
            }

            return variants;
        }

        /// <summary>
        /// Thêm các search terms với pattern replacement
        /// </summary>
        private static void AddSearchTermsWithCleanup(HashSet<string> uniqueTerms, IEnumerable<string> numberVariants, string rest, bool hasVietnameseDiacritics, bool addStreetTypes = true)
        {
            // Kiểm tra và thay thế các mẫu cụ thể nếu có
            var cleanedRest = CleanAddressPattern.Replace(rest, "").Trim();

            foreach (var variant in numberVariants)
            {
                //if (addStreetTypes)
                //{
                //    uniqueTerms.Add($"{variant} {(hasVietnameseDiacritics ? "đường" : "duong")} {cleanedRest ?? rest}");
                //    uniqueTerms.Add($"{variant} {(hasVietnameseDiacritics ? "phố" : "pho")} {cleanedRest ?? rest}");
                //}
                //else
                //{
                    uniqueTerms.Add($"{variant} {rest}");
                //}

                if (!string.IsNullOrEmpty(cleanedRest) && cleanedRest != rest.Trim())
                {
                    uniqueTerms.Add($"{variant} {cleanedRest}");
                }
            }
        }

        /// <summary>
        /// Xử lý từ khóa tìm kiếm để phát hiện và bổ sung thông tin đường nếu cần
        /// </summary>
        /// <param name="keyword">Từ khóa gốc</param>
        /// <param name="hasVietnameseDiacritics">Có dấu tiếng Việt hay không</param>
        /// <returns>Danh sách các từ khóa đã được xử lý</returns>
        private static List<string>? ProcessKeyword(string keyword, bool hasVietnameseDiacritics)
        {
            if (string.IsNullOrWhiteSpace(keyword))
                return null;

            keyword = keyword.Trim();
            var searchTerms = new List<string>();

            // Kiểm tra xem keyword có chứa tiền tố ngõ/hẻm/ngách không
            var alleyTerms = GetSecondarySearchTerms(keyword, hasVietnameseDiacritics);
            if (alleyTerms?.Count > 0)
            {
                return alleyTerms;
            }

            // Xử lý trường hợp thông thường (không có tiền tố ngõ/hẻm/ngách)
            var addressMatch = AddressNumberPattern.Match(keyword);
            if (addressMatch.Success)
            {
                var number = addressMatch.Groups[1].Value.Trim(',', '.');
                var rest = addressMatch.Groups[2].Value;
                var hasStreetIndicator = StreetIndicatorPattern.IsMatch(rest);
                var numberVariants = CreateNumberVariants(number);
                var uniqueTerms = new HashSet<string>();

                if (!hasStreetIndicator)
                {
                    // Thêm các biến thể với đường/phố
                    AddSearchTermsWithCleanup(uniqueTerms, numberVariants, rest, hasVietnameseDiacritics, true);

                    // Thêm secondary search terms
                    var secondaryTerms = GetSecondarySearchTerms(rest, hasVietnameseDiacritics);
                    if (secondaryTerms?.Count > 0)
                    {
                        foreach (var term in secondaryTerms)
                        {
                            uniqueTerms.Add(term);
                        }
                    }
                }
                else
                {
                    // Đã có street indicator, chỉ cần thêm và clean up
                    AddSearchTermsWithCleanup(uniqueTerms, numberVariants, rest, hasVietnameseDiacritics, false);
                }

                searchTerms.AddRange(uniqueTerms);
            }

            // Thêm biến thể: Thêm dấu cách giữa số và chữ
            if (DigitTextPattern.IsMatch(keyword))
            {
                var alterTerm = DigitTextPattern.Replace(keyword, "$1 ");
                searchTerms.Add(alterTerm);
            }

            if (!searchTerms.Any())
            {
                searchTerms.Add(keyword);
            }
            return searchTerms.Distinct().ToList();
        }

        /// <summary>
        /// Kiểm tra xem chuỗi có chứa mỗi ký tự đặc biệt (/, \, ,, .) tối đa 1 lần hay không
        /// </summary>
        /// <param name="text">Chuỗi cần kiểm tra</param>
        /// <returns>True nếu mỗi ký tự đặc biệt chỉ xuất hiện tối đa 1 lần</returns>
        private static bool HasSingleSpecialCharacter(string text)
        {
            return text.Count(c => c == '/') <= 1 &&
                   text.Count(c => c == '\\') <= 1 &&
                   text.Count(c => c == ',') <= 1 &&
                   text.Count(c => c == '.') <= 1;
        }

        /// <summary>
        /// Xử lý các search terms cho địa chỉ có tiền tố ngõ/hẻm/ngách
        /// </summary>
        private static List<string> GetSecondarySearchTerms(string keyword, bool hasVietnameseDiacritics)
        {
            var alleyMatch = AlleyPattern.Match(keyword);
            if (!alleyMatch.Success)
                return null;

            var alleyType = alleyMatch.Groups[1].Value;
            var alleyNumber = alleyMatch.Groups[2].Value.Trim(',', '.');
            var secondNumber = alleyMatch.Groups[3].Value;
            var rest = alleyMatch.Groups[4].Value;

            // Tạo danh sách chứa các biến thể số
            var numberVariants = new List<string>();

            // Thêm số ngõ/ngách đầy đủ
            numberVariants.Add(string.IsNullOrEmpty(secondNumber)
                ? alleyNumber
                : $"{alleyNumber}/{secondNumber}");

            // Thêm các biến thể số khác
            if (HasSingleSpecialCharacter(alleyNumber) &&
                (alleyNumber.Contains('/') || alleyNumber.Contains('\\') || alleyNumber.Contains(',') || alleyNumber.Contains('.')))
            {
                var firstPart = FirstNumberPattern.Match(alleyNumber).Groups[1].Value;
                if (!string.IsNullOrEmpty(firstPart) && firstPart != alleyNumber)
                {
                    numberVariants.Add(firstPart);
                }
            }

            var hasStreetIndicator = StreetIndicatorPattern.IsMatch(rest);
            var uniqueTerms = new HashSet<string>();

            // Thêm biến thể không có tiền tố ngõ/hẻm/ngách
            AddSearchTermsWithCleanup(uniqueTerms, numberVariants, rest, hasVietnameseDiacritics, !hasStreetIndicator);

            return new List<string>(uniqueTerms);
        }

        /// <summary>
        /// Tạo danh sách điều kiện tìm kiếm cho từ khóa có dấu
        /// </summary>
        private static Action<QueryDescriptor<PlacesEs>>[] GetDiacriticsSearchConditions(string keyword, List<string> keywordWithStreets)
        {
            var conditions = new List<Action<QueryDescriptor<PlacesEs>>>
            {
                //s => s.MatchPhrasePrefix(p => p
                //    .Field(f => f.Name)
                //    .Query(keyword)
                //    .Boost(30)
                //),
                s => s.MatchPhrase(p => p
                    .Field(f => f.Name)
                    .Query(keyword)
                    .Boost(25)
                ),
                s => s.Prefix(p => p
                    .Field(f => f.Keywords.Suffix("raw"))
                    .Value(keyword)
                    .Boost(20)
                ),
                //Gây nặng
                //s => s.MatchPhrasePrefix(p => p
                //    .Field(f => f.Keywords)
                //    .Query(keyword)
                //    .Boost(10)
                //),
                s => s.MatchPhrase(m => m
                    .Field(f => f.Keywords.Suffix("raw"))
                    .Query(keyword)
                    .Boost(5)
                ),
                s => s.Match(m => m
                    .Field(f => f.Keywords.Suffix("raw"))
                    .Query(keyword)
                    .Boost(1)
                )
            };

            // Thêm điều kiện tìm kiếm với từ khóa có "đ." sau số nhà nếu có
            if (keywordWithStreets?.Any() == true)
            {
                foreach (var keywordWithStreet in keywordWithStreets)
                {
                    //conditions.Add(s => s.MatchPhrasePrefix(p => p
                    //    .Field(f => f.AddressPermuted)
                    //    .Query(keywordWithStreet)
                    //    .Boost(28)
                    //));

                    conditions.Add(s => s.MatchPhrase(p => p
                        .Field(f => f.Keywords.Suffix("raw"))
                        .Query(keywordWithStreet)
                        .Boost(15)
                    ));
                }
            }

            return conditions.ToArray();
        }

        /// <summary>
        /// Tạo danh sách điều kiện tìm kiếm cho từ khóa không dấu
        /// </summary>
        private static Action<QueryDescriptor<PlacesEs>>[] GetNormalSearchConditions(string keyword, List<string> keywordWithStreets)
        {
            var conditions = new List<Action<QueryDescriptor<PlacesEs>>>
            {
                //s => s.MatchPhrasePrefix(p => p
                //    .Field(f => f.Name)
                //    .Query(keyword)
                //    .Boost(30)
                //),
                s => s.MatchPhrase(p => p
                    .Field(f => f.Name)
                    .Query(keyword)
                    .Boost(25)
                ),
                //s => s.Prefix(p => p
                //    .Field(f => f.AddressPermuted)
                //    .Value(keyword)
                //    .Boost(20)
                //),
                //s => s.MatchPhrasePrefix(mpp => mpp
                //    .Field(f => f.AddressPermuted)
                //    .Query(keyword)
                //    .Boost(10)
                //),
                s => s.MatchPhrase(m => m
                    .Field(f => f.AddressPermuted)
                    .Query(keyword)
                    .Boost(5)
                ),
                s => s.Match(m => m
                    .Field(f => f.AddressPermuted)
                    .Query(keyword)
                    .Boost(1)
                )
            };


            // Thêm điều kiện tìm kiếm với từ khóa có "đ." sau số nhà nếu có
            if (keywordWithStreets?.Any() == true)
            {
                foreach (var keywordWithStreet in keywordWithStreets)
                {
                    conditions.Add(s => s.MatchPhrase(p => p
                        .Field(f => f.AddressPermuted)
                        .Query(keywordWithStreet)
                        .Boost(15)
                    ));

                    //conditions.Add(s => s.MatchPhrasePrefix(mpp => mpp
                    //    .Field(f => f.AddressPermuted)
                    //    .Query(keywordWithStreet)
                    //    .Boost(8)
                    //));
                }
            }
            return conditions.ToArray();
        }

        /// <summary>
        /// Tạo truy vấn đa trường cho từ khóa
        /// </summary>
        private static Func<QueryDescriptor<PlacesEs>, QueryDescriptor<PlacesEs>> CreateMultiMatchQuery(string query, float boost = 1.0f)
        {
            return s => s.MultiMatch(mm => mm
                .Query(query)
                .Fields(Fields.FromFields([
                    Field.FromString("addressPermuted^3")!, // Trường addressPermuted với boost 3
                    Field.FromString("addressPermuted.suggest^2")!, // Trường suggest với boost 2
                    Field.FromString("keywords.suggest^4")! // Trường keywords với boost 4
                ]))
                .Type(TextQueryType.MostFields)
                .Operator(Operator.And)
                .MinimumShouldMatch("75%")
                .TieBreaker(0.3)
                .Boost(boost));
        }

        /// <summary>
        /// Tạo truy vấn tiền tố cụm từ cho từ khóa
        /// </summary>
        private static Func<QueryDescriptor<PlacesEs>, QueryDescriptor<PlacesEs>> CreatePhrasePrefixQuery(string normalizedKeyword)
        {
            return s => s.MultiMatch(mm => mm
                .Query(normalizedKeyword)
                .Fields(Fields.FromFields([
                    Field.FromString("addressPermuted^3")!,
                    Field.FromString("addressPermuted.suggest^2")!,
                    Field.FromString("keywords.suggest^4")!
                ]))
                .Type(TextQueryType.PhrasePrefix)
                .Slop(0)
                .Operator(Operator.And)
                .MinimumShouldMatch("100%")
                .TieBreaker(0.3));
        }


        /// <summary>
        /// Tạo truy vấn lồng custom tìm kiếm trong các địa điểm con
        /// </summary>
        /// <param name="sh">Query descriptor</param>
        /// <param name="normalizedKeyword">Từ khóa đã được chuẩn hóa</param>
        /// <returns>Truy vấn nested query</returns>
        public static QueryDescriptor<PlacesEs> CreateNestedSubPlacesQuery(this QueryDescriptor<PlacesEs> sh, string normalizedKeyword)
        {
            // Sử dụng truy vấn lồng với nested query
            return sh.Nested(n =>
            {
                n.Path(p => p.SubPlaces)
                 .ScoreMode(ChildScoreMode.Avg)
                 .Query(q =>
                 {
                     q.MultiMatch(mm => mm
                         .Query(normalizedKeyword)
                         .Fields(Fields.FromFields([
                             Field.FromString("subPlaces.addressPermuted^3")!,
                            Field.FromString("subPlaces.addressPermuted.suggest^1")!,
                            Field.FromString("subPlaces.keywords^2")!
                         ]))
                         .Type(TextQueryType.MostFields)
                         .Operator(Operator.And)
                         .MinimumShouldMatch("75%")
                         .TieBreaker(0.3)
                     );
                 });
            });
        }

        /// <summary>
        /// Tạo truy vấn tìm kiếm tự động hoàn thành cho dữ liệu vị trí
        /// </summary>
        /// <param name="client">Elasticsearch client</param>
        /// <param name="page">Số trang (bắt đầu từ 1)</param>
        /// <param name="currentLocation">Vị trí hiện tại dưới dạng GeoLocation</param>
        /// <param name="keyword">Từ khóa tìm kiếm</param>
        /// <param name="pageSize">Số lượng kết quả mỗi trang</param>
        /// <param name="indexName">Tên index</param>
        /// <param name="h3Resolution">Độ phân giải H3 (mặc định: 8)</param>
        /// <param name="extendedRadius">Có sử dụng bán kính mở rộng hay không</param>
        /// <param name="forceSearch">Search hỗ trợ sai chính tả gọi sau khi query đầu không ra kết quả</param>
        /// <returns>SearchRequest cho truy vấn tự động hoàn thành</returns>
        public static SearchRequestDescriptor<PlacesEs> CreateAutocompleteQuery(this ElasticsearchClient client,
            int page,
            GeoLocation currentLocation,
            string keyword,
            int pageSize,
            string indexName,
            int? h3Resolution = null,
            bool extendedRadius = false,
            bool forceSearch = false)
        {

            // Chuẩn hóa từ khóa theo các quy tắc khác
            //var normalizedKeyword = keyword.Contains(" ") ? AddressPermutator.NormalizeVietnamese(keyword) : keyword;
            var normalizedKeyword = keyword;

            currentLocation.TryGetLatitudeLongitude(out var latlng); // Lấy tọa độ lat/lng từ vị trí hiện tại


            var descriptor = new SearchRequestDescriptor<PlacesEs>()
                .Index(indexName) // Chỉ định index cần tìm kiếm
                .Size(10); // Giới hạn số lượng kết quả trả về

            // Thêm suggestions cho autocomplete
            //AddSuggestions(descriptor, keyword, normalizedKeyword, latlng!, extendedRadius); // Bỏ để tối ưu tốc độ

            // Thêm query chính
            AddMainQuery(descriptor, keyword, normalizedKeyword, currentLocation, h3Resolution, extendedRadius, forceSearch);

            // Thêm collapse, source config và script fields
            ConfigureResultOptions(descriptor, latlng);

            // Thêm sorting
            AddSorting(descriptor, currentLocation);

            return descriptor;
        }

        /// <summary>
        /// Thêm suggestion cho autocomplete
        /// </summary>
        private static void AddSuggestions(SearchRequestDescriptor<PlacesEs> descriptor, string keyword, string normalizedKeyword, LatLonGeoLocation latlng, bool extendedRadius)
        {
            if (extendedRadius) return;
            descriptor.Suggest(s => // Tạo suggestion cho autocomplete
            {
                //var input = keyword;
                var input = AddressPermutator.NormalizeVietnamese(keyword, false);
                s.Text(input)
                    .Suggesters(g =>
                    {
                        g.Add("suggest", sg =>
                        {
                            var suffix = extendedRadius ? "completion" : "completion_with_location";

                            void config(CompletionSuggesterDescriptor<PlacesEs> c)
                            {
                                c.Field(f => f.AddressPermuted.Suffix(suffix)) // Field dùng để autocomplete
                                    .Analyzer("address_search") // Phân tích từ khóa
                                    .Size(3) // Số lượng suggestion
                                    .SkipDuplicates(true) // Bỏ qua kết quả trùng lặp
                                    .Fuzzy(f => f // Tìm kiếm mờ
                                            .Fuzziness(new Fuzziness("AUTO")) // Độ mờ tự động
                                            .MinLength(5) // Độ dài tối thiểu để áp dụng fuzzy
                                            .PrefixLength(3) // Số ký tự đầu phải khớp
                                            .Transpositions(true) // Cho phép hoán đổi ký tự
                                            .UnicodeAware(true) // Hỗ trợ Unicode
                                    );

                                if (!extendedRadius)
                                {
                                    //var coordinate = new Coordinate(latlng.Lon, latlng.Lat);
                                    //var h3Index4 = coordinate.ToH3Index(4).ToString();
                                    var suggestContext = "location_50km";

                                    double radiusInKm = H3Helper.GetRadiusKmFromH3Res(4);
                                    var h3CellsInRadius = H3Helper.GetH3CellsInRadius(latlng.Lat, latlng.Lon, radiusInKm, out var res);

                                    //var suggestContext = "location";
                                    c.Contexts(ctx =>
                                    {
                                        var contexts = new List<CompletionContext>
                                        {

                                            //new() { Context = new Elastic.Clients.Elasticsearch.Core.Search.Context(latlng), Boost = 3 }
                                        };
                                        foreach (var cell in h3CellsInRadius)
                                        {
                                            contexts.Add(new CompletionContext
                                            {
                                                Context = new Elastic.Clients.Elasticsearch.Core.Search.Context(cell),
                                                Boost = 3
                                            });
                                        }
                                        return ctx.Add(new Field(suggestContext),
                                            new Union<CompletionContext, ICollection<CompletionContext>>(contexts));
                                    });
                                }
                            }

                            sg.Completion(config);
                        });
                        return g;
                    });
            });
        }

        /// <summary>
        /// Thêm query chính cho tìm kiếm
        /// </summary>
        private static void AddMainQuery(SearchRequestDescriptor<PlacesEs> descriptor, string keyword,
            string normalizedKeyword, GeoLocation currentLocation, int? h3Resolution, bool extendedRadius, bool forceSearch = false)
        {
            descriptor.Query(q =>
            {
                var queryDescriptor = new QueryDescriptor<PlacesEs>();
                var searchQuery = GetSearchConditions(keyword, normalizedKeyword, queryDescriptor, forceSearch);

                q.Bool(b =>
                {
                    b.Must(searchQuery);

                    // Bỏ qua GeoDistance filter nếu extendedRadius = true
                    if (!extendedRadius)
                    {
                        var radiusInKm = 50;

                        //// Lấy tọa độ lat/lng từ vị trí hiện tại
                        currentLocation.TryGetLatitudeLongitude(out var latlng);
                        //var h3CellsInRadius = H3Helper.GetH3CellsInRadius(latlng.Lat, latlng.Lon, radiusInKm, out var res);

                        //Thêm bộ lọc theo H3
                        b.Filter(f => f
                            .Bool(bb => bb
                                .Must(
                                    //Bán kính to sẽ làm giảm độ chính xác của H3, nên chỉ dùng geo_distance vì sẽ bị miss 1 số lượng h3CellsInRadius
                                    //mu => mu.Terms(t => t
                                    //    .Field($"h3.{res}.keyword")
                                    //    .Terms(new TermsQueryField(h3CellsInRadius.Select(h3Idx => (FieldValue)h3Idx).ToArray()))
                                    //),
                                    mu => mu.GeoDistance(g => g
                                        .Field(p => p.Location)
                                        .Distance($"{radiusInKm}km") // Bán kính 50km
                                        .Location(currentLocation)
                                    )
                                )
                            )
                        );

                        //H3 chỉ hiệu quả với số lượng h3CellsInRadius ít
                        //Nên kết hợp geo_distance nếu bạn cần chính xác tuyệt đối theo mét(vì H3 là tổ ong, không phải hình tròn)

                        //b.Filter(f => f
                        //    .GeoDistance(g => g
                        //        .Field(p => p.Location)
                        //        .Distance($"{50:###}km") // Bán kính 50km
                        //        .Location(currentLocation)
                        //    )
                        //);
                    }
                });
            });
        }

        /// <summary>
        /// Cấu hình các tùy chọn kết quả
        /// </summary>
        private static void ConfigureResultOptions(SearchRequestDescriptor<PlacesEs> descriptor, LatLonGeoLocation latlng)
        {
            descriptor
                .Collapse(c => c.Field(x => x.FullAddress.Suffix("keyword")))
                //.Source(new SourceConfig(true)) // Trả về toàn bộ thông tin
                .SourceExcludes(Fields.FromFields(
                    [
                        "h3",
                        "addressPermuted",
                        "masterAddress",
                        "keywords"
                    ]
                )); // Loại bỏ các trường không cần thiết trong kết quả

            // Script này sẽ được thực thi cho mỗi document trong kết quả. Với dataset lớn, việc này rất tốn CPU và có thể là bottleneck chính
            //.ScriptFields(sf => // Thêm trường tính toán
            //    sf.Add("distance", fd =>
            //    {
            //        fd.Script(s => s
            //            .Source("doc['location'].arcDistance(params.lat, params.lon)/1000") // Tính khoảng cách theo km
            //            .Params(p => p
            //                    .Add("lat", latlng.Lat) // Truyền lat vào params
            //                    .Add("lon", latlng.Lon) // Truyền lon vào params
            //            )
            //        );
            //    })
            //);
        }

        /// <summary>
        /// Thêm sắp xếp kết quả
        /// </summary>
        private static void AddSorting(SearchRequestDescriptor<PlacesEs> descriptor, GeoLocation currentLocation)
        {
            descriptor.Sort(
                x =>
                {
                    x.Field(a => a.Popular, fs => fs.Order(SortOrder.Desc)); // Theo độ phổ biến
                },
                x =>
                {
                    x.Field(a => a.Processed, fs => fs.Order(SortOrder.Desc)); // Theo trạng thái xử lý
                },
                x => // Sắp xếp kết quả
                {
                    x.Score(sc => sc.Order(SortOrder.Desc)); // Theo điểm số
                },
                x =>
                {
                    x.GeoDistance(g => g // Theo khoảng cách
                        .Field(p => p.Location)
                        .DistanceType(GeoDistanceType.Plane) // Tính toán theo mặt phẳng
                        .Unit(DistanceUnit.Kilometers) // Đơn vị km
                        .Order(SortOrder.Asc) // Tăng dần
                        .Location(new List<GeoLocation> { currentLocation })
                    );
                });
        }

        /// <summary>
        /// Phương thức hỗ trợ để trích xuất khoảng cách từ JsonElement
        /// </summary>
        public static double GetDistance(this JsonElement jsonElement)
        {
            if (jsonElement.ValueKind == JsonValueKind.Array)
            {
                var distArr = jsonElement.EnumerateArray();

                if (distArr.Any())
                {
                    return distArr.First().GetDouble();
                }
            }

            return 0;
        }
    }
}
