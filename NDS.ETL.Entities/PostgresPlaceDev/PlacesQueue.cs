using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Table("places_queue")]
[Index("Districtid", Name = "places_queue_unique", IsUnique = true)]
public partial class PlacesQueue
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("districtid")]
    public long Districtid { get; set; }

    [Column("provinceid")]
    public long Provinceid { get; set; }

    [Column("status")]
    public long Status { get; set; }

    [Column("create_date", TypeName = "timestamp without time zone")]
    public DateTime CreateDate { get; set; }

    [Column("update_date", TypeName = "timestamp without time zone")]
    public DateTime UpdateDate { get; set; }

    [Column("place_type", TypeName = "character varying")]
    public string PlaceType { get; set; } = null!;
}
