using Microsoft.AspNetCore.Mvc;
using NDS.ETL.BackgroundJobs.Model;
using NDS.ETL.BackgroundJobs.Services;

namespace NDS.ETL.BackgroundJobs.Controllers;

/// <summary>
/// Base controller class for Elasticsearch controllers providing common functionality
/// </summary>
public abstract class ESControllerBase : ControllerBase
{
    protected readonly IRequestInfoLogger RequestInfoLogger;
    protected readonly ILogger Logger;

    protected ESControllerBase(IRequestInfoLogger requestInfoLogger, ILogger logger)
    {
        RequestInfoLogger = requestInfoLogger;
        Logger = logger;
    }

    /// <summary>
    /// Processes request info and logs endpoint usage
    /// </summary>
    protected RequestInfo ProcessRequestInfo(string endpointName)
    {
        // Trích xuất thông tin yêu cầu
        var requestInfo = RequestInfoLogger.ExtractRequestInfo(Request);
        // Ghi log thông tin yêu cầu
        RequestInfoLogger.LogRequestInfo(requestInfo, endpointName);
        return requestInfo;
    }

    /// <summary>
    /// Updates coordinate values from request headers if needed
    /// </summary>
    protected (double, double) UpdateCoordinates(RequestInfo requestInfo, double latitude, double longitude)
    {
        // Cập nhật tọa độ từ headers nếu có và không được cung cấp trong tham số truy vấn
        if (requestInfo.Lat.HasValue && latitude == 0)
        {
            latitude = requestInfo.Lat.Value;
        }

        if (requestInfo.Lng.HasValue && longitude == 0)
        {
            longitude = requestInfo.Lng.Value;
        }

        return (latitude, longitude);
    }

    /// <summary>
    /// Updates user information from request headers if needed
    /// </summary>
    protected (string, string) UpdateUserInfo(RequestInfo requestInfo, string userId, string phoneNumber)
    {
        // Cập nhật thông tin người dùng từ headers nếu có
        if (string.IsNullOrEmpty(userId) && !string.IsNullOrEmpty(requestInfo.UserId))
        {
            userId = requestInfo.UserId;
        }

        if (string.IsNullOrEmpty(phoneNumber) && !string.IsNullOrEmpty(requestInfo.PhoneNumber))
        {
            phoneNumber = requestInfo.PhoneNumber;
        }

        return (userId, phoneNumber);
    }

    /// <summary>
    /// Updates nullable coordinates from request headers if needed
    /// </summary>
    protected (double?, double?) UpdateNullableCoordinates(RequestInfo requestInfo, double? latitude, double? longitude)
    {
        // Cập nhật tọa độ từ headers nếu có
        if (!latitude.HasValue && requestInfo.Lat.HasValue)
        {
            latitude = requestInfo.Lat.Value;
        }

        if (!longitude.HasValue && requestInfo.Lng.HasValue)
        {
            longitude = requestInfo.Lng.Value;
        }

        return (latitude, longitude);
    }

    /// <summary>
    /// Executes API logic with standard error handling
    /// </summary>
    protected async Task<ActionResult<T>> ExecuteWithErrorHandling<T>(Func<Task<T>> action, string apiName)
    {
        try
        {
            var result = await action();
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error in {ApiName} API: {Message}", apiName, ex.Message);
            return StatusCode(500, "An error occurred while processing your request");
        }
    }
} 