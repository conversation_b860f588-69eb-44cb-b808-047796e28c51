using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using NDS.ETL.BackgroundJobs.Services;
using NDS.ETL.Entities.ESEntities;
using System.Reflection;

namespace NDS.ETL.BackgroundJobs.Authentication
{
    public class ApiKeyAuthenticationFilter : IAsyncAuthorizationFilter
    {
        private const string API_KEY_HEADER_NAME = "X-API-Key";
        private readonly IApiKeyValidationService _apiKeyValidationService;
        private readonly ILogger<ApiKeyAuthenticationFilter> _logger;

        public ApiKeyAuthenticationFilter(
            IApiKeyValidationService apiKeyValidationService,
            ILogger<ApiKeyAuthenticationFilter> logger)
        {
            _apiKeyValidationService = apiKeyValidationService;
            _logger = logger;
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            // Skip validation if the endpoint doesn't require API key authentication
            if (!IsProtectedEndpoint(context))
            {
                return;
            }

            // Try to get the API key from the request header
            if (!context.HttpContext.Request.Headers.TryGetValue(API_KEY_HEADER_NAME, out var extractedApiKey))
            {
                _logger.LogWarning("API key was not provided for protected endpoint");
                context.Result = new UnauthorizedObjectResult("API key is required");
                return;
            }

            var apiKey = extractedApiKey.ToString();
            
            // Check if the API key is valid
            var (isValid, permissions) = await _apiKeyValidationService.ValidateApiKeyAsync(apiKey);
            
            if (!isValid)
            {
                _logger.LogWarning("Invalid API key: {ApiKey}", apiKey);
                context.Result = new UnauthorizedObjectResult("Invalid API key");
                return;
            }
            
            // If the API key has All permission, allow access to all endpoints
            if (permissions.Contains(PermissionName.All))
            {
                _logger.LogInformation("API key has All permission, access granted");
                return;
            }

            // Get the required permission from the ApiKey attribute
            var requiredPermission = GetRequiredPermission(context);
            
            if (!string.IsNullOrEmpty(requiredPermission))
            {
                // Check if the API key has the required permission
                if (Enum.TryParse<PermissionName>(requiredPermission, true, out var permission) && 
                    !permissions.Contains(permission))
                {
                    _logger.LogWarning("API key {ApiKey} does not have the required permission: {Permission}", apiKey, requiredPermission);
                    context.Result = new ForbidResult();
                    return;
                }
            }

            // API key is valid and has the required permission
            _logger.LogInformation("API key validation successful");
        }

        private bool IsProtectedEndpoint(AuthorizationFilterContext context)
        {
            // Check if the endpoint has the ApiKey attribute
            var apiKeyAttribute = context.ActionDescriptor.EndpointMetadata.OfType<ApiKeyAttribute>().FirstOrDefault();
            return apiKeyAttribute != null;
        }

        private string? GetRequiredPermission(AuthorizationFilterContext context)
        {
            // Get the ApiKey attribute from the endpoint
            var apiKeyAttribute = context.ActionDescriptor.EndpointMetadata.OfType<ApiKeyAttribute>().FirstOrDefault();
            
            // Get the permission parameter using reflection
            if (apiKeyAttribute != null)
            {
                var permissionField = apiKeyAttribute.GetType().GetField("_permission", BindingFlags.NonPublic | BindingFlags.Instance);
                return permissionField?.GetValue(apiKeyAttribute)?.ToString();
            }
            
            return null;
        }

        private bool HasPermission(string requiredPermission, IEnumerable<PermissionName> permissions)
        {
            // Check if the API key has All permission
            if (permissions.Contains(PermissionName.All))
            {
                return true;
            }

            // Check if the required permission is valid enum value
            if (Enum.TryParse<PermissionName>(requiredPermission, true, out var permission))
            {
                return permissions.Contains(permission);
            }

            return false;
        }
    }
}
