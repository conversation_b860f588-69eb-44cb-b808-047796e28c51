using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch.FavPlaceSync;
using System.Collections.Concurrent;
using System.Diagnostics;
using NDS.ETL.Entities.TaxiMongoDB;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.SearchSync;

/// <summary>
/// Bộ xử lý đồng bộ lịch sử tìm kiếm từ MongoDB sang Elasticsearch (refactor theo FavPlaceSync)
/// </summary>
public class SyncProcessor
{
    private readonly DbDataService _dbDataService;
    private readonly ElasticsearchDataService _esDataService;
    private readonly ILogger<SyncProcessor> _logger;
    private const int MAX_CONCURRENT_TASKS = 2;
    private const int DEFAULT_BATCH_SIZE = 1000;
    private const int DEFAULT_MAX_RETRIES = 3;
    private readonly RecurringJobBaseElastic _recurringJobBase;
    private readonly string _indexName;

    public SyncProcessor(
        DbDataService dbDataService,
        ElasticsearchDataService esDataService,
        ILogger<SyncProcessor> logger,
        RecurringJobBaseElastic recurringJobBase,
        string indexName)
    {
        _dbDataService = dbDataService;
        _esDataService = esDataService;
        _logger = logger;
        _recurringJobBase = recurringJobBase;
        _indexName = indexName;
    }

    /// <summary>
    /// Thực hiện đồng bộ toàn bộ dữ liệu lịch sử tìm kiếm (full sync)
    /// </summary>
    public async Task PerformFullSync(DateTime fromDate, DateTime toDate, CancellationToken stoppingToken, int batchSize = DEFAULT_BATCH_SIZE, int maxRetries = DEFAULT_MAX_RETRIES)
    {
        var syncLock = new object();
        var syncStats = new SyncStats();
        var totalProcessed = 0;
        var failedBatches = new ConcurrentBag<(DateTime, DateTime, string?)>();
        _logger.LogInformation("Starting full search history sync from {FromDate} to {ToDate} (paging)", fromDate, toDate);
        DateTime? lastCreatedAt = null;
        string? lastId = null;
        bool hasMore = true;
        while (hasMore && !stoppingToken.IsCancellationRequested)
        {
            var batchStopwatch = Stopwatch.StartNew();
            var batchRetries = 0;
            var success = false;
            List<UserSearchHistory> searchHistories = new();
            while (!success && batchRetries < maxRetries && !stoppingToken.IsCancellationRequested)
            {
                try
                {
                    searchHistories = await _dbDataService.GetUpdatedSearchHistoriesAsync(fromDate, toDate, batchSize, lastCreatedAt, lastId);
                    if (!searchHistories.Any())
                    {
                        hasMore = false;
                        success = true;
                        break;
                    }
                    var searchHistoryEsList = UserSearchHistoryEs.MapToEsDocuments(searchHistories);
                    if (!searchHistoryEsList.Any())
                    {
                        _logger.LogWarning("No valid search histories to sync after conversion for batch (paging)");
                        hasMore = false;
                        success = true;
                        break;
                    }
                    await IndexDocuments(searchHistoryEsList, stoppingToken);
                    lock (syncLock)
                    {
                        totalProcessed += searchHistoryEsList.Count;
                        syncStats.BatchSuccessCount++;
                        LogBatchProgress("Paging", $"{lastCreatedAt:yyyy-MM-dd HH:mm:ss.fff}-{lastId}", searchHistoryEsList.Count, batchStopwatch.Elapsed.TotalSeconds, totalProcessed, -1);
                    }
                    // Lưu lại lastCreatedAt, lastId cuối cùng của batch này
                    var last = searchHistories.Last();
                    lastCreatedAt = last.CreatedAt.ToLocalTime(); // Convert to local time
                    lastId = last.Id.ToString();
                    success = true;
                }
                catch (Exception ex)
                {
                    batchRetries++;
                    if (batchRetries >= maxRetries)
                    {
                        lock (syncLock)
                        {
                            syncStats.BatchFailureCount++;
                            failedBatches.Add((lastCreatedAt ?? fromDate, toDate, lastId));
                            _logger.LogError(ex, "Failed to process batch (paging) after {MaxRetries} attempts", maxRetries);
                        }
                        hasMore = false;
                    }
                    else
                    {
                        lock (syncLock)
                        {
                            syncStats.RetryCount++;
                            _logger.LogWarning("Retrying batch (paging) (Attempt {Attempt}/{MaxRetries})", batchRetries, maxRetries);
                        }
                        await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, batchRetries)), stoppingToken);
                    }
                }
            }
        }
        _logger.LogInformation("Full sync completed (paging). Total processed: {TotalProcessed}, Success: {Success}, Failed: {Failed}", totalProcessed, syncStats.BatchSuccessCount, syncStats.BatchFailureCount);
    }

    /// <summary>
    /// Thực hiện đồng bộ tăng dần (incremental sync) lịch sử tìm kiếm
    /// </summary>
    public async Task PerformIncrementalSync(DateTime fromDate, DateTime toDate, CancellationToken stoppingToken, int batchSize = DEFAULT_BATCH_SIZE, int maxRetries = DEFAULT_MAX_RETRIES)
    {
        var syncLock = new object();
        var syncStats = new SyncStats();
        var totalProcessed = 0;
        var failedBatches = new ConcurrentBag<(DateTime, DateTime, string?)>();
        _logger.LogInformation("Starting incremental search history sync from {FromDate} to {ToDate} (paging)", fromDate, toDate);
        DateTime? lastCreatedAt = null;
        string? lastId = null;
        bool hasMore = true;
        while (hasMore && !stoppingToken.IsCancellationRequested)
        {
            var batchStopwatch = Stopwatch.StartNew();
            var batchRetries = 0;
            var success = false;
            List<UserSearchHistory> searchHistories = new();
            while (!success && batchRetries < maxRetries && !stoppingToken.IsCancellationRequested)
            {
                try
                {
                    searchHistories = await _dbDataService.GetUpdatedSearchHistoriesAsync(fromDate, toDate, batchSize, lastCreatedAt, lastId);
                    if (!searchHistories.Any())
                    {
                        hasMore = false;
                        success = true;
                        break;
                    }
                    var searchHistoryEsList = UserSearchHistoryEs.MapToEsDocuments(searchHistories);
                    if (!searchHistoryEsList.Any())
                    {
                        _logger.LogWarning("No valid search histories to sync after conversion for batch (incremental)");
                        hasMore = false;
                        success = true;
                        break;
                    }
                    await IndexDocuments(searchHistoryEsList, stoppingToken);
                    lock (syncLock)
                    {
                        totalProcessed += searchHistoryEsList.Count;
                        syncStats.BatchSuccessCount++;
                        LogBatchProgress("Incremental", $"{lastCreatedAt:yyyy-MM-dd HH:mm:ss.fff}-{lastId}", searchHistoryEsList.Count, batchStopwatch.Elapsed.TotalSeconds, totalProcessed, -1);
                    }
                    // Lưu lại lastCreatedAt, lastId cuối cùng của batch này
                    var last = searchHistories.Last();
                    lastCreatedAt = last.CreatedAt.ToLocalTime(); // Convert to local time
                    lastId = last.Id.ToString();
                    success = true;
                }
                catch (Exception ex)
                {
                    batchRetries++;
                    if (batchRetries >= maxRetries)
                    {
                        lock (syncLock)
                        {
                            syncStats.BatchFailureCount++;
                            failedBatches.Add((lastCreatedAt ?? fromDate, toDate, lastId));
                            _logger.LogError(ex, "Failed to process batch (incremental) after {MaxRetries} attempts", maxRetries);
                        }
                        hasMore = false;
                    }
                    else
                    {
                        lock (syncLock)
                        {
                            syncStats.RetryCount++;
                            _logger.LogWarning("Retrying batch (incremental) (Attempt {Attempt}/{MaxRetries})", batchRetries, maxRetries);
                        }
                        await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, batchRetries)), stoppingToken);
                    }
                }
            }
        }
        _logger.LogInformation("Incremental sync completed (paging). Total processed: {TotalProcessed}, Success: {Success}, Failed: {Failed}", totalProcessed, syncStats.BatchSuccessCount, syncStats.BatchFailureCount);
    }

    /// <summary>
    /// Upsert danh sách documents vào Elasticsearch
    /// </summary>
    private async Task IndexDocuments(List<UserSearchHistoryEs> documents, CancellationToken stoppingToken)
    {
        // Sử dụng RecurringJobBaseElastic để upsert documents vào Elasticsearch (chuẩn hóa theo FavPlaceSync)
        await _recurringJobBase.IndexMany(documents, stoppingToken, _indexName, DEFAULT_BATCH_SIZE);
    }

    /// <summary>
    /// Ghi log tiến trình xử lý batch
    /// </summary>
    private void LogBatchProgress(string batchType, string batchIdentifier, int recordCount, double elapsedSeconds, int totalProcessed, int totalToProcess)
    {
        // Ghi log tiếng Anh, giải thích bằng tiếng Việt
        _logger.LogInformation("Batch {BatchType} [{BatchIdentifier}]: {RecordCount} records processed in {ElapsedSeconds:F2}s. Total processed: {TotalProcessed}/{TotalToProcess}",
            batchType, batchIdentifier, recordCount, elapsedSeconds, totalProcessed, totalToProcess < 0 ? "?" : totalToProcess.ToString());
        // batchType: loại batch (theo thời gian, theo ID, ...)
        // batchIdentifier: định danh batch (ví dụ: khoảng thời gian, dải ID)
        // recordCount: số lượng bản ghi xử lý trong batch
        // elapsedSeconds: thời gian xử lý batch
        // totalProcessed: tổng số bản ghi đã xử lý
        // totalToProcess: tổng số bản ghi cần xử lý (nếu biết)
    }

    /// <summary>
    /// Thực hiện đồng bộ theo danh sách ID (nếu cần)
    /// </summary>
    public async Task<int> ProcessByIdsAsync(List<string> ids, CancellationToken stoppingToken)
    {
        if (!ids.Any()) return 0;
        _logger.LogInformation("Starting search history sync for {Count} IDs", ids.Count);
        var searchHistories = await _dbDataService.GetByIdsAsync(ids);
        var searchHistoryEsList = UserSearchHistoryEs.MapToEsDocuments(searchHistories);
        if (searchHistoryEsList.Any())
        {
            await IndexDocuments(searchHistoryEsList, stoppingToken);
        }
        _logger.LogInformation("Completed search history sync by IDs. Processed: {Count}", searchHistoryEsList.Count);
        return searchHistoryEsList.Count;
    }
}
