using NDS.ETL.BackgroundJobs.Model.ElasticSearch;

namespace NDS.ETL.BackgroundJobs.Services.ES
{
    /// <summary>
    /// Interface cho dịch vụ lấy chi tiết địa điểm từ Elasticsearch
    /// </summary>
    public interface IESPlaceDetailsService
    {
        /// <summary>
        /// Lấy thông tin chi tiết của địa điểm dựa trên các ID địa điểm
        /// </summary>
        /// <param name="placeIds">Danh sách ID địa điểm</param>
        /// <returns>Tuple chứa địa điểm đầu tiên và danh sách tất cả các địa điểm theo đúng thứ tự của params đầu vào</returns>
        Task<Tuple<PlacesEs, List<PlacesEs>>> GetPlaceDetailsAsync(params string[] placeIds);

        /// <summary>
        /// L<PERSON>y thông tin chi tiết của nhiều địa điểm dựa trên danh sách ID
        /// </summary>
        /// <param name="placeIds"><PERSON><PERSON> sách ID địa điểm</param>
        /// <returns>Danh sách các địa điểm</returns>
        Task<List<PlacesEs>> GetPlacesDetailsByIdsAsync(List<string> placeIds);
    }
}