using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Keyless]
public partial class VwPlace
{
    [Column("id")]
    public long? Id { get; set; }

    [Column("fullAddress", TypeName = "character varying")]
    public string? FullAddress { get; set; }

    [Column("streetNumber", TypeName = "character varying")]
    public string? StreetNumber { get; set; }

    [Column("route", TypeName = "character varying")]
    public string? Route { get; set; }

    [Column("ward", TypeName = "character varying")]
    public string? Ward { get; set; }

    [Column("wardId")]
    public int? WardId { get; set; }

    [Column("district", TypeName = "character varying")]
    public string? District { get; set; }

    [Column("districtId")]
    public int? DistrictId { get; set; }

    [Column("city", TypeName = "character varying")]
    public string? City { get; set; }

    [Column("provinceId")]
    public int? ProvinceId { get; set; }

    [Column("createdAt", TypeName = "timestamp without time zone")]
    public DateTime? CreatedAt { get; set; }

    [Column("creatorId", TypeName = "character varying")]
    public string? CreatorId { get; set; }
}
