using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Keyless]
[Table("planet_osm_ways")]
public partial class PlanetOsmWay
{
    [Column("id")]
    public long Id { get; set; }

    [Column("nodes")]
    public List<long> Nodes { get; set; } = null!;

    [Column("tags", TypeName = "jsonb")]
    public string? Tags { get; set; }
}
