using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.QueryDsl;
using NDS.ETL.BackgroundJobs.Extensions;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using System.Diagnostics;

namespace NDS.ETL.BackgroundJobs.Services.ES
{
    public partial class ESRecommendationService
    {
        #region Constants and Cache

        // Cache cho cấu hình để tránh gọi lại không cần thiết
        private static RecommendPlaceConfigModel? _cachedConfig;
        private static DateTime _configCacheExpiry = DateTime.MinValue;
        private static readonly TimeSpan ConfigCacheTimeout = TimeSpan.FromMinutes(30);

        #endregion

        #region Public API - Search Recommendations

        /// <summary>
        /// Lấy gợi ý tìm kiếm cho người dùng - đã tối ưu hiệu suất và logic nghiệp vụ
        /// </summary>
        public async Task<RecommendSearchItem> GetRecommendedSearchItemsAsync(string userId,
            string phoneNumber,
            double? latitude = null,
            double? longitude = null,
            int? h3Resolution = null,
            int limit = 10,
            string lang = "en")
        {
            var stopwatch = Stopwatch.StartNew();
            _logger.LogInformation("Getting recommended search items for user: {UserId}, phone: {PhoneNumber}, limit: {Limit}",
                userId, phoneNumber, limit);

            // Kiểm tra tham số đầu vào
            if (limit <= 0)
            {
                _logger.LogWarning("Invalid limit parameter: {Limit}, returning empty result", limit);
                return new RecommendSearchItem
                {
                    Title = "Đề xuất tìm kiếm",
                    Places = new List<RecommendedPlace>()
                };
            }

            // Kiểm tra thông tin người dùng
            if (string.IsNullOrWhiteSpace(userId) && string.IsNullOrWhiteSpace(phoneNumber))
            {
                _logger.LogWarning("No user identification provided, returning empty result");
                return new RecommendSearchItem
                {
                    Title = "Đề xuất tìm kiếm",
                    Places = new List<RecommendedPlace>()
                };
            }

            try
            {
                // Khởi tạo kết quả trả về
                var consolidatedResult = new RecommendSearchItem
                {
                    Title = "Đề xuất tìm kiếm",
                    Places = new List<RecommendedPlace>()
                };

                // Lấy cấu hình đề xuất
                var configTask = GetRecommendPlaceConfigAsync();

                // Tính thời gian hợp lệ cho lịch sử
                var now = DateTime.UtcNow;

                // Chờ cấu hình và tính toán thời gian hợp lệ
                var config = await configTask;
                var validSearchHistoryTime = now.AddDays(-config.ValidSearchHistoryDays);
                var validBookingHistoryTime = now.AddDays(-config.ValidBookingHistoryDays);

                // Tạo các tác vụ song song để tối ưu hiệu suất
                var favoritePlacesTask = GetUserFavoritePlacesAsync(userId, phoneNumber);
                // Tính limit cho recent searches - lấy nhiều hơn để có lựa chọn tốt hơn
                var searchLimit = 3; 
                var bookingLimit = 10; 
                var historyTask = GetUserHistoryPlacesAsync(userId, phoneNumber, validSearchHistoryTime, validBookingHistoryTime, latitude, longitude, searchLimit, bookingLimit);

                // Chờ tất cả các tác vụ hoàn thành song song
                await Task.WhenAll(favoritePlacesTask, historyTask);

                // Lấy kết quả từ các tác vụ
                var favoritePlaces = await favoritePlacesTask;
                var (recentSearches, bookingHistory) = await historyTask;

                // Kiểm tra dữ liệu có sẵn
                var hasFavoritePlaces = favoritePlaces.Any();
                var hasSearched = recentSearches.Any();
                var hasBookingHistory = bookingHistory.Any();

                var allRecommendedPlaces = new List<RecommendedPlace>();

                // 1. Ưu tiên tuyệt đối: Recent searches lên đầu (highest priority)
                if (hasSearched)
                {
                    var priorityRecentSearches = recentSearches
                        .Take(Math.Min(3, limit)) // Lấy tối đa 3 recent searches để không chiếm hết slot
                        .Select((place, index) =>
                        {
                            var recommendedPlace = RecommendedPlace.FromPlacesEs(place);
                            // Đặt Seq cao nhất cho recent searches - ưu tiên tuyệt đối
                            recommendedPlace.Seq = 3000 + (recentSearches.Count - index); // Seq cao hơn cả favorite
                            return recommendedPlace;
                        })
                        .ToList();

                    allRecommendedPlaces.AddRange(priorityRecentSearches);
                    _logger.LogInformation("Added {Count} recent searches with highest priority", priorityRecentSearches.Count);
                }

                // 2. Thêm địa điểm yêu thích (ưu tiên cao thứ 2)
                if (hasFavoritePlaces)
                {
                    // Tạo set PlaceId từ recent searches để tránh trùng lặp
                    var existingPlaceIds = allRecommendedPlaces.Select(p => p.PlaceId).ToHashSet();
                    
                    var favoriteRecommendedPlaces = favoritePlaces
                        .Where(place => !existingPlaceIds.Contains(place.PlaceId)) // Tránh trùng với recent searches
                        .Take(config.MaxFavoritePlaces)
                        .Select((place, index) =>
                        {
                            var recommendedPlace = RecommendedPlace.FromPlacesEs(place);
                            recommendedPlace.IsFavorite = true;
                            // Đặt Seq cao cho favorite places nhưng thấp hơn recent searches
                            recommendedPlace.Seq = 2000 + (favoritePlaces.Count - index);
                            return recommendedPlace;
                        })
                        .ToList();

                    allRecommendedPlaces.AddRange(favoriteRecommendedPlaces);
                    _logger.LogInformation("Added {Count} favorite places to recommendations (excluded {ExcludedCount} duplicates)", 
                        favoriteRecommendedPlaces.Count, favoritePlaces.Count - favoriteRecommendedPlaces.Count);
                }

                // 3. Xử lý lịch sử đặt xe - ưu tiên thấp hơn
                if (hasBookingHistory)
                {
                    // Tạo set PlaceId để tránh trùng lặp với recent searches và favorites
                    var existingPlaceIds = allRecommendedPlaces.Select(p => p.PlaceId).ToHashSet();
                    
                    var remainingSlots = Math.Max(0, config.MaxRecentPlaces - allRecommendedPlaces.Count);
                    if (remainingSlots > 0)
                    {
                        var bookingRecommendedPlaces = bookingHistory
                            .Where(x => !existingPlaceIds.Contains(x.PlaceId)) // Loại bỏ trùng lặp
                            .OrderByDescending(x => x.BookingCount)
                            .ThenByDescending(x => x.UpdatedAt)
                            .Take(remainingSlots)
                            .Select((x, index) =>
                            {
                                var recommendedPlace = RecommendedPlace.FromPlacesEs(x);
                                recommendedPlace.Seq = 1000 + (remainingSlots - index); // Seq thấp hơn favorites
                                return recommendedPlace;
                            })
                            .ToList();

                        allRecommendedPlaces.AddRange(bookingRecommendedPlaces);
                        _logger.LogInformation("Added {Count} booking history places (excluded {ExcludedCount} duplicates)",
                            bookingRecommendedPlaces.Count, bookingHistory.Count - bookingRecommendedPlaces.Count);
                    }
                }

                // 4. Bổ sung địa điểm phổ biến trong khu vực nếu cần
                if (latitude.HasValue && longitude.HasValue && allRecommendedPlaces.Count < limit)
                {
                    var remainingSlots = limit - allRecommendedPlaces.Count;
                    var radius = config.DefaultSearchRadiusKm * 1000; // Chuyển đổi km sang m

                    var popularPlaces = await GetPopularPlacesInAreaAsync(
                        userId,
                        phoneNumber,
                        latitude.Value,
                        longitude.Value,
                        radius,
                        remainingSlots,
                        allRecommendedPlaces,
                        MinRadiusMeters);

                    allRecommendedPlaces.AddRange(popularPlaces);
                }

                // 5. Xử lý kết quả cuối cùng - giữ thứ tự ưu tiên và loại bỏ trùng lặp
                consolidatedResult.Places = ProcessFinalResults(
                    allRecommendedPlaces,
                    latitude,
                    longitude,
                    limit);

                _logger.LogInformation("Found {Count} recommended places in {ElapsedMs}ms with priority order: recent searches > favorites > booking history",
                    consolidatedResult.Places.Count, stopwatch.ElapsedMilliseconds);

                return consolidatedResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recommended search items: {Message}", ex.Message);
                return new RecommendSearchItem
                {
                    Title = "Đề xuất tìm kiếm",
                    Places = new List<RecommendedPlace>()
                };
            }
        }

        /// <summary>
        /// Xử lý kết quả cuối cùng - đảm bảo recent searches ưu tiên lên đầu
        /// </summary>
        private List<RecommendedPlace> ProcessFinalResults(
            List<RecommendedPlace> allPlaces,
            double? latitude,
            double? longitude,
            int limit)
        {
            // Tính toán khoảng cách một lần nếu có tọa độ
            if (latitude.HasValue && longitude.HasValue)
            {
                Parallel.ForEach(allPlaces, place =>
                {
                    place.Distance = H3Helper.CalculateDistance(
                        latitude.Value, longitude.Value,
                        place.Location.Lat, place.Location.Lon) / 1000;
                });
            }

            var minRadiusKm = MinRadiusMeters / 1000;
            
            // Loại bỏ trùng lặp và sắp xếp theo thứ tự ưu tiên nghiệp vụ
            return allPlaces
                .GroupBy(p => p.PlaceId)
                .Select(g =>
                {
                    // Trong mỗi group, chọn bản ghi có Seq cao nhất (ưu tiên cao nhất)
                    return g.OrderByDescending(p => p.Seq ?? 0) // 1. Seq cao nhất (recent searches = 3000+)
                           .ThenByDescending(p => p.IsFavorite) // 2. Favorite
                           .ThenByDescending(p => p.BookingCount) // 3. Số lần booking
                           .ThenBy(p => p.Distance ?? double.MaxValue) // 4. Khoảng cách
                           .First();
                })
                .Where(p => !latitude.HasValue || !longitude.HasValue || p.Distance > minRadiusKm) // Loại bỏ địa điểm quá gần
                .OrderByDescending(p => p.Seq ?? 0) // Sắp xếp theo Seq: Recent searches (3000+) > Favorites (2000+) > Booking history (1000+)
                .ThenByDescending(p => p.IsFavorite) // Sau đó theo favorite
                .ThenByDescending(p => p.BookingCount > 0 ? p.BookingCount : 0) // Số lần đặt xe
                .ThenBy(p => p.Distance ?? double.MaxValue) // Khoảng cách gần nhất
                .Take(limit)
                .ToList();
        }

        #endregion

        #region Private Methods - Search Recommendations

        /// <summary>
        /// Thực hiện truy vấn Elasticsearch tổng quát và trả về documents
        /// </summary>
        /// <typeparam name="T">Kiểu document trong Elasticsearch</typeparam>
        /// <param name="indexName">Tên index</param>
        /// <param name="queries">Danh sách các query conditions</param>
        /// <param name="size">Số lượng kết quả tối đa</param>
        /// <param name="sortActions">Các hành động sắp xếp</param>
        /// <param name="collapseField">Field để collapse (tùy chọn)</param>
        /// <returns>Danh sách documents</returns>
        private async Task<List<T>> ExecuteElasticsearchQueryAsync<T>(
            string indexName,
            List<Action<QueryDescriptor<T>>> queries,
            int size,
            Action<SortOptionsDescriptor<T>>? sortActions = null,
            Field? collapseField = null) where T : class
        {
            var searchDescriptor = new SearchRequestDescriptor<T>()
                .Index(indexName)
                .Size(size)
                .Query(q => q.Bool(b => b.Must(queries.ToArray())));

            // Thêm collapse nếu có
            if (collapseField != null)
            {
                searchDescriptor = searchDescriptor.Collapse(c => c.Field(collapseField));
            }

            // Thêm sort nếu có
            if (sortActions != null)
            {
                searchDescriptor = searchDescriptor.Sort(sortActions);
            }

            var response = await _elasticClient.SearchAsync<T>(searchDescriptor);

            if (response.IsValidResponse && response.Documents.Any())
            {
                return response.Documents.ToList();
            }

            return new List<T>();
        }

        /// <summary>
        /// Lấy thông tin chi tiết địa điểm từ danh sách PlaceId và làm phong phú dữ liệu với thông tin booking
        /// </summary>
        /// <param name="placeIds">Danh sách PlaceId cần lấy thông tin</param>
        /// <param name="placePairMap">Dictionary mapping PlaceId -> PlacePairEs để làm phong phú dữ liệu (có thể null)</param>
        /// <returns>Danh sách PlacesEs đã được làm phong phú với dữ liệu booking</returns>
        private async Task<List<PlacesEs>> GetEnrichedPlacesAsync(
            IEnumerable<string> placeIds,
            Dictionary<string, PlacePairEs>? placePairMap = null)
        {
            if (!placeIds.Any())
            {
                return new List<PlacesEs>();
            }

            var placesResult = await GetESPlace(placeIds.ToArray());
            
            // Làm phong phú dữ liệu với thông tin booking nếu có placePairMap
            if (placePairMap != null)
            {
                foreach (var placesEs in placesResult.Item2)
                {
                    if (placePairMap.TryGetValue(placesEs.PlaceId, out var placePairEs))
                    {
                        placesEs.QuoteCount = placePairEs.SearchCount;
                        placesEs.BookingCount = placePairEs.BookingCount;
                        placesEs.UpdatedAt = placePairEs.UpdateAt;
                    }
                }
            }
            
            return placesResult.Item2;
        }

        /// <summary>
        /// Lấy cấu hình đề xuất địa điểm - có cache để tối ưu hiệu suất
        /// </summary>
        private async Task<RecommendPlaceConfigModel> GetRecommendPlaceConfigAsync()
        {
            // Kiểm tra cache còn hiệu lực không
            if (_cachedConfig != null && DateTime.UtcNow < _configCacheExpiry)
            {
                return _cachedConfig;
            }

            // Lấy cấu hình mới (có thể từ database hoặc config file trong tương lai)
            var config = await Task.FromResult(new RecommendPlaceConfigModel());

            // Cập nhật cache
            _cachedConfig = config;
            _configCacheExpiry = DateTime.UtcNow.Add(ConfigCacheTimeout);

            return config;
        }

        /// <summary>
        /// Lấy lịch sử địa điểm của người dùng (bao gồm cả search, booking và user search history) - tối ưu một query
        /// </summary>
        private async Task<(List<PlacesEs> searchPlaces, List<PlacesEs> bookingHistory)> GetUserHistoryPlacesAsync(
            string userId,
            string phoneNumber,
            DateTime validSearchSince,
            DateTime validBookingSince,
            double? latitude = null,
            double? longitude = null,
            int searchLimit = 15,
            int bookingLimit = 25)
        {
            _logger.LogInformation("Retrieving user history for user: {UserId}, phone: {PhoneNumber}, searchLimit: {SearchLimit}, bookingLimit: {BookingLimit}",
                userId, phoneNumber, searchLimit, bookingLimit);

            try
            {
                // Tạo danh sách các query phần tử chung
                var baseQueries = new List<Action<QueryDescriptor<PlacePairEs>>>();

                // Thêm điều kiện lọc theo user ID hoặc số điện thoại
                baseQueries.CreateUserIdentifierQuery("customerId", userId, "customerPhone", phoneNumber);

                // Thêm điều kiện lọc theo khoảng cách địa lý nếu có tọa độ
                if (latitude.HasValue && longitude.HasValue)
                    baseQueries.CreateMinGeoDistanceQuery(latitude.Value, longitude.Value, MinRadiusMeters);

                // Task 1: Lấy recent searches (ưu tiên lastSearchAt)
                var searchTask = Task.Run(async () =>
                {
                    var searchQueries = new List<Action<QueryDescriptor<PlacePairEs>>>(baseQueries);
                    searchQueries.CreateDateRangeQuery("lastSearchAt", validSearchSince, null);

                    var placePairDocuments = await ExecuteElasticsearchQueryAsync<PlacePairEs>(
                        _placePairIdxName,
                        searchQueries,
                        searchLimit,
                        sort => sort.Field(f => f.LastSearchAt!, o => o.Order(SortOrder.Desc)),
                        new Field("placeId") // Collapse để lấy unique places
                    );

                    if (placePairDocuments.Any())
                    {
                        var placePairEsMap = placePairDocuments.ToDictionary(x => x.PlaceId, x => x);
                        var placeIds = placePairEsMap.Keys.ToList();
                        return await GetEnrichedPlacesAsync(placeIds, placePairEsMap);
                    }
                    return new List<PlacesEs>();
                });

                // Task 2: Lấy booking history (ưu tiên bookingCount + updateAt)
                var bookingTask = Task.Run(async () =>
                {
                    var bookingQueries = new List<Action<QueryDescriptor<PlacePairEs>>>(baseQueries);
                    bookingQueries.CreateDateRangeQuery("updateAt", validBookingSince, null);

                    var placePairDocuments = await ExecuteElasticsearchQueryAsync<PlacePairEs>(
                        _placePairIdxName,
                        bookingQueries,
                        bookingLimit,
                        sort => sort
                            .Field(f => f.BookingCount, o => o.Order(SortOrder.Desc))
                            .Field(f => f.UpdateAt, o => o.Order(SortOrder.Desc)),
                        new Field("placeId") // Collapse để lấy unique places

                    );

                    if (placePairDocuments.Any())
                    {
                        var placePairEsMap = placePairDocuments.ToDictionary(x => x.PlaceId, x => x);
                        var placeIds = placePairEsMap.Keys.ToList();
                        return await GetEnrichedPlacesAsync(placeIds, placePairEsMap);
                    }
                    return new List<PlacesEs>();
                });

                // Task 3: Lấy user search history (lịch sử địa điểm user đã click - ưu tiên cao nhất)
                var userSearchHistoryTask = Task.Run(async () =>
                {
                    try
                    {
                        if (string.IsNullOrWhiteSpace(_searchHistoryIdxName))
                        {
                            _logger.LogWarning("Search history index name is not configured");
                            return new List<PlacesEs>();
                        }

                        // Tạo query cho UserSearchHistoryEs
                        var userSearchQueries = new List<Action<QueryDescriptor<UserSearchHistoryEs>>>();

                        // Thêm điều kiện lọc theo số điện thoại 
                        userSearchQueries.Add(q => q.Term(t => t.Field(f => f.CustomerMobileNo).Value(phoneNumber)));
                        
                        // Thêm điều kiện lọc theo thời gian
                        userSearchQueries.Add(q => q.Range(r => r
                            .DateRange(dr => dr.Field(f => f.CreatedAt).Gte(validSearchSince))
                        ));

                        // Thêm điều kiện lọc theo khoảng cách địa lý nếu có tọa độ
                        if (latitude.HasValue && longitude.HasValue)
                            userSearchQueries.CreateMinGeoDistanceQuery(latitude.Value, longitude.Value, MinRadiusMeters);

                        var userSearchHistoryDocuments = await ExecuteElasticsearchQueryAsync<UserSearchHistoryEs>(
                            _searchHistoryIdxName,
                            userSearchQueries,
                            3, // Lấy tối đa 3 điểm gần đây nhất
                            sort => sort.Field(f => f.CreatedAt, o => o.Order(SortOrder.Desc)),
                            new Field("placeId") // Collapse để lấy unique places
                        );

                        if (userSearchHistoryDocuments.Any())
                        {
                            // Chuyển đổi UserSearchHistoryEs thành PlacesEs và lấy thông tin chi tiết
                            var placeIds = userSearchHistoryDocuments.Select(x => x.PlaceId).Where(x => !string.IsNullOrEmpty(x)).ToList();
                            if (placeIds.Any())
                            {
                                var enrichedPlaces = await GetEnrichedPlacesAsync(placeIds);
                                
                                // Cập nhật thông tin UpdatedAt từ UserSearchHistoryEs để sử dụng cho priority sorting
                                foreach (var place in enrichedPlaces)
                                {
                                    var searchHistory = userSearchHistoryDocuments.FirstOrDefault(x => x.PlaceId == place.PlaceId);
                                    if (searchHistory != null)
                                    {
                                        // Cập nhật UpdatedAt với thời gian tìm kiếm gần nhất từ UserSearchHistoryEs
                                        place.UpdatedAt = searchHistory.CreatedAt;
                                    }
                                }
                                
                                _logger.LogInformation("Found {Count} user search history places", enrichedPlaces.Count);
                                return enrichedPlaces;
                            }
                        }
                        
                        return new List<PlacesEs>();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error retrieving user search history: {Message}", ex.Message);
                        return new List<PlacesEs>();
                    }
                });

                // Chờ tất cả ba tasks hoàn thành song song
                await Task.WhenAll(searchTask, bookingTask, userSearchHistoryTask);

                var searchPlaces = await searchTask;
                var bookingHistory = await bookingTask;
                var userSearchHistory = await userSearchHistoryTask;

                // Merge user search history vào searchPlaces với ưu tiên cao nhất
                if (userSearchHistory.Any())
                {
                    // Tạo set PlaceId từ user search history để tránh trùng lặp
                    var userSearchPlaceIds = userSearchHistory.Select(p => p.PlaceId).ToHashSet();
                    
                    // Loại bỏ các place trùng lặp từ searchPlaces
                    var filteredSearchPlaces = searchPlaces.Where(p => !userSearchPlaceIds.Contains(p.PlaceId)).ToList();
                    
                    // Ghép user search history lên đầu danh sách searchPlaces
                    var mergedSearchPlaces = userSearchHistory.Concat(filteredSearchPlaces).ToList();
                    
                    _logger.LogInformation("Merged {UserSearchCount} user search history places with {SearchCount} search places, removed {RemovedCount} duplicates",
                        userSearchHistory.Count, filteredSearchPlaces.Count, searchPlaces.Count - filteredSearchPlaces.Count);
                    
                    searchPlaces = mergedSearchPlaces.Take(searchLimit).ToList();
                }

                _logger.LogInformation("Found {SearchCount} search places, {BookingCount} booking history records and {UserSearchCount} user search history places",
                    searchPlaces.Count, bookingHistory.Count, userSearchHistory.Count);

                return (searchPlaces, bookingHistory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user history: {Message}", ex.Message);
                return (new List<PlacesEs>(), new List<PlacesEs>());
            }
        }

        /// <summary>
        /// Lấy danh sách địa điểm yêu thích của người dùng
        /// </summary>
        private async Task<List<PlacesEs>> GetUserFavoritePlacesAsync(
            string userId,
            string phoneNumber)
        {
            _logger.LogInformation("Retrieving favorite places for user: {UserId}, phone: {PhoneNumber}",
                userId, phoneNumber);

            try
            {
                var favoritePlaces = new List<PlacesEs>();

                // Tạo danh sách các query phần tử cho tìm kiếm địa điểm yêu thích
                var queries = new List<Action<QueryDescriptor<FavPlaceEs>>>();

                // Thêm điều kiện lọc theo user ID hoặc số điện thoại
                queries.CreateUserIdentifierQuery("userId", userId, "phoneNumber", phoneNumber);

                // Tạo request tìm kiếm
                var searchResponse = await _elasticClient.SearchAsync<FavPlaceEs>(s => s
                    .Index(_favoritePlacesIdxName)
                    .Query(q => q
                        .Bool(b => b
                            .Must(queries.ToArray())
                        )
                    )
                    .Sort(sort => sort
                        .Field(f => f
                            .UpdatedAt,
                            s => s.Order(SortOrder.Desc)
                        )
                    )
                );

                if (searchResponse.IsValidResponse && searchResponse.Documents.Any())
                {
                    // Lấy danh sách ID địa điểm yêu thích
                    var favPlaceIds = searchResponse.Documents
                        .Select(d => d.PlaceId)
                        .Distinct()
                        .ToList();

                    if (favPlaceIds.Any())
                    {
                        // Sử dụng method chung để lấy thông tin chi tiết địa điểm
                        favoritePlaces = await GetEnrichedPlacesAsync(favPlaceIds);
                        _logger.LogInformation("Found {Count} favorite places for user", favoritePlaces.Count);
                    }
                }
                else
                {
                    _logger.LogInformation("No favorite places found for user");
                }

                return favoritePlaces;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user favorite places: {Message}", ex.Message);
                return new List<PlacesEs>();
            }
        }

        /// <summary>
        /// Lấy các địa điểm phổ biến trong khu vực - đã tối ưu
        /// </summary>
        private async Task<List<RecommendedPlace>> GetPopularPlacesInAreaAsync(string userId,
            string phoneNumber,
            double latitude,
            double longitude,
            double radiusMeters,
            int limit,
            List<RecommendedPlace> excludedPlaces,
            double? minRadiusMeters = null)
        {
            _logger.LogInformation("Retrieving popular places at coordinates: [{Latitude}, {Longitude}] within {RadiusMeters}m, limit: {Limit}",
                latitude, longitude, radiusMeters, limit);

            try
            {
                var result = new List<RecommendedPlace>();

                // Tạo set các PlaceId đã có để tránh trùng lặp
                var excludedPlaceIds = excludedPlaces.Select(p => p.PlaceId).ToHashSet();

                // Sử dụng H3 resolution phù hợp với radius
                var h3Resolution = H3Helper.GetH3ResolutionFromRadiusKm(radiusMeters / 1000);

                // Gọi service lấy địa điểm phổ biến
                var popularDestinations = await GetPopularDestinationsAsync(
                    userId, phoneNumber, latitude, longitude, h3Resolution, limit * 2, minRadiusMeters);

                if (popularDestinations.Any())
                {
                    result = popularDestinations
                        .Where(place => !excludedPlaceIds.Contains(place.PlaceId))
                        .Take(limit)
                        .Select((place, index) =>
                        {
                            var recommendedPlace = RecommendedPlace.FromPlaceLocation(place);
                            // Đặt Seq thấp để không can thiệp vào lịch sử tìm kiếm
                            recommendedPlace.Seq = 100 + index; // Seq thấp nhất - sau lịch sử tìm kiếm và booking
                            return recommendedPlace;
                        })
                        .ToList();

                    _logger.LogInformation("Found {Count} popular destinations from service", result.Count);
                }

                // Nếu không đủ kết quả, fallback sang tìm kiếm trực tiếp
                if (result.Count < limit)
                {
                    var remainingCount = limit - result.Count;
                    var fallbackPlaces = await GetNearbyPlacesFallbackAsync(
                        latitude, longitude, radiusMeters, remainingCount, excludedPlaceIds, result, minRadiusMeters);

                    result.AddRange(fallbackPlaces);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting popular places in area: {Message}", ex.Message);
                return new List<RecommendedPlace>();
            }
        }

        /// <summary>
        /// Fallback tìm kiếm địa điểm gần đây nếu không đủ kết quả từ service
        /// </summary>
        private async Task<List<RecommendedPlace>> GetNearbyPlacesFallbackAsync(
            double latitude,
            double longitude,
            double radiusMeters,
            int limit,
            HashSet<string> excludedPlaceIds,
            List<RecommendedPlace> existingResults,
            double? minRadiusMeters = null)
        {
            _logger.LogInformation("Fallback search for nearby places, limit: {Limit}", limit);

            try
            {
                var h3Resolution = H3Helper.GetH3ResolutionFromRadiusKm(radiusMeters / 1000);

                // Tìm kiếm trực tiếp từ nearby service  
                var nearbyPlaces = await _nearbyService.SearchNearbyAsync(
                    "", "", latitude, longitude, h3Resolution, limit * 3, true, null, minRadiusMeters);

                // Lọc và chọn địa điểm phù hợp
                var fallbackResult = nearbyPlaces
                    .AsParallel()
                    .Where(place =>
                        !excludedPlaceIds.Contains(place.PlaceId) && existingResults.All(r => r.PlaceId != place.PlaceId) &&
                        !string.IsNullOrEmpty(place.Name) &&
                        !char.IsDigit(place.Name[0]))
                    .OrderBy(p => Guid.NewGuid()) // Trộn ngẫu nhiên để tăng tính đa dạng
                    .Take(limit)
                    .Select((place, index) =>
                    {
                        var recommendedPlace = RecommendedPlace.FromPlacesEs(place);
                        // Đặt Seq thấp cho fallback places
                        recommendedPlace.Seq = 50 + index; // Seq thấp nhất
                        return recommendedPlace;
                    })
                    .ToList();

                _logger.LogInformation("Fallback found {Count} additional places", fallbackResult.Count);
                return fallbackResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in fallback search: {Message}", ex.Message);
                return new List<RecommendedPlace>();
            }
        }

        #endregion
    }

    #region Supporting Models

    /// <summary>
    /// Mô hình cấu hình đề xuất địa điểm
    /// </summary>
    public class RecommendPlaceConfigModel
    {
        public int ValidSearchHistoryDays { get; set; } = 30;
        public int ValidBookingHistoryDays { get; set; } = 365;
        public int MaxRecentPlaces { get; set; } = 10;
        public int MaxFavoritePlaces { get; set; } = 3;
        public int DefaultSearchRadiusKm { get; set; } = 20;
    }


    #endregion
}