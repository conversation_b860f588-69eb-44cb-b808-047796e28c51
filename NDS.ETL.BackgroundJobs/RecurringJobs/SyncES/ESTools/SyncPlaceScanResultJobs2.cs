using System.Diagnostics;
using Elastic.Clients.Elasticsearch;
using KnowledgeBase.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Entities.Context;
using NDS.ETL.Entities.PostgresPlace;
using NDS.ETL.Infrastructure.Common;
using NDS.ETL.Infrastructure.Extensions;
using NDS.ETL.Infrastructure.Utils;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.ESTools;

public class SyncPlaceScanResultJobs2 : RecurringJobBaseElastic
{
    private readonly ILogger<RecurringJobBaseElastic> _logger;
    private readonly ElasticsearchClient _elasticClient;
    private readonly IESPlacesService _iesPlacesService;
    private readonly IScopedResolver<NpgPlaceContext> _dbContextResolver;
    private readonly ElasticSearchConfig? _indexName;

    public SyncPlaceScanResultJobs2(IConfiguration configuration, ILogger<RecurringJobBaseElastic> logger,
        ElasticsearchClient elasticClient, IESPlacesService iesPlacesService,
        IScopedResolver<NpgPlaceContext> dbContextResolver
    ) : base(configuration, logger, elasticClient, null)
    {
        _indexName = configuration.Bind<ElasticSearchConfig>(nameof(ElasticSearchConfig));
        _logger = logger;
        _elasticClient = elasticClient;
        _iesPlacesService = iesPlacesService;
        _dbContextResolver = dbContextResolver;
    }

    public override string JobId => "SyncPlaceScanResult2";
    public override string ConfigSection => "JobPgSyncPlace";

    public override async Task DoWorkAsync(CancellationToken stoppingToken)
    {
        var begin = Stopwatch.GetTimestamp();
        _logger.LogInformation("Starting SyncPlaceScanResult2 job");

        try
        {
            int take = 2000;
            int count = 0;
            bool hasMoreData = true;

            while (hasMoreData && !stoppingToken.IsCancellationRequested)
            {
                var batchStopwatch = Stopwatch.StartNew();

                // Properly use IScopedResolver
                var data = await _dbContextResolver.ResolveAsync(async dbContext =>
                {
                    return await dbContext.PlacesScanResults
                        .AsNoTracking()
                        .Where(x => x.SyncStatus == 0)
                        .OrderBy(x => x.Id)
                        .Skip(0) // Chỉ lấy 2000 bản ghi dau tien , vì trong loop da change SyncStatus => 1 do khong dung da luong.
                        .Take(take)
                        .ToListAsync(stoppingToken);
                });

                // Check if we have any data
                if (data == null || data.Count == 0)
                {
                    hasMoreData = false;
                    _logger.LogInformation("No more data to process");
                    break;
                }

                var lstPlaceId = data.Select(x => x.Placeid).Distinct().ToList();
                _logger.LogInformation(
                    $"Processing batch with {data.Count} records ({lstPlaceId.Count} unique places)");

                var placesResp = await _iesPlacesService.GetPlacesByPlaceIdsAsync(lstPlaceId, stoppingToken, 5000);
                var result = new List<PlacesEs>();

                foreach (var item in data)
                {
                    var placeResp = placesResp.FirstOrDefault(x => x.PlaceId == item.Placeid);
                    if (placeResp != null)
                    {
                        placeResp.CompareScoreMax = item.CompareScoreMax;
                        placeResp.TotalScans = item.TotalScans;
                        placeResp.SourcePlaceId = item.SourcePlaceid;
                        placeResp.ScanType = 1;
                        placeResp.JobScanId = item.HangfireJobId;
                        placeResp.ScanProcessed = item.ScanProcessed == null ? (int)PlacesScanResultProcessed.NotYet : (int)item.ScanProcessed;
                        placeResp.ReasonOperations =
                            item.ReasonOperations?.Select(e => (int)e).ToList() ?? new List<int>();
                        placeResp.Reason = item.Reason;
                        placeResp.Note = item.Note;
                        placeResp.UpdatedAt = item.ScanProcessed == null ? DateTime.Now : item.UpdateAt;
                        placeResp.ScanStatus = 1;
                        placeResp.DetermineAddressLevel = AddressProcessor.DetermineAddressLevel(placeResp.FullAddress);
                        result.Add(placeResp);
                    }
                }

                if (result.Any())
                {
                    await IndexMany(result, stoppingToken, _indexName.Index[0], 5000);

                    // Update status in database using IScopedResolver
                    await _dbContextResolver.ResolveAsync(async dbContext =>
                    {
                        var ids = data.Select(x => x.Id).ToList();
                        var entitiesToUpdate = await dbContext.PlacesScanResults
                            .Where(x => ids.Contains(x.Id))
                            .ToListAsync(stoppingToken);

                        foreach (var entity in entitiesToUpdate)
                        {
                            entity.SyncStatus = 1;
                        }

                        var rowsAffected = await dbContext.SaveChangesAsync(stoppingToken);
                        _logger.LogInformation($"Updated {rowsAffected} records");
                        return true;
                    });
                }

                count += data.Count;
                batchStopwatch.Stop();
                _logger.LogInformation(
                    $"Processed batch of {data.Count} records in {batchStopwatch.Elapsed.TotalSeconds:F2}s, total: {count}");
            }

            var end = Stopwatch.GetTimestamp();
            _logger.LogInformation(
                $"SyncPlaceScanResult2 completed: {count} records in {TimeSpan.FromTicks(end - begin).TotalSeconds:F2}s");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SyncPlaceScanResult2 job");
            throw;
        }
    }
}