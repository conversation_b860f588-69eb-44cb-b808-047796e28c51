using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using NodaTime;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("migration_checkpoints")]
public partial class MigrationCheckpoint
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("checkpoint_time")]
    public LocalDateTime CheckpointTime { get; set; }

    [Column("error_message")]
    [StringLength(4000)]
    public string? ErrorMessage { get; set; }

    [Column("last_processed_id")]
    [StringLength(255)]
    public string LastProcessedId { get; set; } = null!;

    [Column("migration_id")]
    [StringLength(255)]
    public string MigrationId { get; set; } = null!;

    [Column("processed_count")]
    public long ProcessedCount { get; set; }

    [Column("status")]
    [StringLength(255)]
    public string Status { get; set; } = null!;

    [Column("thread_id")]
    [StringLength(255)]
    public string? ThreadId { get; set; }

    [Column("total_count")]
    public long TotalCount { get; set; }
}
