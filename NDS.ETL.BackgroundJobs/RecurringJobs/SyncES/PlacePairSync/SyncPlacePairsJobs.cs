using Elastic.Clients.Elasticsearch;
using KnowledgeBase.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch.PlacePairSync;
using NDS.ETL.Entities.Context;
using NDS.ETL.Infrastructure.Common;
using NDS.ETL.Infrastructure.Utils;
using System.Diagnostics;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.PlacePairSync;

/// <summary>
/// Job đồng bộ dữ liệu BookingPlacePair từ PostgreSQL sang Elasticsearch
/// </summary>
public class SyncPlacePairsJobs : RecurringJobBaseElastic
{
    private readonly ILogger<SyncPlacePairsJobs> _logger;
    private readonly IScopedResolver<NpgPlaceContext> _dbContextResolver;
    private readonly IDistributedLock _distributedLock;
    private readonly ElasticsearchDataService _esService;
    private readonly DbDataService _dbService;
    private readonly SyncStrategyService _strategyService;
    private readonly SyncProcessor _syncProcessor;

    // Các hằng số cấu hình - Tối ưu cho performance
    private const int PAGE_SIZE = 10_000; // Giảm từ 50K xuống 10K để xử lý nhanh hơn
    private const int COMMAND_TIMEOUT = 300; // Giảm từ 600 xuống 300 
    private const string LOCK_KEY = "sync_place_pairs_lock";
    private const int LOCK_TIMEOUT_SECONDS = 1800; // Giảm từ 3600 xuống 1800 (30 phút)
    private const int MAX_RETRIES = 2; // Giảm từ 3 xuống 2 để nhanh hơn
    
    private readonly string _indexName;

    public SyncPlacePairsJobs(
        IConfiguration configuration,
        ILogger<SyncPlacePairsJobs> logger,
        ElasticsearchClient elasticClient,
        IScopedResolver<NpgPlaceContext> dbContextResolver,
        IDistributedLock distributedLock) : base(configuration, logger, elasticClient, null)
    {
        _logger = logger;
        _dbContextResolver = dbContextResolver;
        _distributedLock = distributedLock;

        _indexName = GetIndexName(configuration);

        // Khởi tạo các service
        _esService = new ElasticsearchDataService(logger, elasticClient, _indexName);
        _dbService = new DbDataService(logger);
        _strategyService = new SyncStrategyService(logger, dbContextResolver);
        _syncProcessor = new SyncProcessor(
            logger,
            _esService,
            _dbService,
            _indexName,
            dbContextResolver,
            this);  // Truyền this để SyncProcessor có thể sử dụng IndexManyDocumentsAsync
    }

    private string GetIndexName(IConfiguration configuration)
    {
        var indexNames = configuration.Bind<ElasticSearchConfig>(nameof(ElasticSearchConfig));
        return indexNames.Index.FirstOrDefault(x => x.Contains("place-pair")) ?? "place-pair";
    }

    public override string JobId => "SyncPlacePairs";
    public override string ConfigSection => "JobPgSyncPlace";

    public override async Task DoWorkAsync(CancellationToken stoppingToken)
    {
        // Kiểm tra xem job có đang chạy không
        var lockAcquired = false;
        try
        {
             //await _distributedLock.ForceReleaseLockAsync(LOCK_KEY);

            _logger.LogInformation("Attempting to acquire lock for PlacePair sync");

            // Cố gắng lấy khóa với timeout
            lockAcquired = await _distributedLock.TryAcquireLockAsync(
                LOCK_KEY,
                TimeSpan.FromSeconds(LOCK_TIMEOUT_SECONDS));

            if (!lockAcquired)
            {
                _logger.LogWarning("Could not acquire lock - another sync job is already running");
                return;
            }

            _logger.LogInformation("Lock acquired. Starting PlacePair sync");

            // Tiếp tục thực hiện job khi đã lấy được khóa
            await RunSynchronizationJob(stoppingToken);
        }
        catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
        {
            _logger.LogInformation("Job canceled");
        }
        catch (Exception ex)
        {
            if (ex is OperationCanceledException && stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("PlacePair sync job canceled");
            }
            else
            {
                _logger.LogError(ex, "Error in PlacePair sync job");
                throw;
            }
        }
        finally
        {
            if (lockAcquired)
            {
                await _distributedLock.ReleaseLockAsync(LOCK_KEY);
                _logger.LogInformation("Released lock for PlacePair sync");
            }
        }
    }

    private async Task RunSynchronizationJob(CancellationToken stoppingToken)
    {
        // Khởi tạo đồng hồ đo thời gian tổng thể
        var globalStopwatch = Stopwatch.StartNew();
        _logger.LogInformation("Starting to synchronize PlacePair data to ElasticSearch");

        // Thống kê đồng bộ
        var syncStats = new SyncStats();
        // Synchronization lock để bảo vệ các biến toàn cục trong quá trình cập nhật
        var syncLock = new object();

        // Thời gian giới hạn dữ liệu 6 tháng gần nhất
        var sixMonthsAgo = DateTime.UtcNow.AddMonths(-6);

        try
        {
            await _dbContextResolver.ResolveAsync(async (dbContext) =>
            {
                // Thiết lập timeout cho DbContext để tránh timeout trong các truy vấn lớn
                dbContext.Database.SetCommandTimeout(COMMAND_TIMEOUT);

                // Lấy thông tin từ database
                _logger.LogInformation("Getting database statistics");
                var dbStats = await _dbService.GetDatabaseStats(dbContext, sixMonthsAgo, stoppingToken);

                if (dbStats.TotalRecords == 0)
                {
                    _logger.LogInformation("No records in database within the last 6 months");
                    return true;
                }

                _logger.LogInformation($"DB records: {dbStats.TotalRecords}, max UpdateAt: {dbStats.MaxUpdateAt:yyyy-MM-dd HH:mm:ss.fff}");
                if (dbStats.NewRecordsCount > 0)
                {
                    _logger.LogInformation($"DB new records: {dbStats.NewRecordsCount}, max CreateAt: {dbStats.MaxCreateAt:yyyy-MM-dd HH:mm:ss.fff}");
                    _logger.LogInformation($"📝 New records definition: UpdateAt=null OR UpdateAt=CreatedAt (newly created records)");
                }

                // Lấy thông tin từ Elasticsearch
                var esStats = new ElasticsearchStats
                {
                    IndexExists = await _esService.GetIndexExists(stoppingToken),
                    MaxUpdateAt = await _esService.GetMaxUpdateAtFromEs(stoppingToken),
                    MaxCreateAt = await _esService.GetMaxCreateAtFromEs(stoppingToken),
                    IndexInfo = await _esService.GetIndexStats(stoppingToken)
                };

                if (esStats.IndexExists)
                {
                    _logger.LogInformation($"ES max UpdateAt: {esStats.MaxUpdateAt:yyyy-MM-dd HH:mm:ss.fff}");
                    
                    // If MaxCreateAt is available
                    if (esStats.MaxCreateAt != default)
                    {
                        _logger.LogInformation($"ES max CreateAt: {esStats.MaxCreateAt:yyyy-MM-dd HH:mm:ss.fff}");
                    }
                }
                else
                {
                    _logger.LogInformation($"ES index '{_indexName}' doesn't exist");
                }

                // Quyết định chiến lược đồng bộ
                var syncStrategy = _strategyService.DetermineSyncStrategy(dbStats, esStats, sixMonthsAgo, stoppingToken);

                // Thực hiện đồng bộ theo chiến lược đã chọn
                if (syncStrategy.IsFullSync)
                {
                    _logger.LogInformation($"🔄 Performing FULL sync of PlacePair data. Reason: {syncStrategy.Reason}");

                    try
                    {
                        await _syncProcessor.PerformFullSync(
                            dbContext,
                            dbStats.TotalRecords,
                            syncLock,
                            stoppingToken,
                            syncStats,
                            MAX_RETRIES,
                            PAGE_SIZE);
                    }
                    catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
                    {
                        _logger.LogInformation("Full sync canceled");
                        return true;
                    }
                }
                else if (syncStrategy.RecordsToSync > 0)
                {
                    _logger.LogInformation($"⚡ Performing INCREMENTAL sync of PlacePair data. Reason: {syncStrategy.Reason}");
                    _logger.LogInformation($"📊 Sync details: {syncStrategy.RecordsToSync:N0} records to process");

                    try
                    {
                        // Lấy thống kê thực tế từ strategy
                        var actualNewRecords = await GetActualNewRecordsCount(dbContext, esStats.MaxCreateAt, stoppingToken);
                        var actualUpdatedRecords = syncStrategy.RecordsToSync - actualNewRecords;
                        
                        // Đồng bộ dữ liệu mới nếu có
                        if (actualNewRecords > 0)
                        {
                            _logger.LogInformation($"Step 1: Syncing {actualNewRecords:N0} NEW records (CreatedAt > ES_MaxCreateAt)");
                            _logger.LogInformation($"📝 New records: UpdateAt=null OR UpdateAt=CreatedAt AND CreatedAt > {esStats.MaxCreateAt:HH:mm:ss.fff}");
                            
                            await _syncProcessor.PerformIncrementalSync(
                                dbContext,
                                esStats.MaxUpdateAt,
                                dbStats.MaxUpdateAt,
                                actualNewRecords, 
                                syncLock,
                                stoppingToken,
                                syncStats,
                                MAX_RETRIES,
                                PAGE_SIZE,
                                syncNewRecordsOnly: true,
                                esMaxCreateAt: esStats.MaxCreateAt,
                                dbMaxCreateAt: dbStats.MaxCreateAt);
                        }
                        
                        // Sau đó đồng bộ dữ liệu đã cập nhật nếu có
                        if (actualUpdatedRecords > 0 && esStats.MaxUpdateAt < dbStats.MaxUpdateAt)
                        {
                            _logger.LogInformation($"Step 2: Syncing {actualUpdatedRecords:N0} UPDATED records");
                            _logger.LogInformation($"📝 Updated records: UpdateAt > CreatedAt AND UpdateAt > {esStats.MaxUpdateAt:HH:mm:ss.fff}");
                            
                            await _syncProcessor.PerformIncrementalSync(
                                dbContext,
                                esStats.MaxUpdateAt,
                                dbStats.MaxUpdateAt,
                                actualUpdatedRecords,
                                syncLock,
                                stoppingToken,
                                syncStats,
                                MAX_RETRIES,
                                PAGE_SIZE,
                                syncNewRecordsOnly: false);
                        }
                        
                        // Log nếu không có gì để sync
                        if (actualNewRecords == 0 && actualUpdatedRecords == 0)
                        {
                            _logger.LogInformation("✅ No actual records need syncing based on timestamps");
                        }
                    }
                    catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
                    {
                        _logger.LogInformation("Incremental sync canceled");
                        return true;
                    }
                }
                else
                {
                    _logger.LogInformation($"✅ No records to sync. Reason: {syncStrategy.Reason}");
                }

                syncStats.TotalCount = syncStats.TotalProcessed;
                return true;
            });

            globalStopwatch.Stop();

            var successRate = syncStats.TotalCount > 0 ? (syncStats.TotalProcessed * 100.0 / syncStats.TotalCount) : 0;
            var totalTimeSeconds = globalStopwatch.Elapsed.TotalSeconds;
            var avgRecordsPerSecond = totalTimeSeconds > 0 ? syncStats.TotalProcessed / totalTimeSeconds : 0;

            if (syncStats.TotalProcessed > 0)
            {
                _logger.LogInformation(
                    $"✅ Sync completed successfully! " +
                    $"📊 {syncStats.TotalProcessed:N0}/{syncStats.TotalCount:N0} records ({successRate:F1}%) " +
                    $"⏱️ {totalTimeSeconds:F1}s ({avgRecordsPerSecond:F0} rec/sec) " +
                    $"📦 Batches: {syncStats.BatchSuccessCount} OK, {syncStats.BatchFailureCount} failed, {syncStats.RetryCount} retries");
            }
            else
            {
                _logger.LogInformation($"✅ Sync completed - no records needed processing in {totalTimeSeconds:F1}s");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error synchronizing PlacePair data to ElasticSearch");
            throw;
        }
    }

    /// <summary>
    /// Tính toán số lượng bản ghi mới thực tế cần đồng bộ
    /// </summary>
    private async Task<int> GetActualNewRecordsCount(NpgPlaceContext dbContext, DateTime esMaxCreateAt, CancellationToken stoppingToken)
    {
        try
        {
            // Đếm số lượng bản ghi mới thực sự cần sync (dựa trên CreatedAt)
            var count = await dbContext.BookingPlacePairs
                .AsNoTracking()
                .CountAsync(x => ((x.UpdateAt == null && x.CreatedAt != null) || 
                               (x.UpdateAt != null && x.CreatedAt != null && x.UpdateAt == x.CreatedAt)) &&
                               x.CreatedAt > esMaxCreateAt, stoppingToken);
                               
            _logger.LogInformation($"🔢 Actual new records count: {count:N0} (CreatedAt > {esMaxCreateAt:HH:mm:ss.fff})");
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating actual new records count");
            return 0;
        }
    }
}
