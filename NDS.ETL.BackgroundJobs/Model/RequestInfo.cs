namespace NDS.ETL.BackgroundJobs.Model
{
    /// <summary>
    /// Lớp lưu trữ thông tin về yêu cầu của người dùng được trích xuất từ headers
    /// Đ<PERSON><PERSON><PERSON> sử dụng để theo dõi và xử lý thông tin request trong hệ thống ETL
    /// </summary>
    public class RequestInfo
    {
        // === Thông tin nhận diện request ===
        /// <summary>
        /// Mã định danh duy nhất của request
        /// Dùng để theo dõi và debug request xuyên suốt hệ thống
        /// </summary>
        public string? RequestId { get; set; }
        
        /// <summary>
        /// Phiên bản API đang được sử dụng
        /// </summary>
        public string? Version { get; set; }
        
        /// <summary>
        /// Thời gian request được gửi đến hệ thống
        /// </summary>
        public string? RequestTime { get; set; }
        
        // === Thông tin thiết bị của người dùng ===
        /// <summary>
        /// Loại thiết bị (ví dụ: Android, iOS, Web)
        /// </summary>
        public string? DeviceType { get; set; }
        
        /// <summary>
        /// Mã IMEI của thiết bị di động
        /// Dùng để định danh duy nhất thiết bị
        /// </summary>
        public string? Imei { get; set; }
        
        /// <summary>
        /// Chỉ định thiết bị có bị root/jailbreak hay không
        /// Quan trọng cho bảo mật và kiểm soát ứng dụng
        /// </summary>
        public string? IsRoot { get; set; }
        
        /// <summary>
        /// Tên model của thiết bị (ví dụ: iPhone 13, Samsung Galaxy S21)
        /// </summary>
        public string? DeviceModel { get; set; }
        
        /// <summary>
        /// Kích thước màn hình của thiết bị
        /// Hỗ trợ tối ưu trải nghiệm người dùng
        /// </summary>
        public string? ScreenSize { get; set; }
        
        /// <summary>
        /// Phiên bản SDK được sử dụng trên thiết bị
        /// </summary>
        public string? SdkVersion { get; set; }
        
        /// <summary>
        /// Phiên bản ứng dụng taxi đang sử dụng
        /// </summary>
        public string? TaxiVersion { get; set; }
        
        /// <summary>
        /// Phiên bản hệ điều hành của thiết bị
        /// </summary>
        public string? OsVersion { get; set; }
        
        // === Thông tin người dùng ===
        /// <summary>
        /// Ngôn ngữ mà người dùng đang sử dụng
        /// Hỗ trợ đa ngôn ngữ trong ứng dụng
        /// </summary>
        public string? Lang { get; set; }
        
        /// <summary>
        /// Tên đăng nhập của người dùng
        /// </summary>
        public string? UserName { get; set; }
        
        /// <summary>
        /// ID phiên làm việc của người dùng
        /// Dùng để theo dõi hoạt động trong một phiên
        /// </summary>
        public string? SessionId { get; set; }
        
        /// <summary>
        /// ID của client app gửi request
        /// Dùng để phân biệt nguồn request
        /// </summary>
        public string? ClientId { get; set; }
        
        /// <summary>
        /// Địa chỉ IP của client
        /// Dùng cho bảo mật và phân tích
        /// </summary>
        public string? ClientIp { get; set; }
        
        /// <summary>
        /// Vùng địa lý của người dùng
        /// </summary>
        public string? Region { get; set; }
        
        /// <summary>
        /// Vĩ độ của vị trí người dùng
        /// </summary>
        public double? Lat { get; set; }
        
        /// <summary>
        /// Kinh độ của vị trí người dùng
        /// </summary>
        public double? Lng { get; set; }
        
        /// <summary>
        /// Thẻ đánh dấu cho request
        /// Có thể dùng cho phân loại hoặc nhóm request
        /// </summary>
        public string? Tag { get; set; }
        
        // === Thông tin thanh toán ===
        /// <summary>
        /// Số điện thoại của người dùng
        /// Dùng cho xác thực và thanh toán
        /// </summary>
        public string? PhoneNumber { get; set; }
        
        /// <summary>
        /// Mã nonce dùng một lần cho bảo mật
        /// Thường dùng trong xác thực hoặc thanh toán
        /// </summary>
        public string? Nonce { get; set; }
        
        /// <summary>
        /// Mã ngân hàng sử dụng cho thanh toán
        /// </summary>
        public string? BankCode { get; set; }
        
        /// <summary>
        /// ID người dùng từ ngân hàng/ví điện tử
        /// Dùng để liên kết tài khoản thanh toán
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// Tạo chuỗi biểu diễn đơn giản hóa cho mục đích ghi log
        /// Các thông tin quan trọng sẽ được hiển thị ngắn gọn
        /// </summary>
        /// <returns>Chuỗi thông tin tóm tắt về request</returns>
        public override string ToString()
        {
            return $"[RequestId: {RequestId}] " +
                   $"[User: {UserName ?? "unknown"}] " +
                   $"[Device: {DeviceType ?? "unknown"}/{DeviceModel ?? "unknown"}] " +
                   $"[ClientId: {ClientId ?? "unknown"}] " +
                   $"[ClientIp: {ClientIp ?? "unknown"}]";
        }
    }
}
