using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("sheet3")]
public partial class Sheet3
{
    [Key]
    [Column("Column1.id")]
    public int Column1Id { get; set; }

    [Column("Column1.0")]
    public int? Column10 { get; set; }

    [Column("Column1.mahc")]
    [StringLength(50)]
    public string? Column1Mahc { get; set; }

    [Column("Column1.1")]
    public int? Column11 { get; set; }

    [Column("Column1.tentinh")]
    [StringLength(50)]
    public string? Column1Tentinh { get; set; }

    [Column("Column1.2")]
    [StringLength(50)]
    public string? Column12 { get; set; }

    [Column("Column1.dientichkm2")]
    [StringLength(50)]
    public string? Column1Dientichkm2 { get; set; }

    [Column("Column1.3")]
    [StringLength(50)]
    public string? Column13 { get; set; }

    [Column("Column1.dansonguoi")]
    [StringLength(50)]
    public string? Column1Dansonguoi { get; set; }

    [Column("Column1.4")]
    [StringLength(50)]
    public string? Column14 { get; set; }

    [Column("Column1.trungtamhc")]
    [StringLength(50)]
    public string? Column1Trungtamhc { get; set; }

    [Column("Column1.5")]
    [StringLength(50)]
    public string? Column15 { get; set; }

    [Column("Column1.kinhdo")]
    public int? Column1Kinhdo { get; set; }

    [Column("Column1.6")]
    public int? Column16 { get; set; }

    [Column("Column1.vido")]
    public int? Column1Vido { get; set; }

    [Column("Column1.7")]
    public int? Column17 { get; set; }

    [Column("Column1.truocsapnhap")]
    [StringLength(64)]
    public string? Column1Truocsapnhap { get; set; }

    [Column("Column1.8")]
    [StringLength(64)]
    public string? Column18 { get; set; }

    [Column("Column1.con")]
    [StringLength(50)]
    public string? Column1Con { get; set; }

    [Column("Column1.9")]
    [StringLength(50)]
    public string? Column19 { get; set; }
}
