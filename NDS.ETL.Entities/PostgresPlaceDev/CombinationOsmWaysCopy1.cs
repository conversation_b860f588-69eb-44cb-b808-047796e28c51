using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Table("combination_osm_ways_copy1")]
[Index("Id", Name = "id_copy1", IsUnique = true)]
[Index("Name", Name = "processed_osm_ways_name_copy1_copy1")]
public partial class CombinationOsmWaysCopy1
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("way", TypeName = "geometry(LineString,4326)")]
    public LineString? Way { get; set; }

    [Column("polyline")]
    public string? Polyline { get; set; }

    [Column("name")]
    public string? Name { get; set; }

    [Column("en_name")]
    public string? EnName { get; set; }

    [Column("highway")]
    public string? Highway { get; set; }

    [Column("province_code")]
    public List<int>? ProvinceCode { get; set; }

    [Column("district_code")]
    public List<int>? DistrictCode { get; set; }

    [Column("processed")]
    public bool? Processed { get; set; }

    [Column("oneway")]
    public bool? Oneway { get; set; }

    [Column("tprovince_code", TypeName = "character varying[]")]
    public List<string>? TprovinceCode { get; set; }

    [Column("tdistrict_code", TypeName = "character varying[]")]
    public List<string>? TdistrictCode { get; set; }

    [Column("length")]
    public int? Length { get; set; }

    [Column("same_administration")]
    public bool? SameAdministration { get; set; }

    [Column("alley_of")]
    public long? AlleyOf { get; set; }
}
