# Tài liệu kỹ thuật API Quản lý Tỉnh/Thành phố và Phường/Xã

## Giới thiệu

Tài liệu này mô tả chi tiết các API quản lý thông tin về đơn vị hành chính bao gồm tỉnh/thành phố (provinces) và phường/xã (wards). Các API này được thiết kế để cung cấp dữ liệu về đơn vị hành chính, hỗ trợ quản lý dữ liệu trong hệ thống trước và sau ngày 01/07/2025 (khi có sự thay đổi về đơn vị hành chính).

Các API được bảo vệ bằng xác thực API Key và được tài liệu hóa bằng Swagger.

### Thông tin phát triển:
Tài liệu này thuộc team Taxi-<PERSON><PERSON>n <PERSON>, Phòng Nội Dung Số, Khối Công Nghệ. 

Hỗ trợ kỹ thuật:

Liên hệ: <EMAIL>, <EMAIL>
Đội ngũ phát triển sẽ hỗ trợ các vấn đề kỹ thuật và tích hợp API

## Xác thực

Tất cả các API đều được bảo vệ bằng cơ chế xác thực API Key. Để gọi API, bạn cần:
- Thêm header `X-Api-Key` vào mỗi request với giá trị API key hợp lệ

## Tỉnh/Thành phố (Provinces)

### Mô hình dữ liệu

#### ProvinceResponse

Đại diện cho thông tin về một tỉnh/thành phố.

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| Id | int | ID của tỉnh/thành phố |
| Code | string | Mã của tỉnh/thành phố |
| Name | string | Tên của tỉnh/thành phố |
| Type | string | Loại (tỉnh hoặc thành phố) |
| OldCode | string | Mã cũ của tỉnh/thành phố (nếu có) |
| CenterLocation | string | Tọa độ trung tâm của tỉnh/thành phố (định dạng "lat,lng") |
| Processed | boolean | Trạng thái xử lý của tỉnh/thành phố |
| OldProvinces | OldProvinceInfo[] | Danh sách các tỉnh/thành phố cũ (trước 01/07/2025) được ánh xạ vào tỉnh/thành phố mới này |

#### OldProvinceInfo

Thông tin về tỉnh/thành phố cũ (trước 01/07/2025).

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| Id | int | ID của tỉnh/thành phố cũ |
| Code | string | Mã của tỉnh/thành phố cũ |
| Name | string | Tên của tỉnh/thành phố cũ |
| Type | int | Loại của tỉnh/thành phố cũ |
| CenterLocation | string | Tọa độ trung tâm của tỉnh/thành phố cũ (định dạng "lat,lng") |
| Processed | boolean | Trạng thái xử lý của tỉnh/thành phố cũ |

### Endpoints

#### 1. Lấy tất cả tỉnh/thành phố

```
GET /api/admin/provinces
```

**Mô tả:** Trả về danh sách tất cả tỉnh/thành phố.

**Tham số:** Không có

**Phản hồi:**

- **200 OK**: Mảng các đối tượng `ProvinceResponse`
- **401 Unauthorized**: API Key không hợp lệ
- **500 Internal Server Error**: Lỗi server

**Ví dụ phản hồi:**

```json
[
  {
    "id": 1,
    "code": "01",
    "name": "Hà Nội",
    "type": "Thành phố",
    "oldCode": null,
    "centerLocation": "21.0278,105.8342",
    "processed": true,
    "oldProvinces": []
  },
  {
    "id": 2,
    "code": "02",
    "name": "Hồ Chí Minh",
    "type": "Thành phố",
    "oldCode": null,
    "centerLocation": "10.8231,106.6297",
    "processed": true,
    "oldProvinces": []
  }
]
```

#### 2. Lấy tỉnh/thành phố theo mã

```
GET /api/admin/provinces/code/{code}
```

**Mô tả:** Trả về thông tin chi tiết của một tỉnh/thành phố dựa trên mã.

**Tham số đường dẫn:**
- `code` (string, bắt buộc): Mã của tỉnh/thành phố

**Phản hồi:**

- **200 OK**: Đối tượng `ProvinceResponse`
- **404 Not Found**: Không tìm thấy tỉnh/thành phố với mã đã cung cấp
- **401 Unauthorized**: API Key không hợp lệ
- **500 Internal Server Error**: Lỗi server

**Ví dụ phản hồi:**

```json
{
  "id": 1,
  "code": "01",
  "name": "Hà Nội",
  "type": "Thành phố",
  "oldCode": null,
  "centerLocation": "21.0278,105.8342",
  "processed": true,
  "oldProvinces": [
    {
      "id": 101,
      "code": "old_01",
      "name": "Hà Tây",
      "type": 1,
      "centerLocation": "20.9785,105.5318",
      "processed": true
    }
  ]
}
```

#### 3. Tìm kiếm tỉnh/thành phố theo tên

```
POST /api/admin/provinces/search/name
```

**Mô tả:** Tìm kiếm các tỉnh/thành phố dựa trên tên (có thể là tên tỉnh mới hoặc tên tỉnh cũ).

**Request Body:**
```json
{
  "name": "string" // Từ khóa tìm kiếm (bắt buộc)
}
```

**Phản hồi:**

- **200 OK**: Mảng các đối tượng `ProvinceResponse` phù hợp
- **400 Bad Request**: Thiếu tham số name hoặc name rỗng
- **401 Unauthorized**: API Key không hợp lệ
- **500 Internal Server Error**: Lỗi server

**Ví dụ yêu cầu:**

```
POST /api/admin/provinces/search/name
Content-Type: application/json

{
  "name": "Hà"
}
```

**Ví dụ phản hồi:**

```json
[
  {
    "id": 1,
    "code": "01",
    "name": "Hà Nội",
    "type": "Thành phố",
    "oldCode": null,
    "centerLocation": "21.0278,105.8342",
    "processed": true,
    "oldProvinces": [
      {
        "id": 101,
        "code": "old_01",
        "name": "Hà Tây",
        "type": 1,
        "centerLocation": "20.9785,105.5318",
        "processed": true
      }
    ]
  },
  {
    "id": 15,
    "code": "15",
    "name": "Hà Giang",
    "type": "Tỉnh",
    "oldCode": null,
    "centerLocation": "22.8015,104.9804",
    "processed": true,
    "oldProvinces": []
  }
]
```

#### 4. Lọc tỉnh/thành phố theo loại

```
GET /api/admin/provinces/filter/type
```

**Mô tả:** Lọc các tỉnh/thành phố theo loại.

**Tham số truy vấn:**
- `type` (string, bắt buộc): Loại tỉnh/thành phố (ví dụ: "Tỉnh", "Thành phố")

**Phản hồi:**

- **200 OK**: Mảng các đối tượng `ProvinceResponse` phù hợp
- **401 Unauthorized**: API Key không hợp lệ
- **500 Internal Server Error**: Lỗi server

**Ví dụ yêu cầu:**

```
GET /api/admin/provinces/filter/type?type=Tỉnh
```

**Ví dụ phản hồi:**

```json
[
  {
    "id": 15,
    "code": "15",
    "name": "Hà Giang",
    "type": "Tỉnh",
    "oldCode": null,
    "centerLocation": "22.8015,104.9804",
    "processed": true,
    "oldProvinces": []
  },
  {
    "id": 17,
    "code": "17",
    "name": "Hoà Bình",
    "type": "Tỉnh",
    "oldCode": null,
    "centerLocation": "20.8133,105.3383",
    "processed": true,
    "oldProvinces": []
  }
]
```

## Phường/Xã (Wards)

### Mô hình dữ liệu

#### WardResponse

Đại diện cho thông tin về một phường/xã.

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| Id | int | ID của phường/xã |
| Code | string | Mã của phường/xã |
| ProvinceCode | string | Mã tỉnh/thành phố mà phường/xã thuộc về |
| Name | string | Tên của phường/xã |
| Type | string | Loại (phường, xã, thị trấn) |
| OldCode | string | Mã cũ của phường/xã (nếu có) |
| CenterLocation | string | Tọa độ trung tâm của phường/xã (định dạng "lat,lng") |
| Processed | boolean | Trạng thái xử lý của phường/xã |
| OldWards | OldWardInfo[] | Danh sách các phường/xã cũ (trước 01/07/2025) được ánh xạ vào phường/xã mới này |

#### OldWardInfo

Thông tin về phường/xã cũ (trước 01/07/2025).

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| Id | int | ID của phường/xã cũ |
| Code | string | Mã của phường/xã cũ |
| Name | string | Tên của phường/xã cũ |
| Type | int | Loại của phường/xã cũ |
| CenterLocation | string | Tọa độ trung tâm của phường/xã cũ (định dạng "lat,lng") |
| Processed | boolean | Trạng thái xử lý của phường/xã cũ |

#### PagedWardResponse

Kết quả phân trang cho phường/xã.

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| Items | WardResponse[] | Danh sách các phường/xã trong trang hiện tại |
| TotalCount | int | Tổng số phường/xã (không phân trang) |
| CurrentPage | int | Số trang hiện tại |
| PageSize | int | Kích thước trang (số mục trên mỗi trang) |
| TotalPages | int | Tổng số trang |
| HasPreviousPage | boolean | Có trang trước không |
| HasNextPage | boolean | Có trang sau không |

### Endpoints

#### 1. Lấy tất cả phường/xã (có phân trang)

```
GET /api/admin/wards
```

**Mô tả:** Trả về danh sách phường/xã có phân trang.

**Tham số truy vấn:**
- `pageNumber` (int, tùy chọn, mặc định: 1): Số trang (bắt đầu từ 1)
- `pageSize` (int, tùy chọn, mặc định: 10): Kích thước trang (số mục trên mỗi trang)

**Phản hồi:**

- **200 OK**: Đối tượng `PagedWardResponse`
- **401 Unauthorized**: API Key không hợp lệ
- **500 Internal Server Error**: Lỗi server

**Ví dụ yêu cầu:**

```
GET /api/admin/wards?pageNumber=1&pageSize=10
```

**Ví dụ phản hồi:**

```json
{
  "items": [
    {
      "id": 1,
      "code": "00001",
      "provinceCode": "01",
      "name": "Phường Hàng Trống",
      "type": "Phường",
      "oldCode": null,
      "centerLocation": "21.0275,105.8510",
      "processed": true,
      "oldWards": []
    },
    // ... 9 phường/xã khác ...
  ],
  "totalCount": 11245,
  "currentPage": 1,
  "pageSize": 10,
  "totalPages": 1125,
  "hasPreviousPage": false,
  "hasNextPage": true
}
```

#### 2. Lấy phường/xã theo mã

```
GET /api/admin/wards/code/{code}
```

**Mô tả:** Trả về thông tin chi tiết của một phường/xã dựa trên mã.

**Tham số đường dẫn:**
- `code` (string, bắt buộc): Mã của phường/xã

**Phản hồi:**

- **200 OK**: Đối tượng `WardResponse`
- **404 Not Found**: Không tìm thấy phường/xã với mã đã cung cấp
- **401 Unauthorized**: API Key không hợp lệ
- **500 Internal Server Error**: Lỗi server

**Ví dụ phản hồi:**

```json
{
  "id": 1,
  "code": "00001",
  "provinceCode": "01",
  "name": "Phường Hàng Trống",
  "type": "Phường",
  "oldCode": null,
  "centerLocation": "21.0275,105.8510",
  "processed": true,
  "oldWards": [
    {
      "id": 101,
      "code": "old_00001",
      "name": "Phường Tràng Tiền",
      "type": 1,
      "centerLocation": "21.0252,105.8549",
      "processed": true
    }
  ]
}
```

#### 3. Tìm kiếm phường/xã theo tên (có phân trang)

```
POST /api/admin/wards/search/name
```

**Mô tả:** Tìm kiếm các phường/xã dựa trên tên (có thể là tên mới hoặc tên cũ) với phân trang.

**Request Body:**
```json
{
  "name": "string",     // Từ khóa tìm kiếm (bắt buộc)
  "pageNumber": 1,      // Số trang (tùy chọn, mặc định: 1)
  "pageSize": 10        // Kích thước trang (tùy chọn, mặc định: 10, tối đa: 100)
}
```

**Phản hồi:**

- **200 OK**: Đối tượng `PagedWardResponse` chứa các phường/xã phù hợp
- **400 Bad Request**: Thiếu tham số name hoặc name rỗng
- **401 Unauthorized**: API Key không hợp lệ
- **500 Internal Server Error**: Lỗi server

**Ví dụ yêu cầu:**

```
POST /api/admin/wards/search/name
Content-Type: application/json

{
  "name": "Trống",
  "pageNumber": 1,
  "pageSize": 10
}
```

**Ví dụ phản hồi:**

```json
{
  "items": [
    {
      "id": 1,
      "code": "00001",
      "provinceCode": "01",
      "name": "Phường Hàng Trống",
      "type": "Phường",
      "oldCode": null,
      "centerLocation": "21.0275,105.8510",
      "processed": true,
      "oldWards": []
    }
    // ... các phường/xã khác có tên chứa từ "Trống" ...
  ],
  "totalCount": 3,
  "currentPage": 1,
  "pageSize": 10,
  "totalPages": 1,
  "hasPreviousPage": false,
  "hasNextPage": false
}
```

#### 4. Lọc phường/xã theo loại (có phân trang)

```
GET /api/admin/wards/filter/type
```

**Mô tả:** Lọc các phường/xã theo loại với phân trang.

**Tham số truy vấn:**
- `type` (string, bắt buộc): Loại phường/xã (ví dụ: "Phường", "Xã", "Thị trấn")
- `pageNumber` (int, tùy chọn, mặc định: 1): Số trang (bắt đầu từ 1)
- `pageSize` (int, tùy chọn, mặc định: 10): Kích thước trang (số mục trên mỗi trang)

**Phản hồi:**

- **200 OK**: Đối tượng `PagedWardResponse` chứa các phường/xã phù hợp
- **401 Unauthorized**: API Key không hợp lệ
- **500 Internal Server Error**: Lỗi server

**Ví dụ yêu cầu:**

```
GET /api/admin/wards/filter/type?type=Phường&pageNumber=1&pageSize=10
```

**Ví dụ phản hồi:**

```json
{
  "items": [
    {
      "id": 1,
      "code": "00001",
      "provinceCode": "01",
      "name": "Phường Hàng Trống",
      "type": "Phường",
      "oldCode": null,
      "centerLocation": "21.0275,105.8510",
      "processed": true,
      "oldWards": []
    },
    // ... 9 phường khác ...
  ],
  "totalCount": 1578,
  "currentPage": 1,
  "pageSize": 10,
  "totalPages": 158,
  "hasPreviousPage": false,
  "hasNextPage": true
}
```
#### 5. Tra cứu phường/xã theo tọa độ
POST /api/admin/wards/search/location
**Mô tả:** Trả về thông tin phường/xã chứa tọa độ được cung cấp. API này sử dụng công nghệ PostGIS để thực hiện phép toán không gian ST_Contains, xác định phường/xã nào chứa điểm tọa độ đã cho.

**Request Body:**{
  "location": {
    "latitude": 21.0275,
    "longitude": 105.8510
  },
  "currentAddress": "Số 1, Hàng Trống, Hoàn Kiếm, Hà Nội" // Tùy chọn, dùng để ghi log trong trường hợp không tìm thấy phường/xã
}
**Phản hồi:**

- **200 OK**: Đối tượng `WardResponse` chứa thông tin phường/xã tại vị trí đó
- **400 Bad Request**: Tọa độ không hợp lệ hoặc thiếu dữ liệu yêu cầu
- **404 Not Found**: Không tìm thấy phường/xã tại vị trí
- **401 Unauthorized**: API Key không hợp lệ
- **500 Internal Server Error**: Lỗi server

**Ví dụ yêu cầu:**POST /api/admin/wards/search/location
Content-Type: application/json
X-Api-Key: your-api-key-here
```json
{
  "location": {
    "latitude": 21.01570501218229,
    "longitude": 105.814173523841
  }
}
```
**Ví dụ phản hồi:**
```json
{
  "id": 287,
  "code": "00199",
  "provinceCode": "01",
  "name": "Phường Láng",
  "type": "4",
  "oldCode": "00187;00199",
  "centerLocation": null,
  "processed": null,
  "oldWards": [
    {
      "id": 569,
      "code": "00187",
      "name": "Phường Láng Thượng",
      "type": 0,
      "centerLocation": "[105.8063, 21.0276]",
      "processed": false
    },
    {
      "id": 605,
      "code": "00199",
      "name": "Phường Láng Hạ",
      "type": 0,
      "centerLocation": "[105.8142, 21.0185]",
      "processed": false
    }
  ]
}
```
#### 6. Lấy địa chỉ mới theo tọa độ
POST /api/admin/wards/search/getnewaddressbylocation
**Mô tả:** Trả về thông tin phường/xã mới và một chuỗi địa chỉ đã được cập nhật dựa trên tọa độ cung cấp. API này sẽ thay thế các thành phần hành chính cũ (phường/xã, quận/huyện, tỉnh/thành phố) trong địa chỉ đầu vào bằng thông tin mới tương ứng (sau 01/07/2025).

**Quy tắc xử lý địa chỉ:**
- Nếu địa chỉ hiện tại có ít nhất 3 phần (được phân cách bởi dấu phẩy), API sẽ giữ lại các phần đầu và thay thế 3 phần cuối bằng tên phường/xã mới và tỉnh/thành phố mới
- Nếu địa chỉ có ít hơn 3 phần, API sẽ thêm tên phường/xã mới và tỉnh/thành phố mới vào cuối

**Request Body:**
```json
{
  "location": {
    "latitude": 21.0275,
    "longitude": 105.8510
  },
  "currentAddress": "Số 1, phố Hàng Trống, quận Hoàn Kiếm, thành phố Hà Nội" // Địa chỉ hiện tại để được cập nhật
}
```
**Phản hồi:**

- **200 OK**: Đối tượng `GetNewAddressByLocationResponse` gồm thông tin phường/xã và địa chỉ mới
- **400 Bad Request**: Tọa độ không hợp lệ hoặc thiếu dữ liệu yêu cầu
- **404 Not Found**: Không tìm thấy phường/xã tại vị trí
- **401 Unauthorized**: API Key không hợp lệ
- **500 Internal Server Error**: Lỗi server

**Ví dụ yêu cầu:**POST /api/admin/wards/search/getnewaddressbylocation
Content-Type: application/json
X-Api-Key: your-api-key-here
```json
{
  "location": {
    "latitude": 21.01570501218229,
    "longitude": 105.814173523841
  },
  "currentAddress": "Toà nhà VNPAY , 22 P. Láng Hạ, Láng Hạ, Đống Đa, Hà Nội 100000, Việt Nam"
}
```
**Ví dụ phản hồi:**
```json
{
  "oldAddress": "Toà nhà VNPAY , 22 P. Láng Hạ, Láng Hạ, Đống Đa, Hà Nội 100000, Việt Nam",
  "currentAddress": "Toà nhà VNPAY, 22 P. Láng Hạ, Phường Láng, Thành phố Hà Nội",
  "id": 287,
  "code": "00199",
  "provinceCode": "01",
  "name": "Phường Láng",
  "type": "4",
  "oldCode": "00187;00199",
  "centerLocation": null,
  "processed": null,
  "oldWards": [
    {
      "id": 569,
      "code": "00187",
      "name": "Phường Láng Thượng",
      "type": 0,
      "centerLocation": "[105.8063, 21.0276]",
      "processed": false
    },
    {
      "id": 605,
      "code": "00199",
      "name": "Phường Láng Hạ",
      "type": 0,
      "centerLocation": "[105.8142, 21.0185]",
      "processed": false
    }
  ]
}
```

## Đặc điểm kỹ thuật

### Cơ chế cache

Hệ thống sử dụng cơ chế cache để tối ưu hiệu suất:

- **Thời gian cache**: 4 giờ cho dữ liệu tỉnh/thành phố và phường/xã

### Đặc điểm phân trang

- Hỗ trợ phân trang cho tất cả API trả về danh sách phường/xã
- Giới hạn kích thước trang tối đa: 100 mục
- API phân trang cung cấp các thông tin hữu ích như tổng số trang, tổng số mục, có trang trước/sau

## Hướng dẫn tích hợp

### Sử dụng Swagger UI

1. Truy cập Swagger UI: `/swagger`
2. Nhập thông tin xác thực: 
   - Username: (được cung cấp riêng)
   - Password: (được cung cấp riêng)
3. Nhập API Key vào ô "Authorize" với khóa "X-Api-Key"
4. Thử nghiệm các API có sẵn

### Ví dụ tích hợp (C#)

```csharp
using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;

public async Task<string> GetProvinceByCode(string code)
{
    using var httpClient = new HttpClient();
    
    // Thêm API Key vào header
    httpClient.DefaultRequestHeaders.Add("X-Api-Key", "your-api-key-here");
    
    // Gọi API
    var response = await httpClient.GetAsync($"https://your-api-domain/api/admin/provinces/code/{code}");
    
    // Kiểm tra kết quả
    if (response.IsSuccessStatusCode)
    {
        return await response.Content.ReadAsStringAsync();
    }
    else
    {
        throw new Exception($"API call failed with status code: {response.StatusCode}");
    }
}
```

## Ghi chú quan trọng

1. Dữ liệu đơn vị hành chính được cập nhật để hỗ trợ sự thay đổi có hiệu lực vào ngày 01/07/2025
2. API hỗ trợ truy vấn thông tin đơn vị hành chính cũ thông qua đơn vị hành chính mới
3. Để tối ưu hiệu suất, nên sử dụng phân trang cho các API trả về danh sách lớn