using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("uat_place_migrate")]
[Index("PlaceId", Name = "idx_uat_place_migrate_placeid")]
public partial class UatPlaceMigrate
{
    [Column("placeId")]
    [StringLength(5000)]
    public string PlaceId { get; set; } = null!;

    [Column("name", TypeName = "character varying")]
    public string? Name { get; set; }

    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("address_old", TypeName = "character varying")]
    public string? AddressOld { get; set; }

    [Column("address", TypeName = "character varying")]
    public string? Address { get; set; }

    [Column("name_old", TypeName = "character varying")]
    public string? NameOld { get; set; }

    [Column("location_old", TypeName = "geometry(Point,4326)")]
    public Point? LocationOld { get; set; }

    [Column("location", TypeName = "geometry(Point,4326)")]
    public Point? Location { get; set; }

    [Column("update_user", TypeName = "character varying")]
    public string? UpdateUser { get; set; }

    [Column("status")]
    public int Status { get; set; }

    [Column("districtid")]
    public int? Districtid { get; set; }

    [Column("provinceid")]
    public int? Provinceid { get; set; }

    [Column("wardid")]
    public int? Wardid { get; set; }
}
