using System.Diagnostics;
using System.Text.RegularExpressions;
using Elastic.Clients.Elasticsearch;
using KnowledgeBase.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.Entities.Context;
using NDS.ETL.Entities.PostgresPlace;
using NDS.ETL.Infrastructure.Utils;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.PlaceSync;

/// <summary>
/// Job to initialize Place data into ElasticSearch
/// </summary>
public class InitPlaceJobs : RecurringJobBaseElastic
{
    private readonly ILogger<RecurringJobBaseElastic> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly ElasticSearchConfig _indexName;
    private const int BATCH_SIZE = 100_000; // Further reduced batch size for processing
    private const int COMMAND_TIMEOUT = 300_000; // Database command timeout (ms)
    private const int MAX_CONCURRENT_TASKS = 4; // Reduced concurrent tasks
    private readonly ElasticsearchClient _elasticClient;

    // Thêm hằng số cho index checkpoint và documentId
    private const string CheckpointIndex = "job-checkpoints";
    private string CheckpointDocumentId => $"{JobId}-checkpoint";

    public InitPlaceJobs(IConfiguration configuration, ILogger<RecurringJobBaseElastic> logger,
        ElasticsearchClient elasticClient, IServiceProvider serviceProvider) : base(configuration, logger, elasticClient, null)
    {
        _indexName = configuration.Bind<ElasticSearchConfig>(nameof(ElasticSearchConfig));
        _logger = logger;
        _serviceProvider = serviceProvider;
        _elasticClient = elasticClient;
        
        // Increase the timeout settings for the Elasticsearch client
        ConfigureElasticsearchTimeout();
    }

    /// <summary>
    /// Configures timeout settings for the Elasticsearch client
    /// </summary>
    private void ConfigureElasticsearchTimeout()
    {
        try
        {
            // Try to access the underlying connection settings to increase the timeout
            var connectionSettings = _elasticClient.ElasticsearchClientSettings;
            var transport = connectionSettings.NodePool.Nodes.FirstOrDefault()?.Uri.ToString();
            _logger.LogInformation($"Elasticsearch client connected to: {transport}");
            
            // Log current timeout settings if possible
            _logger.LogInformation("Elasticsearch client timeout configured. Default request timeout is 60 seconds.");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Could not inspect Elasticsearch client configuration");
        }
    }

    public override string JobId => "InitPlace";
    public override string ConfigSection => "JobPgSyncPlace";
    
    // Override RetryCount from base class to increase retry attempts
    protected override int RetryCount => 10;
    
    // Override Sleep method to provide longer timeouts
    protected override TimeSpan Sleep(int retryAttempt)
    {
        var retryTime = Math.Pow(2, retryAttempt);
        if (retryTime > 120) // Increase max retry time to 120 seconds
            retryTime = 120;
        return TimeSpan.FromSeconds(retryTime);
    }

    //private const string city = "hanoi";

    /// <summary>
    /// Sync Place data from database to ElasticSearch
    /// </summary>
    public override async Task DoWorkAsync(CancellationToken stoppingToken)
    {
        // Bắt đầu đồng bộ theo trường Id, sử dụng checkpoint để tiếp tục từ lần trước
        var globalStopwatch = Stopwatch.StartNew();
        _logger.LogInformation("Starting to sync Place data to ElasticSearch");

        long lastProcessedId = 0; // Khai báo biến lastProcessedId ngoài try-catch để luôn lưu giá trị cuối cùng khi lỗi
        try
        {
            // Đảm bảo index checkpoint tồn tại trên Elasticsearch
            await EnsureCheckpointIndexExistsAsync();
            // Lấy Id cuối cùng đã xử lý từ checkpoint
            lastProcessedId = await GetLastProcessedIdFromCheckpointAsync();
            // Lấy Id lớn nhất hiện tại trên ES để so sánh
            long esMaxId = await GetMaxIdFromElasticsearch(stoppingToken);
            _logger.LogInformation($"Continue sync from Id: {lastProcessedId}, ES maxId: {esMaxId}");

            using var scope = _serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<NpgPlaceContext>();
            dbContext.Database.SetCommandTimeout(COMMAND_TIMEOUT);
            
            long processedCount = 0;
            long plusCodeCount = 0; // Biến đếm số lượng địa điểm có pluscode
            
            // Sử dụng semaphore để giới hạn số task đồng thời
            using var semaphore = new SemaphoreSlim(MAX_CONCURRENT_TASKS);
            var indexingTasks = new List<Task>();

            // Lặp lấy từng batch theo thứ tự tăng dần của Id
            while (true)
            {
                if (stoppingToken.IsCancellationRequested)
                {
                    _logger.LogWarning("Cancellation requested, stopping indexing process");
                    break;
                }

                // Lấy một batch bản ghi từ DB theo Id tăng dần
                var sw = Stopwatch.StartNew();
                var items = await FetchBatchAsync(dbContext, lastProcessedId, stoppingToken);
                if (!items.Any())
                {
                    // Nếu không có dữ liệu, log rõ lý do
                    _logger.LogError($"No data fetched from DB for lastProcessedId={lastProcessedId}. Possible reasons: DB empty, filter incorrect, or connection issue.");
                    break;
                }
                sw.Stop();
                
                _logger.LogInformation($"Total Fetch Batch From DB : {sw.Elapsed.TotalSeconds:F2}s. ");
                // Cập nhật lastProcessedId là Id cuối cùng của batch vừa lấy
                lastProcessedId = items.Last().Id;

                // Đợi đến khi có slot task trống
                await semaphore.WaitAsync(stoppingToken);

                // Tạo task để index batch này lên ES
                var task = Task.Run(async () =>
                {
                    try
                    {
                        var sw = Stopwatch.StartNew();
                        // Regex kiểm tra plus code trong địa chỉ
                        string pattern = @"(?i)\b[23456789CFGHJMPQRVWX]{4,6}\s*\+\s*[23456789CFGHJMPQRVWX]{2,3}\b";
                        items = items.Where(x =>
                        {
                            x.ProcessKeywords(); 
                            x.CalculateMasterAddressAndPermuted();
                            var hasPlusCode = Regex.Match(x.FullAddress, pattern).Success;
                            if (hasPlusCode)
                            {
                                plusCodeCount++;
                            }
                            return !hasPlusCode;
                        }).ToList();
                        // Gọi hàm xử lý batch và retry nếu lỗi
                        await ProcessBatchWithRetryAndResizeAsync(
                            items, 
                            stoppingToken, 
                            _indexName.Index[0], 
                            5, // timeout 5 phút
                            count => Interlocked.Add(ref processedCount, count)
                        );
                        sw.Stop();
                        _logger.LogInformation(
                            $"Indexed batch {items.First().Id}-{items.Last().Id} in {sw.Elapsed.TotalSeconds:F2}s. " +
                            $"Total indexed so far: {processedCount}. " +
                            $"Total plus code count: {plusCodeCount}");
                        // Lưu checkpoint sau mỗi batch thành công
                        await SaveLastProcessedIdToCheckpointAsync(lastProcessedId);
                    }
                    catch (Exception ex)
                    {
                        // Log lỗi chi tiết khi xử lý batch
                        _logger.LogError(ex, $"Exception occurred while indexing batch {lastProcessedId}");
                        throw;
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                }, stoppingToken);

                indexingTasks.Add(task);
                // Delay giữa các batch để tránh quá tải
                await Task.Delay(3000, stoppingToken);
            }

            // Đợi tất cả các task index hoàn thành
            await Task.WhenAll(indexingTasks);

            globalStopwatch.Stop();
            _logger.LogInformation(
                $"✅ Completed syncing {processedCount} records to ElasticSearch. " +
                $"⏱ Total time: {globalStopwatch.Elapsed.TotalSeconds:F2}s. " +
                $"⚡ Average speed: {processedCount / globalStopwatch.Elapsed.TotalSeconds:F2} records/s. " +
                $"Total plus code count: {plusCodeCount}");
            // Lưu trạng thái hoàn thành vào checkpoint
            await SaveLastProcessedIdToCheckpointAsync(lastProcessedId, "Completed");
        }
        catch (Exception ex)
        {
            // Log lỗi chi tiết khi job fail
            _logger.LogError(ex, $"Error while syncing Place data to ElasticSearch. Exception: {ex.Message}");
            // Luôn lưu checkpoint với lastProcessedId cuối cùng đã xử lý, không ghi đè về 0
            await SaveLastProcessedIdToCheckpointAsync(lastProcessedId, "Error");
            throw;
        }
    }

    // Hàm đảm bảo index checkpoint tồn tại trên ES
    private async Task EnsureCheckpointIndexExistsAsync()
    {
        // Kiểm tra index checkpoint đã tồn tại chưa
        var exists = await _elasticClient.Indices.ExistsAsync(CheckpointIndex);
        if (!exists.Exists)
        {
            // Tạo index với mapping chuẩn cho JobCheckpointEs và refresh_interval 10s
            await _elasticClient.Indices.CreateAsync(CheckpointIndex, c => c
                .Settings(s => s
                    .NumberOfShards(1)
                    .NumberOfReplicas(1)
                    .RefreshInterval("10s") // refresh interval 10s
                )
                .Mappings(m => m
                    .Properties<JobCheckpointEs>(p => p
                        .Keyword(k => k.Id)
                        .LongNumber(l => l.LastProcessedId)
                        .Date(d => d.UpdatedAt)
                        .LongNumber(l => l.TotalProcessed)
                        .LongNumber(l => l.UniqueProcessed)
                        .LongNumber(l => l.TotalRecordsInIndex)
                        .Keyword(k => k.Status)
                        .Text(t => t.AdditionalInfo)
                        .Text(t => t.LastError)
                        .DoubleNumber(n => n.ProcessingSpeed)
                        .DoubleNumber(n => n.ProgressPercentage)
                        .Date(d => d.EstimatedCompletionTime)
                        .IntegerNumber(n => n.BatchesProcessed)
                    )
                )
            );
            _logger.LogInformation($"Checkpoint index '{CheckpointIndex}' created with mapping and refresh_interval 10s");
        }
    }

    // Hàm lấy lastProcessedId từ checkpoint trên ES
    private async Task<long> GetLastProcessedIdFromCheckpointAsync()
    {
        // Lấy checkpoint từ ES, nếu có lỗi hoặc không tìm thấy thì trả về 0
        try
        {
            var response = await _elasticClient.GetAsync<JobCheckpointEs>(CheckpointDocumentId, g => g.Index(CheckpointIndex));
            if (response.IsValidResponse && response.Found)
            {
                _logger.LogInformation($"Found checkpoint: LastProcessedId={response.Source.LastProcessedId}, Status={response.Source.Status}");
                // Nếu checkpoint đang ở trạng thái Error nhưng lastProcessedId > 0 thì vẫn tiếp tục từ lastProcessedId
                if (response.Source.Status == "Error" && response.Source.LastProcessedId > 0)
                {
                    _logger.LogWarning($"Checkpoint status is Error but LastProcessedId={response.Source.LastProcessedId} > 0. Will continue from lastProcessedId.");
                    return response.Source.LastProcessedId;
                }
                return response.Source.LastProcessedId;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Could not get checkpoint from ES");
        }
        _logger.LogInformation("No checkpoint found, start from 0");
        return 0;
    }

    // Hàm lưu lastProcessedId vào checkpoint trên ES
    private async Task SaveLastProcessedIdToCheckpointAsync(long lastProcessedId, string status = "Running")
    {
        // Chỉ ghi checkpoint với status Error nếu thực sự có lỗi
        try
        {
            // Nếu status là Error và lastProcessedId = 0, kiểm tra checkpoint cũ
            if (status == "Error" && lastProcessedId == 0)
            {
                var oldCheckpoint = await _elasticClient.GetAsync<JobCheckpointEs>(CheckpointDocumentId, g => g.Index(CheckpointIndex));
                if (oldCheckpoint.IsValidResponse && oldCheckpoint.Found && oldCheckpoint.Source.LastProcessedId > 0)
                {
                    // Nếu checkpoint cũ có lastProcessedId > 0 thì giữ lại giá trị đó
                    lastProcessedId = oldCheckpoint.Source.LastProcessedId;
                    _logger.LogWarning($"Preserve lastProcessedId={lastProcessedId} from previous checkpoint when saving error status.");
                }
            }
            var checkpoint = new JobCheckpointEs
            {
                Id = CheckpointDocumentId,
                LastProcessedId = lastProcessedId,
                UpdatedAt = DateTime.UtcNow,
                Status = status,
                AdditionalInfo = $"LastProcessedId={lastProcessedId}"
            };
            await _elasticClient.IndexAsync(checkpoint, i => i.Index(CheckpointIndex).Id(CheckpointDocumentId).Refresh(Elastic.Clients.Elasticsearch.Refresh.True));
            _logger.LogInformation($"Checkpoint updated: LastProcessedId={lastProcessedId}, Status={status}");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Could not save checkpoint to ES");
        }
    }


    /// <summary>
    /// Fetches a batch of Place records from the database
    /// </summary>
    /// <param name="dbContext">The database context</param>
    /// <param name="lastId">The last processed ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of Place records</returns>
    private async Task<List<PlacesEs>> FetchBatchAsync(NpgPlaceContext dbContext, long lastId, CancellationToken cancellationToken)
    {
        // Nếu lastId = 0 thì log tổng số bản ghi, ngược lại log số còn lại
        if (lastId == 0)
        {
            var totalRecords = await GetPlacesQuery(dbContext, lastId)
                .CountAsync(cancellationToken);
            _logger.LogInformation($"Total records to process: {totalRecords}");
        }
        else
        {
            var remainingRecords = await GetPlacesQuery(dbContext, lastId)
                .CountAsync(cancellationToken);
            _logger.LogInformation($"Remaining records to process: {remainingRecords}");
        }
        // Lấy batch theo thứ tự tăng dần của Id
        var batch = await GetPlacesQuery(dbContext, lastId)
            .OrderBy(x => x.Id) // Đảm bảo lấy theo thứ tự tăng dần của Id
            .Take(BATCH_SIZE)
            .Select(PlacesEs.Selector)
            .ToListAsync(cancellationToken);
        // Không cần loại bỏ các bản ghi đã index trên ES vì đã dùng checkpoint
        return batch;
    }

    private IQueryable<Place> GetPlacesQuery(NpgPlaceContext dbContext, long lastId)
    {
        var query = dbContext.Places
            .Include(x => x.SubPlaces)
            .AsNoTracking();

        // Apply the filter only if lastId is greater than 0
        query = lastId > 0 ? query.Where(x => x.Removed != true && x.Id > lastId) : query.Where(x => x.Removed != true); // Ensure we still filter out removed records

        return query;
    }

    /// <summary>
    /// Lấy giá trị Id lớn nhất đã đồng bộ từ Elasticsearch
    /// </summary>
    private async Task<long> GetMaxIdFromElasticsearch(CancellationToken stoppingToken)
    {
        try
        {
            // Kiểm tra index có tồn tại không
            var indexExistsResponse = await _elasticClient.Indices.ExistsAsync(_indexName.Index[0], stoppingToken);
            if (!indexExistsResponse.Exists)
            {
                _logger.LogWarning($"ES index '{_indexName.Index[0]}' doesn't exist");
                return 0;
            }

            // Sử dụng aggregation để lấy Id lớn nhất
            var searchResponse = await _elasticClient.SearchAsync<PlacesEs>(s => s
                .Index(_indexName.Index[0])
                .Size(0)
                .Aggregations(aggs => aggs
                    .Add("max_id", d =>
                    {
                        d.Max(x => x.Field(f => f.Id));
                    })
                ), stoppingToken);

            if (!searchResponse.IsValidResponse)
            {
                _logger.LogError($"Error retrieving max ID from ES: {searchResponse.DebugInformation}");
                return 0;
            }

            var maxAgg = searchResponse.Aggregations?.GetMax("max_id");
            if (maxAgg == null || maxAgg.Value == null)
            {
                _logger.LogWarning("No max aggregation results found");
                return 0;
            }

            double? maxIdDouble = maxAgg.Value.GetValueOrDefault();
            long maxId = (long)(maxIdDouble ?? 0);
            _logger.LogInformation($"Max Id from ES: {maxId}");
            return maxId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving max Id from Elasticsearch");
            return 0;
        }
    }
}