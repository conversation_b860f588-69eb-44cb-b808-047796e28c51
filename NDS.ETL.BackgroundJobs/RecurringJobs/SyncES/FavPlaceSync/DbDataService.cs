using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch.FavPlaceSync;
using NDS.ETL.Entities.Context;
using NDS.ETL.Entities.PostgresPlace;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.FavPlaceSync;

/// <summary>
/// Dịch vụ làm việc với dữ liệu cơ sở dữ liệu
/// </summary>
public class DbDataService
{
    private readonly ILogger _logger;

    public DbDataService(ILogger logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// L<PERSON>y thông tin thống kê từ cơ sở dữ liệu
    /// </summary>
    public async Task<DatabaseStats> GetDatabaseStats(NpgPlaceContext dbContext, CancellationToken stoppingToken)
    {
        // Lấy ID lớn nhất trong cơ sở dữ liệu
        var maxId = await dbContext.FavoritePlaces
            .AsNoTracking()
            .MaxAsync(i => i.Id, stoppingToken);
        
        // Lấy thời gian cập nhật mới nhất
        DateTime maxUpdatedAt;
        try 
        {
            maxUpdatedAt = await dbContext.FavoritePlaces
                .AsNoTracking()
                .MaxAsync(i => i.UpdatedAt, stoppingToken);
        }
        catch (InvalidOperationException)
        {
            _logger.LogWarning("No records found when getting MaxUpdatedAt, using DateTime.MinValue");
            maxUpdatedAt = DateTime.MinValue;
        }

        // Lấy thời gian tạo mới nhất cho các bản ghi mới (UpdatedAt = null)
        // Sử dụng FirstOrDefault thay vì Max để tránh lỗi khi không có bản ghi nào
        var newRecord = await dbContext.FavoritePlaces
            .Where(x => x.UpdatedAt == null && x.CreatedAt != null)
            .OrderByDescending(x => x.CreatedAt)
            .FirstOrDefaultAsync(stoppingToken);
            
        var maxCreateAt = newRecord?.CreatedAt ?? DateTime.MinValue;
            
        if (newRecord != null)
        {
            _logger.LogInformation($"Found newest record with ID {newRecord.Id}, CreatedAt: {maxCreateAt:yyyy-MM-dd HH:mm:ss.fff}");
        }

        // Tổng số bản ghi
        var totalRecords = await dbContext.FavoritePlaces.CountAsync(stoppingToken);
        
        // Đếm số lượng bản ghi mới (có CreatedAt nhưng UpdatedAt là null)
        var newRecordsCount = await dbContext.FavoritePlaces
            .AsNoTracking()
            .Where(x => x.UpdatedAt == null && x.CreatedAt != null)
            .CountAsync(stoppingToken);
        
        return new DatabaseStats
        {
            MaxId = maxId,
            MaxUpdatedAt = maxUpdatedAt,
            MaxCreateAt = maxCreateAt,
            TotalRecords = totalRecords,
            NewRecordsCount = newRecordsCount
        };
    }

    /// <summary>
    /// Lấy dữ liệu theo khoảng ID
    /// </summary>
    public async Task<List<FavoritePlace>> FetchBatchByIdRangeAsync(
        NpgPlaceContext dbContext, 
        long startId, 
        long endId, 
        CancellationToken stoppingToken)
    {
        return await dbContext.FavoritePlaces
            .Where(x => x.Id >= startId && x.Id <= endId)
            .OrderBy(x => x.Id)
            .ToListAsync(stoppingToken);
    }

    /// <summary>
    /// Lấy dữ liệu mới (UpdatedAt = null) theo khoảng ID
    /// </summary>
    public async Task<List<FavoritePlace>> FetchNewBatchByIdRangeAsync(
        NpgPlaceContext dbContext, 
        long startId, 
        long endId, 
        CancellationToken stoppingToken)
    {
        return await dbContext.FavoritePlaces
            .Where(x => x.Id >= startId && x.Id <= endId)
            .Where(x => x.UpdatedAt == null && x.CreatedAt != null)
            .OrderBy(x => x.Id)
            .ToListAsync(stoppingToken);
    }

    /// <summary>
    /// Lấy dữ liệu theo khoảng thời gian cập nhật
    /// </summary>
    public async Task<List<FavoritePlace>> FetchBatchByUpdatedAtRangeAsync(
        NpgPlaceContext dbContext, 
        DateTime startUpdatedAt, 
        DateTime endUpdatedAt, 
        CancellationToken stoppingToken)
    {
        return await dbContext.FavoritePlaces
            .Where(x => x.UpdatedAt >= startUpdatedAt && x.UpdatedAt <= endUpdatedAt)
            .OrderBy(x => x.UpdatedAt)
            .ToListAsync(stoppingToken);
    }

    /// <summary>
    /// Lấy dữ liệu đã cập nhật (UpdatedAt không null và lớn hơn lastUpdatedAt)
    /// </summary>
    public async Task<List<FavoritePlace>> FetchUpdatedRecordsAsync(
        NpgPlaceContext dbContext,
        long startId,
        long endId, 
        DateTime lastUpdatedAt,
        CancellationToken stoppingToken)
    {
        return await dbContext.FavoritePlaces
            .Where(x => x.Id >= startId && x.Id <= endId)
            .Where(x => x.UpdatedAt != null && x.UpdatedAt > lastUpdatedAt)
            .OrderBy(x => x.Id)
            .ToListAsync(stoppingToken);
    }

    /// <summary>
    /// Chuyển đổi dữ liệu từ cơ sở dữ liệu sang định dạng ElasticSearch
    /// </summary>
    public List<FavPlaceEs> MapToEsDocuments(List<FavoritePlace> data)
    {
        return data.Select(t => new FavPlaceEs
        {
            Id = t.Id,
            PlaceDetailId = t.PlaceDetailId,
            Location = new Location
            {
                Lat = t.Location?.Y ?? 0.0,
                Lon = t.Location?.X ?? 0.0
            },
            PhoneNumber = t.PhoneNumber,
            Name = t.Name,
            PlaceId = t.PlaceId,
            Address = t.Address,
            FullAddress = t.FullAddress,
            StreetNumber = t.StreetNumber,
            Active = t.Active,
            Keywords = t.Keywords,
            Route = t.Route,
            Ward = t.Ward,
            District = t.District,
            City = t.City,
            Type = t.Type,
            DataFrom = t.DataFrom,
            CreatedAt = t.CreatedAt,
            UpdatedAt = t.UpdatedAt,
            BookingCount = t.BookingCount,
            ParentAddress = t.ParentAddress,
            ParentId = t.ParentId,
            Removed = t.Removed,
            TagInput = t.TagInput,
            UserId = t.UserId
        }).ToList();
    }
} 