using NDS.ETL.Entities.PostgresPlace;

namespace NDS.ETL.BackgroundJobs.Services;

/// <summary>
/// Service interface for handling operations related to provinces
/// </summary>
public interface IProvincesService
{
    /// <summary>
    /// Gets all provinces
    /// </summary>
    /// <returns>List of provinces</returns>
    Task<List<ProvincesNew>> GetAllProvincesAsync();
    
    /// <summary>
    /// Gets a province by its ID
    /// </summary>
    /// <param name="id">Province ID</param>
    /// <returns>Province entity</returns>
    Task<ProvincesNew?> GetProvinceByIdAsync(int id);
    
    /// <summary>
    /// Gets a province by its code
    /// </summary>
    /// <param name="code">Province code</param>
    /// <returns>Province entity</returns>
    Task<ProvincesNew?> GetProvinceByCodeAsync(string code);
    
    /// <summary>
    /// Gets provinces by name
    /// </summary>
    /// <param name="name">Name to search for</param>
    /// <returns>List of matching provinces</returns>
    Task<List<ProvincesNew>> GetProvincesByNameAsync(string name);
    
    /// <summary>
    /// Gets provinces by type
    /// </summary>
    /// <param name="type">Province type</param>
    /// <returns>List of matching provinces</returns>
    Task<List<ProvincesNew>> GetProvincesByTypeAsync(string type);
    
    /// <summary>
    /// Invalidates all province related caches
    /// </summary>
    void InvalidateAllProvinceCache();
}