using System.Collections.Generic;

namespace NDS.ETL.BackgroundJobs.Model.API;

/// <summary>
/// Response model for ward data
/// </summary>
public class WardResponse
{
    /// <summary>
    /// Kh?i t?o ??i t??ng WardResponse v?i danh s�ch ph??ng/x� c? tr?ng
    /// </summary>
    public WardResponse()
    {
        OldWards = new List<OldWardInfo>();
    }
    
    /// <summary>
    /// Ward ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// Ward code
    /// </summary>
    public string? Code { get; set; }
    
    /// <summary>
    /// Province code
    /// </summary>
    public string? ProvinceCode { get; set; }
    
    /// <summary>
    /// Ward name
    /// </summary>
    public string? Name { get; set; }
    
    /// <summary>
    /// Ward type
    /// </summary>
    public string? Type { get; set; }
    
    /// <summary>
    /// Old ward code
    /// </summary>
    public string? OldCode { get; set; }
    
    /// <summary>
    /// Ward center coordinates (format: "lat,lng")
    /// </summary>
    public string? CenterLocation { get; set; }
    
    /// <summary>
    /// Whether the ward has been processed
    /// </summary>
    public bool? Processed { get; set; }
    
    /// <summary>
    /// Danh s�ch c�c ph??ng/x� c? (tr??c 1/7/2025) ???c �nh x? v�o ph??ng/x� m?i n�y
    /// </summary>
    public List<OldWardInfo> OldWards { get; set; }
}

/// <summary>
/// Th�ng tin v? ph??ng/x� c? tr??c 1/7/2025
/// </summary>
public class OldWardInfo
{
    /// <summary>
    /// ID ph??ng/x� c?
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// M� ph??ng/x� c?
    /// </summary>
    public string Code { get; set; } = null!;
    
    /// <summary>
    /// T�n ph??ng/x� c?
    /// </summary>
    public string? Name { get; set; }
    
    /// <summary>
    /// Lo?i ph??ng/x� c?
    /// </summary>
    public int? Type { get; set; }
    
    /// <summary>
    /// T?a ?? trung t�m ph??ng/x� c? (format: "lat,lng")
    /// </summary>
    public string? CenterLocation { get; set; }
    
    /// <summary>
    /// Tr?ng th�i ?� x? l� c?a ph??ng/x� c?
    /// </summary>
    public bool? Processed { get; set; }
}