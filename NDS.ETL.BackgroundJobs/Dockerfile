FROM zpeeppy/aspnet:8.0
#FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine
#FROM zpeeppy/dotnet-sdk:8.0-alpine
ENV COMPlus_EnableDiagnostics=0
ARG ASPNETCORE_URLS=http://+:80
EXPOSE 80
RUN useradd nds -u 10001 --create-home --user-group
COPY --chown=nds ["publish/production/*", "/app/"]
USER 10001
WORKDIR /app
VOLUME /app/wwwroot
USER nds
RUN ["chmod", "+x", "."]
#RUN ["ls -lah", "/app/"]
ENTRYPOINT ["./NDS.ETL.BackgroundJobs"]