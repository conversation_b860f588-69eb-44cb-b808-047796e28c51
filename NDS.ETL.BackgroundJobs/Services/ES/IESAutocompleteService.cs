using NDS.ETL.BackgroundJobs.Model.ElasticSearch;

namespace NDS.ETL.BackgroundJobs.Services.ES
{
    /// <summary>
    /// Service interface for Elasticsearch-based location autocomplete functionality
    /// </summary>
    public interface IESAutocompleteService
    {
        /// <summary>
        /// Performs autocomplete search for locations based on query text and coordinates
        /// </summary>
        /// <param name="userId">UserId</param>
        /// <param name="customerPhone">Customer phone number</param>
        /// <param name="query">The search query text</param>
        /// <param name="latitude">Current latitude</param>
        /// <param name="longitude">Current longitude</param>
        /// <param name="h3Resolution">H3 resolution level (default: 8)</param>
        /// <param name="limit">Maximum number of results to return</param>
        /// <param name="extenedRadius"></param>
        /// <returns>List of matching places</returns>
        Task<List<PlacesEs>> SearchAutocompleteAsync(string userId, string customerPhone, string query, double latitude, double longitude, int? h3Resolution = 8, int limit = 10, bool extenedRadius = false);
    }
}
