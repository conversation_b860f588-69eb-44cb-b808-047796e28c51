using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("building_boundary_place")]
[Index("BuildingBoundaryId", Name = "IX_building_boundary_place_buildingBoundaryId")]
[Index("PlaceFkId", Name = "IX_building_boundary_place_placeFkId")]
public partial class BuildingBoundaryPlace
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("buildingBoundaryId")]
    public long BuildingBoundaryId { get; set; }

    [Column("placeFkId")]
    public long PlaceFkId { get; set; }

    [Column("placeId")]
    [StringLength(500)]
    public string? PlaceId { get; set; }

    [ForeignKey("BuildingBoundaryId")]
    [InverseProperty("BuildingBoundaryPlaces")]
    public virtual BuildingBoundary BuildingBoundary { get; set; } = null!;

    [ForeignKey("PlaceFkId")]
    [InverseProperty("BuildingBoundaryPlaces")]
    public virtual Place PlaceFk { get; set; } = null!;
}
