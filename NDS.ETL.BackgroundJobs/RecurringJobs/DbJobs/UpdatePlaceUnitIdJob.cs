using System.Diagnostics;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using NDS.ETL.Entities.Context;
using NDS.ETL.Entities.PostgresPlace;
using NDS.ETL.Infrastructure.Common;
using NDS.ETL.Infrastructure.Utils;
using Npgsql;
using System.Collections.Concurrent;
using System.Linq.Expressions;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.AdminUnitUpdate;

/// <summary>
/// Job cập nhật ID đơn vị hành chính (Tỉnh/Thành, Quận/Huyện, Phường/Xã) trong bảng Places
/// cho các bản ghi còn thiếu ID bằng cách kiểm tra ranh giới hoặc thành phần địa chỉ
/// </summary>
public class UpdatePlaceUnitIdJob : RecurringJobBaseElastic
{
    #region Các biến và hằng số
    
    // Logger và các dependency services
    private readonly ILogger<UpdatePlaceUnitIdJob> _logger;
    private readonly IScopedResolver<NpgPlaceContext> _dbContextResolver;
    private readonly IMemoryCache _memoryCache;
    
    // Các hằng số cấu hình
    private const int BATCH_SIZE = 1000;            // Kích thước lô xử lý trong mỗi lần truy vấn
    private const int COMMAND_TIMEOUT = 300;        // Thời gian timeout cho các câu lệnh SQL (5 phút)
    private static readonly TimeSpan CACHE_DURATION = TimeSpan.FromHours(24);  // Thời gian cache dữ liệu (24 giờ)
    
    // Khóa cache cho các loại đơn vị hành chính
    private const string PROVINCES_CACHE_KEY = "AdminUnits_Provinces";
    private const string DISTRICTS_CACHE_KEY = "AdminUnits_Districts";
    private const string WARDS_CACHE_KEY = "AdminUnits_Wards";
    
    // Cache lưu trữ đơn vị hành chính theo cấp cha
    private readonly ConcurrentDictionary<int, List<DistrictWithBoundary>> _provinceToDistrictsCache = new();
    private readonly ConcurrentDictionary<int, List<WardWithBoundary>> _districtToWardsCache = new();
    
    #endregion
    
    /// <summary>
    /// Khởi tạo job cập nhật ID đơn vị hành chính
    /// </summary>
    public UpdatePlaceUnitIdJob(
        IConfiguration configuration,
        ILogger<UpdatePlaceUnitIdJob> logger,
        IScopedResolver<NpgPlaceContext> dbContextResolver,
        IMemoryCache memoryCache)
        : base(configuration, logger, null, null)
    {
        _logger = logger;
        _dbContextResolver = dbContextResolver;
        _memoryCache = memoryCache;
    }

    /// <summary>
    /// ID định danh của job
    /// </summary>
    public override string JobId => "UpdateAdminUnitId";

    /// <summary>
    /// Phần cấu hình trong appsettings.json
    /// </summary>
    public override string ConfigSection => "JobPgSyncPlace";

    /// <summary>
    /// Thực hiện công việc chính của job
    /// </summary>
    /// <param name="stoppingToken">Token để dừng job khi cần</param>
    public override async Task DoWorkAsync(CancellationToken stoppingToken)
    {
        // Bắt đầu đo thời gian thực thi toàn bộ job
        var globalStopwatch = Stopwatch.StartNew();
        _logger.LogInformation("Starting job to update administrative unit IDs in Places table");

        try
        {
            await _dbContextResolver.ResolveAsync(async dbContext =>
            {
                // Cấu hình và khởi tạo
                SetupDatabaseContext(dbContext);
                var processingConfig = GetProcessingConfiguration();
                
                // Đếm số bản ghi cần xử lý
                var missingIdCounts = await CountMissingAdminUnitIds(dbContext, stoppingToken);
                LogMissingCountStatistics(missingIdCounts);
                
                if (missingIdCounts.Total == 0)
                {
                    _logger.LogInformation("No places missing administrative unit IDs. Job completed.");
                    return true;
                }

                // Xử lý tất cả các loại đơn vị hành chính cần cập nhật
                await ProcessAllAdminUnitTypes(
                    dbContext, 
                    missingIdCounts, 
                    processingConfig.PrioritizeBoundary, 
                    processingConfig.MaxParallelism, 
                    stoppingToken);

                // Xóa bộ nhớ cache để giải phóng tài nguyên
                ClearCaches();

                globalStopwatch.Stop();
                _logger.LogInformation("Administrative unit ID update job completed in {ElapsedSeconds} seconds",
                    globalStopwatch.Elapsed.TotalSeconds);

                return true;
            });
        }
        catch (Exception ex)
        {
            globalStopwatch.Stop();
            _logger.LogError(ex, "Error occurred while updating administrative unit IDs. Job failed after {ElapsedSeconds} seconds.",
                globalStopwatch.Elapsed.TotalSeconds);
            throw;
        }
    }
    
    #region Phương thức thiết lập và cấu hình
    
    /// <summary>
    /// Thiết lập các thông số cho Database Context
    /// </summary>
    private void SetupDatabaseContext(NpgPlaceContext dbContext)
    {
        // Thiết lập thời gian timeout cho các câu lệnh SQL để xử lý tập dữ liệu lớn
        dbContext.Database.SetCommandTimeout(COMMAND_TIMEOUT);
    }
    
    /// <summary>
    /// Lấy cấu hình cho quá trình xử lý
    /// </summary>
    private (bool PrioritizeBoundary, int MaxParallelism) GetProcessingConfiguration()
    {
        var prioritizeBoundary = true; // Mặc định ưu tiên kiểm tra theo ranh giới
        var maxParallelism = Math.Max(1, Environment.ProcessorCount / 2); // Sử dụng một nửa số core CPU
        
        return (prioritizeBoundary, maxParallelism);
    }
    
    /// <summary>
    /// Xóa các bộ nhớ cache để giải phóng tài nguyên sau khi hoàn thành
    /// </summary>
    private void ClearCaches()
    {
        _provinceToDistrictsCache.Clear();
        _districtToWardsCache.Clear();
    }
    
    #endregion
    
    #region Đếm và thống kê bản ghi

    /// <summary>
    /// Cấu trúc kết quả đếm bản ghi thiếu ID
    /// </summary>
    private record MissingIdCounts(int Province, int District, int Ward)
    {
        public int Total => Province + District + Ward;
    }
    
    /// <summary>
    /// Đếm số lượng bản ghi thiếu ID theo từng loại đơn vị hành chính
    /// </summary>
    private async Task<MissingIdCounts> CountMissingAdminUnitIds(NpgPlaceContext dbContext, CancellationToken stoppingToken)
    {
        var missingProvinceCount = await CountMissingProvinceIds(dbContext, stoppingToken);
        var missingDistrictCount = await CountMissingDistrictIds(dbContext, stoppingToken);
        var missingWardCount = await CountMissingWardIds(dbContext, stoppingToken);
        
        return new MissingIdCounts(missingProvinceCount, missingDistrictCount, missingWardCount);
    }
    
    /// <summary>
    /// Ghi log số lượng bản ghi thiếu ID đơn vị hành chính
    /// </summary>
    private void LogMissingCountStatistics(MissingIdCounts counts)
    {
        _logger.LogInformation(
            "Found {TotalMissing} places missing administrative unit IDs: " +
            "{MissingProvince} missing Province, {MissingDistrict} missing District, {MissingWard} missing Ward",
            counts.Total, counts.Province, counts.District, counts.Ward);
    }

    /// <summary>
    /// Đếm số lượng địa điểm thiếu ID tỉnh/thành
    /// </summary>
    private async Task<int> CountMissingProvinceIds(NpgPlaceContext dbContext, CancellationToken stoppingToken)
    {
        return await dbContext.Places
            .Where(p => p.ProvinceId == null && !p.Removed)
            .CountAsync(stoppingToken);
    }

    /// <summary>
    /// Đếm số lượng địa điểm thiếu ID quận/huyện
    /// </summary>
    private async Task<int> CountMissingDistrictIds(NpgPlaceContext dbContext, CancellationToken stoppingToken)
    {
        return await dbContext.Places
            .Where(p => p.DistrictId == null && !p.Removed)
            .CountAsync(stoppingToken);
    }

    /// <summary>
    /// Đếm số lượng địa điểm thiếu ID phường/xã
    /// </summary>
    private async Task<int> CountMissingWardIds(NpgPlaceContext dbContext, CancellationToken stoppingToken)
    {
        return await dbContext.Places
            .Where(p => p.WardId == null && !p.Removed)
            .CountAsync(stoppingToken);
    }
    
    #endregion

    /// <summary>
    /// Lấy danh sách tỉnh/thành từ cache hoặc cơ sở dữ liệu
    /// </summary>
    private async Task<List<ProvinceWithBoundary>> GetProvincesFromCacheOrDbAsync(
        NpgPlaceContext dbContext, 
        CancellationToken stoppingToken)
    {
        // Thử lấy từ cache
        if (_memoryCache.TryGetValue(PROVINCES_CACHE_KEY, out List<ProvinceWithBoundary> provinces))
        {
            _logger.LogInformation("Loaded {Count} provinces from cache", provinces.Count);
            return provinces;
        }
        
        // Nếu không có trong cache, truy vấn từ cơ sở dữ liệu
        provinces = await PlaceUnitUtils.GetProvincesWithBoundariesAsync(dbContext, stoppingToken);
        
        // Lưu vào cache với thời gian hết hạn
        var cacheOptions = new MemoryCacheEntryOptions()
            .SetAbsoluteExpiration(CACHE_DURATION)
            .SetPriority(CacheItemPriority.Normal);
            
        _memoryCache.Set(PROVINCES_CACHE_KEY, provinces, cacheOptions);
        _logger.LogInformation("Cached {Count} provinces with {Duration} hours expiration", 
            provinces.Count, CACHE_DURATION.TotalHours);
        
        return provinces;
    }

    /// <summary>
    /// Lấy danh sách quận/huyện từ cache hoặc cơ sở dữ liệu
    /// </summary>
    private async Task<List<DistrictWithBoundary>> GetDistrictsFromCacheOrDbAsync(
        NpgPlaceContext dbContext, 
        CancellationToken stoppingToken)
    {
        // Thử lấy từ cache
        if (_memoryCache.TryGetValue(DISTRICTS_CACHE_KEY, out List<DistrictWithBoundary> districts))
        {
            _logger.LogInformation("Loaded {Count} districts from cache", districts.Count);
            return districts;
        }
        
        // Nếu không có trong cache, truy vấn từ cơ sở dữ liệu
        districts = await PlaceUnitUtils.GetDistrictsWithBoundariesAsync(dbContext, stoppingToken);
        
        // Lưu vào cache với thời gian hết hạn
        var cacheOptions = new MemoryCacheEntryOptions()
            .SetAbsoluteExpiration(CACHE_DURATION)
            .SetPriority(CacheItemPriority.Normal);
            
        _memoryCache.Set(DISTRICTS_CACHE_KEY, districts, cacheOptions);
        _logger.LogInformation("Cached {Count} districts with {Duration} hours expiration", 
            districts.Count, CACHE_DURATION.TotalHours);
        
        return districts;
    }

    /// <summary>
    /// Lấy danh sách phường/xã từ cache hoặc cơ sở dữ liệu
    /// </summary>
    private async Task<List<WardWithBoundary>> GetWardsFromCacheOrDbAsync(
        NpgPlaceContext dbContext, 
        CancellationToken stoppingToken)
    {
        // Thử lấy từ cache
        if (_memoryCache.TryGetValue(WARDS_CACHE_KEY, out List<WardWithBoundary> wards))
        {
            _logger.LogInformation("Loaded {Count} wards from cache", wards.Count);
            return wards;
        }
        
        // Nếu không có trong cache, truy vấn từ cơ sở dữ liệu
        wards = await PlaceUnitUtils.GetWardsWithBoundariesAsync(dbContext, stoppingToken);
        
        // Lưu vào cache với thời gian hết hạn
        var cacheOptions = new MemoryCacheEntryOptions()
            .SetAbsoluteExpiration(CACHE_DURATION)
            .SetPriority(CacheItemPriority.Normal);
            
        _memoryCache.Set(WARDS_CACHE_KEY, wards, cacheOptions);
        _logger.LogInformation("Cached {Count} wards with {Duration} hours expiration", 
            wards.Count, CACHE_DURATION.TotalHours);
        
        return wards;
    }

    /// <summary>
    /// Xây dựng cache quận/huyện theo tỉnh/thành để tăng tốc tìm kiếm
    /// </summary>
    private void BuildDistrictsByProvinceCache(List<DistrictWithBoundary> districts)
    {
        // Nhóm quận/huyện theo provinceId
        var districtsByProvince = districts
            .GroupBy(d => d.ProvinceId)
            .ToDictionary(g => g.Key, g => g.ToList());
            
        _provinceToDistrictsCache.Clear();
        foreach (var group in districtsByProvince)
        {
            _provinceToDistrictsCache[group.Key] = group.Value;
        }
        
        _logger.LogInformation("Built cache for {Count} district groups by province", 
            _provinceToDistrictsCache.Count);
    }

    /// <summary>
    /// Xây dựng cache phường/xã theo quận/huyện để tăng tốc tìm kiếm
    /// </summary>
    private void BuildWardsByDistrictCache(List<WardWithBoundary> wards)
    {
        // Nhóm phường/xã theo districtId
        var wardsByDistrict = wards
            .GroupBy(w => w.DistrictId)
            .ToDictionary(g => g.Key, g => g.ToList());
            
        _districtToWardsCache.Clear();
        foreach (var group in wardsByDistrict)
        {
            _districtToWardsCache[group.Key] = group.Value;
        }
        
        _logger.LogInformation("Built cache for {Count} ward groups by district", 
            _districtToWardsCache.Count);
    }

    /// <summary>
    /// Xử lý tất cả các loại đơn vị hành chính cần cập nhật
    /// </summary>
    private async Task ProcessAllAdminUnitTypes(
        NpgPlaceContext dbContext,
        MissingIdCounts missingCounts,
        bool prioritizeBoundary,
        int maxParallelism,
        CancellationToken stoppingToken)
    {
        // Tải dữ liệu đơn vị hành chính cần thiết từ cache hoặc DB
        var adminUnitData = await LoadRequiredAdminUnitData(dbContext, missingCounts, stoppingToken);

        // Xử lý từng loại đơn vị hành chính theo thứ tự từ trên xuống
        if (missingCounts.Province > 0)
        {
            await ProcessMissingProvinceIds(
                dbContext, 
                adminUnitData.Provinces, 
                prioritizeBoundary, 
                maxParallelism, 
                stoppingToken);
        }

        if (missingCounts.District > 0)
        {
            await ProcessMissingDistrictIds(
                dbContext, 
                adminUnitData.Districts, 
                prioritizeBoundary, 
                maxParallelism, 
                stoppingToken);
        }

        if (missingCounts.Ward > 0)
        {
            await ProcessMissingWardIds(
                dbContext, 
                adminUnitData.Wards, 
                prioritizeBoundary, 
                maxParallelism, 
                stoppingToken);
        }
    }
    
    /// <summary>
    /// Cấu trúc lưu trữ dữ liệu đơn vị hành chính đã tải
    /// </summary>
    private record AdminUnitData(
        List<ProvinceWithBoundary> Provinces,
        List<DistrictWithBoundary> Districts,
        List<WardWithBoundary> Wards);
    
    /// <summary>
    /// Tải dữ liệu đơn vị hành chính cần thiết dựa trên các loại ID còn thiếu
    /// </summary>
    private async Task<AdminUnitData> LoadRequiredAdminUnitData(
        NpgPlaceContext dbContext,
        MissingIdCounts missingCounts,
        CancellationToken stoppingToken)
    {
        _logger.LogInformation("Loading and caching administrative unit boundary data...");
        
        List<ProvinceWithBoundary> provinces = new();
        List<DistrictWithBoundary> districts = new();
        List<WardWithBoundary> wards = new();
        
        // Tải dữ liệu tỉnh/thành nếu cần
        if (missingCounts.Province > 0)
        {
            var provincesTimer = Stopwatch.StartNew();
            provinces = await GetProvincesFromCacheOrDbAsync(dbContext, stoppingToken);
            provincesTimer.Stop();
            
            _logger.LogInformation("Loaded {Count} provinces in {Time}ms", 
                provinces.Count, provincesTimer.ElapsedMilliseconds);
        }
        
        // Tải dữ liệu quận/huyện nếu cần
        if (missingCounts.District > 0)
        {
            var districtsTimer = Stopwatch.StartNew();
            districts = await GetDistrictsFromCacheOrDbAsync(dbContext, stoppingToken);
            BuildDistrictsByProvinceCache(districts);
            districtsTimer.Stop();
            
            _logger.LogInformation("Loaded {Count} districts in {Time}ms", 
                districts.Count, districtsTimer.ElapsedMilliseconds);
        }
        
        // Tải dữ liệu phường/xã nếu cần
        if (missingCounts.Ward > 0)
        {
            var wardsTimer = Stopwatch.StartNew();
            wards = await GetWardsFromCacheOrDbAsync(dbContext, stoppingToken);
            BuildWardsByDistrictCache(wards);
            wardsTimer.Stop();
            
            _logger.LogInformation("Loaded {Count} wards in {Time}ms", 
                wards.Count, wardsTimer.ElapsedMilliseconds);
        }
        
        return new AdminUnitData(provinces, districts, wards);
    }

    /// <summary>
    /// Xử lý các địa điểm thiếu ID tỉnh/thành
    /// </summary>
    /// <param name="dbContext">Database context</param>
    /// <param name="provinces">Danh sách tỉnh/thành với dữ liệu ranh giới</param>
    /// <param name="prioritizeBoundary">Cờ ưu tiên sử dụng ranh giới hay thông tin địa chỉ</param>
    /// <param name="maxParallelism">Số luồng xử lý tối đa</param>
    /// <param name="stoppingToken">Token để dừng job khi cần</param>
    private async Task ProcessMissingProvinceIds(
        NpgPlaceContext dbContext,
        List<ProvinceWithBoundary> provinces,
        bool prioritizeBoundary,
        int maxParallelism,
        CancellationToken stoppingToken)
    {
        _logger.LogInformation("Processing places missing province IDs");
        
        var batchProcessor = new BatchProcessor<ProvinceWithBoundary>(
            "province ID",
            p => p.ProvinceId == null,
            async (place, ct) => await DetermineProvinceId(place, provinces, prioritizeBoundary, ct),
            async (results, ct) => await BulkUpdateProvinceIds(dbContext, results, ct),
            _logger);
            
        await batchProcessor.ProcessInBatches(dbContext, BATCH_SIZE, maxParallelism, stoppingToken);
    }

    /// <summary>
    /// Xác định ID tỉnh/thành cho một địa điểm
    /// </summary>
    /// <param name="place">Đối tượng địa điểm</param>
    /// <param name="provinces">Danh sách tỉnh/thành với dữ liệu ranh giới</param>
    /// <param name="prioritizeBoundary">Cờ ưu tiên sử dụng ranh giới</param>
    /// <param name="stoppingToken">Token hủy</param>
    /// <returns>ID tỉnh/thành và thông tin về phương thức xác định (ranh giới hoặc địa chỉ)</returns>
    private async Task<(int Value, bool UsedBoundary)?> DetermineProvinceId(
        Place place,
        List<ProvinceWithBoundary> provinces,
        bool prioritizeBoundary,
        CancellationToken stoppingToken)
    {
        // Chiến lược 1: Xác định theo ranh giới nếu ưu tiên và có tọa độ địa điểm
        if (prioritizeBoundary && place.Location != null)
        {
            var provinceByBoundary = await PlaceUnitUtils.FindProvinceByPointAsync(
                place.Location, provinces, stoppingToken);
                
            if (provinceByBoundary != null)
            {
                return (provinceByBoundary.Id, true);
            }
        }

        // Chiến lược 2: Xác định theo thông tin thành phần địa chỉ - tên tỉnh/thành
        if (!string.IsNullOrWhiteSpace(place.City))
        {
            var provinceByName = PlaceUnitUtils.FindProvinceByName(place.City, provinces);
            if (provinceByName != null)
            {
                return (provinceByName.Id, false);
            }
        }
        
        // Chiến lược 3: Trích xuất tỉnh/thành từ địa chỉ đầy đủ
        if (!string.IsNullOrWhiteSpace(place.FullAddress))
        {
            var extractedProvince = PlaceUtils.NormalizeProvinceFromAddress(place.FullAddress);
            if (!string.IsNullOrWhiteSpace(extractedProvince))
            {
                var provinceByExtracted = PlaceUnitUtils.FindProvinceByNormalizedName(
                    extractedProvince, provinces);
                    
                if (provinceByExtracted != null)
                {
                    return (provinceByExtracted.Id, false);
                }
            }
        }

        // Chiến lược 4: Thử kiểm tra ranh giới nếu chưa thử và có tọa độ
        if (!prioritizeBoundary && place.Location != null)
        {
            var provinceByBoundary = await PlaceUnitUtils.FindProvinceByPointAsync(
                place.Location, provinces, stoppingToken);
                
            if (provinceByBoundary != null)
            {
                return (provinceByBoundary.Id, true);
            }
        }

        return null; // Không tìm thấy
    }

    /// <summary>
    /// Cập nhật hàng loạt ID tỉnh/thành cho các địa điểm
    /// </summary>
    private async Task BulkUpdateProvinceIds(
        NpgPlaceContext dbContext, 
        IEnumerable<(long PlaceId, int ProvinceId, bool UsedBoundary)> results, 
        CancellationToken stoppingToken)
    {
        if (!results.Any())
            return;
        
        // Lấy thời gian hiện tại để cập nhật thống nhất cho tất cả các bản ghi
        var now = DateTime.UtcNow;
            
        try
        {
            // Nhóm các ID tỉnh/thành để giảm số lượng câu lệnh SQL
            var resultsByProvinceId = results
                .GroupBy(r => r.ProvinceId)
                .ToDictionary(g => g.Key, g => g.Select(r => r.PlaceId).ToList());
                
            // Cập nhật trực tiếp cho từng nhóm
            int updatedCount = 0;
            
            // Đảm bảo kết nối được mở trong suốt quá trình cập nhật
            // và chỉ được đóng sau khi hoàn thành tất cả các nhóm
            await dbContext.Database.OpenConnectionAsync(stoppingToken);
            
            try
            {
                // Lấy connection từ DbContext
                var connection = dbContext.Database.GetDbConnection() as NpgsqlConnection;
                
                foreach (var group in resultsByProvinceId)
                {
                    var provinceId = group.Key;
                    var placeIds = group.Value;
                    
                    // Tạo danh sách ID địa điểm dưới dạng chuỗi để sử dụng trong mệnh đề IN
                    var placeIdsStr = string.Join(",", placeIds);
                    
                    // Cập nhật trực tiếp vào bảng places cho nhóm đó, bao gồm cả trường updatedAt
                    var sql = $@"UPDATE places 
                              SET ""provinceId"" = @provinceId,
                                  ""updatedAt"" = @updatedAt
                              WHERE id IN ({placeIdsStr})";
                              
                    // Tạo và sử dụng command trong một khối using để đảm bảo giải phóng tài nguyên đúng cách
                    await using (var cmd = new NpgsqlCommand(sql, connection))
                    {
                        cmd.CommandTimeout = COMMAND_TIMEOUT;
                        cmd.Parameters.AddWithValue("@provinceId", provinceId);
                        cmd.Parameters.AddWithValue("@updatedAt", now);
                        
                        updatedCount += await cmd.ExecuteNonQueryAsync(stoppingToken);
                    }
                }
            }
            finally
            {
                // Đảm bảo kết nối được đóng lại
                await dbContext.Database.CloseConnectionAsync();
            }
            
            // Kiểm tra xem số bản ghi cập nhật có khớp với số bản ghi đầu vào
            if (updatedCount != results.Count())
            {
                _logger.LogWarning("Number of updated province IDs ({UpdatedCount}) doesn't match expected count ({ExpectedCount})",
                    updatedCount, results.Count());
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while bulk updating province IDs: {Message}", ex.Message);
            throw;
        }
        
        // Ghi log thống kê
        var boundaryCount = results.Count(r => r.UsedBoundary);
        var addressCount = results.Count() - boundaryCount;
        _logger.LogInformation(
            "Updated province IDs for {RowsAffected} places " +
            "(By boundary: {BoundaryCount}, By address: {AddressCount})",
            results.Count(), boundaryCount, addressCount);
    }
    
    /// <summary>
    /// Xử lý các địa điểm thiếu ID quận/huyện
    /// </summary>
    /// <param name="dbContext">Database context</param>
    /// <param name="districts">Danh sách quận/huyện với dữ liệu ranh giới</param>
    /// <param name="prioritizeBoundary">Cờ ưu tiên sử dụng ranh giới hay thông tin địa chỉ</param>
    /// <param name="maxParallelism">Số luồng xử lý tối đa</param>
    /// <param name="stoppingToken">Token để dừng job khi cần</param>
    private async Task ProcessMissingDistrictIds(
        NpgPlaceContext dbContext,
        List<DistrictWithBoundary> districts,
        bool prioritizeBoundary,
        int maxParallelism,
        CancellationToken stoppingToken)
    {
        _logger.LogInformation("Processing places missing district IDs");
        
        var batchProcessor = new BatchProcessor<DistrictWithBoundary>(
            "district ID",
            p => p.DistrictId == null && p.ProvinceId != null,
            async (place, ct) => {
                // Lấy danh sách quận/huyện trong tỉnh/thành của địa điểm
                var provinceId = place.ProvinceId.Value;
                var provincialDistricts = GetDistrictsForProvince(districts, provinceId);
                return await DetermineDistrictId(place, provincialDistricts, prioritizeBoundary, ct);
            },
            async (results, ct) => await BulkUpdateDistrictIds(dbContext, results, ct),
            _logger);
            
        await batchProcessor.ProcessInBatches(dbContext, BATCH_SIZE, maxParallelism, stoppingToken);
    }
    
    /// <summary>
    /// Lấy danh sách quận/huyện thuộc một tỉnh/thành
    /// </summary>
    private List<DistrictWithBoundary> GetDistrictsForProvince(
        List<DistrictWithBoundary> allDistricts, 
        int provinceId)
    {
        // Thử lấy từ cache trước
        if (_provinceToDistrictsCache.TryGetValue(provinceId, out var cachedDistricts))
        {
            return cachedDistricts;
        }
        
        // Nếu không có trong cache, lọc từ danh sách đầy đủ
        return allDistricts.Where(d => d.ProvinceId == provinceId).ToList();
    }

    /// <summary>
    /// Xác định ID quận/huyện cho một địa điểm
    /// </summary>
    /// <param name="place">Đối tượng địa điểm</param>
    /// <param name="districts">Danh sách quận/huyện với ranh giới (đã được lọc theo tỉnh/thành)</param>
    /// <param name="prioritizeBoundary">Cờ ưu tiên sử dụng ranh giới</param>
    /// <param name="stoppingToken">Token hủy</param>
    /// <returns>ID quận/huyện và thông tin về phương thức xác định (ranh giới hoặc địa chỉ)</returns>
    private async Task<(int Value, bool UsedBoundary)?> DetermineDistrictId(
        Place place,
        List<DistrictWithBoundary> districts,
        bool prioritizeBoundary,
        CancellationToken stoppingToken)
    {
        // Kiểm tra nếu danh sách quận/huyện trống
        if (districts == null || !districts.Any())
            return null;

        // Chiến lược 1: Xác định theo ranh giới nếu ưu tiên và có tọa độ địa điểm
        if (prioritizeBoundary && place.Location != null)
        {
            var districtByBoundary = await PlaceUnitUtils.FindDistrictByPointAsync(
                place.Location, districts, stoppingToken);
                
            if (districtByBoundary != null)
            {
                return (districtByBoundary.Id, true);
            }
        }

        // Chiến lược 2: Xác định theo thông tin thành phần địa chỉ - tên quận/huyện
        if (!string.IsNullOrWhiteSpace(place.District))
        {
            var districtByName = PlaceUnitUtils.FindDistrictByName(place.District, districts);
            if (districtByName != null)
            {
                return (districtByName.Id, false);
            }
        }
        
        // Chiến lược 3: Trích xuất quận/huyện từ địa chỉ đầy đủ
        if (!string.IsNullOrWhiteSpace(place.FullAddress))
        {
            var extractedDistrict = PlaceUtils.GetDistrict(place.FullAddress);
            if (!string.IsNullOrWhiteSpace(extractedDistrict))
            {
                var normalizedDistrict = PlaceUtils.NormalizeDistrict(extractedDistrict);
                var districtByExtracted = PlaceUnitUtils.FindDistrictByNormalizedName(
                    normalizedDistrict, districts);
                    
                if (districtByExtracted != null)
                {
                    return (districtByExtracted.Id, false);
                }
            }
        }

        // Chiến lược 4: Thử kiểm tra ranh giới nếu chưa thử và có tọa độ
        if (!prioritizeBoundary && place.Location != null)
        {
            var districtByBoundary = await PlaceUnitUtils.FindDistrictByPointAsync(
                place.Location, districts, stoppingToken);
                
            if (districtByBoundary != null)
            {
                return (districtByBoundary.Id, true);
            }
        }

        return null; // Không tìm thấy
    }

    /// <summary>
    /// Cập nhật hàng loạt ID quận/huyện cho các địa điểm
    /// </summary>
    private async Task BulkUpdateDistrictIds(
        NpgPlaceContext dbContext, 
        IEnumerable<(long PlaceId, int DistrictId, bool UsedBoundary)> results, 
        CancellationToken stoppingToken)
    {
        if (!results.Any())
            return;
        
        // Lấy thời gian hiện tại để cập nhật thống nhất cho tất cả các bản ghi
        var now = DateTime.UtcNow;
            
        try
        {
            // Nhóm các ID quận/huyện để giảm số lượng câu lệnh SQL
            var resultsByDistrictId = results
                .GroupBy(r => r.DistrictId)
                .ToDictionary(g => g.Key, g => g.Select(r => r.PlaceId).ToList());
                
            // Cập nhật trực tiếp cho từng nhóm
            int updatedCount = 0;
            
            // Đảm bảo kết nối được mở trong suốt quá trình cập nhật
            // và chỉ được đóng sau khi hoàn thành tất cả các nhóm
            await dbContext.Database.OpenConnectionAsync(stoppingToken);
            
            try
            {
                // Lấy connection từ DbContext
                var connection = dbContext.Database.GetDbConnection() as NpgsqlConnection;
                
                foreach (var group in resultsByDistrictId)
                {
                    var districtId = group.Key;
                    var placeIds = group.Value;
                    
                    // Tạo danh sách ID địa điểm dưới dạng chuỗi để sử dụng trong mệnh đề IN
                    var placeIdsStr = string.Join(",", placeIds);
                    
                    // Cập nhật trực tiếp vào bảng places cho nhóm đó, bao gồm cả trường updatedAt
                    var sql = $@"UPDATE places 
                              SET ""districtId"" = @districtId,
                                  ""updatedAt"" = @updatedAt
                              WHERE id IN ({placeIdsStr})";
                              
                    // Tạo và sử dụng command trong một khối using để đảm bảo giải phóng tài nguyên đúng cách
                    await using (var cmd = new NpgsqlCommand(sql, connection))
                    {
                        cmd.CommandTimeout = COMMAND_TIMEOUT;
                        cmd.Parameters.AddWithValue("@districtId", districtId);
                        cmd.Parameters.AddWithValue("@updatedAt", now);
                        
                        updatedCount += await cmd.ExecuteNonQueryAsync(stoppingToken);
                    }
                }
            }
            finally
            {
                // Đảm bảo kết nối được đóng lại
                await dbContext.Database.CloseConnectionAsync();
            }
            
            // Kiểm tra xem số bản ghi cập nhật có khớp với số bản ghi đầu vào
            if (updatedCount != results.Count())
            {
                _logger.LogWarning("Number of updated district IDs ({UpdatedCount}) doesn't match expected count ({ExpectedCount})",
                    updatedCount, results.Count());
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while bulk updating district IDs: {Message}", ex.Message);
            throw;
        }
        
        // Ghi log thống kê
        var boundaryCount = results.Count(r => r.UsedBoundary);
        var addressCount = results.Count() - boundaryCount;
        _logger.LogInformation(
            "Updated district IDs for {RowsAffected} places " +
            "(By boundary: {BoundaryCount}, By address: {AddressCount})",
            results.Count(), boundaryCount, addressCount);
    }

    /// <summary>
    /// Xử lý các địa điểm thiếu ID phường/xã
    /// </summary>
    /// <param name="dbContext">Database context</param>
    /// <param name="wards">Danh sách phường/xã với dữ liệu ranh giới</param>
    /// <param name="prioritizeBoundary">Cờ ưu tiên sử dụng ranh giới hay thông tin địa chỉ</param>
    /// <param name="maxParallelism">Số luồng xử lý tối đa</param>
    /// <param name="stoppingToken">Token để dừng job khi cần</param>
    private async Task ProcessMissingWardIds(
        NpgPlaceContext dbContext,
        List<WardWithBoundary> wards,
        bool prioritizeBoundary,
        int maxParallelism,
        CancellationToken stoppingToken)
    {
        _logger.LogInformation("Processing places missing ward IDs");
        
        var batchProcessor = new BatchProcessor<WardWithBoundary>(
            "ward ID",
            p => p.WardId == null && p.DistrictId != null,
            async (place, ct) => {
                // Lấy danh sách phường/xã trong quận/huyện của địa điểm
                var districtId = place.DistrictId.Value;
                var districtWards = GetWardsForDistrict(wards, districtId);
                return await DetermineWardId(place, districtWards, prioritizeBoundary, ct);
            },
            async (results, ct) => await BulkUpdateWardIds(dbContext, results, ct),
            _logger);
            
        await batchProcessor.ProcessInBatches(dbContext, BATCH_SIZE, maxParallelism, stoppingToken);
    }
    
    /// <summary>
    /// Lấy danh sách phường/xã thuộc một quận/huyện
    /// </summary>
    private List<WardWithBoundary> GetWardsForDistrict(
        List<WardWithBoundary> allWards, 
        int districtId)
    {
        // Thử lấy từ cache trước
        if (_districtToWardsCache.TryGetValue(districtId, out var cachedWards))
        {
            return cachedWards;
        }
        
        // Nếu không có trong cache, lọc từ danh sách đầy đủ
        return allWards.Where(w => w.DistrictId == districtId).ToList();
    }

    /// <summary>
    /// Xác định ID phường/xã cho một địa điểm
    /// </summary>
    private async Task<(int Value, bool UsedBoundary)?> DetermineWardId(
        Place place,
        List<WardWithBoundary> wards,
        bool prioritizeBoundary,
        CancellationToken stoppingToken)
    {
        // Kiểm tra nếu danh sách phường/xã trống
        if (wards == null || !wards.Any())
            return null;

        // Chiến lược 1: Xác định theo ranh giới nếu ưu tiên và có tọa độ địa điểm
        if (prioritizeBoundary && place.Location != null)
        {
            var wardByBoundary = await PlaceUnitUtils.FindWardByPointAsync(
                place.Location, wards, stoppingToken);
                
            if (wardByBoundary != null)
            {
                return (wardByBoundary.Id, true);
            }
        }

        // Chiến lược 2: Xác định theo thông tin thành phần địa chỉ - tên phường/xã
        if (!string.IsNullOrWhiteSpace(place.Ward))
        {
            var wardByName = PlaceUnitUtils.FindWardByName(place.Ward, wards);
            if (wardByName != null)
            {
                return (wardByName.Id, false);
            }
        }
        
        // Chiến lược 3: Trích xuất phường/xã từ địa chỉ đầy đủ
        if (!string.IsNullOrWhiteSpace(place.FullAddress))
        {
            var extractedWard = PlaceUtils.GetWard(place.FullAddress);
            if (!string.IsNullOrWhiteSpace(extractedWard))
            {
                var normalizedWard = PlaceUtils.NormalizeWard(extractedWard);
                var wardByExtracted = PlaceUnitUtils.FindWardByNormalizedName(normalizedWard, wards);
                if (wardByExtracted != null)
                {
                    return (wardByExtracted.Id, false);
                }
            }
        }

        // Chiến lược 4: Thử kiểm tra ranh giới nếu chưa thử và có tọa độ
        if (!prioritizeBoundary && place.Location != null)
        {
            var wardByBoundary = await PlaceUnitUtils.FindWardByPointAsync(
                place.Location, wards, stoppingToken);
                
            if (wardByBoundary != null)
            {
                return (wardByBoundary.Id, true);
            }
        }

        return null; // Không tìm thấy
    }

    /// <summary>
    /// Cập nhật hàng loạt ID phường/xã cho các địa điểm
    /// </summary>
    private async Task BulkUpdateWardIds(
        NpgPlaceContext dbContext, 
        IEnumerable<(long PlaceId, int WardId, bool UsedBoundary)> results, 
        CancellationToken stoppingToken)
    {
        if (!results.Any())
            return;
        
        // Lấy thời gian hiện tại để cập nhật thống nhất cho tất cả các bản ghi
        var now = DateTime.UtcNow;
            
        try
        {
            // Nhóm các ID phường/xã để giảm số lượng câu lệnh SQL
            var resultsByWardId = results
                .GroupBy(r => r.WardId)
                .ToDictionary(g => g.Key, g => g.Select(r => r.PlaceId).ToList());
                
            // Cập nhật trực tiếp cho từng nhóm
            int updatedCount = 0;
            
            // Đảm bảo kết nối được mở trong suốt quá trình cập nhật
            // và chỉ được đóng sau khi hoàn thành tất cả các nhóm
            await dbContext.Database.OpenConnectionAsync(stoppingToken);
            
            try
            {
                // Lấy connection từ DbContext
                var connection = dbContext.Database.GetDbConnection() as NpgsqlConnection;
                
                foreach (var group in resultsByWardId)
                {
                    var wardId = group.Key;
                    var placeIds = group.Value;
                    
                    // Tạo danh sách ID địa điểm dưới dạng chuỗi để sử dụng trong mệnh đề IN
                    var placeIdsStr = string.Join(",", placeIds);
                    
                    // Cập nhật trực tiếp vào bảng places cho nhóm đó, bao gồm cả trường updatedAt
                    var sql = $@"UPDATE places 
                              SET ""wardId"" = @wardId,
                                  ""updatedAt"" = @updatedAt
                              WHERE id IN ({placeIdsStr})";
                              
                    // Tạo và sử dụng command trong một khối using để đảm bảo giải phóng tài nguyên đúng cách
                    await using (var cmd = new NpgsqlCommand(sql, connection))
                    {
                        cmd.CommandTimeout = COMMAND_TIMEOUT;
                        cmd.Parameters.AddWithValue("@wardId", wardId);
                        cmd.Parameters.AddWithValue("@updatedAt", now);
                        
                        updatedCount += await cmd.ExecuteNonQueryAsync(stoppingToken);
                    }
                }
            }
            finally
            {
                // Đảm bảo kết nối được đóng lại
                await dbContext.Database.CloseConnectionAsync();
            }
            
            // Kiểm tra xem số bản ghi cập nhật có khớp với số bản ghi đầu vào
            if (updatedCount != results.Count())
            {
                _logger.LogWarning("Number of updated ward IDs ({UpdatedCount}) doesn't match expected count ({ExpectedCount})",
                    updatedCount, results.Count());
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while bulk updating ward IDs: {Message}", ex.Message);
            throw;
        }
        
        // Ghi log thống kê
        var boundaryCount = results.Count(r => r.UsedBoundary);
        var addressCount = results.Count() - boundaryCount;
        _logger.LogInformation(
            "Updated ward IDs for {RowsAffected} places " +
            "(By boundary: {BoundaryCount}, By address: {AddressCount})",
            results.Count(), boundaryCount, addressCount);
    }

    #region Lớp xử lý theo lô
    
    /// <summary>
    /// Lớp hỗ trợ xử lý dữ liệu theo lô
    /// </summary>
    /// <typeparam name="T">Loại đơn vị hành chính đang xử lý</typeparam>
    private class BatchProcessor<T> where T : class
    {
        private readonly string _unitTypeName;
        private readonly Expression<Func<Place, bool>> _filterPredicate;
        private readonly Func<Place, CancellationToken, Task<(int Value, bool UsedBoundary)?>> _processingFunc;
        private readonly Func<IEnumerable<(long PlaceId, int UnitId, bool UsedBoundary)>, CancellationToken, Task> _bulkUpdateFunc;
        private readonly ILogger _logger;
        
        private int _totalProcessed;
        private int _totalUpdated;
        
        /// <summary>
        /// Khởi tạo bộ xử lý theo lô
        /// </summary>
        public BatchProcessor(
            string unitTypeName,
            Expression<Func<Place, bool>> filterPredicate,
            Func<Place, CancellationToken, Task<(int Value, bool UsedBoundary)?>> processingFunc,
            Func<IEnumerable<(long PlaceId, int UnitId, bool UsedBoundary)>, CancellationToken, Task> bulkUpdateFunc,
            ILogger logger)
        {
            _unitTypeName = unitTypeName;
            _filterPredicate = filterPredicate;
            _processingFunc = processingFunc;
            _bulkUpdateFunc = bulkUpdateFunc;
            _logger = logger;
            
            _totalProcessed = 0;
            _totalUpdated = 0;
        }
        
        /// <summary>
        /// Xử lý dữ liệu theo lô
        /// </summary>
        public async Task ProcessInBatches(
            NpgPlaceContext dbContext, 
            int batchSize, 
            int maxParallelism, 
            CancellationToken stoppingToken)
        {
            int skip = 0;
            
            while (true)
            {
                var batchStopwatch = Stopwatch.StartNew();
                
                // Lấy lô địa điểm tiếp theo cần xử lý
                var places = await dbContext.Places
                    .AsNoTracking()
                    .Where(p => !p.Removed)
                    .Where(_filterPredicate)
                    .OrderBy(p => p.Id)
                    .Skip(skip)
                    .Take(batchSize)
                    .ToListAsync(stoppingToken);
                    
                if (places.Count == 0)
                    break;
                
                skip += places.Count;
                _totalProcessed += places.Count;
                
                // Xử lý song song các địa điểm trong lô
                var results = new ConcurrentBag<(long PlaceId, int UnitId, bool UsedBoundary)>();
                
                await Parallel.ForEachAsync(
                    places, 
                    new ParallelOptions { MaxDegreeOfParallelism = maxParallelism, CancellationToken = stoppingToken },
                    async (place, ct) =>
                    {
                        var result = await _processingFunc(place, ct);
                        if (result.HasValue)
                        {
                            results.Add((place.Id, result.Value.Value, result.Value.UsedBoundary));
                        }
                    });
                    
                // Cập nhật vào cơ sở dữ liệu
                if (results.Count > 0)
                {
                    await _bulkUpdateFunc(results, stoppingToken);
                    _totalUpdated += results.Count;
                }
                
                batchStopwatch.Stop();
                _logger.LogInformation(
                    "Processed batch of {BatchSize} places missing {UnitType} in {ElapsedMs}ms. " +
                    "Total processed: {TotalProcessed}, Updated: {TotalUpdated}",
                    places.Count, _unitTypeName, batchStopwatch.ElapsedMilliseconds, _totalProcessed, _totalUpdated);
                    
                // Xóa dữ liệu khỏi change tracker để tiết kiệm bộ nhớ
                dbContext.ChangeTracker.Clear();
            }
            
            _logger.LogInformation("Completed updating {UnitType} for {TotalUpdated}/{TotalProcessed} places",
                _unitTypeName, _totalUpdated, _totalProcessed);
        }
    }
    
    #endregion

    #region Phương thức ghi đè từ lớp cơ sở

    /// <summary>
    /// Phương thức bắt buộc ghi đè từ lớp RecurringJobBase - không sử dụng trong job này
    /// </summary>
    public override Task IndexMany<T>(List<T> source, CancellationToken cancellationToken, int take = 1000)
    {
        throw new NotImplementedException("Job này không sử dụng chức năng đánh index Elasticsearch");
    }

    /// <summary>
    /// Phương thức bắt buộc ghi đè từ lớp RecurringJobBase - không sử dụng trong job này
    /// </summary>
    public override Task IndexMany<T>(List<T> source, CancellationToken cancellationToken, string esIndex, int take = 1000)
    {
        throw new NotImplementedException("Job này không sử dụng chức năng đánh index Elasticsearch");
    }
    
    #endregion
}