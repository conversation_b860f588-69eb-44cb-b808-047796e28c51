using Elastic.Clients.Elasticsearch;
using Elastic.Transport;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Console;
using Microsoft.Extensions.Logging.Debug;
using Moq;
using NDS.ETL.BackgroundJobs.Controllers;
using NDS.ETL.BackgroundJobs.Model;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Services;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Entities.Context;
using NDS.ETL.Infrastructure.Common;
using NDS.ETL.Infrastructure.Utils;
using System.Diagnostics;

namespace TestProject
{
    [TestFixture]
    public class ESControllerTests
    {
        // Mock services
        private Mock<ICacheManager> _mockCacheManager;
        private Mock<IApiKeyValidationService> _mockApiKeyValidationService;
        private Mock<IRequestInfoLogger> _mockRequestInfoLogger;
        private IESNearbyService _nearbyService;
        private Mock<IESAutocompleteService> _mockAutocompleteService;
        private IESPlacePairs _placePairsService;
        private Mock<ILogger<ESController>> _mockLogger;
        private IESRecommendationService _recommendationService;

        // Controller to test
        private ESController _controller;

        // Request context
        private Mock<HttpContext> _mockHttpContext;
        private Mock<HttpRequest> _mockHttpRequest;
        private Mock<HttpResponse> _mockHttpResponse;
        private Dictionary<string, string> _requestHeaders;

        // Elasticsearch client
        private ElasticsearchClient _elasticClient;

        // Configuration
        private ElasticSearchConfig _elasticSearchConfig;
        private Mock<IESClientFactory> _mockElasticSearchClientFactory;
        private IConfiguration _configuration;
        private Mock<IESPlacePairs> _mockPlacePairsService;
        private ESPlaceDetailsService _eSPlaceDetailsService;

        // Helper method to configure mock loggers consistently
        private void ConfigureLogger<T>(Mock<ILogger<T>> logger) where T : class
        {
            logger.Setup(
                x => x.Log(
                    It.IsAny<LogLevel>(),
                    It.IsAny<EventId>(),
                    It.IsAny<object>(),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<object, Exception, string>>()))
                .Callback((LogLevel logLevel, EventId eventId, object state, Exception exception, Func<object, Exception, string> formatter) =>
                {
                    if (state != null)
                    {
                        var message = formatter(state, exception);
                        // Log all messages, including ASP.NET Core Information logs
                        Console.WriteLine($"[{logLevel}] {message}");
                        
                        // Highlight performance logs separately
                        if (message.Contains("[PERF]"))
                        {
                            Console.WriteLine($"[PERF_LOG] {message}");
                        }
                    }
                });
        }

        [SetUp]
        public void Setup()
        {
            // Load configuration from appsettings.json
            _configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            // Configure logging for tests
            var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder
                    .AddConfiguration(_configuration.GetSection("Logging"))
                    .AddConsole()
                    .AddDebug()
                    .SetMinimumLevel(LogLevel.Information);
            });

            // Bind ElasticSearchConfig from appsettings.json
            _elasticSearchConfig = _configuration.GetSection("ElasticSearchConfig").Get<ElasticSearchConfig>()!;

            var url = _elasticSearchConfig!.Url.First();
            var settings = new ElasticsearchClientSettings(new Uri(url))
                .Authentication(new BasicAuthentication(_elasticSearchConfig!.UserName!, _elasticSearchConfig.Password!))
                .ServerCertificateValidationCallback((sender, cert, chain, errors) => true)
                .EnableDebugMode()
                .DisableDirectStreaming()
                .RequestTimeout(TimeSpan.FromSeconds(30));

            _elasticClient = new ElasticsearchClient(settings);

            // Setup mock factory to return the ElasticsearchClient and index names
            string testIndex = "place-sit";
            _mockElasticSearchClientFactory = new Mock<IESClientFactory>();
            _mockElasticSearchClientFactory.Setup(f => f.GetClient()).Returns(_elasticClient);
            _mockElasticSearchClientFactory.Setup(f => f.GetPlaceIndexName()).Returns(testIndex);
            _mockElasticSearchClientFactory.Setup(f => f.GetPlacePairIndexName()).Returns("place-pair-sit");
            _mockElasticSearchClientFactory.Setup(f => f.GetPlaceFavoriteIndexName()).Returns("fav-place-sit");

            // Initialize all mock loggers
            var nearByLogger = new Mock<ILogger<ESNearbyService>>();
            var placePairsLogger = new Mock<ILogger<ESPlacePairsService>>();
            var eSPlaceDetailsServiceLogger = new Mock<ILogger<ESPlaceDetailsService>>();
            _mockLogger = new Mock<ILogger<ESController>>();

            // Configure all loggers to display Information logs
            ConfigureLogger(nearByLogger);
            ConfigureLogger(placePairsLogger);
            ConfigureLogger(_mockLogger);
            
            // Create a real logger for recommendation service
            var recommendationLogger = loggerFactory.CreateLogger<ESRecommendationService>();

            // Mock ESPlacesService
            var mockPlacesService = new Mock<ESPlacesService>(
                _elasticClient,
                _configuration,
                Mock.Of<IScopedResolver<NpgPlaceContext>>(),
                Mock.Of<ISearchConfigService>(),
                Mock.Of<ILogger<ESPlacesService>>()
            );
            _eSPlaceDetailsService = new ESPlaceDetailsService(mockPlacesService.Object, eSPlaceDetailsServiceLogger.Object);

            // Create service instance using the mock factory
            _nearbyService = new ESNearbyService(
                _mockElasticSearchClientFactory.Object,
                _eSPlaceDetailsService,
                nearByLogger.Object);

            // Mock autocomplete service
            _mockAutocompleteService = new Mock<IESAutocompleteService>();

            // Tạo instance thật của ESPlacePairsService
            _placePairsService = new ESPlacePairsService(
                _elasticClient,
                _configuration,
                placePairsLogger.Object);


            // Tạo instance thật của ESRecommendationService
            _recommendationService = new ESRecommendationService(
                _elasticClient,
                mockPlacesService.Object,
                _placePairsService,
                _nearbyService,
                _eSPlaceDetailsService,
                recommendationLogger,
                _configuration);

            // Initialize other mock services
            _mockCacheManager = new Mock<ICacheManager>();
            _mockApiKeyValidationService = new Mock<IApiKeyValidationService>();
            _mockRequestInfoLogger = new Mock<IRequestInfoLogger>();

            // Setup mock RequestInfoLogger
            var requestInfo = new RequestInfo
            {
                //UserId = "",
                //PhoneNumber = "0964151505",
                //Lat = 21.021673,
                //Lng = 105.803715
            };
            _mockRequestInfoLogger.Setup(x => x.ExtractRequestInfo(It.IsAny<HttpRequest>()))
                .Returns(requestInfo);

            // Setup mock HttpContext
            _mockHttpContext = new Mock<HttpContext>();
            _mockHttpRequest = new Mock<HttpRequest>();
            _mockHttpResponse = new Mock<HttpResponse>();
            _requestHeaders = new Dictionary<string, string>();

            _mockHttpContext.Setup(x => x.Request).Returns(_mockHttpRequest.Object);
            _mockHttpContext.Setup(x => x.Response).Returns(_mockHttpResponse.Object);

            // Initialize controller and set HttpContext
            _controller = new ESController(
                _mockCacheManager.Object,
                _mockApiKeyValidationService.Object,
                _mockRequestInfoLogger.Object,
                _mockAutocompleteService.Object,
                _nearbyService,
                _recommendationService,
                _eSPlaceDetailsService,
                _mockLogger.Object);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = _mockHttpContext.Object
            };

            /// <summary>
            /// Mock place pairs service
            /// </summary>
            _mockPlacePairsService = new Mock<IESPlacePairs>();

            /// <summary>
            /// Thiết lập mock cho GetRecentRoutesWithPairedPlaces
            /// </summary>
            _mockPlacePairsService
                .Setup(s => s.GetRecentRoutesWithPairedPlaces(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<int>(), // Thêm tham số page
                    It.IsAny<LatLonGeoLocation>(),
                    It.IsAny<double?>(), // Thêm tham số minDistanceMeters
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync((string userId, string phoneNumber, int limit, int page, LatLonGeoLocation location, double? minDistanceMeters, CancellationToken token) =>
                {
                    // Tạo dữ liệu mẫu cho test
                    var routes = new List<PlacePairRoute>();

                    // Tính toán offset dựa trên page và limit
                    int offset = page * limit;

                    for (int i = 0; i < limit; i++)
                    {
                        int index = offset + i;
                        routes.Add(new PlacePairRoute
                        {
                            BookingPlacePairId = 1000 + index,
                            CustomerId = userId ?? $"test-user-{index}",
                            CustomerPhone = phoneNumber ?? $"098765432{index}",
                            BookingCount = index + 1,
                            LastBookingAt = DateTime.UtcNow.AddDays(-index),
                            LastSearchAt = DateTime.UtcNow.AddHours(-index),
                            LastEstimateAmount = $"{50000 + index * 10000}",
                            Pickup = new PlacePairPoint
                            {
                                PlaceId = $"pickup-{index}",
                                FullAddress = $"Địa điểm đón {index}, Hà Nội",
                                Location = new Location
                                {
                                    Lat = location?.Lat ?? 21.0 + (index * 0.01),
                                    Lon = location?.Lon ?? 105.8 + (index * 0.01)
                                },
                                Distance = 100 * index
                            },
                            Arrival = new PlacePairPoint
                            {
                                PlaceId = $"arrival-{index}",
                                FullAddress = $"Địa điểm đến {index}, Hà Nội",
                                Location = new Location
                                {
                                    Lat = (location?.Lat ?? 21.0) + 0.05 + (index * 0.01),
                                    Lon = (location?.Lon ?? 105.8) + 0.05 + (index * 0.01)
                                }
                            }
                        });
                    }

                    return routes;
                });
        }

        [Test]
        [TestCase(21.021673, 105.803715, 9, 5)] // H3 resolution 9 ~ bán kính 174m
        //[TestCase(21.000103, 105.841003, 8, 3)] // H3 resolution 8 ~ bán kính 461m
        //[TestCase(21.007831, 105.845657, 10, 4)] // H3 resolution 10 ~ bán kính 65m
        public async Task NearBy_ReturnsExpectedResults(double latitude, double longitude, int h3Resolution, int limit)
        {
            // Act - Đo thời gian xử lý
            var stopwatch = Stopwatch.StartNew();
            var result = await _controller.NearBy(latitude, longitude, h3Resolution, limit);
            stopwatch.Stop();

            // Assert - Kiểm tra kiểu kết quả
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());

            // Kiểm tra dữ liệu kết quả
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult, Is.Not.Null);
            Assert.That(okResult.Value, Is.InstanceOf<List<PlacesEs>>());

            var returnedPlaces = okResult.Value as List<PlacesEs>;
            Assert.That(returnedPlaces, Is.Not.Null);
            Assert.That(returnedPlaces.Count, Is.LessThanOrEqualTo(limit));

            // Log kết quả cho mục đích debug
            Console.WriteLine($"=====================================================");
            Console.WriteLine($"TEST CASE: ({latitude}, {longitude}) với H3 resolution {h3Resolution} và limit={limit}");
            Console.WriteLine($"Tìm thấy {returnedPlaces.Count} kết quả - Thời gian: {stopwatch.ElapsedMilliseconds}ms");
            Console.WriteLine($"=====================================================");

            foreach (var place in returnedPlaces)
            {
                Console.WriteLine($" - {place.Name} ({place.Type}): {place.FullAddress} - {place.Distance}m");


                // Log thông tin địa điểm con nếu có
                if (place.SubPlaces?.Any() == true)
                {
                    Console.WriteLine($"   Có {place.SubPlaces.Count} địa điểm con:");
                    foreach (var subPlace in place.SubPlaces)
                    {
                        Console.WriteLine($"    * {subPlace.Name}");
                    }
                }
            }

            // Kiểm tra tính chính xác của kết quả sắp xếp
            if (returnedPlaces.Count >= 2)
            {
                // Xác nhận rằng kết quả được sắp xếp theo thứ tự ưu tiên:
                // (1) Popular DESC, (2) BookingCount DESC, (3) Distance ASC
                for (int i = 0; i < returnedPlaces.Count - 1; i++)
                {
                    var current = returnedPlaces[i];
                    var next = returnedPlaces[i + 1];

                    if (current.Popular != next.Popular)
                    {
                        Assert.That(current.Popular, Is.GreaterThanOrEqualTo(next.Popular),
                            "Kết quả không được sắp xếp đúng theo độ phổ biến");
                    }
                    else if (current.BookingCount != next.BookingCount)
                    {
                        Assert.That(current.BookingCount, Is.GreaterThanOrEqualTo(next.BookingCount),
                            "Kết quả không được sắp xếp đúng theo số lần đặt");
                    }
                    else
                    {
                        Assert.That(current.Distance, Is.LessThanOrEqualTo(next.Distance),
                            "Kết quả không được sắp xếp đúng theo khoảng cách");
                    }
                }
            }
        }

        [Test]
        public async Task NearBy_WithDefaultValues_ReturnsResults()
        {
            // Act - Gọi không truyền tham số để sử dụng giá trị mặc định
            var result = await _controller.NearBy();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());

            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult, Is.Not.Null);

            var returnedPlaces = okResult.Value as List<PlacesEs>;
            Assert.That(returnedPlaces, Is.Not.Null);

            // Log kết quả
            Console.WriteLine($"Tìm kiếm với giá trị mặc định trả về {returnedPlaces.Count} kết quả");

            foreach (var place in returnedPlaces)
            {
                Console.WriteLine($" - {place.Name} ({place.Type}): {place.FullAddress} - {place.Distance}m");
            }
        }

        [Test]
        public async Task NearBy_WithLimitExceedingMaximum_LimitsToMaximum()
        {
            // Act - Gọi với limit vượt quá giới hạn (10 > 5)
            var result = await _controller.NearBy(21.021673, 105.803715, 9, 10); // H3 resolution 9 ~ bán kính 174m

            // Assert
            var okResult = result.Result as OkObjectResult;
            var returnedPlaces = okResult?.Value as List<PlacesEs>;

            Assert.That(returnedPlaces, Is.Not.Null);
            Assert.That(returnedPlaces.Count, Is.LessThanOrEqualTo(5),
                "Controller nên giới hạn kết quả tối đa là 5 ngay cả khi limit được đặt là 10");

            // Log kết quả
            Console.WriteLine($"Tìm kiếm với limit=10 (vượt giới hạn) trả về {returnedPlaces.Count} kết quả");
        }

        [Test]
        public async Task NearBy_Performance_Test()
        {
            // Quanh khu vực Hà Nội
            var locations = new List<(double lat, double lon, string name)>
            {
                (21.021673, 105.803715, "Khu vực 1 - Ba Đình"),
                (21.000103, 105.841003, "Khu vực 2 - Đống Đa"),
                (21.007831, 105.845657, "Khu vực 3 - Hoàn Kiếm"),
                (21.031219, 105.853027, "Khu vực 4 - Long Biên"),
                (20.991324, 105.804276, "Khu vực 5 - Cầu Giấy")
            };

            // Danh sách H3 resolution thử nghiệm
            // H3 resolution và bán kính tương ứng:
            // - Resolution 7 ~ bán kính 1220m
            // - Resolution 8 ~ bán kính 461m
            // - Resolution 9 ~ bán kính 174m
            // - Resolution 10 ~ bán kính 65m
            var h3ResolutionValues = new List<int> { 10, 9, 8, 7 };

            var results = new List<(string location, int h3Resolution, int resultCount, long executionTime)>();

            foreach (var location in locations)
            {
                foreach (var h3Resolution in h3ResolutionValues)
                {
                    // Act - Đo thời gian xử lý
                    var stopwatch = Stopwatch.StartNew();
                    var response = await _controller.NearBy(location.lat, location.lon, h3Resolution, 5);
                    stopwatch.Stop();

                    // Lấy kết quả
                    var okResult = response.Result as OkObjectResult;
                    var places = okResult?.Value as List<PlacesEs>;

                    // Ghi lại kết quả
                    results.Add((
                        location.name,
                        h3Resolution,
                        places?.Count ?? 0,
                        stopwatch.ElapsedMilliseconds
                    ));
                }
            }

            // Log bảng kết quả hiệu suất
            Console.WriteLine("\n--- BÁO CÁO HIỆU SUẤT TÌM KIẾM ĐỊA ĐIỂM GẦN ĐÂY ---");
            Console.WriteLine($"{"Khu vực",-20} | {"H3 Resolution",-12} | {"Số kết quả",-12} | {"Thời gian (ms)",-15}");
            Console.WriteLine(new string('-', 70));

            foreach (var r in results)
            {
                Console.WriteLine($"{r.location,-20} | {r.h3Resolution,-12} | {r.resultCount,-12} | {r.executionTime,-15}");
            }

            // Tính toán tổng kết
            var avgTime = results.Average(r => r.executionTime);
            var maxTime = results.Max(r => r.executionTime);
            var minTime = results.Min(r => r.executionTime);
            var totalResults = results.Sum(r => r.resultCount);

            Console.WriteLine(new string('-', 70));
            Console.WriteLine($"Tổng số kết quả: {totalResults}");
            Console.WriteLine($"Thời gian trung bình: {avgTime:F2}ms");
            Console.WriteLine($"Thời gian tối đa: {maxTime}ms");
            Console.WriteLine($"Thời gian tối thiểu: {minTime}ms");

            // Kiểm tra thời gian tối đa không vượt quá ngưỡng
            Assert.That(maxTime, Is.LessThan(5000), "Thời gian xử lý tối đa không nên vượt quá 5 giây");
        }

        [Test]
        public async Task NearBy_Concurrent_Requests()
        {
            // Nhiều yêu cầu đồng thời với các tham số khác nhau
            var tasks = new List<Task<ActionResult<List<PlacesEs>>>>
            {
                _controller.NearBy(21.021673, 105.803715, 9, 5),  // H3 resolution 9 ~ bán kính 174m
                _controller.NearBy(21.000103, 105.841003, 8, 3),  // H3 resolution 8 ~ bán kính 461m
                _controller.NearBy(21.007831, 105.845657, 10, 4), // H3 resolution 10 ~ bán kính 65m
                _controller.NearBy(21.031219, 105.853027, 9, 5),  // H3 resolution 9 ~ bán kính 174m
                _controller.NearBy(20.991324, 105.804276, 7, 5)   // H3 resolution 7 ~ bán kính 1220m
            };

            // Act
            var stopwatch = Stopwatch.StartNew();
            var results = await Task.WhenAll(tasks);
            stopwatch.Stop();

            // Assert
            Assert.That(results, Has.All.Not.Null, "Tất cả các yêu cầu đồng thời phải trả về kết quả không null");

            // Kiểm tra từng kết quả
            for (int i = 0; i < results.Length; i++)
            {
                var okResult = results[i].Result as OkObjectResult;
                Assert.That(okResult, Is.Not.Null, $"Yêu cầu {i + 1} không trả về OkObjectResult");

                var places = okResult.Value as List<PlacesEs>;
                Assert.That(places, Is.Not.Null, $"Yêu cầu {i + 1} không trả về danh sách PlacesEs");
            }

            // Log kết quả
            Console.WriteLine($"Xử lý {tasks.Count} yêu cầu đồng thời trong {stopwatch.ElapsedMilliseconds}ms");
            Console.WriteLine($"Thời gian trung bình cho mỗi yêu cầu: {stopwatch.ElapsedMilliseconds / tasks.Count}ms");

            // Kiểm tra thời gian tổng cộng không vượt quá ngưỡng
            Assert.That(stopwatch.ElapsedMilliseconds, Is.LessThan(10000),
                "Xử lý đồng thời nhiều yêu cầu không nên mất quá 10 giây");
        }

        [Test]
        public async Task RecommendPlaces_ReturnsExpectedResults()
        {
            // Arrange
            string? userId = null;
            //string phoneNumber = "0964151505";
            //string phoneNumber = "0869222158";
            //double? latitude = 21.021673;
            //double? longitude = 105.803715;

            string phoneNumber = "0912025340";
            double? latitude = 21.0017869;
            double? longitude = 105.8074045;
            //double? latitude = 20.97755;
            //double? longitude = 105.82515

            //double? latitude = null;
            //double? longitude = null;
            int limit = 5;
            int page = 0;

            // Act
            var stopwatch = Stopwatch.StartNew();
            var result = await _controller.RecommendPlaces(userId, phoneNumber, latitude, longitude, null, limit, page);
            stopwatch.Stop();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());

            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult, Is.Not.Null);
            Assert.That(okResult.Value, Is.InstanceOf<List<RecommendedPlace>>());

            var returnedPlaces = okResult.Value as List<RecommendedPlace>;
            Assert.That(returnedPlaces, Is.Not.Null);

            // Log kết quả cho mục đích debug
            Console.WriteLine($"=====================================================");
            Console.WriteLine($"TEST CASE: RecommendPlaces - Thời gian: {stopwatch.ElapsedMilliseconds}ms");
            Console.WriteLine($"Tìm thấy {returnedPlaces.Count} kết quả gợi ý địa điểm");
            Console.WriteLine($"=====================================================");

            foreach (var place in returnedPlaces)
            {
                Console.WriteLine($" - {(!string.IsNullOrWhiteSpace(place.Name) ? place.Name + ": " : place.Address +", ")}{place.FullAddress.Trim()} (Popular: {place.Popular} - {place.Distance:##0.##}km)");
            }
        }

        [Test]
        public async Task RecommendSearch_ReturnsExpectedResults()
        {
            // Arrange
            string? userId = null;
            //string phoneNumber = "0964151505";
            string phoneNumber = "0869222158";
            double? latitude = 21.021673;
            double? longitude = 105.803715;

            //string phoneNumber = "0912025340";
            //double? latitude = 21.0017869;
            //double? longitude = 105.8074045;

            //double? latitude = 20.97755;
            //double? longitude = 105.82515;
            int h3Resolution = 10; // H3 resolution 10 ~ bán kính 65m
            int limit = 5;

            // Act
            var stopwatch = Stopwatch.StartNew();
            var result = await _controller.RecommendSearch(userId, phoneNumber, latitude, longitude, h3Resolution, limit);
            stopwatch.Stop();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());

            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult, Is.Not.Null);
            Assert.That(okResult.Value, Is.InstanceOf<RecommendSearchItem>());

            var item = okResult.Value as RecommendSearchItem;
            Assert.That(item, Is.Not.Null);

            // Log kết quả cho mục đích debug
            Console.WriteLine($"=====================================================");
            Console.WriteLine($"TEST CASE: RecommendSearch - Thời gian: {stopwatch.ElapsedMilliseconds}ms");
            Console.WriteLine($"Tìm thấy {item.Places.Count} danh mục gợi ý tìm kiếm");
            Console.WriteLine($"=====================================================");

            Console.WriteLine($" - Danh mục: {item.Title} ({item.Places.Count} địa điểm)");
            foreach (var place in item.Places)
            {
                Console.WriteLine($"   * {(!string.IsNullOrWhiteSpace(place.Name) ? place.Name + "," : place.Address + ",")} {place.FullAddress} - {place.Distance:##0.##}km");
            }
        }

        [Test]
        public async Task RecommendRoutes_ReturnsExpectedResults()
        {
            // Arrange
            string? userId = null;
            //string phoneNumber = "0966961429";
            string phoneNumber = "0869222158";
            double? latitude = 21.021673;
            double? longitude = 105.803715;

            //string phoneNumber = "0912025340";
            //double? latitude = 21.0017869;
            //double? longitude = 105.8074045;

            //double? latitude = 20.97755;
            //double? longitude = 105.82515;
            int limit = 2;
            int page = 1;

            // Act
            var stopwatch = Stopwatch.StartNew();
            var result = await _controller.RecommendRoutes(userId, phoneNumber, latitude, longitude, null, limit, page);
            stopwatch.Stop();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());

            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult, Is.Not.Null);
            Assert.That(okResult.Value, Is.InstanceOf<List<RecommendedRoute>>());

            var returnedRoutes = okResult.Value as List<RecommendedRoute>;
            Assert.That(returnedRoutes, Is.Not.Null);

            // Log kết quả cho mục đích debug
            Console.WriteLine($"=====================================================");
            Console.WriteLine($"TEST CASE: RecommendRoutes - Thời gian: {stopwatch.ElapsedMilliseconds}ms");
            Console.WriteLine($"Tìm thấy {returnedRoutes.Count} lộ trình gợi ý");
            Console.WriteLine($"=====================================================");

            foreach (var route in returnedRoutes)
            {
                Console.WriteLine($" - Lộ trình: {(!route.Origin.FullAddress.StartsWith(route.Origin.Address) ? route.Origin.Address + ", " : "")}{route.Origin.FullAddress} " +
                                  $"=> {(!route.Destination.FullAddress.StartsWith(route.Destination.Address??"") ? route.Destination.Address + ", " : "")}{route.Destination.FullAddress}");
                Console.WriteLine($"   * Khoảng cách: {route.Distance:##0.##}km  - Giá ước tính: {route.EstimateAmount} VND");
                Console.WriteLine($"   * Ghi chú: {route.Note}");
            }
        }

        [Test]
        public async Task RecommendPlaces_WithDefaultValues_ReturnsResults()
        {
            // Act - Gọi không truyền tham số để sử dụng giá trị mặc định
            var result = await _controller.RecommendPlaces();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());

            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult, Is.Not.Null);

            var returnedPlaces = okResult.Value as List<RecommendedPlace>;
            Assert.That(returnedPlaces, Is.Not.Null);

            // Log kết quả
            Console.WriteLine($"Gợi ý địa điểm với giá trị mặc định trả về {returnedPlaces.Count} kết quả");
        }

        [Test]
        public async Task RecommendSearch_WithDefaultValues_ReturnsResults()
        {
            // Act - Gọi không truyền tham số để sử dụng giá trị mặc định
            var result = await _controller.RecommendSearch();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());

            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult, Is.Not.Null);

            var returnedItems = okResult.Value as List<RecommendSearchItem>;
            Assert.That(returnedItems, Is.Not.Null);

            // Log kết quả
            Console.WriteLine($"Gợi ý tìm kiếm với giá trị mặc định trả về {returnedItems.Count} kết quả");
        }

        [Test]
        public async Task RecommendRoutes_WithDefaultValues_ReturnsResults()
        {
            // Act - Gọi không truyền tham số để sử dụng giá trị mặc định
            var result = await _controller.RecommendRoutes();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());

            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult, Is.Not.Null);

            var returnedRoutes = okResult.Value as List<RecommendedRoute>;
            Assert.That(returnedRoutes, Is.Not.Null);

            // Log kết quả
            Console.WriteLine($"Gợi ý lộ trình với giá trị mặc định trả về {returnedRoutes.Count} kết quả");
        }

        [Test]
        public async Task Recommendation_Concurrent_Requests()
        {
            // Tạo các yêu cầu đồng thời khác nhau
            var recommendTasks = new List<Task>
            {
                // Gợi ý địa điểm
                _controller.RecommendPlaces(userId: "user1", phoneNumber: "098765", latitude: 21.0, longitude: 105.8, 1), // H3 resolution 10 ~ bán kính 65m
                _controller.RecommendPlaces(userId: "user2", phoneNumber: "098766", latitude: 21.1, longitude: 105.9, 2),  // H3 resolution 9 ~ bán kính 174m
                
                // Gợi ý tìm kiếm
                _controller.RecommendSearch(userId: "user1", phoneNumber: "098765", latitude: 21.0, longitude: 105.8, 1), // H3 resolution 10 ~ bán kính 65m
                _controller.RecommendSearch(userId: "user2", phoneNumber: "098766", latitude: 21.1, longitude: 105.9, 5),  // H3 resolution 9 ~ bán kính 174m
                
                // Gợi ý lộ trình
                _controller.RecommendRoutes(userId: "user1", phoneNumber: "098765", latitude: 21.0, longitude: 105.8, 1), // H3 resolution 10 ~ bán kính 65m
                _controller.RecommendRoutes(userId: "user2", phoneNumber: "098766", latitude: 21.1, longitude: 105.9, 5)   // H3 resolution 9 ~ bán kính 174m
            };

            // Act - Thực thi đồng thời
            var stopwatch = Stopwatch.StartNew();
            await Task.WhenAll(recommendTasks);
            stopwatch.Stop();

            // Assert - Kiểm tra thời gian thực hiện
            Console.WriteLine($"Xử lý {recommendTasks.Count} yêu cầu gợi ý đồng thời trong {stopwatch.ElapsedMilliseconds}ms");
            Console.WriteLine($"Thời gian trung bình cho mỗi yêu cầu: {stopwatch.ElapsedMilliseconds / recommendTasks.Count}ms");

            // Đảm bảo thời gian xử lý đồng thời không quá chậm
            Assert.That(stopwatch.ElapsedMilliseconds, Is.LessThan(15000),
                "Xử lý đồng thời nhiều yêu cầu gợi ý không nên mất quá 15 giây");
        }

        [Test]
        public async Task Recommendation_Performance_Test()
        {
            string? userId = null;
            string phoneNumber = "0964151505";
            // Quanh khu vực Hà Nội
            var locations = new List<(double lat, double lon, string name)>
            {
                (21.021673, 105.803715, "Ba Đình"),
                (21.000103, 105.841003, "Đống Đa"),
                (21.007831, 105.845657, "Hoàn Kiếm")
            };

            var results = new List<(string api, string location, long executionTime, int resultCount)>();

            // Act - Thực hiện các API gợi ý với các vị trí khác nhau
            foreach (var location in locations)
            {
                // Test RecommendPlaces
                var placeStopwatch = Stopwatch.StartNew();
                var placeResponse = await _controller.RecommendPlaces(
                    userId,
                    phoneNumber,
                    latitude: location.lat,
                    longitude: location.lon,
                    1,  // 1km
                    limit: 10);
                placeStopwatch.Stop();

                var placeResult = placeResponse.Result as OkObjectResult;
                var places = placeResult?.Value as List<RecommendedPlace>;
                results.Add(("RecommendPlaces", location.name, placeStopwatch.ElapsedMilliseconds, places?.Count ?? 0));

                // Test RecommendSearch
                var searchStopwatch = Stopwatch.StartNew();
                var searchResponse = await _controller.RecommendSearch(
                    userId,
                    phoneNumber,
                    latitude: location.lat,
                    longitude: location.lon,
                    1, // 1km
                    limit: 10);
                searchStopwatch.Stop();

                var searchResult = searchResponse.Result as OkObjectResult;
                var searches = searchResult?.Value as List<RecommendSearchItem>;
                results.Add(("RecommendSearch", location.name, searchStopwatch.ElapsedMilliseconds, searches?.Count ?? 0));

                // Test RecommendRoutes
                var routeStopwatch = Stopwatch.StartNew();
                var routeResponse = await _controller.RecommendRoutes(
                    userId,
                    phoneNumber,
                    latitude: location.lat,
                    longitude: location.lon,
                    1, // 1km
                    limit: 10);
                routeStopwatch.Stop();

                var routeResult = routeResponse.Result as OkObjectResult;
                var routes = routeResult?.Value as List<RecommendedRoute>;
                results.Add(("RecommendRoutes", location.name, routeStopwatch.ElapsedMilliseconds, routes?.Count ?? 0));
            }

            // Log bảng kết quả hiệu suất
            Console.WriteLine("\n--- BÁO CÁO HIỆU SUẤT CÁC API GỢI Ý ---");
            Console.WriteLine($"{"API",-20} | {"Khu vực",-15} | {"Thời gian (ms)",-15} | {"Số kết quả",-12}");
            Console.WriteLine(new string('-', 70));

            foreach (var r in results)
            {
                Console.WriteLine($"{r.api,-20} | {r.location,-15} | {r.executionTime,-15} | {r.resultCount,-12}");
            }

            // Tính toán tổng kết
            var avgTime = results.Average(r => r.executionTime);
            var maxTime = results.Max(r => r.executionTime);
            var minTime = results.Min(r => r.executionTime);

            Console.WriteLine(new string('-', 70));
            Console.WriteLine($"Thời gian trung bình: {avgTime:F2}ms");
            Console.WriteLine($"Thời gian tối đa: {maxTime}ms");
            Console.WriteLine($"Thời gian tối thiểu: {minTime}ms");

            // Kiểm tra thời gian tối đa không vượt quá ngưỡng
            Assert.That(maxTime, Is.LessThan(10000), "Thời gian xử lý tối đa không nên vượt quá 10 giây");
        }

        [Test]
        public async Task RecommendRoutes_Performance_Analysis()
        {
            // Arrange
            string? userId = null;
            string phoneNumber = "0966961429";
            double? latitude = 21.021673;
            double? longitude = 105.803715;
            int limit = 5;
            int page = 1;

            Console.WriteLine("\n\n===== PERFORMANCE ANALYSIS FOR RECOMMENDED ROUTES =====\n");

            // Act
            var stopwatch = Stopwatch.StartNew();
            var result = await _controller.RecommendRoutes(userId, phoneNumber, latitude, longitude, null, limit, page);
            stopwatch.Stop();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());

            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult, Is.Not.Null);
            Assert.That(okResult.Value, Is.InstanceOf<List<RecommendedRoute>>());

            var returnedRoutes = okResult.Value as List<RecommendedRoute>;
            Assert.That(returnedRoutes, Is.Not.Null);

            // Log kết quả tổng hợp
            Console.WriteLine($"\n===== RESULTS SUMMARY =====");
            Console.WriteLine($"Total execution time: {stopwatch.ElapsedMilliseconds}ms");
            Console.WriteLine($"Routes found: {returnedRoutes?.Count ?? 0}");
            Console.WriteLine($"===========================\n");
        }
        
        [Test]
        public async Task RecommendRoutes_DetailedLogging_Analysis()
        {
            // Arrange
            string? userId = null;
            string phoneNumber = "0964151505";
            double? latitude = 21.021673;
            double? longitude = 105.803715;
            int limit = 5;
            int page = 1;
            string lang = "en";

            Console.WriteLine("\n\n===== DETAILED LOGGING ANALYSIS FOR RECOMMENDED ROUTES =====\n");
            Console.WriteLine("This test focuses on capturing all logging output from the recommendation service");
            Console.WriteLine("All logs with [PERF] prefix are performance-related metrics\n");

            // Act - Measure total execution time
            var stopwatch = Stopwatch.StartNew();
            var result = await _controller.RecommendRoutes(userId, phoneNumber, latitude, longitude, null, limit, page);
            stopwatch.Stop();

            // Assert
            var okResult = result.Result as OkObjectResult;
            var returnedRoutes = okResult?.Value as List<RecommendedRoute>;

            // Print summary of test results
            Console.WriteLine($"\n===== TEST RESULTS SUMMARY =====");
            Console.WriteLine($"Total execution time: {stopwatch.ElapsedMilliseconds}ms");
            Console.WriteLine($"Routes found: {returnedRoutes?.Count ?? 0}");
            
            if (returnedRoutes?.Any() == true)
            {
                Console.WriteLine("\nRoute details:");
                foreach (var route in returnedRoutes.Take(3))
                {
                    Console.WriteLine($" - {route.Origin.Address} => {route.Destination.Address} ({route.Distance:F2}km)");
                }
                
                if (returnedRoutes.Count > 3)
                {
                    Console.WriteLine($" - ... and {returnedRoutes.Count - 3} more routes");
                }
            }
            
            Console.WriteLine("==================================\n");
        }
    }
}