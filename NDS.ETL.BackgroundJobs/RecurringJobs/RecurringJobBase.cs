using NDS.ETL.BackgroundJobs.Services;
using NDS.ETL.Infrastructure.Common;
using KnowledgeBase.Core.UnitOfWork;
using KnowledgeBase.Core.Extensions;

namespace NDS.ETL.BackgroundJobs.RecurringJobs;

public abstract class RecurringJobBase<TContext> : IRecurringJob where TContext : UnitOfWorkBase
	{
		private readonly object _locker = new();
		private readonly ILogger<RecurringJobBase<TContext>> _logger;
		private readonly IScopedResolver<TContext> _dbContext;
		private bool _isRunning;
		private readonly JobConfig? _config;

		protected RecurringJobBase(
			IConfiguration configuration
			, ILogger<RecurringJobBase<TContext>> logger
			, IScopedResolver<TContext> dbContext)
		{
			_logger = logger;
			_dbContext = dbContext;
			_config = configuration.Bind<List<JobConfig>>(ConfigSection)?.FirstOrDefault(t => t.JobId == JobId);
    }

		public bool IsRunning
		{
			get
			{
				lock (_locker) { return _isRunning; }
			}
			set
			{
				lock (_locker) { _isRunning = value; }
			}
		}

		public abstract string JobId { get; }

		public virtual bool Disable => _config?.Disable ?? true;

		public virtual bool Immediately => _config?.Immediately ?? false;

		public virtual string CronExpression => _config?.CronExpression ?? "";

		public abstract string ConfigSection { get; }

		public async Task ExecuteAsync(CancellationToken stoppingToken)
		{
			if (IsRunning) return;
			try
			{
				IsRunning = true;
				await DoWorkAsync(stoppingToken);
			}
			catch (Exception e)
			{
				Console.WriteLine(e);
				throw;
			}
			finally
			{
				IsRunning = false;
			}
		}

		public abstract Task DoWorkAsync(CancellationToken stoppingToken);

		public abstract Task IndexMany<T>(List<T> source, CancellationToken cancellationToken, int take = 1000) where T : class;
		
		public abstract Task IndexMany<T>(List<T> source, CancellationToken cancellationToken,string esIndex, int take = 1000) where T : class;

		public async Task DataHandle<T>(Func<UnitOfWorkBase, int, int, Task<List<T>>> dataHandler, CancellationToken cancellationToken, int? take = 1000, Func<T, bool> filter = null)
			where T : class
		{
			try
			{
				Console.WriteLine($"--> {JobId} Started!");
				await _dbContext.ResolveAsync(async context =>
				{
					return true;
				});
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "--> {handleName} EXCEPTION", JobId);
				throw;
			}
			finally
			{
				Console.WriteLine($"--> {JobId} Completed!");
			}
		}

		public async Task DataHandle<T>(Func<UnitOfWorkBase, Task<List<T>>> dataHandler, CancellationToken cancellationToken)
			where T : class
		{
			try
			{
				Console.WriteLine($"--> {JobId} Started!");
				await _dbContext.ResolveAsync(async context =>
				{
					return true;
				});
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "--> {handleName} EXCEPTION", JobId);
				throw;
			}
			finally
			{
				Console.WriteLine($"--> {JobId} Completed!");
			}
		}
	}