# #--- APPCENTER CMS Plan ---
# version: 2
# plan:
#   project-key: APPCT
#   key: APPCENTER
#   name: App<PERSON>enter CMS
#   description: AppCenter CMS Backend
# stages:
#   - Default Stage:
#       manual: false
#       final: false
#       jobs:
#         - Default Job
# variables:
#   docker_password: BAMSCRT@0@0@dbNk6sDDhzDGiPY0GQ63lQ==
#   uddd_docker_password: BAMSCRT@0@0@/GeMTf/QvbSd7BKB2HrZaQ==
# Default Job:
#   key: JOB1
#   tasks:
#     - checkout:
#         force-clean-build: 'false'
#         description: Checkout Default Repository
#     - script:
#         working-dir: Vnpay/src/Vnpay.AppCenter.Web.Mvc
#         scripts:
#           - npm run build
#           # Environment variables
# #          - export ANT_OPTS=-Xmx700m
#           # The working subdirectory is $bamboo_working_directory
# #          - cd rockets && $bamboo_working_directory/ant-build.sh --verbose
# #    - command:
# #        executable: NodeJS
# #        argument: run build
# #        working-dir:  Vnpay/src/Vnpay.AppCenter.Web.Mvc
# #        description: Npm build task
# #    - command:
# #        executable: NetCore
# #        argument: restore --source https://api.nuget.org/v3/index.json --packages /var/data/packages
# #        working-dir:  Vnpay/src/Vnpay.AppCenter.Web.Mvc
# #        description: Dotnet restore
#     - command:
#         executable: NetCore
#         argument: publish -c Release -r linux-x64 --output ../../../publish/production --self-contained=false -p:PublishSingleFile=true --packages /var/data/packages
#         working-dir:  Vnpay/src/Vnpay.AppCenter.Web.Mvc
#         description: Dotnet publish
#     - script:
#         interpreter: SHELL
#         scripts:
#           - |-
#             SET IMAGE_TAG=%bamboo_planRepository_revision:~0,8%-b%bamboo_buildNumber%
#             SET STAG_IMAGE_TAG=stag-%IMAGE_TAG%
#             docker build --force-rm=true -t registry.vnpay.vn/vnpay/uddd/appcenter:%STAG_IMAGE_TAG% .
#             echo | set /p="%bamboo_uddd_docker_password%" | docker login --username uddd --password-stdin https://registry.vnpay.vn
#             docker push registry.vnpay.vn/vnpay/uddd/appcenter:%STAG_IMAGE_TAG%
#             docker tag registry.vnpay.vn/vnpay/uddd/appcenter:%STAG_IMAGE_TAG% registry.vnpay.vn/vnpay/uddd/appcenter:latest
#             docker push registry.vnpay.vn/vnpay/uddd/appcenter:latest
#             FOR /F %%i IN ('docker images --format "{{.Repository}}:{{.Tag}}" ^| findstr "registry.vnpay.vn/vnpay/uddd/appcenter" ^| findstr -v "%STAG_IMAGE_TAG%" ^| findstr -v "latest"') ^
#             DO docker rmi %%i
#             echo Y | docker image prune
#             exit /b 0
#         working-dir: publish/production
#         description: Docker task
# #  artifacts:
# #    - name: AppCenter CMS
# #      location: publish/production
# #      pattern: '**/*'
# #      shared: true
# #      required: true
#   artifact-subscriptions: []
# repositories:
#   - Azure AppCenter (master):
#       scope: global
# #triggers: []
# triggers:
#   - polling:
#       period: '180'
#       description: Checking for build
# branches:
#   create: manually
#   delete: never
#   link-to-jira: true
# notifications: []
# labels: []
# dependencies:
#   require-all-stages-passing: false
#   enabled-for-branches: true
#   block-strategy: none
#   plans: []
# other:
#   concurrent-build-plugin: system-default
# ---
# version: 2
# plan:
#   key: APPCT-APPCENTER
# plan-permissions:
#   - roles:
#       - logged-in
#     permissions:
#       - view
#   - users:
#       - bamboo
#     permissions:
#       - view
#       - edit
#       - build
#       - clone
#       - admin
# ...
# #--- AppCenter CMS Deployment ---
# ---
# version: 2
# deployment:
#   name: Deployment for AppCenter CMS
#   source-plan: APPCT-APPCENTER
# release-naming:
#   next-version-name: r1
#   applies-to-branches: false
#   auto-increment: true
#   auto-increment-variables: []
# environments:
#   - Staging
# Staging:
#   triggers:
#     - build-success
#   tasks:
# #    - clean
# #    - artifact-download:
# #        artifacts:
# #          - destination: publish
# #            name: AppCenter CMS
# #        description: Download release contents
# #    - script:
# #        interpreter: SHELL
# #        scripts:
# #          - |-
# #            SET IMAGE_TAG=%bamboo_planRepository_revision:~0,8%-b%bamboo_buildNumber%
# #            SET STAG_IMAGE_TAG=stag-%IMAGE_TAG%
# #            docker build --force-rm=true -t registry.vnpay.vn/vnpay/uddd/appcenter:%STAG_IMAGE_TAG% .
# #            echo | set /p="%bamboo_uddd_docker_password%" | docker login --username uddd --password-stdin https://registry.vnpay.vn
# #            docker push registry.vnpay.vn/vnpay/uddd/appcenter:%STAG_IMAGE_TAG%
# #            docker tag registry.vnpay.vn/vnpay/uddd/appcenter:%STAG_IMAGE_TAG% registry.vnpay.vn/vnpay/uddd/appcenter:latest
# #            docker push registry.vnpay.vn/vnpay/uddd/appcenter:latest
# #            FOR /F %%i IN ('docker images --format "{{.Repository}}:{{.Tag}}" ^| findstr "registry.vnpay.vn/vnpay/uddd/appcenter" ^| findstr -v "%STAG_IMAGE_TAG%" ^| findstr -v "latest"') ^
# #            DO docker rmi %%i
# #        working-dir: publish
# #        description: Docker task
# #    For UNIX
# #    - script:
# #        interpreter: SHELL
# #        scripts:
# #          - |-
# #            export IMAGE_TAG=${bamboo_planRepository_revision:0:8}-b${bamboo_buildNumber}
# #            echo "IMAGE_TAG: ${IMAGE_TAG}"
# #            export STAG_IMAGE_TAG=stag-${IMAGE_TAG}
# #            echo "STAG_IMAGE_TAG: ${STAG_IMAGE_TAG}"
# #            docker build --force-rm=true -t registry.vnpay.vn/vnpay/uddd/appcenter:${STAG_IMAGE_TAG} .
# #            echo "${bamboo.uddd_docker_password}" | docker login --username uddd --password-stdin https://registry.vnpay.vn
# #            docker push registry.vnpay.vn/vnpay/uddd/appcenter:${STAG_IMAGE_TAG}
# #            docker tag registry.vnpay.vn/vnpay/uddd/appcenter:${STAG_IMAGE_TAG} registry.vnpay.vn/vnpay/uddd/appcenter:latest
# #            docker push registry.vnpay.vn/vnpay/uddd/appcenter:latest
# #            docker rmi -f $(docker images --format "{{.Repository}}:{{.Tag}}" | grep 'registry.vnpay.vn/vnpay/uddd/appcenter' | grep -v $STAG_IMAGE_TAG | grep -v 'latest') || true
# #            echo "IMAGE_TAG: ${IMAGE_TAG}"
# #        working-dir: publish
# #        description: Docker task
# #    - command:
# #        executable: Docker
# #        argument: logout https://registry.vnpay.vn
# #        description: Docker logout
# #    - script:
# #        interpreter: SHELL
# #        scripts:
# #          - |-
# #            export IMAGE_TAG=${bamboo_planRepository_revision:0:8}-b${bamboo_buildNumber}
# #            export STAG_IMAGE_TAG=stag-${IMAGE_TAG}
# #            # helm uninstall appcenter -n appcenter
# #            # sleep 10
# #            helm repo add --username=uddd --password=${bamboo.helm_vnp_repo_password} vnprepo https://registry.vnpay.vn/chartrepo/vnpay & helm repo update vnprepo
# #            helm upgrade --install appcenter vnprepo/appcenter --namespace appcenter \
# #            --set image.tag=$STAG_IMAGE_TAG,env=Staging,\
# #            nodeSelector."kubernetes\.io/hostname"=mbapi.vnpaytest.vn,\
# #            ingress.enabled=true,ingress.className=nginx,\
# #            ingress.hosts[0].host=appcenter.vnpaytest.vn,\
# #            ingress.hosts[0].paths[0].path=/,ingress.hosts[0].paths[0].pathType=Prefix
# #        working-dir: publish
# #        description: Deploy to K8S
# #    - script:
# #        interpreter: SHELL
# #        scripts:
# #          - |-
# #            SET IMAGE_TAG=%bamboo_planRepository_revision:~0,8%-b%bamboo_buildNumber%
# #            SET STAG_IMAGE_TAG=stag-%IMAGE_TAG%
# #            REM helm uninstall appcenter -n appcenter
# #            REM sleep 5
# #            helm repo add --username=uddd --password=%bamboo_helm_vnp_repo_password% vnprepo https://registry.vnpay.vn/chartrepo/vnpay & helm repo update vnprepo
# #            helm upgrade --install appcenter vnprepo/appcenter --namespace appcenter ^
# #            --set image.tag=%STAG_IMAGE_TAG%,env=Staging,^
# #            nodeSelector."kubernetes\.io/hostname"=mbapi.vnpaytest.vn,^
# #            ingress.enabled=true,ingress.className=nginx,^
# #            ingress.hosts[0].host=appcenter.vnpaytest.vn,^
# #            ingress.hosts[0].paths[0].path=/,ingress.hosts[0].paths[0].pathType=Prefix
# #        description: Deploy to K8S
#     - ssh:
#         host: ************
#         command: "export REVISION=${bamboo.planRepository.revision}\n
#         export IMAGE_TAG=${REVISION:0:8}-b${bamboo.buildNumber}\n
#         export STAG_IMAGE_TAG=stag-${IMAGE_TAG}\n
#         # helm repo add --username=robo --password=${bamboo.helm_repo_password} mbvnpay https://mbcms.vnpaytest.vn/chartrepo/vnpay & helm repo update mbvnpay\n
#         helm repo add --username=uddd --password=${bamboo.helm_uddd_repo_password} vnprepo https://registry.vnpay.vn/chartrepo/vnpay & helm repo update vnprepo\n
#         helm uninstall appcenter -n appcenter\n
#         sleep 5\n
#         helm upgrade --install appcenter vnprepo/appcenter --namespace appcenter \
#         --set image.tag=$STAG_IMAGE_TAG,env=Staging,replicaCount=2,\
#         resources.limits.memory=2048Mi,\
#         nodeSelector.\"kubernetes\\\\.io/hostname\"=mbapi.vnpaytest.vn,\
#         ingress.enabled=true,ingress.className=nginx,\
#         ingress.hosts[0].host=appcenter.vnpaytest.vn,\
#         ingress.hosts[0].paths[0].path=/,ingress.hosts[0].paths[0].pathType=Prefix\n
#         for i in $(docker images --format '{{.Repository}}:{{.Tag}}' | grep 'registry.vnpay.vn/vnpay/uddd/appcenter' | grep -v '$STAG_IMAGE_TAG' | grep -v 'latest'); do docker rmi $i || true; done"
#         authentication:
#           shared-credentials: vntaxi_ssh
#         description: Deploy to K8S
#   final-tasks: []
#   variables:
#     docker_password: BAMSCRT@0@0@dbNk6sDDhzDGiPY0GQ63lQ==
#     loidd_docker_password: BAMSCRT@0@0@/GeMTf/QvbSd7BKB2HrZaQ==
#     helm_repo_password: BAMSCRT@0@0@TlV/OldhDCqdNUbxuUGmEg==
#     helm_uddd_repo_password: BAMSCRT@0@0@/GeMTf/QvbSd7BKB2HrZaQ==
#   requirements: []
# #    - Helm
#   notifications:
#     - events:
#         - deployment-finished
#       recipients:
#         - users:
#             - bamboo
# ---
# version: 2
# deployment:
#   name: Deployment for AppCenter CMS
# deployment-permissions:
#   - roles:
#       - logged-in
#     permissions:
#       - view
#   - users:
#       - bamboo
#       - anhdd
#     permissions:
#       - view
#       - edit
# environment-permissions:
#   - Staging:
#       - roles:
#           - logged-in
#         permissions:
#           - view
#       - users:
#           - bamboo
#         permissions:
#           - view
#           - edit
#           - deploy
# ...
# #--- APPCENTER API Plan ---
# ---
# version: 2
# plan:
#   project-key: APPCT
#   key: APPCTAPI
#   name: AppCenter API
#   description: AppCenter API Server
# stages:
#   - Default Stage:
#       manual: false
#       final: false
#       jobs:
#         - Default Job
# variables:
#   docker_password: BAMSCRT@0@0@dbNk6sDDhzDGiPY0GQ63lQ==
#   uddd_docker_password: BAMSCRT@0@0@/GeMTf/QvbSd7BKB2HrZaQ==
# Default Job:
#   key: JOB1
#   tasks:
#     - checkout:
#         force-clean-build: 'false'
#         description: Checkout Default Repository
#     - command:
#         executable: NetCore
#         argument: publish -c Release -r linux-x64 --output ../../../publish/host --self-contained=false -p:PublishSingleFile=true --packages /var/data/packages
#         working-dir:  Vnpay/src/Vnpay.AppCenter.Web.Host
#         description: Dotnet publish
#     - script:
#         interpreter: SHELL
#         scripts:
#           - |-
#             SET IMAGE_TAG=%bamboo_planRepository_revision:~0,8%-b%bamboo_buildNumber%
#             SET STAG_IMAGE_TAG=stag-%IMAGE_TAG%
#             docker build --force-rm=true -t registry.vnpay.vn/vnpay/uddd/appcenterapi:%STAG_IMAGE_TAG% .
#             echo | set /p="%bamboo_uddd_docker_password%" | docker login --username uddd --password-stdin https://registry.vnpay.vn
#             docker push registry.vnpay.vn/vnpay/uddd/appcenterapi:%STAG_IMAGE_TAG%
#             docker tag registry.vnpay.vn/vnpay/uddd/appcenterapi:%STAG_IMAGE_TAG% registry.vnpay.vn/vnpay/uddd/appcenterapi:latest
#             docker push registry.vnpay.vn/vnpay/uddd/appcenterapi:latest
#             FOR /F %%i IN ('docker images --format "{{.Repository}}:{{.Tag}}" ^| findstr "registry.vnpay.vn/vnpay/uddd/appcenterapi" ^| findstr -v "%STAG_IMAGE_TAG%" ^| findstr -v "latest" ^| findstr -v "none"') ^
#             DO docker rmi %%i
#             echo Y | docker image prune
#             exit /b 0
#         working-dir: publish/host
#         description: Docker task
# #  artifacts:
# #    - name: AppCenter API
# #      location: publish/host
# #      pattern: '**/*'
# #      shared: true
# #      required: true
#   artifact-subscriptions: []
# repositories:
#   - Azure AppCenter (master):
#       scope: global
# triggers: []
# #triggers:
# #  - polling:
# #      period: '180'
# #      description: Checking for build
# branches:
#   create: manually
#   delete: never
#   link-to-jira: true
# notifications: []
# labels: []
# dependencies:
#   require-all-stages-passing: false
#   enabled-for-branches: true
#   block-strategy: none
#   plans: []
# other:
#   concurrent-build-plugin: system-default
# ---
# version: 2
# plan:
#   key: APPCT-APPCTAPI
# plan-permissions:
#   - roles:
#       - logged-in
#     permissions:
#       - view
#   - users:
#       - bamboo
#     permissions:
#       - view
#       - edit
#       - build
#       - clone
#       - admin
# ...
# #--- AppCenter API Deployment ---
# ---
# version: 2
# deployment:
#   name: Deployment for AppCenter API
#   source-plan: APPCT-APPCTAPI
# release-naming:
#   next-version-name: r1
#   applies-to-branches: false
#   auto-increment: true
#   auto-increment-variables: []
# environments:
#   - Staging
# Staging:
#   triggers:
#     - build-success
#   tasks:
# #    - clean
# #    - artifact-download:
# #        artifacts:
# #          - destination: publish
# #            name: AppCenter API
# #        description: Download release contents
# #    - script:
# #        interpreter: SHELL
# #        scripts:
# #          - |-
# #            SET IMAGE_TAG=%bamboo_planRepository_revision:~0,8%-b%bamboo_buildNumber%
# #            SET STAG_IMAGE_TAG=stag-%IMAGE_TAG%
# #            docker build --force-rm=true -t registry.vnpay.vn/vnpay/uddd/appcenterapi:%STAG_IMAGE_TAG% .
# #            echo | set /p="%bamboo_uddd_docker_password%" | docker login --username uddd --password-stdin https://registry.vnpay.vn
# #            docker push registry.vnpay.vn/vnpay/uddd/appcenterapi:%STAG_IMAGE_TAG%
# #            docker tag registry.vnpay.vn/vnpay/uddd/appcenterapi:%STAG_IMAGE_TAG% registry.vnpay.vn/vnpay/uddd/appcenterapi:latest
# #            docker push registry.vnpay.vn/vnpay/uddd/appcenterapi:latest
# #            FOR /F %%i IN ('docker images --format "{{.Repository}}:{{.Tag}}" ^| findstr "registry.vnpay.vn/vnpay/uddd/appcenterapi" ^| findstr -v "%STAG_IMAGE_TAG%" ^| findstr -v "latest"') ^
# #            DO docker rmi %%i
# #        working-dir: publish
# #        description: Docker task
# # For UNIX
# #    - script:
# #        interpreter: SHELL
# #        scripts:
# #          - |-
# #            export IMAGE_TAG=${bamboo_planRepository_revision:0:8}-b${bamboo_buildNumber}-${bamboo_deploy_version}
# #            echo "IMAGE_TAG: ${IMAGE_TAG}"
# #            export STAG_IMAGE_TAG=stag-${IMAGE_TAG}
# #            echo "STAG_IMAGE_TAG: ${STAG_IMAGE_TAG}"
# #            docker build --force-rm=true -t registry.vnpay.vn/vnpay/uddd/appcenterapi:${STAG_IMAGE_TAG} .
# #            echo "${bamboo.uddd_docker_password}" | docker login --username uddd --password-stdin https://registry.vnpay.vn
# #            docker push registry.vnpay.vn/vnpay/uddd/appcenterapi:${STAG_IMAGE_TAG}
# #            docker tag registry.vnpay.vn/vnpay/uddd/appcenterapi:${STAG_IMAGE_TAG} registry.vnpay.vn/vnpay/uddd/appcenterapi:latest
# #            docker push registry.vnpay.vn/vnpay/uddd/appcenterapi:latest
# #            docker rmi -f $(docker images | grep 'registry.vnpay.vn/vnpay/uddd/appcenterapi' | grep -v $STAG_IMAGE_TAG) || true
# #            echo "IMAGE_TAG: ${IMAGE_TAG}"
# #        working-dir: publish
# #        description: Docker task
# #    - command:
# #        executable: Docker
# #        argument: logout https://registry.vnpay.vn
# #        description: Docker logout
# #    - script:
# #        interpreter: SHELL
# #        scripts:
# #          - |-
# #            export IMAGE_TAG=${bamboo_planRepository_revision:0:8}-b${bamboo_buildNumber}-${bamboo_deploy_version}
# #            export STAG_IMAGE_TAG=stag-${IMAGE_TAG}
# #            helm uninstall appcenterapi -n appcenter
# #            helm repo add --username=uddd --password=${bamboo.helm_vnp_repo_password} vnprepo https://registry.vnpay.vn/chartrepo/vnpay & helm repo update vnprepo
# #            helm upgrade --install appcenterapi vnprepo/appcenterapi --namespace appcenter \
# #            --set image.tag=$STAG_IMAGE_TAG,env=Staging,\
# #            nodeSelector."kubernetes\\.io/hostname"=mbapi.vnpaytest.vn,\
# #            ingress.enabled=true,ingress.className=nginx,\
# #            ingress.hosts[0].host=mbapi.vnpaytest.vn,\
# #            ingress.hosts[0].paths[0].path=/appcenter,ingress.hosts[0].paths[0].pathType=Prefix
# #        working-dir: publish
# #        description: Deploy to K8S
# #    - script:
# #        interpreter: SHELL
# #        scripts:
# #          - |-
# #            SET IMAGE_TAG=%bamboo_planRepository_revision:~0,8%-b%bamboo_buildNumber%
# #            SET STAG_IMAGE_TAG=stag-%IMAGE_TAG%
# #            REM helm uninstall appcenterapi -n appcenter
# #            REM sleep 5
# #            helm repo add --username=uddd --password=%bamboo_helm_vnp_repo_password% vnprepo https://registry.vnpay.vn/chartrepo/vnpay & helm repo update vnprepo
# #            helm upgrade --install appcenterapi vnprepo/appcenterapi --namespace appcenter ^
# #            --set image.tag=%STAG_IMAGE_TAG%,env=Staging,^
# #            nodeSelector."kubernetes\.io/hostname"=mbapi.vnpaytest.vn,^
# #            ingress.enabled=true,ingress.className=nginx,^
# #            ingress.hosts[0].host=mbapi.vnpaytest.vn,^
# #            ingress.hosts[0].paths[0].path=/appcenter,ingress.hosts[0].paths[0].pathType=Prefix
# ##        working-dir: publish
# #        description: Deploy to K8S
#     - ssh:
#         host: ************
#         command: "export REVISION=${bamboo.planRepository.revision}\n
#         export IMAGE_TAG=${REVISION:0:8}-b${bamboo.buildNumber}\n
#         export STAG_IMAGE_TAG=stag-${IMAGE_TAG}\n
#         # helm repo add --username=robo --password=${bamboo.helm_repo_password} mbvnpay https://mbcms.vnpaytest.vn/chartrepo/vnpay & helm repo update mbvnpay\n
#         helm repo add --username=uddd --password=${bamboo.helm_uddd_repo_password} vnprepo https://registry.vnpay.vn/chartrepo/vnpay & helm repo update vnprepo\n
#         helm uninstall appcenterapi -n appcenter\n
#         sleep 5\n
#         helm upgrade --install appcenterapi vnprepo/appcenterapi --namespace appcenter \
#         --set image.tag=$STAG_IMAGE_TAG,env=Staging,\
#         resources.limits.memory=1024Mi,\
#         nodeSelector.\"kubernetes\\\\.io/hostname\"=mbapi.vnpaytest.vn,\
#         ingress.enabled=true,ingress.className=nginx,\
#         ingress.hosts[0].host=mbapi.vnpaytest.vn,\
#         ingress.hosts[0].paths[0].path=/appcenter,ingress.hosts[0].paths[0].pathType=Prefix\n
#         for i in $(docker images --format '{{.Repository}}:{{.Tag}}' | grep 'registry.vnpay.vn/vnpay/uddd/appcenterapi' | grep -v '$STAG_IMAGE_TAG' | grep -v 'latest'); do docker rmi $i || true; done"
#         authentication:
#           shared-credentials: vntaxi_ssh
#         description: Deploy to K8S
#   final-tasks: []
#   variables:
#     docker_password: BAMSCRT@0@0@dbNk6sDDhzDGiPY0GQ63lQ==
#     loidd_docker_password: BAMSCRT@0@0@/GeMTf/QvbSd7BKB2HrZaQ==
#     helm_repo_password: BAMSCRT@0@0@TlV/OldhDCqdNUbxuUGmEg==
#     helm_uddd_repo_password: BAMSCRT@0@0@/GeMTf/QvbSd7BKB2HrZaQ==
#   requirements: []
# #    - Helm
#   notifications:
#     - events:
#         - deployment-finished
#       recipients:
#         - users:
#             - bamboo
# ---
# version: 2
# deployment:
#   name: Deployment for AppCenter API
# deployment-permissions:
#   - roles:
#       - logged-in
#     permissions:
#       - view
#   - users:
#       - bamboo
#     permissions:
#       - view
#       - edit
# environment-permissions:
#   - Staging:
#       - roles:
#           - logged-in
#         permissions:
#           - view
#       - users:
#           - bamboo
#           - sonnt5
#         permissions:
#           - view
#           - edit
#           - deploy
# ...