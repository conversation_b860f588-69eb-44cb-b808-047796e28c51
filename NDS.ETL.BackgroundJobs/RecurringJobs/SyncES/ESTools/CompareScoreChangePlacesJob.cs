using System.Collections.Concurrent;
using System.Diagnostics;
using Elastic.Clients.Elasticsearch;
using KnowledgeBase.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.BackgroundJobs.Model.PlaceApproveMaster;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Entities.Context;
using NDS.ETL.Infrastructure.Utils;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.ESTools
{
    /// <summary>
    /// Job so sánh sự thay đổi điểm của các địa điểm để xác định cần phê duyệt hay không
    /// </summary>
    public class CompareScoreChangePlacesJob : RecurringJobBaseElastic
    {
        private readonly ILogger<RecurringJobBaseElastic> _logger;
        private readonly string _indexName;
        private readonly IESPlacesService _iesPlacesService;
        private readonly IServiceProvider _serviceProvider;
        // Thời gian timeout cho các lệnh database, đơn vị tính là giây
        private const int COMMAND_TIMEOUT = 300;

        public CompareScoreChangePlacesJob(IConfiguration configuration, IESPlacesService iesPlacesService, ElasticsearchClient elasticClient, IServiceProvider serviceProvider, ILogger<RecurringJobBaseElastic> logger) : base(configuration, logger, elasticClient, null)
        {
            _logger = logger;
            var indexNames = configuration.Bind<ElasticSearchConfig>(nameof(ElasticSearchConfig));
            _indexName = indexNames.Index.FirstOrDefault(x => x.StartsWith("place")) ?? "place";
            _iesPlacesService = iesPlacesService;
            _serviceProvider = serviceProvider;
        }

        public override string JobId => "CompareScoreChangePlacesJob";

        public override string ConfigSection => "JobPgSyncPlace";

        public override async Task DoWorkAsync(CancellationToken stoppingToken)
        {
            // Đo thời gian xử lý toàn bộ job
            var processingStartTime = Stopwatch.GetTimestamp();
            _logger.LogInformation("CompareScoreChangePlacesJob DoWorkAsync started");

            // Thêm counter để theo dõi số lượng xử lý
            int totalProcessed = 0;
            int totalSuccess = 0;
            int totalSkipped = 0;
            int totalErrors = 0;

            try
            {
                // Tạo scope mới để quản lý dependency injection
                using var scope = _serviceProvider.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<NpgPlaceContext>();
                dbContext.Database.SetCommandTimeout(COMMAND_TIMEOUT);

                // Bước 1: Lấy danh sách các địa điểm cần so sánh - chỉ lấy những địa điểm chưa được phê duyệt (StatusApprove=0)
                var dataQueryTimer = Stopwatch.StartNew();
                var pendingPlaces = await dbContext.PlaceApproves
                    .Where(x => x.StatusApprove == 0 && !x.IsDeleted)
                    .OrderBy(x => x.Id)
                    .ToListAsync(stoppingToken);
                var totalPlacesCount = pendingPlaces.Count;
                dataQueryTimer.Stop();

                _logger.LogInformation("Total time to get data from PlaceApproves: {Elapsed} ms - Total: {Total}", dataQueryTimer.ElapsedMilliseconds, totalPlacesCount);
                if (totalPlacesCount == 0) return; // Không có dữ liệu cần xử lý

                // Bước 2: Lấy cấu hình điểm ngưỡng so sánh từ bảng JobConfigPlaces
                var jobConfigs = await dbContext.JobConfigPlaces
                    .Where(x => x.JobId == "CompareScoreChangePlacesJob")
                    .ToListAsync(stoppingToken);

                // Lấy cấu hình ngưỡng điểm cho địa chỉ
                var addressConfig = jobConfigs.FirstOrDefault(x => x.KeyConfig == "scoreAddressChange");
                // Lấy cấu hình ngưỡng điểm cho vị trí
                var locationConfig = jobConfigs.FirstOrDefault(x => x.KeyConfig == "scoreLocationChange");

                if (addressConfig == null || locationConfig == null)
                {
                    _logger.LogError("Cannot find required configs for CompareScoreChangePlacesJob");
                    return;
                }

                // Chuyển đổi giá trị cấu hình từ chuỗi sang số nguyên
                int.TryParse(addressConfig.ValueConfig, out int addressThreshold);
                int.TryParse(locationConfig.ValueConfig, out int locationThreshold);

                // Bước 3: Chia nhỏ dữ liệu thành các batch để xử lý song song
                int batchSize = 50; // Giảm kích thước batch từ 100 xuống 50
                var placeBatches = new List<List<Entities.PostgresPlace.PlaceApprove>>();
                
                // Chia dữ liệu thành các batch
                for (int skip = 0; skip < totalPlacesCount; skip += batchSize)
                {
                    placeBatches.Add(pendingPlaces.Skip(skip).Take(batchSize).ToList());
                }
                
                // Thiết lập số luồng xử lý tối đa dựa trên số nhân CPU, nhưng giới hạn thấp hơn
                int maxThreadCount = Math.Min(Environment.ProcessorCount / 2, placeBatches.Count);
                // Semaphore để kiểm soát số lượng request đồng thời đến ElasticSearch, giảm để tránh quá tải
                using var elasticRequestLimiter = new SemaphoreSlim(Math.Min(2, maxThreadCount)); // Giảm từ 3 xuống 2
                
                _logger.LogInformation("Đang xử lý {BatchCount} batch với {ThreadCount} luồng", placeBatches.Count, maxThreadCount);
                
                // Theo dõi kết quả xử lý để cập nhật counter
                var batchResults = await Task.WhenAll(
                    placeBatches.Select((batch, batchIndex) => 
                        ProcessBatchAsync(batch, batchIndex, addressThreshold, locationThreshold, elasticRequestLimiter, stoppingToken))
                );
                
                // Tính tổng số bản ghi đã xử lý
                foreach (var result in batchResults) {
                    totalProcessed += result.Processed;
                    totalSuccess += result.Success;
                    totalSkipped += result.Skipped;
                    totalErrors += result.Errors;
                }

                // Ghi log tổng thời gian xử lý với thông tin chi tiết hơn
                var processingEndTime = Stopwatch.GetTimestamp();
                _logger.LogInformation("Completed processing {TotalProcessed} places (Success: {Success}, Skipped: {Skipped}, Errors: {Errors}) in {TotalTime} seconds", 
                    totalProcessed, totalSuccess, totalSkipped, totalErrors,
                    TimeSpan.FromTicks(processingEndTime - processingStartTime).TotalSeconds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while comparing score change places.");
                throw;
            }
        }

        /// <summary>
        /// Phương thức xử lý từng batch dữ liệu địa điểm
        /// </summary>
        /// <returns>Kết quả xử lý batch bao gồm số lượng bản ghi đã xử lý, thành công, bỏ qua và lỗi</returns>
        private async Task<(int Processed, int Success, int Skipped, int Errors)> ProcessBatchAsync(
            List<Entities.PostgresPlace.PlaceApprove> placesBatch, 
            int batchIndex, 
            int addressThreshold, 
            int locationThreshold, 
            SemaphoreSlim elasticRequestLimiter,
            CancellationToken stoppingToken)
        {
            // Khởi tạo các biến đếm
            int processed = 0;
            int success = 0;
            int skipped = 0;
            int errors = 0;
            
            // Đo thời gian xử lý batch
            var batchTimer = Stopwatch.StartNew();
            _logger.LogInformation("Bat dau xu ly batch {BatchIndex}: {BatchCount} dia diem", batchIndex, placesBatch.Count);

            // Tạo scope mới để mỗi luồng có DbContext riêng biệt, tránh xung đột
            using var scope = _serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<NpgPlaceContext>();
            dbContext.Database.SetCommandTimeout(COMMAND_TIMEOUT);

            // Lấy danh sách PlaceId để truy vấn từ Elasticsearch
            var placeIds = placesBatch.Select(x => x.PlaceId).ToList();
            var esQueryTimer = Stopwatch.StartNew();
            
            // Sử dụng semaphore để kiểm soát số lượng request đến ElasticSearch
            var elasticPlacesDict = new ConcurrentDictionary<string, PlacesEs>();
            try
            {
                // Chờ đến lượt được phép truy cập ES
                await elasticRequestLimiter.WaitAsync(stoppingToken);
                _logger.LogInformation("Batch {BatchIndex}: Dang truy cap ElasticSearch", batchIndex);
                
                // Thêm retry logic với exponential backoff để xử lý lỗi tạm thời
                const int maxRetries = 3; // Số lần thử lại tối đa
                for (int retry = 0; retry < maxRetries; retry++)
                {
                    try
                    {
                        // Lấy dữ liệu từ ElasticSearch
                        var elasticPlaces = await _iesPlacesService.GetPlacesByPlaceIdsAsync(placeIds, stoppingToken);
                        foreach (var place in elasticPlaces)
                        {
                            elasticPlacesDict.TryAdd(place.PlaceId, place);
                        }
                        break; // Thành công thì thoát vòng lặp
                    }
                    catch (Exception ex) when (retry < maxRetries - 1) // Nếu còn cơ hội retry
                    {
                        // Tính thời gian chờ với exponential backoff: 1s, 2s, 4s...
                        int delayMs = (int)Math.Pow(2, retry) * 1000;
                        _logger.LogWarning(ex, "Batch {BatchIndex}: Lỗi khi truy cập ElasticSearch. Retry {RetryCount} sau {Delay}ms", 
                            batchIndex, retry + 1, delayMs);
                        await Task.Delay(delayMs, stoppingToken);
                    }
                }
            }
            finally
            {
                // Đảm bảo semaphore luôn được release, ngay cả khi có lỗi
                elasticRequestLimiter.Release();
            }
            
            esQueryTimer.Stop();
            _logger.LogInformation("Thoi gian lay du lieu tu Elasticsearch (batch {BatchIndex}): {Elapsed} ms - Tong: {Total}", 
                batchIndex, esQueryTimer.ElapsedMilliseconds, elasticPlacesDict.Count);

            // Log kiểm tra danh sách placeId đã khớp
            var matchedPlaceIds = elasticPlacesDict.Keys.ToList();
            var unmatchedPlaceIds = placeIds.Except(matchedPlaceIds).ToList();
            _logger.LogInformation("Batch {BatchIndex}: So luong placeId khop: {MatchedCount}, khong khop: {UnmatchedCount}", 
                batchIndex, matchedPlaceIds.Count, unmatchedPlaceIds.Count);

            // Không có dữ liệu từ ElasticSearch thì bỏ qua batch này
            if (elasticPlacesDict.IsEmpty) {
                skipped = placesBatch.Count;
                return (placesBatch.Count, 0, skipped, 0);
            }

            // Ánh xạ dữ liệu giữa PlaceApprove và dữ liệu Elasticsearch để dễ xử lý
            var placeDataMappings = placesBatch.Select(item => new PlaceApproveMasterDto
            {
                PlaceApprove = item,
                PlacesEs = elasticPlacesDict.TryGetValue(item.PlaceId, out var placeEs) ? placeEs : null
            }).ToList();

            // Danh sách các địa điểm cần cập nhật lại trong ElasticSearch
            var placesToUpdateInElastic = new List<PlacesEs>();

            // Xử lý song song các phần tử trong batch để tăng tốc
            // ConcurrentBag là thread-safe collection, an toàn khi nhiều luồng cùng thêm dữ liệu
            var processedPlaces = new ConcurrentBag<(Entities.PostgresPlace.PlaceApprove PlaceApprove, PlacesEs PlacesEs)>();
            
            // Xử lý song song từng mapping PlaceApprove - PlacesEs
            await Parallel.ForEachAsync(placeDataMappings, new ParallelOptions { CancellationToken = stoppingToken }, 
                async (placeMapping, token) =>
            {
                // Bỏ qua nếu không có dữ liệu ElasticSearch tương ứng
                if (placeMapping.PlacesEs == null) return;

                // Kiểm tra trạng thái ScanProcessed
                if (placeMapping.PlacesEs.ScanProcessed == 3)
                {
                    // Địa điểm đã được quét và xử lý trước đó, cập nhật trạng thái thành 4 (đã xử lý)
                    placeMapping.PlaceApprove.StatusApprove = 4;
                    placeMapping.PlacesEs.StatusApprovePlace = 4;
                    processed++;
                    success++;
                }
                else
                {
                    // Kiểm tra vị trí hợp lệ
                    if (placeMapping.PlacesEs.Location == null || placeMapping.PlaceApprove.Location == null) {
                        skipped++;
                        return;
                    }

                    // Tính khoảng cách giữa 2 vị trí (đơn vị: mét)
                    double distanceInMeters = GeolocationExtensions.DistanceTo(
                        placeMapping.PlacesEs.Location.Lat, placeMapping.PlacesEs.Location.Lon,
                        placeMapping.PlaceApprove.Location.Y, placeMapping.PlaceApprove.Location.X);

                    // Tính độ tương đồng của địa chỉ (từ 0 đến 1)
                    double addressSimilarity = placeMapping.PlacesEs.Address?.ToLower()
                        .CalculateSimilarity(placeMapping.PlaceApprove.Address?.ToLower() ?? string.Empty) ?? 0;

                    // Chuyển độ tương đồng thành điểm khác biệt (từ 0 đến 100)
                    double addressDifferenceScore = 100 - addressSimilarity * 100;

                    // Gán trạng thái dựa trên ngưỡng:
                    // - StatusCalculate=2: Tự động duyệt (dưới ngưỡng)
                    // - StatusCalculate=1: Cần xem xét thủ công (vượt ngưỡng)
                    placeMapping.PlaceApprove.StatusCalculate =
                        addressDifferenceScore <= addressThreshold && distanceInMeters <= locationThreshold ? 2 : 1;

                    // Ghi lại điểm khác biệt để kiểm tra sau
                    placeMapping.PlaceApprove.LocationAddressDiffScore = 
                        $"locationScore: {distanceInMeters}; addressScore: {addressDifferenceScore}";
                    
                    // Cập nhật trạng thái thành 4 (đã xử lý)
                    placeMapping.PlaceApprove.StatusApprove = 4;
                    placeMapping.PlacesEs.StatusApprovePlace = 4;
                    placeMapping.PlaceApprove.DetermineAddressLevel = placeMapping.PlacesEs.DetermineAddressLevel;
                    processed++;
                    success++;
                }

                // Thêm vào kết quả xử lý
                processedPlaces.Add((placeMapping.PlaceApprove, placeMapping.PlacesEs));
            });

            // Cập nhật trạng thái PlaceApprove trong database
            if (processedPlaces.Any())
            {
                try {
                    // Tạo danh sách các PlaceApprove đã xử lý
                    var placeApprovesToUpdate = processedPlaces.Select(p => p.PlaceApprove).ToList();
                    
                    // Sử dụng EF Core để cập nhật hàng loạt
                    foreach (var placeApprove in placeApprovesToUpdate)
                    {
                        // Đánh dấu entity là đã được sửa đổi
                        dbContext.Entry(placeApprove).State = EntityState.Modified;
                        
                        // Chỉ định rõ các thuộc tính đã thay đổi để tối ưu hóa update
                        dbContext.Entry(placeApprove).Property(p => p.StatusApprove).IsModified = true;
                        dbContext.Entry(placeApprove).Property(p => p.StatusCalculate).IsModified = true;
                        dbContext.Entry(placeApprove).Property(p => p.LocationAddressDiffScore).IsModified = true;
                    }
                    
                    // Lưu tất cả thay đổi vào database
                    var rowsAffected = await dbContext.SaveChangesAsync(stoppingToken);
                    _logger.LogInformation("Đã cập nhật {RowsAffected} bản ghi PlaceApprove thành công", rowsAffected);
                }
                catch (Exception ex)
                {
                    // Ghi log lỗi khi thực hiện cập nhật bằng EF Core
                    _logger.LogError(ex, "Lỗi khi cập nhật PlaceApproves với EF Core: {Message}", ex.Message);
                    throw;
                }
                
                // Thêm vào danh sách cập nhật ElasticSearch
                foreach (var placeItem in processedPlaces)
                {
                    placesToUpdateInElastic.Add(placeItem.PlacesEs);
                }
            }

            // Cập nhật lại Elasticsearch với retry logic để đảm bảo dữ liệu đồng bộ
            if (placesToUpdateInElastic.Count > 0)
            {
                try {
                    // Chia nhỏ thành các sub-batch để đảm bảo không gửi quá nhiều dữ liệu cùng lúc
                    int elasticBatchSize = 100; // Kích thước sub-batch cho Elasticsearch
                    var elasticBatches = new List<List<PlacesEs>>();
                    
                    for (int i = 0; i < placesToUpdateInElastic.Count; i += elasticBatchSize)
                    {
                        elasticBatches.Add(placesToUpdateInElastic.Skip(i).Take(elasticBatchSize).ToList());
                    }
                    
                    _logger.LogInformation("Batch {BatchIndex}: Chia dữ liệu cập nhật Elasticsearch thành {ElasticBatchCount} sub-batch", 
                        batchIndex, elasticBatches.Count);
                    
                    bool allSucceeded = true;
                    foreach (var elasticBatch in elasticBatches)
                    {
                        // Sử dụng hàm Retry với exponential backoff và jitter để tránh thundering herd
                        bool updated = await RetryElasticOperationAsync(async () => {
                            await elasticRequestLimiter.WaitAsync(stoppingToken);
                            try {
                                _logger.LogInformation("Batch {BatchIndex}: Đang cập nhật {Count} bản ghi vào Elasticsearch", 
                                    batchIndex, elasticBatch.Count);
                                await IndexMany(elasticBatch, stoppingToken, _indexName, elasticBatch.Count);
                                return true;
                            }
                            catch (Exception ex) {
                                _logger.LogError(ex, "Batch {BatchIndex}: Lỗi khi cập nhật sub-batch vào Elasticsearch: {Message}", 
                                    batchIndex, ex.Message);
                                return false;
                            }
                            finally {
                                elasticRequestLimiter.Release();
                            }
                        }, maxRetries: 5, batchIndex); // Tăng số lần retry từ 3 lên 5
                        
                        if (!updated) {
                            allSucceeded = false;
                            errors += elasticBatch.Count;
                            success -= elasticBatch.Count; // Điều chỉnh lại số lượng thành công
                        }
                        
                        // Thêm delay giữa các lần gọi Elasticsearch để tránh quá tải
                        await Task.Delay(200, stoppingToken);
                    }
                    
                    if (allSucceeded) {
                        _logger.LogInformation("Batch {BatchIndex}: Đã cập nhật tất cả {Count} bản ghi vào Elasticsearch thành công", 
                            batchIndex, placesToUpdateInElastic.Count);
                    }
                }
                catch (Exception ex) {
                    _logger.LogError(ex, "Batch {BatchIndex}: Lỗi cập nhật ElasticSearch sau tất cả các lần thử lại", batchIndex);
                    errors += placesToUpdateInElastic.Count;
                    success -= placesToUpdateInElastic.Count; // Điều chỉnh lại số lượng thành công
                }
            }

            // Kết thúc xử lý batch
            batchTimer.Stop();
            _logger.LogInformation("Da xu ly xong batch {BatchIndex} trong {Elapsed} ms", batchIndex, batchTimer.ElapsedMilliseconds);
            
            return (processed, success, skipped, errors);
        }
        
        /// <summary>
        /// Phương thức retry với exponential backoff và jitter cho Elasticsearch
        /// </summary>
        private async Task<bool> RetryElasticOperationAsync(Func<Task<bool>> operation, int maxRetries, int batchIndex)
        {
            Random random = new Random();
            for (int retry = 0; retry < maxRetries; retry++)
            {
                try
                {
                    return await operation();
                }
                catch (Exception ex)
                {
                    // Kiểm tra nếu là lỗi 429 (Too Many Requests)
                    bool isTooManyRequests = ex.Message.Contains("429") || ex.Message.Contains("too many requests");
                    
                    if (retry < maxRetries - 1)
                    {
                        // Tính thời gian chờ với exponential backoff
                        int baseDelayMs = (int)Math.Pow(2, retry) * 1000;
                        
                        // Thêm jitter (±30%) để tránh thundering herd
                        int jitter = random.Next(-baseDelayMs * 30 / 100, baseDelayMs * 30 / 100);
                        int delayMs = baseDelayMs + jitter;
                        
                        // Thêm thời gian chờ lâu hơn cho lỗi 429
                        if (isTooManyRequests)
                        {
                            delayMs *= 2;
                        }
                        
                        _logger.LogWarning(ex, "Batch {BatchIndex}: {ErrorType}. Retry {RetryCount} sau {Delay}ms", 
                            batchIndex, 
                            isTooManyRequests ? "Lỗi 429 - Too Many Requests" : "Lỗi khi cập nhật Elasticsearch",
                            retry + 1, 
                            delayMs);
                            
                        await Task.Delay(delayMs);
                    }
                    else if (isTooManyRequests)
                    {
                        // Nếu là lần thử cuối cùng và vẫn gặp lỗi 429, đợi thêm và thử lại một lần nữa
                        _logger.LogWarning("Batch {BatchIndex}: Vẫn gặp lỗi 429 sau {RetryCount} lần thử. Đợi lâu hơn và thử lần cuối...", 
                            batchIndex, retry + 1);
                        await Task.Delay(15000); // Đợi 15 giây
                        try
                        {
                            return await operation();
                        }
                        catch (Exception finalEx)
                        {
                            _logger.LogError(finalEx, "Batch {BatchIndex}: Không thể cập nhật Elasticsearch sau lần thử cuối cùng", 
                                batchIndex);
                        }
                    }
                }
            }
            return false; // Thất bại sau tất cả các lần thử
        }
    }
}
