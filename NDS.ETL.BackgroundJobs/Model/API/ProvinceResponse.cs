using System.Collections.Generic;

namespace NDS.ETL.BackgroundJobs.Model.API;

/// <summary>
/// Response model for province data
/// </summary>
public class ProvinceResponse
{
    /// <summary>
    /// Kh?i t?o ??i t??ng ProvinceResponse v?i danh s�ch t?nh c? tr?ng
    /// </summary>
    public ProvinceResponse()
    {
        OldProvinces = new List<OldProvinceInfo>();
    }
    
    /// <summary>
    /// Province ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// Province code
    /// </summary>
    public string? Code { get; set; }
    
    /// <summary>
    /// Province name
    /// </summary>
    public string? Name { get; set; }
    
    /// <summary>
    /// Province type
    /// </summary>
    public string? Type { get; set; }
    
    /// <summary>
    /// Old province code
    /// </summary>
    public string? OldCode { get; set; }
    
    /// <summary>
    /// Province center coordinates (format: "lat,lng")
    /// </summary>
    public string? CenterLocation { get; set; }
    
    /// <summary>
    /// Whether the province has been processed
    /// </summary>
    public bool? Processed { get; set; }
    
    /// <summary>
    /// Danh s�ch c�c t?nh c? (tr??c 1/7/2025) ???c �nh x? v�o t?nh m?i n�y
    /// </summary>
    public List<OldProvinceInfo> OldProvinces { get; set; }
}

/// <summary>
/// Th�ng tin v? t?nh c? tr??c 1/7/2025
/// </summary>
public class OldProvinceInfo
{
    /// <summary>
    /// ID t?nh c?
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// M� t?nh c?
    /// </summary>
    public string Code { get; set; } = null!;
    
    /// <summary>
    /// T�n t?nh c?
    /// </summary>
    public string? Name { get; set; }
    
    /// <summary>
    /// Lo?i t?nh c?
    /// </summary>
    public int? Type { get; set; }
    
    /// <summary>
    /// V? tr� trung t�m t?nh c? (format: "lat,lng")
    /// </summary>
    public string? CenterLocation { get; set; }
    
    /// <summary>
    /// Tr?ng th�i x? l� c?a t?nh c?
    /// </summary>
    public bool? Processed { get; set; }
}