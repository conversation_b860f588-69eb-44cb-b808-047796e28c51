using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Keyless]
[Table("building_boundary")]
public partial class BuildingBoundary
{
    [Column("id")]
    public int Id { get; set; }

    [Column("latitude")]
    public double Latitude { get; set; }

    [Column("longitude")]
    public double Longitude { get; set; }

    [Column("areaInMeters")]
    public double AreaInMeters { get; set; }

    [Column("confidence")]
    public double Confidence { get; set; }

    [Column("geometry")]
    public string? Geometry { get; set; }

    [Column("plusCode")]
    [StringLength(155)]
    public string? PlusCode { get; set; }

    [Column("provinceCode")]
    [StringLength(20)]
    public string? ProvinceCode { get; set; }

    [Column("provinceName")]
    [StringLength(155)]
    public string? ProvinceName { get; set; }

    [Column("districtCode")]
    [StringLength(20)]
    public string? DistrictCode { get; set; }

    [Column("districtName")]
    [StringLength(155)]
    public string? DistrictName { get; set; }

    [Column("wardCode")]
    [StringLength(20)]
    public string? WardCode { get; set; }

    [Column("wardName")]
    [StringLength(155)]
    public string? WardName { get; set; }

    [Column("processStatus")]
    public int ProcessStatus { get; set; }

    [Column("name")]
    [StringLength(155)]
    public string? Name { get; set; }
}
