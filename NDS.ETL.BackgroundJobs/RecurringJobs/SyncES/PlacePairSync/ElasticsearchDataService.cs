using Elastic.Clients.Elasticsearch;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using IndexState = NDS.ETL.BackgroundJobs.Model.ElasticSearch.PlacePairSync.IndexState;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.PlacePairSync
{
    /// <summary>
    /// Dịch vụ làm việc với dữ liệu Elasticsearch
    /// </summary>
    public class ElasticsearchDataService
    {
        private readonly ILogger _logger;
        private readonly ElasticsearchClient _elasticClient;
        private readonly string _indexName;
        private const int FULL_SYNC_DAYS_THRESHOLD = 7; // Số ngày kể từ lần sync cuối để trigger full sync

        public ElasticsearchDataService(
            ILogger logger,
            ElasticsearchClient elasticClient,
            string indexName)
        {
            _logger = logger;
            _elasticClient = elasticClient;
            _indexName = indexName;
        }

        /// <summary>
        /// Ki<PERSON>m tra xem index có tồn tại không
        /// </summary>
        public async Task<bool> GetIndexExists(CancellationToken stoppingToken)
        {
            try
            {
                var indexExistsResponse = await _elasticClient.Indices.ExistsAsync(_indexName, stoppingToken);
                return indexExistsResponse.Exists;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking if index '{_indexName}' exists");
                return false;
            }
        }
        
        /// <summary>
        /// Lấy thời gian cập nhật mới nhất từ Elasticsearch
        /// </summary>
        public async Task<DateTime> GetMaxUpdateAtFromEs(CancellationToken stoppingToken)
        {
            try
            {
                // Kiểm tra xem index có tồn tại hay không
                var indexExistsResponse = await _elasticClient.Indices.ExistsAsync(_indexName, stoppingToken);
                if (!indexExistsResponse.Exists)
                {
                    _logger.LogWarning($"ES index '{_indexName}' doesn't exist");
                    return DateTime.MinValue;
                }

                // Truy vấn UpdateAt lớn nhất từ Elasticsearch
                var response = await _elasticClient.SearchAsync<PlacePairEs>(s => s
                    .Index(_indexName)
                    .Size(0)
                    .Aggregations(aggs => aggs
                        .Add("max_updated_at", d =>
                        {
                            d.Max(x =>
                            {
                                x.Field(f => f.UpdateAt);
                            });
                        })
                    ), stoppingToken);

                // Lấy kết quả từ aggregation
                if (response.Aggregations == null)
                {
                    _logger.LogWarning("No aggregations returned from ES");
                    return DateTime.MinValue;
                }
            
                var maxAgg = response.Aggregations.GetMax("max_updated_at");
                if (maxAgg == null || maxAgg.Value == null)
                {
                    _logger.LogWarning("No max aggregation results found for UpdatedAt");
                    return DateTime.MinValue;
                }

                double? maxUpdatedAtDouble = maxAgg.Value.GetValueOrDefault();
                if (maxUpdatedAtDouble <= 0)
                {
                    _logger.LogWarning("Invalid max UpdatedAt value returned from aggregation");
                    return DateTime.MinValue;
                }

                // Convert double value to DateTime (assuming it's a Unix timestamp in milliseconds)
                var lastSyncTime = DateTimeOffset.FromUnixTimeMilliseconds((long)maxUpdatedAtDouble).UtcDateTime;
                return lastSyncTime;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving max UpdateAt from Elasticsearch");
                return DateTime.MinValue;
            }
        }

        /// <summary>
        /// Lấy thời gian tạo mới nhất từ Elasticsearch cho các bản ghi mới 
        /// (UpdateAt = null HOẶC UpdateAt = CreatedAt)
        /// </summary>
        public async Task<DateTime> GetMaxCreateAtFromEs(CancellationToken stoppingToken)
        {
            try
            {
                // Kiểm tra xem index có tồn tại hay không
                var indexExistsResponse = await _elasticClient.Indices.ExistsAsync(_indexName, stoppingToken);
                if (!indexExistsResponse.Exists)
                {
                    _logger.LogWarning($"ES index '{_indexName}' doesn't exist");
                    return DateTime.MinValue;
                }

                // Đầu tiên kiểm tra xem có bản ghi nào với điều kiện new records không
                // New records: UpdateAt = null HOẶC UpdateAt = CreatedAt
                var countResponse = await _elasticClient.CountAsync<PlacePairEs>(c => c
                    .Indices(_indexName)
                    .Query(q => q
                        .Bool(b => b
                            .Should(
                                // UpdateAt = null
                                s => s.Bool(b2 => b2
                                    .MustNot(mn => mn
                                        .Exists(e => e
                                            .Field(f => f.UpdateAt)
                                        )
                                    )
                                ),
                                // UpdateAt = CreatedAt (sử dụng script query)
                                s => s.Script(sc => sc
                                    .Script(script => script
                                        .Source("doc['updateAt'].value == doc['createdAt'].value")
                                    )
                                )
                            )
                            .MinimumShouldMatch(1)
                        )
                    ), stoppingToken);

                if (countResponse.Count == 0)
                {
                    _logger.LogInformation("No new records found in ES (UpdateAt=null or UpdateAt=CreatedAt)");
                    return DateTime.MinValue;
                }

                // Truy vấn CreatedAt lớn nhất từ Elasticsearch cho các bản ghi mới
                var response = await _elasticClient.SearchAsync<PlacePairEs>(s => s
                    .Index(_indexName)
                    .Size(0)
                    .Query(q => q
                        .Bool(b => b
                            .Should(
                                // UpdateAt = null
                                s => s.Bool(b2 => b2
                                    .MustNot(mn => mn
                                        .Exists(e => e
                                            .Field(f => f.UpdateAt)
                                        )
                                    )
                                ),
                                // UpdateAt = CreatedAt 
                                s => s.Script(sc => sc
                                    .Script(script => script
                                        .Source("doc['updateAt'].value == doc['createdAt'].value")
                                    )
                                )
                            )
                            .MinimumShouldMatch(1)
                        )
                    )
                    .Aggregations(aggs => aggs
                        .Add("max_created_at", d =>
                        {
                            d.Max(x =>
                            {
                                x.Field(f => f.CreatedAt);
                            });
                        })
                    ), stoppingToken);

                // Lấy kết quả từ aggregation
                if (response.Aggregations == null)
                {
                    _logger.LogWarning("No aggregations returned from ES for CreatedAt");
                    return DateTime.MinValue;
                }
            
                var maxAgg = response.Aggregations.GetMax("max_created_at");
                if (maxAgg == null || maxAgg.Value == null)
                {
                    _logger.LogWarning("No max aggregation results found for CreatedAt despite having new records");
                    return DateTime.MinValue;
                }

                double? maxCreatedAtDouble = maxAgg.Value.GetValueOrDefault();
                if (maxCreatedAtDouble <= 0)
                {
                    _logger.LogWarning("Invalid max CreatedAt value returned from aggregation");
                    return DateTime.MinValue;
                }

                // Convert double value to DateTime (assuming it's a Unix timestamp in milliseconds)
                var maxCreatedAt = DateTimeOffset.FromUnixTimeMilliseconds((long)maxCreatedAtDouble).UtcDateTime;
                _logger.LogInformation($"Found max CreatedAt in ES for {countResponse.Count} new records (UpdateAt=null or UpdateAt=CreatedAt): {maxCreatedAt}");
                return maxCreatedAt;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving max CreatedAt from Elasticsearch");
                return DateTime.MinValue;
            }
        }

        /// <summary>
        /// Phương thức lấy thông tin về index trong Elasticsearch
        /// </summary>
        public async Task<IndexState?> GetIndexStats(CancellationToken stoppingToken)
        {
            try
            {
                var indexExistsResponse = await _elasticClient.Indices.ExistsAsync(_indexName, stoppingToken);
                if (!indexExistsResponse.Exists)
                {
                    return null;
                }

                var statsResponse = await _elasticClient.Indices.StatsAsync(stoppingToken);
                if (!statsResponse.IsValidResponse)
                {
                    _logger.LogError($"Error retrieving index stats: {statsResponse.DebugInformation}");
                    return null;
                }

                if (statsResponse.Indices == null || !statsResponse.Indices.TryGetValue(_indexName, out var indexStats))
                {
                    _logger.LogError($"Index '{_indexName}' not found in stats response");
                    return null;
                }

                // Lấy số lượng tài liệu trong index
                long docCount = 0;
                if (indexStats.Total != null && indexStats.Total.Docs != null)
                {
                    docCount = indexStats.Total.Docs.Count;
                }
                else
                {
                    _logger.LogWarning("Index stats information is incomplete");
                }

                // Lấy thông tin về thời gian tạo index
                var idxResponse = await _elasticClient.Indices.GetAsync(_indexName, stoppingToken);
                idxResponse.Indices.TryGetValue(_indexName, out var indexState);
                var indexCreationDate = indexState?.Settings?.Index?.CreationDate;
                var creationDateTime = indexCreationDate.HasValue
                    ? DateTimeOffset.FromUnixTimeMilliseconds(indexCreationDate.Value).UtcDateTime
                    : DateTime.UtcNow.AddDays(-FULL_SYNC_DAYS_THRESHOLD - 1);

                return new IndexState
                {
                    CreationTime = creationDateTime,
                    DocumentCount = docCount
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving index stats for '{_indexName}'");
                return null;
            }
        }
    }
} 