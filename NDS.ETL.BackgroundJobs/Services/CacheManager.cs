using Microsoft.Extensions.Caching.Memory;
using System.Collections.Concurrent;

namespace NDS.ETL.BackgroundJobs.Services
{
    public interface ICacheManager
    {
        void ClearCacheByPrefix(string prefix);
        void AddToCache(string key, object value, TimeSpan expiration);
        object GetFromCache(string key);
        void RemoveFromCache(string key);
    }

    public class CacheManager : ICacheManager
    {
        private readonly IMemoryCache _cache;
        private readonly ConcurrentDictionary<string, HashSet<string>> _prefixToKeysMap;
        private readonly ILogger<CacheManager> _logger;

        public CacheManager(IMemoryCache cache, ILogger<CacheManager> logger)
        {
            _cache = cache;
            _logger = logger;
            _prefixToKeysMap = new ConcurrentDictionary<string, HashSet<string>>();
        }

        public void AddToCache(string key, object value, TimeSpan expiration)
        {
            _cache.Set(key, value, expiration);
            
            // Track the key with its prefix
            string prefix = GetKeyPrefix(key);
            if (!string.IsNullOrEmpty(prefix))
            {
                _prefixToKeysMap.AddOrUpdate(
                    prefix,
                    // If the prefix doesn't exist, create a new HashSet with this key
                    _ => new HashSet<string> { key },
                    // If the prefix exists, add the key to the existing HashSet
                    (_, keys) =>
                    {
                        lock (keys)
                        {
                            keys.Add(key);
                            return keys;
                        }
                    });
            }
            
            // Register callback to remove the key from tracking when it expires or is removed
            var cacheEntryOptions = new MemoryCacheEntryOptions().SetAbsoluteExpiration(expiration)
                .RegisterPostEvictionCallback((evictedKey, _, reason, _) =>
                {
                    RemoveKeyFromTracking(evictedKey.ToString());
                });
            
            // Re-set with the callback
            _cache.Set(key, value, cacheEntryOptions);
        }

        public object GetFromCache(string key)
        {
            _cache.TryGetValue(key, out var value);
            return value;
        }

        public void RemoveFromCache(string key)
        {
            _cache.Remove(key);
            RemoveKeyFromTracking(key);
        }

        public void ClearCacheByPrefix(string prefix)
        {
            _logger.LogInformation("Clearing all cache entries with prefix: {Prefix}", prefix);
            
            if (_prefixToKeysMap.TryRemove(prefix, out var keys))
            {
                foreach (var key in keys.ToList())
                {
                    _logger.LogDebug("Removing cache entry with key: {Key}", key);
                    _cache.Remove(key);
                }
            }
            else
            {
                _logger.LogInformation("No cache entries found with prefix: {Prefix}", prefix);
            }
        }

        private string GetKeyPrefix(string key)
        {
            int separatorIndex = key.IndexOf('_');
            return separatorIndex > 0 ? key.Substring(0, separatorIndex + 1) : string.Empty;
        }
        
        private void RemoveKeyFromTracking(string key)
        {
            string prefix = GetKeyPrefix(key);
            if (!string.IsNullOrEmpty(prefix) && _prefixToKeysMap.TryGetValue(prefix, out var keys))
            {
                lock (keys)
                {
                    keys.Remove(key);
                    // If the set is empty, remove the prefix entry
                    if (keys.Count == 0)
                    {
                        _prefixToKeysMap.TryRemove(prefix, out _);
                    }
                }
            }
        }
    }
}
