using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Table("places_scan_2")]
public partial class PlacesScan2
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("place_id", TypeName = "character varying")]
    public string PlaceId { get; set; } = null!;

    [Column("rule_name")]
    [StringLength(255)]
    public string? RuleName { get; set; }

    [Column("trust")]
    public bool? Trust { get; set; }

    [Column("way_distance")]
    public long? WayDistance { get; set; }

    [Column("created_at", TypeName = "timestamp without time zone")]
    public DateTime CreatedAt { get; set; }

    [Column("updated_at", TypeName = "timestamp without time zone")]
    public DateTime UpdatedAt { get; set; }

    [Column("scan_by_way")]
    public long? ScanByWay { get; set; }
}
