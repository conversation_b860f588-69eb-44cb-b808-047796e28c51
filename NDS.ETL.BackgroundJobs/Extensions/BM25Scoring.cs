using NDS.ETL.BackgroundJobs.Model.ElasticSearch;
using NDS.ETL.Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;
using KnowledgeBase.Core.Extensions;

namespace NDS.ETL.BackgroundJobs.Extensions
{
    /// <summary>
    /// <PERSON><PERSON> cấp các phương thức tính điểm BM25 cho các kết quả tìm kiếm
    /// </summary>
    public static class BM25Scoring
    {
        /// <summary>
        /// Kiểm tra xem chuỗi có chứa dấu tiếng Việt hay không
        /// </summary>
        public static bool HasVietnameseDiacritics(this string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;
                
            // Danh sách các ký tự tiếng Việt có dấu
            string vietnameseDiacritics = "áàảãạâấầẩẫậăắằẳẵặéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ";
            vietnameseDiacritics += vietnameseDiacritics.ToUpper();
            
            // Ki<PERSON><PERSON> tra từng ký tự trong chuỗi
            foreach (char c in text)
            {
                if (vietnameseDiacritics.Contains(c))
                {
                    return true;
                }
            }
            
            return false;
        }

        /// <summary>
        /// C<PERSON>u trúc kết quả tính toán BM25
        /// </summary>
        public class BM25Result
        {
            public double TfScore { get; set; }
            public double ProximityScore { get; set; }
            public int TermMatches { get; set; }
            public int TotalTerms { get; set; }
            public int AdjacentPairs { get; set; }
            public double MatchRatio => TotalTerms > 0 ? (double)TermMatches / TotalTerms : 0;
            public double AdjacentRatio => TotalTerms > 1 ? (double)AdjacentPairs / (TotalTerms - 1) : 0;
        }

        /// <summary>
        /// Tính toán điểm BM25 cho các kết quả tìm kiếm
        /// </summary>
        public static void CalculateBM25Scores<T>(List<T> results, string normalizedQuery, ILogger logger)
            where T : PlacesEs
        {
            // Tiền xử lý query - chuẩn hóa những địa chỉ phổ biến
            var preprocessedQuery = PreprocessQuery(normalizedQuery);
            
            // Kiểm tra xem query có dấu hay không
            var hasVietnameseDiacritics = preprocessedQuery.HasVietnameseDiacritics();
            logger.LogDebug("Query '{Query}' has Vietnamese diacritics: {HasDiacritics}", preprocessedQuery, hasVietnameseDiacritics);
            
            // Chuẩn hóa query theo đúng dạng (có dấu hoặc không dấu)
            var normalizedQueryWithDiacritics = hasVietnameseDiacritics 
                ? VietnameseAddressPermutator.NormalizeVietnameseAddress(preprocessedQuery)
                : null;
            var normalizedQueryWithoutDiacritics = AddressPermutator.NormalizeVietnamese(preprocessedQuery);
            
            foreach (var result in results.Where(r => r.FullAddress != null))
            {
                // Chuẩn hóa địa chỉ và tên theo yêu cầu
                var normalizedFullAddressWithDiacritics = hasVietnameseDiacritics 
                    ? VietnameseAddressPermutator.NormalizeVietnameseAddress(result.FullAddress)
                    : null;
                var normalizedFullAddressWithoutDiacritics = AddressPermutator.NormalizeVietnamese(result.FullAddress);
                
                var normalizedNameWithDiacritics = hasVietnameseDiacritics && !string.IsNullOrEmpty(result.Name)
                    ? VietnameseAddressPermutator.NormalizeVietnameseAddress(result.Name) 
                    : null;
                var normalizedNameWithoutDiacritics = !string.IsNullOrEmpty(result.Name)
                    ? AddressPermutator.NormalizeVietnamese(result.Name)
                    : string.Empty;
                
                // Tính điểm theo phương pháp giống BM25 đơn giản hóa
                var originalScore = result.Score ?? 1.0;
                double newScore = originalScore;
                
                // Tính toán điểm dựa vào việc có dấu hay không
                BM25Result bm25ResultWithDiacritics = null;
                BM25Result bm25ResultWithoutDiacritics = null;
                
                if (hasVietnameseDiacritics)
                {
                    bm25ResultWithDiacritics = CalculateBM25ForText(
                        normalizedFullAddressWithDiacritics, 
                        normalizedQueryWithDiacritics.Split([' ', ',', '.', '-', '_'], StringSplitOptions.RemoveEmptyEntries));
                }
                
                bm25ResultWithoutDiacritics = CalculateBM25ForText(
                    normalizedFullAddressWithoutDiacritics,
                    normalizedQueryWithoutDiacritics.Split([' ', ',', '.', '-', '_'], StringSplitOptions.RemoveEmptyEntries));
                
                // Kết hợp kết quả hoặc lấy kết quả duy nhất
                var bm25Result = new BM25Result
                {
                    TfScore = hasVietnameseDiacritics 
                        ? Math.Max(bm25ResultWithDiacritics.TfScore, bm25ResultWithoutDiacritics.TfScore)
                        : bm25ResultWithoutDiacritics.TfScore,
                    ProximityScore = hasVietnameseDiacritics 
                        ? Math.Max(bm25ResultWithDiacritics.ProximityScore, bm25ResultWithoutDiacritics.ProximityScore)
                        : bm25ResultWithoutDiacritics.ProximityScore,
                    TermMatches = hasVietnameseDiacritics 
                        ? Math.Max(bm25ResultWithDiacritics.TermMatches, bm25ResultWithoutDiacritics.TermMatches)
                        : bm25ResultWithoutDiacritics.TermMatches,
                    TotalTerms = hasVietnameseDiacritics 
                        ? bm25ResultWithDiacritics.TotalTerms
                        : bm25ResultWithoutDiacritics.TotalTerms,
                    AdjacentPairs = hasVietnameseDiacritics 
                        ? Math.Max(bm25ResultWithDiacritics.AdjacentPairs, bm25ResultWithoutDiacritics.AdjacentPairs)
                        : bm25ResultWithoutDiacritics.AdjacentPairs
                };
                
                // Tính proximity score nếu cần
                if (bm25Result.TotalTerms > 1)
                {
                    double proximityScoreWithDiacritics = 0;
                    double proximityScoreWithoutDiacritics = 0;
                    
                    if (hasVietnameseDiacritics)
                    {
                        proximityScoreWithDiacritics = CalculateProximityScore(
                            normalizedFullAddressWithDiacritics, 
                            normalizedQueryWithDiacritics.Split([' ', ',', '.', '-', '_'], StringSplitOptions.RemoveEmptyEntries));
                    }
                    
                    proximityScoreWithoutDiacritics = CalculateProximityScore(
                        normalizedFullAddressWithoutDiacritics,
                        normalizedQueryWithoutDiacritics.Split([' ', ',', '.', '-', '_'], StringSplitOptions.RemoveEmptyEntries));
                    
                    bm25Result.ProximityScore = hasVietnameseDiacritics
                        ? Math.Max(proximityScoreWithDiacritics, proximityScoreWithoutDiacritics)
                        : proximityScoreWithoutDiacritics;
                }
                
                // Tính boost factor dựa trên loại khớp
                double boostFactorWithDiacritics = 1.0;
                double boostFactorWithoutDiacritics = 1.0;
                
                if (hasVietnameseDiacritics)
                {
                    boostFactorWithDiacritics = CalculateBoostFactor(
                        normalizedNameWithDiacritics, 
                        normalizedFullAddressWithDiacritics, 
                        normalizedQueryWithDiacritics,
                        result.Keywords != null ? VietnameseAddressPermutator.NormalizeVietnameseAddress(result.Keywords) : null);
                }
                
                boostFactorWithoutDiacritics = CalculateBoostFactor(
                    normalizedNameWithoutDiacritics,
                    normalizedFullAddressWithoutDiacritics,
                    normalizedQueryWithoutDiacritics,
                    result.Keywords != null ? AddressPermutator.NormalizeVietnamese(result.Keywords) : null);
                
                double boostFactor = hasVietnameseDiacritics
                    ? Math.Max(boostFactorWithDiacritics, boostFactorWithoutDiacritics)
                    : boostFactorWithoutDiacritics;
                
                // Tăng thêm boost factor nếu có nhiều cụm từ liền kề
                if (bm25Result.AdjacentPairs > 0)
                {
                    boostFactor += bm25Result.AdjacentRatio * 0.8;
                }
                
                // Kết hợp điểm BM25 với điểm ban đầu
                if (bm25Result.TfScore > 0 || bm25Result.ProximityScore > 0)
                {
                    // Áp dụng trọng số
                    newScore = CombineScores(originalScore, bm25Result, boostFactor);
                    
                    // Gán lại điểm
                    result.Score = newScore;
                    
                    // Log thông tin
                    LogScoringDetails(result, originalScore, bm25Result, boostFactor, newScore, logger);
                }
                else if (boostFactor > 1.0)
                {
                    // Nếu không có TF score nhưng có boost factor
                    result.Score = originalScore * boostFactor;
                }
            }
        }
        
        /// <summary>
        /// Tính toán điểm BM25 cho một text cụ thể với query terms
        /// </summary>
        private static BM25Result CalculateBM25ForText(string normalizedText, string[] queryTerms)
        {
            int termMatches = 0;
            double tfScore = 0;
            int adjacentPairs = 0;
            
            // Hệ số k1 và b tương tự như trong BM25
            double k1 = 1.5;
            double b = 0.75;
            
            // Độ dài chuẩn hóa
            double avgLength = 50; // Giả định độ dài trung bình của địa chỉ là 50 ký tự
            double docLength = normalizedText.Length;
            double lengthRatio = docLength / avgLength;
            
            // Đếm số cụm từ liền kề
            if (queryTerms.Length > 1)
            {
                for (int i = 0; i < queryTerms.Length - 1; i++)
                {
                    string adjacentPair = queryTerms[i] + " " + queryTerms[i + 1];
                    if (normalizedText.Contains(adjacentPair, StringComparison.OrdinalIgnoreCase))
                    {
                        adjacentPairs++;
                    }
                }
            }
            
            foreach (var term in queryTerms)
            {
                if (term.Length < 2) continue; // Bỏ qua từ quá ngắn
                
                // Đếm số lần xuất hiện của term trong địa chỉ
                int termFreq = CountOccurrences(normalizedText, term);
                
                if (termFreq > 0)
                {
                    termMatches++;
                    
                    // Tính TF theo công thức BM25
                    double tf = (termFreq * (k1 + 1)) / (termFreq + k1 * (1 - b + b * lengthRatio));
                    
                    // Giả lập IDF = 1 + log(N/(n+1)) với N cố định là 1000, n là 1
                    double idf = Math.Log(1000.0 / 2.0);
                    
                    tfScore += tf * idf;
                }
            }
            
            return new BM25Result
            {
                TfScore = tfScore,
                TermMatches = termMatches,
                TotalTerms = queryTerms.Length,
                AdjacentPairs = adjacentPairs,
                ProximityScore = 0 // sẽ được tính sau nếu cần
            };
        }
        
        /// <summary>
        /// Tiền xử lý query để chuẩn hóa các định dạng địa chỉ phổ biến
        /// </summary>
        private static string PreprocessQuery(string query)
        {
            // Chuẩn hóa "Đ." hoặc "D." thành "Đường"
            query = Regex.Replace(query, @"\bĐ\.\s*", "Đường ", RegexOptions.IgnoreCase);
            query = Regex.Replace(query, @"\bD\.\s*", "Đường ", RegexOptions.IgnoreCase);
            
            // Chuẩn hóa "Ng." thành "Ngõ"
            query = Regex.Replace(query, @"\bNg\.\s*", "Ngõ ", RegexOptions.IgnoreCase);
            
            // Chuẩn hóa "P." thành "Phường"
            query = Regex.Replace(query, @"\bP\.\s*", "Phường ", RegexOptions.IgnoreCase);
            
            // Chuẩn hóa "Q." thành "Quận"
            query = Regex.Replace(query, @"\bQ\.\s*", "Quận ", RegexOptions.IgnoreCase);
            
            // Chuẩn hóa "Tp." hoặc "TP." thành "Thành phố"
            query = Regex.Replace(query, @"\b(Tp\.|TP\.)\s*", "Thành phố ", RegexOptions.IgnoreCase);
            
            // Chuẩn hóa các ký tự liên tục thành 1 khoảng trắng
            query = Regex.Replace(query, @"\s+", " ");
            
            return query.Trim();
        }
        
        /// <summary>
        /// Tính điểm dựa trên độ gần nhau của các từ (proximity scoring)
        /// </summary>
        private static double CalculateProximityScore(string text, string[] terms)
        {
            if (terms.Length <= 1) return 0;
            
            double proximityScore = 0;
            string textLower = text.ToLower();
            
            // Tính điểm cho cụm từ hoàn chỉnh
            string fullPhrase = string.Join(" ", terms);
            int fullPhraseCount = CountOccurrences(textLower, fullPhrase.ToLower());
            if (fullPhraseCount > 0)
            {
                // Khớp cụm từ đầy đủ được điểm cao nhất
                proximityScore += fullPhraseCount * 10.0;
            }
            
            // Tính điểm cho các cặp từ liền kề
            for (int i = 0; i < terms.Length - 1; i++)
            {
                if (terms[i].Length < 2 || terms[i + 1].Length < 2) continue;
                
                string pair = terms[i].ToLower() + " " + terms[i + 1].ToLower();
                int pairCount = CountOccurrences(textLower, pair);
                
                if (pairCount > 0)
                {
                    // Điểm cho cặp từ liền kề
                    proximityScore += pairCount * 3.0;
                }
            }
            
            // Điểm cho các từ cách nhau không quá 3 từ
            for (int i = 0; i < terms.Length - 2; i++)
            {
                if (terms[i].Length < 2 || terms[i + 2].Length < 2) continue;
                
                var regex = new Regex(
                    $"\\b{Regex.Escape(terms[i].ToLower())}\\b.{{1,30}}\\b{Regex.Escape(terms[i + 2].ToLower())}\\b", 
                    RegexOptions.IgnoreCase);
                
                var matches = regex.Matches(textLower);
                proximityScore += matches.Count * 1.0;
            }
            
            return proximityScore;
        }
        
        /// <summary>
        /// Tính toán hệ số boost dựa trên loại khớp
        /// </summary>
        private static double CalculateBoostFactor(string normalizedName, string normalizedFullAddress, string normalizedQuery, string keywords)
        {
            // Khớp chính xác với tên địa điểm (boost cao nhất)
            if (!string.IsNullOrEmpty(normalizedName) && normalizedName.Equals(normalizedQuery, StringComparison.OrdinalIgnoreCase))
            {
                return 2.0;
            }
            // Khớp với phần đầu của tên địa điểm
            if (!string.IsNullOrEmpty(normalizedName) && normalizedName.StartsWith(normalizedQuery, StringComparison.OrdinalIgnoreCase))
            {
                return 1.8;
            }
            // Tên địa điểm có chứa query
            if (!string.IsNullOrEmpty(normalizedName) && normalizedName.Contains(normalizedQuery, StringComparison.OrdinalIgnoreCase))
            {
                return 1.6;
            }
            // Địa chỉ đầy đủ khớp chính xác
            if (normalizedFullAddress.Equals(normalizedQuery, StringComparison.OrdinalIgnoreCase))
            {
                return 1.5;
            }
            // Địa chỉ đầy đủ bắt đầu bằng query
            if (normalizedFullAddress.StartsWith(normalizedQuery, StringComparison.OrdinalIgnoreCase))
            {
                return 1.4;
            }
            // Địa chỉ đầy đủ chứa query
            if (normalizedFullAddress.Contains(normalizedQuery, StringComparison.OrdinalIgnoreCase))
            {
                return 1.3;
            }
            // Keywords chứa query
            if (!string.IsNullOrEmpty(keywords))
            {
                var normalizedKeywords = VietnameseAddressPermutator.NormalizeVietnameseAddress(keywords);
                if (normalizedKeywords.Contains(normalizedQuery, StringComparison.OrdinalIgnoreCase))
                {
                    return 1.2;
                }
            }
            
            return 1.0;
        }
        
        /// <summary>
        /// Kết hợp các điểm số để tạo ra điểm cuối cùng
        /// </summary>
        private static double CombineScores(double originalScore, BM25Result bm25Result, double boostFactor)
        {
            // Trọng số cho các thành phần
            double originalWeight = 0.2;
            double bm25Weight = 0.4;
            double proximityWeight = 0.4;
            
            // Kết hợp điểm ban đầu với điểm BM25, proximity và matchRatio
            double newScore = (originalScore * originalWeight) + 
                              (bm25Result.TfScore * bm25Weight * bm25Result.MatchRatio) + 
                              (bm25Result.ProximityScore * proximityWeight);
            
            // Áp dụng boost factor
            return newScore * boostFactor;
        }
        
        /// <summary>
        /// Ghi log thông tin điểm số chi tiết
        /// </summary>
        private static void LogScoringDetails<T>(T result, double originalScore, BM25Result bm25Result, 
            double boostFactor, double newScore, ILogger logger) where T : PlacesEs
        {
            // Log thông tin boost để debug
            if (boostFactor >= 1.3 || bm25Result.MatchRatio >= 0.7 || bm25Result.AdjacentRatio > 0)
            {
                logger.LogDebug(
                    "BM25 scoring: PlaceId {PlaceId}, Name '{Name}', Original score {OriginalScore}, " +
                    "TF score {TfScore}, Proximity {ProximityScore}, Match ratio {MatchRatio}, " + 
                    "Adjacent pairs {AdjacentPairs}/{TotalTerms}, BoostFactor {BoostFactor}, Final score {NewScore}",
                    result.PlaceId,
                    result.Name,
                    originalScore,
                    bm25Result.TfScore,
                    bm25Result.ProximityScore,
                    bm25Result.MatchRatio,
                    bm25Result.AdjacentPairs,
                    bm25Result.TotalTerms - 1,
                    boostFactor,
                    newScore);
            }
        }

        /// <summary>
        /// Phương thức đếm số lần xuất hiện của substring trong string
        /// </summary>
        private static int CountOccurrences(string text, string pattern)
        {
            if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(pattern))
                return 0;

            int count = 0;
            int i = 0;
            while ((i = text.IndexOf(pattern, i, StringComparison.OrdinalIgnoreCase)) != -1)
            {
                i += pattern.Length;
                count++;
            }
            return count;
        }
    }
} 