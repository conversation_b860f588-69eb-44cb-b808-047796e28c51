using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using NodaTime;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("default_columns")]
public partial class DefaultColumn
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("key")]
    public string? Key { get; set; }

    [Column("value")]
    public string? Value { get; set; }

    [Column("description")]
    public string? Description { get; set; }

    [Column("create_date")]
    public LocalDateTime CreateDate { get; set; }

    [Column("update_date")]
    public LocalDateTime UpdateDate { get; set; }
}
