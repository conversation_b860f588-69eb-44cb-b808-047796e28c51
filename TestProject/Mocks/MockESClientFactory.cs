using Elastic.Clients.Elasticsearch;
using Elastic.Transport;
using NDS.ETL.BackgroundJobs.Services.ES;
using NDS.ETL.Infrastructure.Utils;

namespace TestProject.Mocks
{
    /// <summary>
    /// Mock implementation of IESClientFactory for testing
    /// </summary>
    public class MockIesClientFactory : IESClientFactory
    {
        private readonly ElasticsearchClient _elasticClient;
        private readonly string _placeIdxName;
        private readonly string _placePairIdxName;
        private readonly string _placeFavIdxName;

        /// <summary>
        /// Constructor that takes ElasticsearchClient and index names
        /// </summary>
        public MockIesClientFactory(
            ElasticsearchClient elasticClient,
            string placeIndexName,
            string placePairIndexName,
            string placeFavIndexName)
        {
            _elasticClient = elasticClient;
            _placeIdxName = placeIndexName;
            _placePairIdxName = placePairIndexName;
            _placeFavIdxName = placeFavIndexName;
        }

        /// <summary>
        /// Constructor that creates a client from ElasticSearchConfig
        /// </summary>
        public MockIesClientFactory(ElasticSearchConfig config)
        {
            _placeIdxName = config.Index.FirstOrDefault(x => x.StartsWith("place")) ?? "place";
            _placePairIdxName = config.Index.FirstOrDefault(x => x.StartsWith("place-pair")) ?? "place-pair";
            _placeFavIdxName = config.Index.FirstOrDefault(x => x.StartsWith("fav-place")) ?? "fav-place";

            var url = config.Url.First();
            var settings = new ElasticsearchClientSettings(new Uri(url))
                .DefaultIndex(_placeIdxName)
                .Authentication(new BasicAuthentication(config.UserName!, config.Password!))
                .ServerCertificateValidationCallback((sender, cert, chain, errors) => true)
                .EnableDebugMode()
                .DisableDirectStreaming()
                .RequestTimeout(TimeSpan.FromSeconds(30));

            _elasticClient = new ElasticsearchClient(settings);
        }

        /// <summary>
        /// Gets the ElasticsearchClient
        /// </summary>
        public ElasticsearchClient GetClient() => _elasticClient;

        /// <summary>
        /// Gets the place index name
        /// </summary>
        public string GetPlaceIndexName() => _placeIdxName;

        /// <summary>
        /// Gets the place pair index name
        /// </summary>
        public string GetPlacePairIndexName() => _placePairIdxName;

        /// <summary>
        /// Gets the favorite places index name
        /// </summary>
        public string GetPlaceFavoriteIndexName() => _placeFavIdxName;
    }
}
