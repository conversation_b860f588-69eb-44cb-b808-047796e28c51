using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Model.API;
using NDS.ETL.Entities.Context;
using NDS.ETL.Entities.PostgresPlace;
using System.Diagnostics;
using Newtonsoft.Json;
using NetTopologySuite.IO;
using NetTopologySuite.Geometries;
using NDS.ETL.Infrastructure.Utils;

namespace NDS.ETL.BackgroundJobs.Services;

/// <summary>
/// Implementation of the wards service
/// </summary>
public class WardsService : IWardsService
{
    private readonly NpgPlaceContext _context;
    private readonly IProvincesService _provincesService;
    private readonly ILogger<WardsService> _logger;
    private readonly ICacheManager _cacheManager;

    // Các khóa cache với tiền tố để tránh xung đột
    private const string WARD_ALL_CACHE_KEY = "wards_all";
    private const string WARD_PAGE_CACHE_PREFIX = "wards_page_";
    private const string WARD_ID_CACHE_PREFIX = "wards_id_";
    private const string WARD_CODE_CACHE_PREFIX = "wards_code_";
    private const string WARD_NAME_CACHE_PREFIX = "wards_name_";
    private const string WARD_NAME_PAGE_CACHE_PREFIX = "wards_name_page_";
    private const string WARD_TYPE_CACHE_PREFIX = "wards_type_";
    private const string WARD_TYPE_PAGE_CACHE_PREFIX = "wards_type_page_";
    
    // Thời gian cache hết hạn (wards hiếm khi thay đổi)
    private static readonly TimeSpan CacheExpirationTime = TimeSpan.FromDays(180);
    
    /// <summary>
    /// Lớp chứa thông tin geometry cho GeoJSON
    /// </summary>
    private class BoundaryGeometry
    {
        public string type { get; set; } = "Polygon";
        public double[][][] coordinates { get; set; } = null!;
    }

    public WardsService(
        NpgPlaceContext context,
        IProvincesService provincesService,
        ILogger<WardsService> logger,
        ICacheManager cacheManager)
    {
        _context = context;
        _provincesService = provincesService;
        _logger = logger;
        _cacheManager = cacheManager;
    }

    /// <summary>
    /// Gets all wards with pagination
    /// </summary>
    public async Task<PagedResult<WardNew>> GetAllWardsAsync(int pageNumber = 1, int pageSize = 10)
    {
        // Kiểm tra tham số phân trang
        if (pageNumber < 1) pageNumber = 1;
        if (pageSize < 1) pageSize = 10;
        if (pageSize > 100) pageSize = 100; // Giới hạn kích thước trang tối đa
        
        // Tạo khóa cache cụ thể cho trang dữ liệu
        string cacheKey = $"{WARD_PAGE_CACHE_PREFIX}{pageNumber}_{pageSize}";
        
        // Kiểm tra dữ liệu trong cache trước
        var cachedResult = _cacheManager.GetFromCache(cacheKey) as PagedResult<WardNew>;
        
        if (cachedResult != null)
        {
            _logger.LogInformation("Cache hit: Retrieved wards page {PageNumber} with size {PageSize} from cache", 
                pageNumber, pageSize);
            return cachedResult;
        }
        
        _logger.LogInformation("Cache miss: Getting wards page {PageNumber} with size {PageSize} from database", 
            pageNumber, pageSize);
        
        // Tính toán số lượng để bỏ qua
        int skip = (pageNumber - 1) * pageSize;
        
        // Lấy tổng số bản ghi để tính số trang
        int totalCount = await _context.WardNews.CountAsync();
        
        // Tính tổng số trang
        int totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
        
        // Lấy dữ liệu từ database theo trang, chỉ chọn các trường cần thiết
        var wards = await _context.WardNews
            .Select(w => new WardNew
            {
                Id = w.Id,
                Code = w.Code,
                Provincecode = w.Provincecode,
                Name = w.Name,
                Type = w.Type,
                Oldcode = w.Oldcode,
                CenterLocation = w.CenterLocation,
                Processed = w.Processed,
                WardsBridges = w.WardsBridges.Select(wb => new WardsBridge
                {
                    WardsNewCode = wb.WardsNewCode,
                    WardsOldCode = wb.WardsOldCode,
                    WardsOldCodeNavigation = wb.WardsOldCodeNavigation != null ? new Ward()
                    {
                        Id = wb.WardsOldCodeNavigation.Id,
                        Code = wb.WardsOldCodeNavigation.Code,
                        Name = wb.WardsOldCodeNavigation.Name,
                        Type = wb.WardsOldCodeNavigation.Type,
                        CenterLocation = wb.WardsOldCodeNavigation.CenterLocation,
                        Processed = wb.WardsOldCodeNavigation.Processed
                    } : null
                }).ToList()
            })
            .OrderBy(w => w.Name)
            .Skip(skip)
            .Take(pageSize)
            .ToListAsync();
            
        // Tạo kết quả phân trang
        var result = new PagedResult<WardNew>
        {
            Items = wards,
            TotalCount = totalCount,
            CurrentPage = pageNumber,
            PageSize = pageSize,
            TotalPages = totalPages
        };
            
        // Lưu vào cache để sử dụng sau này
        _cacheManager.AddToCache(cacheKey, result, CacheExpirationTime);
        _logger.LogInformation("Added wards page {PageNumber} to cache with {Count} wards", 
            pageNumber, wards.Count);
        
        return result;
    }

    /// <summary>
    /// Gets a ward by its ID
    /// </summary>
    public async Task<WardNew?> GetWardByIdAsync(int id)
    {        
        // Tạo khóa cache cụ thể cho ward theo ID
        string cacheKey = $"{WARD_ID_CACHE_PREFIX}{id}";
        
        // Kiểm tra cache trước
        var cachedWard = _cacheManager.GetFromCache(cacheKey) as WardNew;
        
        if (cachedWard != null)
        {
            _logger.LogInformation("Cache hit: Retrieved ward with id {Id} from cache", id);
            return cachedWard;
        }
        
        _logger.LogInformation("Cache miss: Getting ward with id {Id} from database", id);
        
        // Truy vấn database nếu không có trong cache, chỉ chọn các trường cần thiết
        var ward = await _context.WardNews
            .Select(w => new WardNew
            {
                Id = w.Id,
                Code = w.Code,
                Provincecode = w.Provincecode,
                Name = w.Name,
                Type = w.Type,
                Oldcode = w.Oldcode,
                CenterLocation = w.CenterLocation,
                Processed = w.Processed,
                WardsBridges = w.WardsBridges.Select(wb => new WardsBridge
                {
                    WardsNewCode = wb.WardsNewCode,
                    WardsOldCode = wb.WardsOldCode,
                    WardsOldCodeNavigation = wb.WardsOldCodeNavigation != null ? new Ward()
                    {
                        Id = wb.WardsOldCodeNavigation.Id,
                        Code = wb.WardsOldCodeNavigation.Code,
                        Name = wb.WardsOldCodeNavigation.Name,
                        Type = wb.WardsOldCodeNavigation.Type,
                        CenterLocation = wb.WardsOldCodeNavigation.CenterLocation,
                        Processed = wb.WardsOldCodeNavigation.Processed
                    } : null
                }).ToList()
            })
            .FirstOrDefaultAsync(w => w.Id == id);
            
        // Nếu tìm thấy ward, lưu vào cache
        if (ward != null)
        {
            _cacheManager.AddToCache(cacheKey, ward, CacheExpirationTime);
            _logger.LogInformation("Added ward with id {Id} to cache", id);
        }
        
        return ward;
    }

    /// <summary>
    /// Gets a ward by its code
    /// </summary>
    public async Task<WardNew?> GetWardByCodeAsync(string code)
    {
        if (string.IsNullOrEmpty(code))
        {
            _logger.LogWarning("Ward code parameter is null or empty");
            return null;
        }
        
        // Tạo khóa cache cụ thể cho ward theo code
        string cacheKey = $"{WARD_CODE_CACHE_PREFIX}{code.ToLower()}";
        
        // Kiểm tra cache trước
        var cachedWard = _cacheManager.GetFromCache(cacheKey) as WardNew;
        
        if (cachedWard != null)
        {
            _logger.LogInformation("Cache hit: Retrieved ward with code {Code} from cache", code);
            return cachedWard;
        }
        
        _logger.LogInformation("Cache miss: Getting ward with code {Code} from database", code);
        
        // Truy vấn database nếu không có trong cache, chỉ chọn các trường cần thiết
        var ward = await _context.WardNews
            .Select(w => new WardNew
            {
                Id = w.Id,
                Code = w.Code,
                Provincecode = w.Provincecode,
                Name = w.Name,
                Type = w.Type,
                Oldcode = w.Oldcode,
                CenterLocation = w.CenterLocation,
                Processed = w.Processed,
                WardsBridges = w.WardsBridges.Select(wb => new WardsBridge
                {
                    WardsNewCode = wb.WardsNewCode,
                    WardsOldCode = wb.WardsOldCode,
                    WardsOldCodeNavigation = wb.WardsOldCodeNavigation != null ? new Ward()
                    {
                        Id = wb.WardsOldCodeNavigation.Id,
                        Code = wb.WardsOldCodeNavigation.Code,
                        Name = wb.WardsOldCodeNavigation.Name,
                        Type = wb.WardsOldCodeNavigation.Type,
                        CenterLocation = wb.WardsOldCodeNavigation.CenterLocation,
                        Processed = wb.WardsOldCodeNavigation.Processed
                    } : null
                }).ToList()
            })
            .FirstOrDefaultAsync(w => w.Code != null && w.Code.ToLower() == code.ToLower());
            
        // Nếu tìm thấy ward, lưu vào cache
        if (ward != null)
        {
            _cacheManager.AddToCache(cacheKey, ward, CacheExpirationTime);
            _logger.LogInformation("Added ward with code {Code} to cache", code);
        }
        
        return ward;
    }

    /// <summary>
    /// Gets wards by name with pagination
    /// </summary>
    public async Task<PagedResult<WardNew>> GetWardsByNameAsync(string name, int pageNumber = 1, int pageSize = 10)
    {
        // Kiểm tra tham số đầu vào
        if (string.IsNullOrEmpty(name))
        {
            _logger.LogWarning("Search name parameter is null or empty");
            return new PagedResult<WardNew>
            {
                Items = new List<WardNew>(),
                TotalCount = 0,
                CurrentPage = pageNumber,
                PageSize = pageSize,
                TotalPages = 0
            };
        }
        
        // Kiểm tra tham số phân trang
        if (pageNumber < 1) pageNumber = 1;
        if (pageSize < 1) pageSize = 10;
        if (pageSize > 100) pageSize = 100; // Giới hạn kích thước trang tối đa
        
        // Tạo khóa cache cho tìm kiếm theo tên với phân trang
        string cacheKey = $"{WARD_NAME_PAGE_CACHE_PREFIX}{name.ToLower()}_{pageNumber}_{pageSize}";
        
        // Kiểm tra cache trước
        var cachedResult = _cacheManager.GetFromCache(cacheKey) as PagedResult<WardNew>;
        
        if (cachedResult != null)
        {
            _logger.LogInformation("Cache hit: Retrieved wards by name: {Name} page {PageNumber} from cache", 
                name, pageNumber);
            return cachedResult;
        }
        
        _logger.LogInformation("Cache miss: Searching wards by name: {Name} page {PageNumber} from database", 
            name, pageSize);

        // Tạo truy vấn cơ sở để đếm tổng số kết quả mà không lấy các trường lớn
        var countQuery = _context.WardNews
            .Where(w => (w.Name != null &&
                         EF.Functions.Like(w.Name.ToLower(), $"%{name.ToLower()}%")) ||
                        (w.WardsBridges.Any(wb => wb.WardsOldCodeNavigation != null &&
                                                 wb.WardsOldCodeNavigation.Name != null &&
                                                 EF.Functions.Like(wb.WardsOldCodeNavigation.Name.ToLower(), $"%{name.ToLower()}%"))));
                                                 
        // Đếm tổng số kết quả
        int totalCount = await countQuery.CountAsync();
        
        // Tính tổng số trang
        int totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
        
        // Tính toán số lượng để bỏ qua
        int skip = (pageNumber - 1) * pageSize;
        
        // Tạo truy vấn để lấy dữ liệu, chỉ chọn các trường cần thiết
        var dataQuery = _context.WardNews
            .Where(w => (w.Name != null &&
                         EF.Functions.Like(w.Name.ToLower(), $"%{name.ToLower()}%")) ||
                        (w.WardsBridges.Any(wb => wb.WardsOldCodeNavigation != null &&
                                                 wb.WardsOldCodeNavigation.Name != null &&
                                                 EF.Functions.Like(wb.WardsOldCodeNavigation.Name.ToLower(), $"%{name.ToLower()}%"))));
        
        // Lấy dữ liệu từ database theo trang và sắp xếp, chỉ chọn các trường cần thiết
        var wards = await dataQuery
            .Select(w => new WardNew
            {
                Id = w.Id,
                Code = w.Code,
                Provincecode = w.Provincecode,
                Name = w.Name,
                Type = w.Type,
                Oldcode = w.Oldcode,
                CenterLocation = w.CenterLocation,
                Processed = w.Processed,
                WardsBridges = w.WardsBridges.Select(wb => new WardsBridge
                {
                    WardsNewCode = wb.WardsNewCode,
                    WardsOldCode = wb.WardsOldCode,
                    WardsOldCodeNavigation = wb.WardsOldCodeNavigation != null ? new Ward()
                    {
                        Id = wb.WardsOldCodeNavigation.Id,
                        Code = wb.WardsOldCodeNavigation.Code,
                        Name = wb.WardsOldCodeNavigation.Name,
                        Type = wb.WardsOldCodeNavigation.Type,
                        CenterLocation = wb.WardsOldCodeNavigation.CenterLocation,
                        Processed = wb.WardsOldCodeNavigation.Processed
                    } : null
                }).ToList()
            })
            .OrderBy(w => w.Name)
            .Skip(skip)
            .Take(pageSize)
            .ToListAsync();

        // Tạo kết quả phân trang
        var result = new PagedResult<WardNew>
        {
            Items = wards,
            TotalCount = totalCount,
            CurrentPage = pageNumber,
            PageSize = pageSize,
            TotalPages = totalPages
        };
            
        // Lưu kết quả vào cache
        _cacheManager.AddToCache(cacheKey, result, CacheExpirationTime);
        _logger.LogInformation("Added search results for name: {Name} page {PageNumber} to cache with {Count} wards", 
            name, pageNumber, wards.Count);
        
        return result;
    }

    /// <summary>
    /// Gets wards by type with pagination
    /// </summary>
    public async Task<PagedResult<WardNew>> GetWardsByTypeAsync(string type, int pageNumber = 1, int pageSize = 10)
    {
        // Kiểm tra tham số đầu vào
        if (string.IsNullOrEmpty(type))
        {
            _logger.LogWarning("Type parameter is null or empty");
            return new PagedResult<WardNew>
            {
                Items = new List<WardNew>(),
                TotalCount = 0,
                CurrentPage = pageNumber,
                PageSize = pageSize,
                TotalPages = 0
            };
        }
        
        // Kiểm tra tham số phân trang
        if (pageNumber < 1) pageNumber = 1;
        if (pageSize < 1) pageSize = 10;
        if (pageSize > 100) pageSize = 100; // Giới hạn kích thước trang tối đa
        
        // Tạo khóa cache cho tìm kiếm theo loại với phân trang
        string cacheKey = $"{WARD_TYPE_PAGE_CACHE_PREFIX}{type.ToLower()}_{pageNumber}_{pageSize}";
        
        // Kiểm tra cache trước
        var cachedResult = _cacheManager.GetFromCache(cacheKey) as PagedResult<WardNew>;
        
        if (cachedResult != null)
        {
            _logger.LogInformation("Cache hit: Retrieved wards by type: {Type} page {PageNumber} from cache", 
                type, pageNumber);
            return cachedResult;
        }
        
        _logger.LogInformation("Cache miss: Getting wards by type: {Type} page {PageNumber} from database", 
            type, pageNumber);
        
        // Tạo truy vấn cơ sở
        var query = _context.WardNews
            .Where(w => w.Type != null && 
                    EF.Functions.Like(w.Type.ToLower(), $"%{type.ToLower()}%"));
        
        // Đếm tổng số kết quả
        int totalCount = await query.CountAsync();
        
        // Tính tổng số trang
        int totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
        
        // Tính toán số lượng để bỏ qua
        int skip = (pageNumber - 1) * pageSize;
        
        // Lấy dữ liệu từ database theo trang và sắp xếp, chỉ chọn các trường cần thiết
        var wards = await query
            .Select(w => new WardNew
            {
                Id = w.Id,
                Code = w.Code,
                Provincecode = w.Provincecode,
                Name = w.Name,
                Type = w.Type,
                Oldcode = w.Oldcode,
                CenterLocation = w.CenterLocation,
                Processed = w.Processed,
                WardsBridges = w.WardsBridges.Select(wb => new WardsBridge
                {
                    WardsNewCode = wb.WardsNewCode,
                    WardsOldCode = wb.WardsOldCode,
                    WardsOldCodeNavigation = wb.WardsOldCodeNavigation != null ? new Ward()
                    {
                        Id = wb.WardsOldCodeNavigation.Id,
                        Code = wb.WardsOldCodeNavigation.Code,
                        Name = wb.WardsOldCodeNavigation.Name,
                        Type = wb.WardsOldCodeNavigation.Type,
                        CenterLocation = wb.WardsOldCodeNavigation.CenterLocation,
                        Processed = wb.WardsOldCodeNavigation.Processed
                    } : null
                }).ToList()
            })
            .OrderBy(w => w.Name)
            .Skip(skip)
            .Take(pageSize)
            .ToListAsync();
        
        // Tạo kết quả phân trang
        var result = new PagedResult<WardNew>
        {
            Items = wards,
            TotalCount = totalCount,
            CurrentPage = pageNumber,
            PageSize = pageSize,
            TotalPages = totalPages
        };
            
        // Lưu kết quả vào cache
        _cacheManager.AddToCache(cacheKey, result, CacheExpirationTime);
        _logger.LogInformation("Added wards with type: {Type} page {PageNumber} to cache with {Count} wards", 
            type, pageNumber, wards.Count);
        
        return result;
    }
    
    /// <summary>
    /// Invalidates all ward related caches
    /// </summary>
    public void InvalidateAllWardCache()
    {
        _logger.LogInformation("Invalidating all ward caches");
        
        // Xóa tất cả các cache có tiền tố wards
        _cacheManager.ClearCacheByPrefix("wards_");
        
        _logger.LogInformation("All ward caches have been invalidated");
    }

    /// <summary>
    /// Gets ward by location coordinates using ST_Contains function
    /// </summary>
    public async Task<WardNew?> GetWardByLocationAsync(double latitude, double longitude, string? currentAddress = null)
    {
        // Kiểm tra tham số tọa độ hợp lệ
        if (latitude < -90 || latitude > 90)
        {
            _logger.LogWarning("Invalid latitude value: {Latitude}. Must be between -90 and 90", latitude);
            return null;
        }
        
        if (longitude < -180 || longitude > 180)
        {
            _logger.LogWarning("Invalid longitude value: {Longitude}. Must be between -180 and 180", longitude);
            return null;
        }

        // Tạo khóa cache cho tọa độ này (làm tròn đến 6 chữ số thập phân để tối ưu cache)
        string locationKey = $"{Math.Round(latitude, 6)}_{Math.Round(longitude, 6)}";
        string cacheKey = $"ward_location_{locationKey}";
        
        // Kiểm tra cache trước
        var cachedWard = _cacheManager.GetFromCache(cacheKey) as WardNew;
        
        if (cachedWard != null)
        {
            _logger.LogInformation("Cache hit: Retrieved ward by location ({Latitude}, {Longitude}) from cache", 
                latitude, longitude);
            return cachedWard;
        }
        
        _logger.LogInformation("Cache miss: Finding ward by location ({Latitude}, {Longitude}) from database. Current address: {CurrentAddress}", 
            latitude, longitude, currentAddress ?? "not provided");
        
        try
        {
            // Sử dụng Entity Framework với PostGIS để tìm ward chứa tọa độ này
            // Tương đương với SQL: SELECT * FROM ward_new WHERE ST_Contains(boundary_geom, ST_SetSRID(ST_MakePoint(longitude, latitude), 4326))
            var ward = await _context.WardNews
                .Where(w => w.BoundaryGeom != null && 
                           w.BoundaryGeom.Contains(new NetTopologySuite.Geometries.Point(longitude, latitude) { SRID = 4326 }))
                .Select(w => new WardNew
                {
                    Id = w.Id,
                    Code = w.Code,
                    Provincecode = w.Provincecode,
                    Name = w.Name,
                    Type = w.Type,
                    Oldcode = w.Oldcode,
                    CenterLocation = w.CenterLocation,
                    Processed = w.Processed,
                    WardsBridges = w.WardsBridges.Select(wb => new WardsBridge
                    {
                        WardsNewCode = wb.WardsNewCode,
                        WardsOldCode = wb.WardsOldCode,
                        WardsOldCodeNavigation = wb.WardsOldCodeNavigation != null ? new Ward()
                        {
                            Id = wb.WardsOldCodeNavigation.Id,
                            Code = wb.WardsOldCodeNavigation.Code,
                            Name = wb.WardsOldCodeNavigation.Name,
                            Type = wb.WardsOldCodeNavigation.Type,
                            CenterLocation = wb.WardsOldCodeNavigation.CenterLocation,
                            Processed = wb.WardsOldCodeNavigation.Processed
                        } : null
                    }).ToList()
                })
                .FirstOrDefaultAsync();
            
            // Nếu tìm thấy ward, lưu vào cache
            if (ward != null)
            {
                _cacheManager.AddToCache(cacheKey, ward, CacheExpirationTime);
                _logger.LogInformation("Found and cached ward: {WardName} (ID: {WardId}, Code: {WardCode}) for location ({Latitude}, {Longitude})", 
                    ward.Name, ward.Id, ward.Code, latitude, longitude);
            }
            else
            {
                _logger.LogWarning("No ward found for location ({Latitude}, {Longitude}). Current address was: {CurrentAddress}", 
                    latitude, longitude, currentAddress ?? "not provided");
            }

            return ward;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error finding ward by location ({Latitude}, {Longitude}): {Error}", 
                latitude, longitude, ex.Message);
            throw;
        }
    }
    
    /// <summary>
    /// Lấy phường/xã mới và địa chỉ cập nhật dựa trên tọa độ
    /// </summary>
    /// <param name="latitude">Vĩ độ</param>
    /// <param name="longitude">Kinh độ</param>
    /// <param name="currentAddress">Địa chỉ hiện tại (tùy chọn)</param>
    /// <returns>Phường/xã mới và địa chỉ đã được cập nhật</returns>
    public async Task<(WardNew? Ward, string? CurrentAddress)> GetNewAddressByLocationAsync(double latitude, double longitude, string? currentAddress = null)
    {
        // Kiểm tra tham số tọa độ hợp lệ
        if (latitude < -90 || latitude > 90)
        {
            _logger.LogWarning("Invalid latitude value: {Latitude}. Must be between -90 and 90", latitude);
            return (null, currentAddress);
        }
        
        if (longitude < -180 || longitude > 180)
        {
            _logger.LogWarning("Invalid longitude value: {Longitude}. Must be between -180 and 180", longitude);
            return (null, currentAddress);
        }

        // Tạo khóa cache cho tọa độ này (làm tròn đến 6 chữ số thập phân để tối ưu cache)
        string locationKey = $"{Math.Round(latitude, 6)}_{Math.Round(longitude, 6)}";
        string cacheKey = $"ward_location_{locationKey}";
        
        // Kiểm tra cache trước
        var cachedWard = _cacheManager.GetFromCache(cacheKey) as WardNew;
        
        if (cachedWard != null)
        {
            _logger.LogInformation("Cache hit: Retrieved ward by location ({Latitude}, {Longitude}) from cache", 
                latitude, longitude);
            
            // Xử lý địa chỉ
            var updatedAddress = await ProcessAddressAsync(cachedWard, currentAddress);
            return (cachedWard, updatedAddress);
        }
        
        _logger.LogInformation("Cache miss: Finding ward by location ({Latitude}, {Longitude}) from database. Current address: {CurrentAddress}", 
            latitude, longitude, currentAddress ?? "not provided");
        
        try
        {
            // Sử dụng Entity Framework với PostGIS để tìm ward chứa tọa độ này
            // Tương đương với SQL: SELECT * FROM ward_new WHERE ST_Contains(boundary_geom, ST_SetSRID(ST_MakePoint(longitude, latitude), 4326))
            var ward = await _context.WardNews
                .Where(w => w.BoundaryGeom != null && 
                           w.BoundaryGeom.Contains(new NetTopologySuite.Geometries.Point(longitude, latitude) { SRID = 4326 }))
                .Select(w => new WardNew
                {
                    Id = w.Id,
                    Code = w.Code,
                    Provincecode = w.Provincecode,
                    Name = w.Name,
                    Type = w.Type,
                    Oldcode = w.Oldcode,
                    CenterLocation = w.CenterLocation,
                    Processed = w.Processed,
                    WardsBridges = w.WardsBridges.Select(wb => new WardsBridge
                    {
                        WardsNewCode = wb.WardsNewCode,
                        WardsOldCode = wb.WardsOldCode,
                        WardsOldCodeNavigation = wb.WardsOldCodeNavigation != null ? new Ward()
                        {
                            Id = wb.WardsOldCodeNavigation.Id,
                            Code = wb.WardsOldCodeNavigation.Code,
                            Name = wb.WardsOldCodeNavigation.Name,
                            Type = wb.WardsOldCodeNavigation.Type,
                            CenterLocation = wb.WardsOldCodeNavigation.CenterLocation,
                            Processed = wb.WardsOldCodeNavigation.Processed
                        } : null
                    }).ToList()
                })
                .FirstOrDefaultAsync();
            
            // Nếu tìm thấy ward, lưu vào cache
            if (ward != null)
            {
                _cacheManager.AddToCache(cacheKey, ward, CacheExpirationTime);
                _logger.LogInformation("Found and cached ward: {WardName} (ID: {WardId}, Code: {WardCode}) for location ({Latitude}, {Longitude})", 
                    ward.Name, ward.Id, ward.Code, latitude, longitude);
                
                // Xử lý địa chỉ
                var updatedAddress = await ProcessAddressAsync(ward, currentAddress);
                return (ward, updatedAddress);
            }
            else
            {
                _logger.LogWarning("No ward found for location ({Latitude}, {Longitude}). Current address was: {CurrentAddress}", 
                    latitude, longitude, currentAddress ?? "not provided");
                return (null, currentAddress);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error finding ward by location ({Latitude}, {Longitude}): {Error}", 
                latitude, longitude, ex.Message);
            throw;
        }
    }
    
    /// <summary>
    /// Xử lý địa chỉ dựa trên phường/xã mới
    /// </summary>
    /// <param name="ward">Phường/xã tìm được</param>
    /// <param name="currentAddress">Địa chỉ hiện tại</param>
    /// <returns>Địa chỉ đã được cập nhật</returns>
    private async Task<string?> ProcessAddressAsync(WardNew? ward, string? currentAddress)
    {
        if (string.IsNullOrWhiteSpace(currentAddress))
        {
            return null;
        }
        
        // Chuyển đổi địa chỉ về dạng chuẩn
        var normalizedAddress = currentAddress.ConvertToSortAddress();
        if (string.IsNullOrWhiteSpace(normalizedAddress))
        {
            return currentAddress;
        }
        
        _logger.LogInformation("Processing address: {CurrentAddress}", normalizedAddress);
        
        var newWardName = ward?.Name ?? "Unknown Ward";
        var newProvince = await _provincesService.GetProvinceByCodeAsync(ward?.Provincecode ?? "unknown");
        var provinceName = newProvince?.Name ?? "Unknown Province";

        // Tách địa chỉ hiện tại thành các phần
        var addressParts = normalizedAddress.Split(',').Select(part => part.Trim()).ToArray();

        if (addressParts.Length >= 3)
        {
            // Cắt bỏ 3 thành phần cuối (ward, district, city) và thay thế bằng 2 thành phần mới
            var keepParts = addressParts.Take(addressParts.Length - 3).ToList();

            // Thêm ward name và province name mới
            keepParts.Add(newWardName);
            keepParts.Add(provinceName);

            // Tạo địa chỉ mới
            var updatedAddress = string.Join(", ", keepParts);
            _logger.LogInformation("Updated address by replacing last 3 components with new ward and province: {UpdatedAddress}", updatedAddress);
            return updatedAddress;
        }
        else
        {
            // Nếu địa chỉ có ít hơn 3 phần, chỉ thêm ward và province mới vào cuối
            var allParts = addressParts.ToList();
            allParts.Add(newWardName);
            allParts.Add(provinceName);

            var updatedAddress = string.Join(", ", allParts);
            _logger.LogWarning("Address has less than 3 components, appended new ward and province: {UpdatedAddress}", updatedAddress);
            return updatedAddress;
        }
    }

    /// <summary>
    /// Phương thức hỗ trợ giảm số lượng dấu ngoặc vuông từ boundary JSON string xuống mức 3
    /// </summary>
    /// <param name="boundaryJson">Chuỗi JSON boundary cần giảm bracket</param>
    /// <param name="currentBracketCount">Số lượng bracket hiện tại</param>
    /// <returns>Chuỗi JSON đã được giảm xuống 3 bracket hoặc null nếu không thể xử lý</returns>
    private string? ReduceBracketsToThree(string boundaryJson, int currentBracketCount)
    {
        if (currentBracketCount <= 3)
            return boundaryJson;

        try
        {
            // Số lần cần giảm bracket
            int reductionLevels = currentBracketCount - 3;
            
            _logger.LogDebug("Reducing {ReductionLevels} bracket levels from {CurrentCount} to 3", 
                reductionLevels, currentBracketCount);

            // Deserialize thành object động
            object? currentData = JsonConvert.DeserializeObject(boundaryJson);
            
            // Giảm dần từng cấp bracket
            for (int level = 0; level < reductionLevels && currentData != null; level++)
            {
                // Kiểm tra xem dữ liệu hiện tại có phải là mảng không
                if (currentData is Newtonsoft.Json.Linq.JArray jArray && jArray.Count > 0)
                {
                    // Lấy phần tử đầu tiên của mảng
                    currentData = jArray[0];
                }
                else
                {
                    _logger.LogWarning("Cannot reduce bracket level {Level}, data is not an array or is empty", level);
                    return null;
                }
            }

            // Serialize lại thành JSON string
            return JsonConvert.SerializeObject(currentData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reducing bracket count from {CurrentCount} to 3", currentBracketCount);
            return null;
        }
    }

    /// <summary>
    /// Chuyển đổi dữ liệu boundary từ format cũ (3 mảng) sang format mới (4 mảng)
    /// </summary>
    public async Task ConvertBoundaryFormatAsync()
    {
        try
        {
            var begin = Stopwatch.GetTimestamp();
            _logger.LogInformation("ConvertBoundaryFormatAsync DoWorkAsync started");

            try
            {
                int skip = 0, take = 1000;
                int count = 0;
                int converted = 0;
                int skipped = 0;
                int reducedBrackets = 0;

                while (true)
                {
                    var t1 = Stopwatch.GetTimestamp();
                    // Lấy các ward có boundary không null nhưng chưa có boundary_geom
                    var data = await _context.WardNews
                        .Where(m => m.Boundary != null && m.BoundaryGeom == null)
                        .OrderBy(m => m.Id)
                        .Skip(skip)
                        .Take(take)
                        .ToListAsync();

                    if (!data.Any()) break;

                    // Xử lý từng ward trong batch
                    for (int i = 0; i < data.Count; i++)
                    {
                        try
                        {
                            if (!string.IsNullOrWhiteSpace(data[i].Boundary))
                            {
                                // Kiểm tra format của dữ liệu boundary hiện tại
                                var boundaryText = data[i].Boundary.Trim();
                                
                                // Đếm số lượng dấu [ ở đầu để xác định format
                                int bracketCount = 0;
                                for (int j = 0; j < boundaryText.Length && boundaryText[j] == '['; j++)
                                {
                                    bracketCount++;
                                }

                                // Nếu bracket count > 3, giảm xuống 3 trước khi xử lý
                                if (bracketCount > 3)
                                {
                                    _logger.LogDebug("Ward {WardId} has {BracketCount} brackets, reducing to 3", 
                                        data[i].Id, bracketCount);
                                    
                                    var reducedBoundary = ReduceBracketsToThree(boundaryText, bracketCount);
                                    if (reducedBoundary != null)
                                    {
                                        boundaryText = reducedBoundary;
                                        bracketCount = 3; // Sau khi giảm sẽ là 3 bracket
                                        reducedBrackets++;
                                        _logger.LogDebug("Successfully reduced brackets for ward {WardId}", data[i].Id);
                                    }
                                    else
                                    {
                                        _logger.LogWarning("Failed to reduce brackets for ward {WardId}, skipping", data[i].Id);
                                        continue;
                                    }
                                }

                                if (bracketCount == 3)
                                {
                                    // Format cũ (3 mảng) - cần chuyển đổi
                                    _logger.LogDebug("Processing ward {WardId} with old format (3 arrays)", data[i].Id);
                                    
                                    var deserializedCoordinates = JsonConvert.DeserializeObject<double[][][]>(boundaryText);

                                    if (deserializedCoordinates != null && deserializedCoordinates.Length > 0)
                                    {
                                        var arr = deserializedCoordinates[0];
                                        if (arr[arr.Length - 1][0] != arr[0][0] && arr[arr.Length - 1][1] != arr[0][1])
                                        {
                                            // New row to add
                                            double[] newRow = new double[] { arr[0][0], arr[0][1] };

                                            // Resize the jagged array
                                            Array.Resize(ref arr, arr.Length + 1);
                                            arr[arr.Length - 1] = newRow;

                                            //
                                            deserializedCoordinates[0] = arr;

                                            //
                                            var reSerializedCoordinates = JsonConvert.SerializeObject(deserializedCoordinates);
                                            data[i].Boundary = reSerializedCoordinates;
                                            data[i].BoundaryGeom = new NetTopologySuite.IO.GeoJsonReader().Read<Polygon>(data[i].Boundary);
                                        }
                                        else if (data[i].BoundaryGeom == null)
                                        {
                                            var formatBoundary = JsonConvert.SerializeObject(new BoundaryGeometry()
                                            {
                                                type = "Polygon",
                                                coordinates = deserializedCoordinates
                                            });
                                            data[i].BoundaryGeom = new NetTopologySuite.IO.GeoJsonReader().Read<Polygon>(formatBoundary);
                                        }
                                    }
                                }                                
                                else
                                {
                                    // Format không xác định
                                    _logger.LogWarning("Ward {WardId} has unrecognized boundary format with {BracketCount} opening brackets", 
                                        data[i].Id, bracketCount);
                                }
                            }
                        }
                        catch (JsonException jsonEx)
                        {
                            _logger.LogError(jsonEx, "JSON deserialization error for ward {WardId}: {Error}", 
                                data[i].Id, jsonEx.Message);
                            // Tiếp tục với ward tiếp theo
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error processing ward {WardId}: {Error}", 
                                data[i].Id, ex.Message);
                            // Tiếp tục với ward tiếp theo
                        }
                    }

                    // Cập nhật dữ liệu vào database
                    _context.WardNews.UpdateRange(data);
                    await _context.SaveChangesAsync();

                    count += data.Count;
                    skip += take;
                    
                    var t2 = Stopwatch.GetTimestamp();
                    _logger.LogInformation(
                        "ConvertBoundaryFormatAsync: Processed {BatchSize} wards, total processed: {TotalCount}, converted: {Converted}, skipped: {Skipped}, reduced brackets: {ReducedBrackets} - Time: {ElapsedSeconds} seconds",
                        data.Count, count, converted, skipped, reducedBrackets, TimeSpan.FromTicks(t2 - t1).TotalSeconds);
                }

                var end = Stopwatch.GetTimestamp();
                _logger.LogInformation(
                    "ConvertBoundaryFormatAsync completed: Total {TotalCount} wards processed, {ConvertedCount} converted from old format, {SkippedCount} already in new format, {ReducedBracketsCount} had brackets reduced in {TotalSeconds} seconds",
                    count, converted, skipped, reducedBrackets, TimeSpan.FromTicks(end - begin).TotalSeconds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ConvertBoundaryFormatAsync inner try block");
                throw;
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in ConvertBoundaryFormatAsync: {Message}", e.Message);
            throw;
        }
    }
}