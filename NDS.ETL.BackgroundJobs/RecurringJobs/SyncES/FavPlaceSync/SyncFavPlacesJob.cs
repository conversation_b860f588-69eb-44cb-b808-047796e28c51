using Elastic.Clients.Elasticsearch;
using KnowledgeBase.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch.FavPlaceSync;
using NDS.ETL.Entities.Context;
using NDS.ETL.Infrastructure.Common;
using NDS.ETL.Infrastructure.Utils;
using System.Diagnostics;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.FavPlaceSync;

/// <summary>
/// Job đồng bộ dữ liệu FavoritePlace từ PostgreSQL sang Elasticsearch
/// </summary>
public class SyncFavPlacesJob : RecurringJobBaseElastic
{
    private readonly ILogger<SyncFavPlacesJob> _logger;
    private readonly IScopedResolver<NpgPlaceContext> _dbContextResolver;
    private readonly ElasticsearchClient _elasticClient;
    private readonly IDistributedLock _distributedLock;
    private readonly ElasticsearchDataService _esService;
    private readonly DbDataService _dbService;
    private readonly SyncStrategyService _strategyService;
    private readonly SyncProcessor _syncProcessor;
    
    // Các hằng số cấu hình
    private const int PAGE_SIZE = 50_000;
    private const int COMMAND_TIMEOUT = 3000;
    private const string LOCK_KEY = "sync_fav_places_lock";
    private const int LOCK_TIMEOUT_SECONDS = 3600; // 1 giờ
    private const int LOCK_STALE_THRESHOLD_MINUTES = 120; // 2 giờ
    
    private readonly string _indexName;

    public SyncFavPlacesJob(
        IConfiguration configuration,
        ILogger<SyncFavPlacesJob> logger,
        ElasticsearchClient elasticClient,
        IScopedResolver<NpgPlaceContext> dbContextResolver,
        IDistributedLock distributedLock) : base(configuration, logger, elasticClient, null)
    {
        _logger = logger;
        _dbContextResolver = dbContextResolver;
        _elasticClient = elasticClient;
        _distributedLock = distributedLock;
        
        _indexName = GetIndexName(configuration);
        
        // Khởi tạo các service
        _esService = new ElasticsearchDataService(logger, elasticClient, _indexName);
        _dbService = new DbDataService(logger);
        _strategyService = new SyncStrategyService(logger, dbContextResolver);
        _syncProcessor = new SyncProcessor(
            logger, 
            _esService, 
            _dbService, 
            elasticClient, 
            _indexName,
            dbContextResolver,
            this);  // Truyền this để SyncProcessor có thể sử dụng IndexManyDocumentsAsync
    }

    private string GetIndexName(IConfiguration configuration)
    {
        var indexNames = configuration.Bind<ElasticSearchConfig>(nameof(ElasticSearchConfig));
        return indexNames.Index.FirstOrDefault(x => x.Contains("fav-place")) ?? "fav-place";
    }

    public override string JobId => "SyncFavPlaces";
    public override string ConfigSection => "JobPgSyncPlace";

    public override async Task DoWorkAsync(CancellationToken stoppingToken)
    {
        // Kiểm tra xem job có đang chạy không
        var lockAcquired = false;
        try
        {
            _logger.LogInformation("Attempting to acquire lock for FavPlace sync");
            
            // Cố gắng lấy khóa với timeout
            lockAcquired = await _distributedLock.TryAcquireLockAsync(
                LOCK_KEY, 
                TimeSpan.FromSeconds(LOCK_TIMEOUT_SECONDS));
            
            if (!lockAcquired)
            {
                _logger.LogWarning("Could not acquire lock - another sync job is already running");
                return;
            }
            
            _logger.LogInformation("Lock acquired. Starting FavPlace sync");
            
            // Tiếp tục thực hiện job khi đã lấy được khóa
            try {
                await RunSynchronizationJob(stoppingToken);
            }
            catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("Job canceled");
            }
        }
        catch (Exception ex)
        {
            if (ex is OperationCanceledException && stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("FavPlace sync job canceled");
            }
            else
            {
                _logger.LogError(ex, "Error in FavPlace sync job");
                throw;
            }
        }
        finally
        {
            // Đảm bảo giải phóng khóa khi job kết thúc
            if (lockAcquired)
            {
                try
                {
                    await _distributedLock.ReleaseLockAsync(LOCK_KEY);
                    _logger.LogInformation("Lock released for FavPlace sync");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error releasing lock for FavPlace sync");
                }
            }
        }
    }

    private async Task RunSynchronizationJob(CancellationToken stoppingToken)
    {
        var globalStopwatch = Stopwatch.StartNew();
        _logger.LogInformation("Starting FavPlace sync to ElasticSearch");

        var syncStats = new SyncStats();
        var maxRetries = 3;
        var syncLock = new object(); // Thread-safe statistics collection

        try
        {
            await _dbContextResolver.ResolveAsync(async (dbContext) =>
            {
                dbContext.Database.SetCommandTimeout(COMMAND_TIMEOUT);

                // Lấy thông tin từ cơ sở dữ liệu
                var dbStats = await _dbService.GetDatabaseStats(dbContext, stoppingToken);
                _logger.LogInformation($"DB records: {dbStats.TotalRecords}, max UpdatedAt: {dbStats.MaxUpdatedAt:yyyy-MM-dd HH:mm:ss.fff}");
                if (dbStats.NewRecordsCount > 0)
                {
                    _logger.LogInformation($"DB new records: {dbStats.NewRecordsCount}, max CreateAt: {dbStats.MaxCreateAt:yyyy-MM-dd HH:mm:ss.fff}");
                    _logger.LogInformation($"📝 New records definition: Records that need to be created in ES");
                }

                // Lấy thông tin từ Elasticsearch
                var esStats = new ElasticsearchStats
                {
                    MaxId = await _esService.GetMaxIdFromElasticsearch(stoppingToken) ?? 0,
                    IndexExists = (await _esService.GetMaxIdFromElasticsearch(stoppingToken)).HasValue,
                    MaxUpdatedAt = await _esService.GetLastSyncTimeFromEs(stoppingToken),
                    MaxCreateAt = await _esService.GetLastCreateTimeFromEs(stoppingToken),
                    IndexInfo = await _esService.GetIndexStats(stoppingToken)
                };
                
                if (esStats.IndexExists)
                {
                    _logger.LogInformation($"ES max UpdatedAt: {esStats.MaxUpdatedAt:yyyy-MM-dd HH:mm:ss.fff}");
                    if (esStats.MaxCreateAt != default)
                    {
                        _logger.LogInformation($"ES max CreateAt: {esStats.MaxCreateAt:yyyy-MM-dd HH:mm:ss.fff}");
                    }
                }
                else
                {
                    _logger.LogInformation($"ES index '{_indexName}' doesn't exist");
                }
                
                // Quyết định chiến lược đồng bộ
                var syncStrategy = _strategyService.DetermineSyncStrategy(dbStats, esStats, stoppingToken);
                
                // Thực hiện đồng bộ theo chiến lược đã chọn
                if (syncStrategy.IsFullSync)
                {
                    _logger.LogInformation($"🔄 Performing FULL sync of FavPlace data. Reason: {syncStrategy.Reason}");
                    
                    try {
                        await _syncProcessor.PerformFullSync(
                            dbContext, 
                            dbStats.MaxId, 
                            dbStats.TotalRecords, 
                            syncLock, 
                            stoppingToken, 
                            syncStats, 
                            maxRetries,
                            PAGE_SIZE);
                    }
                    catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested) {
                        _logger.LogInformation("Full sync canceled");
                        return true;
                    }
                }
                else if (syncStrategy.RecordsToSync > 0)
                {
                    _logger.LogInformation($"⚡ Performing INCREMENTAL sync of FavPlace data. Reason: {syncStrategy.Reason}");
                    _logger.LogInformation($"📊 Sync details: {syncStrategy.RecordsToSync:N0} records to process");
                    
                    try {
                        // Thực hiện đồng bộ theo 2 bước: Đầu tiên cho các bản ghi mới, sau đó cho các bản ghi đã cập nhật
                        if (dbStats.NewRecordsCount > 0)
                        {
                            _logger.LogInformation($"Step 1: Syncing {dbStats.NewRecordsCount} NEW records");
                            
                            await _syncProcessor.PerformIncrementalSync(
                                dbContext, 
                                esStats.MaxUpdatedAt, 
                                dbStats.MaxUpdatedAt,
                                esStats.MaxId,
                                dbStats.NewRecordsCount, 
                                syncLock, 
                                stoppingToken, 
                                syncStats, 
                                maxRetries,
                                PAGE_SIZE,
                                true, // syncNewRecordsOnly = true
                                esStats.MaxCreateAt,
                                dbStats.MaxCreateAt);
                        }
                        
                        // Kiểm tra lại xem vẫn còn bản ghi cần cập nhật không
                        var recordsToUpdate = syncStrategy.RecordsToSync - dbStats.NewRecordsCount;
                        if (recordsToUpdate > 0 && esStats.MaxUpdatedAt < dbStats.MaxUpdatedAt)
                        {
                            _logger.LogInformation($"Step 2: Syncing {recordsToUpdate} UPDATED records");
                            _logger.LogInformation($"📝 Updated records definition: UpdatedAt > ES_MaxUpdatedAt");
                            
                            await _syncProcessor.PerformIncrementalSync(
                                dbContext, 
                                esStats.MaxUpdatedAt, 
                                dbStats.MaxUpdatedAt,
                                esStats.MaxId,
                                recordsToUpdate, 
                                syncLock, 
                                stoppingToken, 
                                syncStats, 
                                maxRetries,
                                PAGE_SIZE,
                                false, // syncNewRecordsOnly = false
                                null,
                                null);
                        }
                    }
                    catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested) {
                        _logger.LogInformation("Incremental sync canceled");
                        return true;
                    }
                }
                else
                {
                    _logger.LogInformation($"✅ No records to sync. Reason: {syncStrategy.Reason}");
                }

                syncStats.TotalCount = syncStats.TotalProcessed;
                return true;
            });

            globalStopwatch.Stop();
            
            var successRate = syncStats.TotalCount > 0 ? (syncStats.TotalProcessed * 100.0 / syncStats.TotalCount) : 0;
            var totalTimeSeconds = globalStopwatch.Elapsed.TotalSeconds;
            var avgRecordsPerSecond = totalTimeSeconds > 0 ? syncStats.TotalProcessed / totalTimeSeconds : 0;
            
            _logger.LogInformation(
                $"✅ Completed sync of {syncStats.TotalProcessed:N0}/{syncStats.TotalCount:N0} records " +
                $"({successRate:F2}%). ⏱ {totalTimeSeconds:F2}s ({avgRecordsPerSecond:F0} rec/sec). " +
                $"Batches: {syncStats.BatchSuccessCount} OK, {syncStats.BatchFailureCount} failed, {syncStats.RetryCount} retries");
        }
        catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
        {
            _logger.LogInformation("Sync job canceled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in FavPlace sync");
            throw;
        }
    }

    private async Task ForceReleaseOtherLock()
    {
        // Kiểm tra xem lock có tồn tại không và có phải là stale lock không
        var lockInfo = await _distributedLock.GetLockInfoAsync(LOCK_KEY);
        if (lockInfo != null)
        {
            var lockAge = DateTime.UtcNow - lockInfo.CreatedAt;
            if (lockAge.TotalMinutes > LOCK_STALE_THRESHOLD_MINUTES)
            {
                _logger.LogWarning($"Detected stale lock from {lockInfo.OwnerId}, age: {lockAge.TotalMinutes:F1} minutes. Forcing release.");
                try
                {
                    await _distributedLock.ForceReleaseLockAsync(LOCK_KEY);
                    _logger.LogInformation("Successfully released stale lock");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to release stale lock");
                }
            }
            else
            {
                _logger.LogInformation($"Found active lock from {lockInfo.OwnerId}, age: {lockAge.TotalMinutes:F1} minutes");
            }
        }
    }
} 