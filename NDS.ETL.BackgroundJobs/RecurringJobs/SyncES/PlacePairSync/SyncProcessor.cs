using Microsoft.EntityFrameworkCore;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch.PlacePairSync;
using NDS.ETL.Entities.Context;
using NDS.ETL.Infrastructure.Common;
using System.Collections.Concurrent;
using System.Diagnostics;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch.Dto;

namespace NDS.ETL.BackgroundJobs.RecurringJobs.SyncES.PlacePairSync;

/// <summary>
/// Xử lý quá trình đồng bộ dữ liệu
/// </summary>
public class SyncProcessor
{
    private readonly ILogger _logger;
    private readonly ElasticsearchDataService _esService;
    private readonly DbDataService _dbService;
    private readonly string _indexName;
    private readonly IScopedResolver<NpgPlaceContext> _dbContextResolver;
    private readonly RecurringJobBaseElastic _recurringJobBase;
    private const int MAX_CONCURRENT_TASKS = 2;
    private const int BATCH_SIZE = 10_000;
    private const int COMMAND_TIMEOUT = 600;

    public SyncProcessor(
        ILogger logger,
        ElasticsearchDataService esService,
        DbDataService dbService,
        string indexName,
        IScopedResolver<NpgPlaceContext> dbContextResolver,
        RecurringJobBaseElastic recurringJobBase)
    {
        _logger = logger;
        _esService = esService;
        _dbService = dbService;
        _indexName = indexName;
        _dbContextResolver = dbContextResolver;
        _recurringJobBase = recurringJobBase;
    }

    /// <summary>
    /// Thực hiện đồng bộ toàn bộ dữ liệu
    /// </summary>
    public async Task PerformFullSync(
        NpgPlaceContext dbContext,
        int totalRecords,
        object syncLock,
        CancellationToken stoppingToken,
        SyncStats syncStats,
        int maxRetries,
        int pageSize)
    {
        DateTime sixMonthsAgo = DateTime.UtcNow.AddMonths(-6);
        
        // Tìm các batch có dữ liệu
        var batchIdsWithData = await _dbService.GetBatchesWithData(dbContext, sixMonthsAgo, pageSize, stoppingToken);
        
        if (!batchIdsWithData.Any())
        {
            _logger.LogInformation("No data to process within the last 6 months");
            return;
        }
        
        _logger.LogInformation($"Found {batchIdsWithData.Count} batches with data for full sync");
        
        // Tạo SemaphoreSlim để kiểm soát số lượng task đồng thời
        using var semaphore = new SemaphoreSlim(MAX_CONCURRENT_TASKS);
        
        // Thread-safe collections để theo dõi tiến trình
        var processingTasks = new List<Task>(); // Danh sách lưu trữ các task đang xử lý
        var failedBatches = new ConcurrentBag<(long startId, long endId)>(); // Lưu trữ các batch thất bại

        // Xử lý từng batch dữ liệu
        foreach (var batchId in batchIdsWithData)
        {
            // Tính toán vị trí bắt đầu của batch hiện tại
            var startId = batchId * pageSize;
            var endId = startId + pageSize - 1;

            // Chờ semaphore có slot trống trước khi bắt đầu task mới
            await semaphore.WaitAsync(stoppingToken);

            // Tạo và bắt đầu task mới để xử lý batch dữ liệu
            processingTasks.Add(Task.Run(async () =>
            {
                int batchRetries = 0;
                bool success = false;

                while (!success && batchRetries < maxRetries)
                {
                    try
                    {
                        // Đo thời gian xử lý một batch
                        var batchStopwatch = Stopwatch.StartNew();

                        // Tạo DbContext mới cho mỗi task để tránh vấn đề về concurrency
                        await _dbContextResolver.ResolveAsync(async (batchDbContext) =>
                        {
                            // Thiết lập timeout cho DbContext này
                            batchDbContext.Database.SetCommandTimeout(COMMAND_TIMEOUT);

                            // Truy vấn dữ liệu từ database cho batch hiện tại
                            var data = await _dbService.FetchBatchByIdRangeAsync(
                                batchDbContext, 
                                startId, 
                                endId, 
                                sixMonthsAgo,
                                stoppingToken);

                            if (data.Any())
                            {
                                // Chuyển đổi dữ liệu từ database sang định dạng ElasticSearch
                                var esDocuments = _dbService.MapToEsDocuments(data);

                                // IndexMany là thao tác ghi vào ElasticSearch
                                await _recurringJobBase.IndexMany(esDocuments, stoppingToken, _indexName, BATCH_SIZE);

                                batchStopwatch.Stop();

                                // Sử dụng lock để đảm bảo chỉ một thread có thể cập nhật bộ đếm và ghi log tại một thời điểm
                                lock (syncLock)
                                {
                                    syncStats.TotalProcessed += data.Count;
                                    syncStats.BatchSuccessCount++;
                                    
                                    _logger.LogInformation(
                                        $"Indexed batch {data.First().Id}-{data.Last().Id} ({data.Count} records) in {batchStopwatch.Elapsed.TotalSeconds:F2}s. " +
                                        $"Progress: {syncStats.TotalProcessed}/{totalRecords} records");
                                }
                                
                                success = true;
                            }
                            else
                            {
                                _logger.LogDebug($"No records found in batch ID range {startId}-{endId}");
                                success = true;
                            }

                            return true;
                        });
                    }
                    catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
                    {
                        _logger.LogDebug("Task canceled");
                        throw;
                    }
                    catch (Exception ex)
                    {
                        if (ex is OperationCanceledException && stoppingToken.IsCancellationRequested)
                        {
                            throw;
                        }

                        batchRetries++;

                        if (batchRetries >= maxRetries)
                        {
                            lock (syncLock)
                            {
                                syncStats.BatchFailureCount++;
                                failedBatches.Add((startId, endId));
                                _logger.LogError(ex, $"Failed to process batch ID range {startId}-{endId} after {maxRetries} attempts");
                            }
                        }
                        else
                        {
                            lock (syncLock)
                            {
                                syncStats.RetryCount++;
                                _logger.LogWarning($"Retrying batch ID={startId}-{endId} (Attempt {batchRetries}/{maxRetries})");
                            }
                        }
                    }
                    finally
                    {
                        // Luôn đảm bảo semaphore được giải phóng khi task hoàn thành, ngay cả khi có lỗi
                        semaphore.Release();
                    }
                }
            }, stoppingToken));
        }

        // Chờ tất cả các task hoàn thành trước khi kết thúc
        await Task.WhenAll(processingTasks);
        
        if (failedBatches.Any())
        {
            _logger.LogWarning($"Failed to process {failedBatches.Count} batches out of {batchIdsWithData.Count} total batches");
        }
    }
    
    /// <summary>
    /// Thực hiện đồng bộ dữ liệu gia tăng
    /// </summary>
    public async Task PerformIncrementalSync(
        NpgPlaceContext dbContext,
        DateTime esMaxUpdateAt,
        DateTime dbMaxUpdateAt,
        int recordsToSync,
        object syncLock,
        CancellationToken stoppingToken,
        SyncStats syncStats,
        int maxRetries,
        int pageSize,
        bool syncNewRecordsOnly = false,
        DateTime esMaxCreateAt = default,
        DateTime dbMaxCreateAt = default)
    {
        // Ghi log đầu việc đồng bộ phân biệt giữa dữ liệu mới và dữ liệu cập nhật
        if (syncNewRecordsOnly)
        {
            _logger.LogInformation($"Beginning incremental sync for NEW records (updateAt is null). ES max CreateAt: {esMaxCreateAt:yyyy-MM-dd HH:mm:ss.fff}, DB max CreateAt: {dbMaxCreateAt:yyyy-MM-dd HH:mm:ss.fff}");
        }
        else
        {
            _logger.LogInformation($"Beginning incremental sync for UPDATED records (updateAt is not null). ES max UpdateAt: {esMaxUpdateAt:yyyy-MM-dd HH:mm:ss.fff}, DB max UpdateAt: {dbMaxUpdateAt:yyyy-MM-dd HH:mm:ss.fff}");
        }
        
        // Tìm các batch có dữ liệu mới hoặc cập nhật
        var batchIdsWithData = await _dbContextResolver.ResolveAsync(async (ctx) =>
        {
            // Thiết lập timeout cho DbContext để tránh timeout trong các truy vấn lớn
            ctx.Database.SetCommandTimeout(COMMAND_TIMEOUT);
            
            var query = ctx.BookingPlacePairs.AsQueryable();
            
            if (syncNewRecordsOnly)
            {
                // Tìm các batch có dữ liệu mới (updateAt = null HOẶC updateAt = createdAt)
                // Chỉ kiểm tra các bản ghi có CreatedAt > esMaxCreateAt
                if (esMaxCreateAt != null && dbMaxCreateAt != null && esMaxCreateAt < dbMaxCreateAt)
                {
                    query = query.Where(x => x.CreatedAt > esMaxCreateAt && 
                                           ((x.UpdateAt == null && x.CreatedAt != null) || 
                                            (x.UpdateAt != null && x.CreatedAt != null && x.UpdateAt == x.CreatedAt)));
                }
                else
                {
                    // Fallback: tất cả new records
                    query = query.Where(x => (x.UpdateAt == null && x.CreatedAt != null) || 
                                           (x.UpdateAt != null && x.CreatedAt != null && x.UpdateAt == x.CreatedAt));
                }
                
                // Kiểm tra nếu có bản ghi thỏa mãn điều kiện
                if (!await query.AnyAsync(stoppingToken))
                {
                    _logger.LogInformation("No NEW records to sync (UpdateAt=null or UpdateAt=CreatedAt)");
                    return new List<long>();
                }
                
                var minId = await query
                    .MinAsync(m => (int?)m.Id, cancellationToken: stoppingToken) ?? 0;
                    
                var maxId = await query
                    .MaxAsync(m => (int?)m.Id, cancellationToken: stoppingToken) ?? 0;
                    
                if (minId == 0 && maxId == 0)
                    return new List<long>();
                
                _logger.LogInformation($"Incremental sync for NEW records: ID range from {minId} to {maxId}, " +
                    $"CreatedAt > {esMaxCreateAt}, conditions: UpdateAt=null OR UpdateAt=CreatedAt");
            }
            else
            {
                // Tìm các batch có dữ liệu cập nhật (updateAt > createdAt và updateAt > esMaxUpdateAt)
                query = query.Where(x => x.UpdateAt != null && x.CreatedAt != null && 
                                       x.UpdateAt > x.CreatedAt && x.UpdateAt > esMaxUpdateAt);
                
                // Kiểm tra nếu có bản ghi thỏa mãn điều kiện
                if (!await query.AnyAsync(stoppingToken))
                {
                    _logger.LogInformation("No UPDATED records to sync (UpdateAt > CreatedAt and UpdateAt > esMaxUpdateAt)");
                    return new List<long>();
                }
                
                var minId = await query
                    .MinAsync(m => (int?)m.Id, cancellationToken: stoppingToken) ?? 0;
                    
                var maxId = await query
                    .MaxAsync(m => (int?)m.Id, cancellationToken: stoppingToken) ?? 0;
                    
                if (minId == 0 && maxId == 0)
                    return new List<long>();
                
                _logger.LogInformation($"Incremental sync for UPDATED records: ID range from {minId} to {maxId}, " +
                    $"UpdateAt > CreatedAt and UpdateAt > {esMaxUpdateAt}");
            }
            
            // Tính toán số lượng batch cần kiểm tra
            long startBatch, endBatch;
            try
            {
                startBatch = await query.Select(x => x.Id / pageSize).MinAsync(stoppingToken);
                endBatch = await query.Select(x => x.Id / pageSize).MaxAsync(stoppingToken);
            }
            catch (InvalidOperationException)
            {
                _logger.LogWarning("Error calculating batch range, defaulting to 0");
                startBatch = endBatch = 0;
            }
            
            long totalBatchesToCheck = endBatch - startBatch + 1;
            
            _logger.LogInformation($"Checking {totalBatchesToCheck} potential batches from {startBatch} to {endBatch} for {(syncNewRecordsOnly ? "NEW" : "UPDATED")} records");
            
            // Lấy các batch có dữ liệu
            var result = await query
                .Select(x => x.Id / pageSize) // Sử dụng phép chia nguyên
                .Distinct()
                .OrderBy(x => x)
                .ToListAsync(stoppingToken);
            
            _logger.LogInformation($"Found {result.Count} batches with {(syncNewRecordsOnly ? "NEW" : "UPDATED")} data for incremental sync");
            
            return result;
        });
        
        if (!batchIdsWithData.Any())
        {
            _logger.LogInformation($"No {(syncNewRecordsOnly ? "NEW" : "UPDATED")} data to process for incremental sync");
            return;
        }
        
        // Tạo SemaphoreSlim để kiểm soát số lượng task đồng thời
        using var semaphore = new SemaphoreSlim(MAX_CONCURRENT_TASKS);
        
        // Thread-safe collections để theo dõi tiến trình
        var processingTasks = new List<Task>(); // Danh sách lưu trữ các task đang xử lý
        var failedBatches = new ConcurrentBag<(long startId, long endId)>(); // Lưu trữ các batch thất bại
        
        int totalRecordsToProcess = recordsToSync;

        // Xử lý từng batch dữ liệu
        foreach (var batchId in batchIdsWithData)
        {
            // Tính toán vị trí bắt đầu của batch hiện tại
            var startId = batchId * pageSize;
            var endId = startId + pageSize - 1;

            // Chờ semaphore có slot trống trước khi bắt đầu task mới
            await semaphore.WaitAsync(stoppingToken);

            // Tạo và bắt đầu task mới để xử lý batch dữ liệu
            processingTasks.Add(Task.Run(async () =>
            {
                int batchRetries = 0;
                bool success = false;

                while (!success && batchRetries < maxRetries)
                {
                    try
                    {
                        // Đo thời gian xử lý một batch
                        var batchStopwatch = Stopwatch.StartNew();

                        // Tạo DbContext mới cho mỗi task để tránh vấn đề về concurrency
                        await _dbContextResolver.ResolveAsync(async (batchDbContext) =>
                        {
                            // Thiết lập timeout cho DbContext này
                            batchDbContext.Database.SetCommandTimeout(COMMAND_TIMEOUT);

                            // Lấy dữ liệu theo điều kiện đồng bộ (mới hoặc cập nhật)
                            List<BookingPlacePairDto> data;
                            if (syncNewRecordsOnly)
                            {
                                // Lấy dữ liệu mới (createdAt có giá trị và updateAt = null)
                                data = await _dbService.FetchNewBatchDataAsync(
                                    batchDbContext,
                                    startId,
                                    endId,
                                    stoppingToken);
                                    
                                // Nếu có giá trị esMaxCreateAt, lọc thêm theo CreatedAt
                                if (esMaxCreateAt != default)
                                {
                                    data = data.Where(x => x.CreatedAt > esMaxCreateAt).ToList();
                                }
                            }
                            else
                            {
                                // Lấy dữ liệu cập nhật (updateAt không null và lớn hơn esMaxUpdateAt)
                                data = await _dbService.FetchUpdatedRecordsAsync(
                                    batchDbContext,
                                    startId,
                                    endId,
                                    esMaxUpdateAt,
                                    stoppingToken);
                            }

                            if (data.Any())
                            {
                                // Chuyển đổi dữ liệu từ database sang định dạng ElasticSearch
                                var esDocuments = _dbService.MapToEsDocuments(data);

                                // IndexMany là thao tác ghi vào ElasticSearch
                                await _recurringJobBase.IndexMany(esDocuments, stoppingToken, _indexName, BATCH_SIZE);

                                batchStopwatch.Stop();

                                // Sử dụng lock để đảm bảo chỉ một thread có thể cập nhật bộ đếm và ghi log tại một thời điểm
                                lock (syncLock)
                                {
                                    syncStats.TotalProcessed += data.Count;
                                    syncStats.BatchSuccessCount++;
                                    
                                    _logger.LogInformation(
                                        $"{(syncNewRecordsOnly ? "NEW" : "UPDATED")} batch {data.First().Id}-{data.Last().Id} ({data.Count} records) in {batchStopwatch.Elapsed.TotalSeconds:F2}s. " +
                                        $"Progress: {syncStats.TotalProcessed}/{totalRecordsToProcess} records");
                                }
                                
                                success = true;
                            }
                            else
                            {
                                _logger.LogDebug($"No {(syncNewRecordsOnly ? "NEW" : "UPDATED")} records found in batch ID range {startId}-{endId}");
                                success = true;
                            }

                            return true;
                        });
                    }
                    catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
                    {
                        _logger.LogDebug("Task canceled");
                        throw;
                    }
                    catch (Exception ex)
                    {
                        if (ex is OperationCanceledException && stoppingToken.IsCancellationRequested)
                        {
                            throw;
                        }

                        batchRetries++;

                        if (batchRetries >= maxRetries)
                        {
                            lock (syncLock)
                            {
                                syncStats.BatchFailureCount++;
                                failedBatches.Add((startId, endId));
                                _logger.LogError(ex, $"Failed to process {(syncNewRecordsOnly ? "NEW" : "UPDATED")} batch ID range {startId}-{endId} after {maxRetries} attempts");
                            }
                        }
                        else
                        {
                            lock (syncLock)
                            {
                                syncStats.RetryCount++;
                                _logger.LogWarning($"Retrying {(syncNewRecordsOnly ? "NEW" : "UPDATED")} batch ID={startId}-{endId} (Attempt {batchRetries}/{maxRetries})");
                            }
                        }
                    }
                    finally
                    {
                        // Luôn đảm bảo semaphore được giải phóng khi task hoàn thành, ngay cả khi có lỗi
                        semaphore.Release();
                    }
                }
            }, stoppingToken));
        }

        // Chờ tất cả các task hoàn thành trước khi kết thúc
        await Task.WhenAll(processingTasks);
        
        if (failedBatches.Any())
        {
            _logger.LogWarning($"Failed to process {failedBatches.Count} {(syncNewRecordsOnly ? "NEW" : "UPDATED")} batches out of {batchIdsWithData.Count} total batches");
        }
    }
} 