using Elastic.Clients.Elasticsearch.Mapping;
using KnowledgeBase.Core.Extensions;
using NDS.ETL.Infrastructure.Extensions;
using NDS.ETL.Infrastructure.Utils;
using System.Linq.Expressions;

namespace NDS.ETL.BackgroundJobs.Model.ElasticSearch;

public class SubPlacesEs : IElasticSearchModel
{
    public long Id { get; set; }

    public long? ParentId { get; set; }
    public string? PlaceDetailId { get; set; }
    public string? PhoneNumber { get; set; }
    public string PlaceId { get; set; } = null!;
    public Location Location { get; set; }
    public string? Name { get; set; }
    public string? Keywords { get; set; }
    public string FullAddress { get; set; } = null!;
    public string? Address { get => FullAddress.ConvertToSortAddress(); set => FullAddress = value; }
    public string? StreetNumber { get; set; }
    public string? Route { get; set; }
    public string? Ward { get; set; }
    public string? District { get; set; }
    public string? City { get; set; }
    public int Type { get; set; }
    public bool Active { get; set; }
    public List<string>? Tags { get; set; }
    public string? TagInput { get; set; }
    public bool Popular { get; set; }
    public bool Processed { get; set; }
    public int BookingCount { get; set; }
    public bool Removed { get; set; }
    public bool Hidden { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string? CreatorId { get; set; }
    public string? LastUpdateUser { get; set; }
    public string? DataFrom { get; set; }
    public string? ParentPlaceId { get; set; }
    public int? DistrictId { get; set; }
    public int? ProvinceId { get; set; }
    public int? WardId { get; set; }
    public bool Pending { get; set; }
    public int QuoteCount { get; set; }
    public string MasterAddress { get; set; }
    public List<string> AddressPermuted { get; set; }
    public double Score { get; set; }
    public double Distance { get; set; }

    public void CalculateMasterAddressAndPermuted()
    {

        MasterAddress = FullAddress;
        if (!string.IsNullOrWhiteSpace(Name) && !MasterAddress.ToUnsign().Contains(Name.ToUnsign()))
        {
            MasterAddress = Name + ", " + MasterAddress;
        }

        AddressPermuted = AddressPermutator.GenerateOptimizedPermutations(MasterAddress, 15);

        var insertItems = VietnameseAddressPermutator.GenerateOptimizedPermutations(MasterAddress, 3);
        AddressPermuted.InsertRange(0, insertItems);
    }

    public static Expression<Func<Entities.PostgresPlace.SubPlace, SubPlacesEs>> Selector => t => new SubPlacesEs
    {
        Id = t.Id,
        PlaceDetailId = t.PlaceDetailId,
        ParentPlaceId = t.ParentPlaceId,
        ParentId = t.ParentId,
        Location = new Location
        {
            Lat = t.Location.Y,
            Lon = t.Location.X
        },
        PhoneNumber = t.PhoneNumber,
        Name = t.Name,
        PlaceId = t.PlaceId,
        Address = t.Address,
        FullAddress = t.FullAddress,
        StreetNumber = t.StreetNumber,
        Active = t.Active,
        Tags = t.Tags,
        Keywords = t.Keywords,
        Route = t.Route,
        Ward = t.Ward,
        WardId = t.WardId,
        District = t.District,
        DistrictId = t.DistrictId,
        City = t.City,
        Type = t.Type,
        DataFrom = t.DataFrom,
        CreatedAt = t.CreatedAt,
        UpdatedAt = t.UpdatedAt,
        Pending = t.Pending,
        ProvinceId = t.ProvinceId,
        Processed = t.Processed,
        QuoteCount = t.QuoteCount,
        BookingCount = t.BookingCount,
        Popular = t.Popular
    };


    public static void MappingProperties(PropertiesDescriptor<PlacesEs> d)
    {
        d.LongNumber(ToCamelCase(nameof(Id)))
            .Keyword(ToCamelCase(nameof(Active)))
            .Text(ToCamelCase(nameof(Address)), t => t.Analyzer("icu"))
            .Text(ToCamelCase(nameof(Name)), t => t.Analyzer("icu"))
            .GeoPoint(ToCamelCase(nameof(Location)))
            .Keyword(ToCamelCase(nameof(PlaceId)))
            .Keyword(ToCamelCase(nameof(ParentPlaceId)))
            .Keyword(ToCamelCase(nameof(PlaceDetailId)))
            .Keyword(ToCamelCase(nameof(PhoneNumber)))
            .Boolean(ToCamelCase(nameof(Popular)))
            .Boolean(ToCamelCase(nameof(Processed)))
            .IntegerNumber(ToCamelCase(nameof(ProvinceId)))
            .IntegerNumber(ToCamelCase(nameof(DistrictId)))
            .IntegerNumber(ToCamelCase(nameof(WardId)))
            .LongNumber(ToCamelCase(nameof(BookingCount)))
            .Keyword(ToCamelCase(nameof(Removed)))
            .Keyword(ToCamelCase(nameof(Hidden)))
            .Keyword(ToCamelCase(nameof(CreatorId)))
            .Keyword(ToCamelCase(nameof(LastUpdateUser)))
            .Keyword(ToCamelCase(nameof(DataFrom)))
            .Text(ToCamelCase(nameof(StreetNumber)), t => t.Analyzer("icu"))
            .Text(ToCamelCase(nameof(Route)), t => t.Analyzer("icu"))
            .Text(ToCamelCase(nameof(Ward)), t => t.Analyzer("icu"))
            .Text(ToCamelCase(nameof(District)), t => t.Analyzer("icu"))
            .Text(ToCamelCase(nameof(City)), t => t.Analyzer("icu"))
            .Keyword(ToCamelCase(nameof(Type)))
            .Keyword(ToCamelCase(nameof(Tags)))
            .Text(ToCamelCase(nameof(TagInput)), t => t.Analyzer("icu"))
            .Boolean(ToCamelCase(nameof(Pending)))
            .LongNumber(ToCamelCase(nameof(QuoteCount)))
            .Date(ToCamelCase(nameof(CreatedAt)))
            .Date(ToCamelCase(nameof(UpdatedAt)));

        d.Text(ToCamelCase(nameof(AddressPermuted)), t => t
                .Analyzer("address_search")
                .SearchAnalyzer("address_search")
                .IndexOptions(IndexOptions.Positions)
                .Norms(false)
                .TermVector(TermVectorOption.WithPositionsOffsets) // có thể dùng để highlight
                .Fields(ff => ff
                    .Text("suggest", st => st
                        .Analyzer("address_suggest")
                        .SearchAnalyzer("address_search")
                        .IndexOptions(IndexOptions.Positions))
                    .Completion("completion", c => c
                        .Analyzer("address_suggest")
                        .PreservePositionIncrements(true)
                        .PreserveSeparators(true)
                        .MaxInputLength(100))))
            .Keyword(p => p.MasterAddress, k => k
                .Index(true)
                .IgnoreAbove(256)
                .Store(true)
                .DocValues(true)
                .Norms(false)
                .EagerGlobalOrdinals(true)); // chuẩn hóa để match chính xác

        d.Text(ToCamelCase(nameof(Keywords)), t => t
                .Analyzer("address_search")
                .SearchAnalyzer("address_search")
                .IndexOptions(IndexOptions.Positions)
                .Norms(false)
                .Fields(ff => ff
                    .Text("keyword", st => st
                        .Analyzer("icu"))
                ))
            ;
        
        d.Keyword(ToCamelCase(nameof(Score)), t => t.Index(false)); //Bỏ qua không index Score
        d.Keyword(ToCamelCase(nameof(Distance)), t => t.Index(false)); //Bỏ qua không index Distance
    }

    public static string ToCamelCase(string input)
    {
        if (string.IsNullOrEmpty(input) || char.IsLower(input[0]))
            return input;

        return char.ToLower(input[0]) + input.Substring(1);
    }
}