using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Core.Search;
using Elastic.Clients.Elasticsearch.QueryDsl;
using NDS.ETL.BackgroundJobs.Model.ElasticSearch;

namespace NDS.ETL.BackgroundJobs.Extensions
{
    /// <summary>
    /// Extension methods for the Elasticsearch client to simplify common query patterns
    /// </summary>
    public static class ESPlacePairsExtensions
    {
        /// <summary>
        /// Tạo các điều kiện tìm kiếm cho truy vấn cặp địa điểm
        /// </summary>
        /// <param name="keyword">Từ khóa tìm kiếm gốc</param>
        /// <param name="normalizedKeyword">Từ khóa đã được chuẩn hóa</param>
        /// <param name="query">Query descriptor</param>
        /// <param name="forceSearch">Search hỗ trợ sai chính tả</param>
        /// <returns><PERSON><PERSON> sách các điều kiện tìm kiếm</returns>
        public static QueryDescriptor<PlacePairEs> GetSearchConditions(string keyword, string normalizedKeyword, QueryDescriptor<PlacePairEs> query, bool forceSearch = false)
        {
            string[] terms = keyword.Trim().Split(' ', StringSplitOptions.RemoveEmptyEntries);
            bool isShortQuery = terms.Length <= 2;

            // Tạo các function truy vấn
            var exactMatchQuery = CreateExactMatchQuery(keyword);

            // Chỉ thêm MultiMatch và PhrasePrefix cho từ khóa dài để tránh kết quả sai lệch
            if (!isShortQuery || keyword.Length > 5 || forceSearch)
            {
                // Điều kiện 2: Tìm kiếm đa trường với từ khóa gốc
                var multiMatchQuery = CreateMultiMatchQuery(keyword, 5.0f);

                // Điều kiện 3: Tìm kiếm với tiền tố cụm từ
                var phrasePrefixQuery = CreatePhrasePrefixQuery(normalizedKeyword);

                // Sử dụng lambda để chuyển đổi hàm thành Action
                return query.Bool(b => b
                    .Should(
                        q => exactMatchQuery(q),
                        q => multiMatchQuery(q),
                        q => phrasePrefixQuery(q)
                    )
                    .MinimumShouldMatch(MinimumShouldMatch.Fixed(1)) // Yêu cầu khớp ít nhất 1 điều kiện
                );
            }
            else
            {
                // Với từ khóa ngắn, chỉ dùng khớp chính xác để tăng độ chính xác
                return query.Bool(b => b
                    .Should(q => exactMatchQuery(q))
                    .MinimumShouldMatch(MinimumShouldMatch.Fixed(1))
                );
            }
        }

        /// <summary>
        /// Tạo truy vấn khớp chính xác cho từ khóa - tối ưu cho tìm kiếm lịch sử đi taxi
        /// </summary>
        private static Func<QueryDescriptor<PlacePairEs>, QueryDescriptor<PlacePairEs>> CreateExactMatchQuery(string keyword)
        {
            var hasVietnameseDiacritics = keyword.HasVietnameseDiacritics();
            string[] terms = keyword.Trim().Split(' ', StringSplitOptions.RemoveEmptyEntries);
            bool hasTwoOrMoreTerms = terms.Length >= 2 && terms.Any(x => x.Length > 2) && hasVietnameseDiacritics;

            // Nếu từ khóa quá ngắn, sử dụng các matching linh hoạt hơn
            if (terms.Length <= 2)
            {
                var shouldQueries = new List<Func<QueryDescriptor<PlacePairEs>, QueryDescriptor<PlacePairEs>>>();

                // Thêm các truy vấn cho từ khóa đầy đủ
                shouldQueries.Add(s => s.Prefix(p => p
                    .Field(f => f.AddressPermuted)
                    .Value(keyword)
                    .Boost(50)
                ));

                shouldQueries.Add(s => s.Wildcard(w => w
                    .Field(f => f.AddressPermuted)
                    .Value($"*{keyword}*")
                    .Boost(40)
                ));

                // Thêm truy vấn cho từng từ riêng lẻ trong từ khóa
                if (terms.Length > 1)
                {
                    // Thay thế SpanNear bằng MatchPhrase với Slop
                    shouldQueries.Add(s => s.MatchPhrase(mp => mp
                        .Field(f => f.AddressPermuted)
                        .Query(keyword)
                        .Slop(5) // Cho phép các từ cách nhau tối đa 5 vị trí
                        .Boost(60) // Ưu tiên cao nhất cho các từ xuất hiện gần nhau
                    ));

                    // Thêm truy vấn cho từng từ riêng lẻ
                    foreach (var term in terms)
                    {
                        if (term.Length >= 2) // Chỉ tìm các từ có ít nhất 2 ký tự
                        {
                            shouldQueries.Add(s => s.Prefix(p => p
                                .Field(f => f.AddressPermuted)
                                .Value(term)
                                .Boost(35)
                            ));

                            shouldQueries.Add(s => s.Wildcard(w => w
                                .Field(f => f.AddressPermuted)
                                .Value($"*{term}*")
                                .Boost(30)
                            ));
                        }
                    }

                    // Thêm truy vấn match cho từng từ với toán tử OR
                    shouldQueries.Add(s => s.Match(m => m
                        .Field(f => f.AddressPermuted)
                        .Query(keyword)
                        .Operator(Operator.Or) // Chỉ cần khớp một trong các từ
                        .MinimumShouldMatch("50%") // Yêu cầu ít nhất 50% số từ phải khớp
                        .Boost(25)
                    ));
                }

                // Thay thế Term bằng MatchPhrase để khớp chính xác
                shouldQueries.Add(s => s.MatchPhrase(mp => mp
                    .Field(f => f.AddressPermuted)
                    .Query(keyword)
                    .Boost(20)
                ));

                // Thêm điều kiện MatchPhrasePrefix để tăng tỷ lệ match
                shouldQueries.Add(s => s.MatchPhrasePrefix(mpp => mpp
                    .Field(f => f.AddressPermuted)
                    .Query(keyword)
                    .Boost(15)
                ));

                // Thêm điều kiện Prefix để tăng tỷ lệ match
                shouldQueries.Add(s => s.Prefix(p => p
                    .Field(f => f.AddressPermuted)
                    .Value(keyword)
                    .Boost(10)
                ));

                return q => q.Bool(b =>
                {
                    foreach (var query in shouldQueries)
                    {
                        b.Should(query(new QueryDescriptor<PlacePairEs>()));
                    }
                    b.MinimumShouldMatch(1);
                });
            }
            else
            {
                return q => q
                    .Bool(b =>
                    {
                        b.Should(
                            // Ưu tiên cao nhất cho khớp chính xác phần đầu của địa chỉ đầy đủ
                            s => s.MatchPhrasePrefix(p => p
                                .Field(f => f.FullAddress)
                                .Query(keyword)
                                .Boost(50)
                                .MaxExpansions(5) // Giảm số lượng mở rộng cho prefix
                            ),
                            // Khớp chính xác với địa chỉ đầy đủ
                            s => s.MatchPhrase(p => p
                                .Field(f => f.FullAddress)
                                .Query(keyword)
                                .Boost(40)
                                .Slop(0) // Yêu cầu khớp chính xác thứ tự từ
                            ),
                            // Tìm kiếm với tiền tố trong địa chỉ được hoán vị
                            s => s.Prefix(p => p
                                .Field(f => f.AddressPermuted)
                                .Value(keyword)
                                .Boost(30)
                            ),
                            // Tìm kiếm cụm từ trong địa chỉ hoán vị với tiền tố
                            s => s.MatchPhrasePrefix(mpp => mpp
                                .Field(f => f.AddressPermuted)
                                .Query(keyword)
                                .Boost(20)
                                .MaxExpansions(5) // Giảm số lượng mở rộng cho prefix
                            ),
                            // Tìm kiếm cụm từ trong địa chỉ hoán vị
                            s => s.MatchPhrase(m => m
                                .Field(f => f.AddressPermuted)
                                .Query(keyword)
                                .Boost(10)
                                .Slop(0) // Khớp chính xác thứ tự từ
                            ),
                            // Chỉ dùng match khi từ khóa có ít nhất 2 từ
                            s => s.Match(m => m
                                .Field(f => f.AddressPermuted)
                                .Query(keyword)
                                .Operator(Operator.And) // Tất cả các từ phải xuất hiện
                                .Boost(5)
                            ),
                            // Boost thêm cho địa điểm có số lần đặt xe nhiều
                            s => s.FunctionScore(fs =>
                            {
                                fs.Query(q => q
                                    .MatchPhrase(m => m
                                        .Field(f => f.FullAddress)
                                        .Query(keyword)
                                    )
                                )
                                .Functions(f => f
                                    .FieldValueFactor(fvf => fvf
                                        .Field(ff => ff.BookingCount)
                                        .Factor(0.5)
                                        .Modifier(FieldValueFactorModifier.Log1p)
                                        .Missing(1)
                                    )
                                )
                                .BoostMode(FunctionBoostMode.Multiply)
                                .ScoreMode(FunctionScoreMode.Sum);
                            })
                        );
                        b.MinimumShouldMatch(hasTwoOrMoreTerms ? 2 : 1); // Tăng MinimumShouldMatch khi có 2 từ trở lên
                    });
            }
        }

        /// <summary>
        /// Tạo truy vấn đa trường cho từ khóa
        /// </summary>
        private static Func<QueryDescriptor<PlacePairEs>, QueryDescriptor<PlacePairEs>> CreateMultiMatchQuery(string query, float boost = 1.0f)
        {
            string[] terms = query.Trim().Split(' ', StringSplitOptions.RemoveEmptyEntries);

            // Nếu chỉ có 1 từ ngắn, sử dụng Match thay vì Term để tránh kết quả sai lệch
            if (terms.Length == 1 && terms[0].Length <= 3)
            {
                return s => s.Match(m => m
                    .Field(f => f.AddressPermuted)
                    .Query(query)
                    .Operator(Operator.And)
                    .Boost(boost)
                );
            }

            // Nếu chỉ có 1 từ dài hơn, sử dụng Match chính xác
            if (terms.Length == 1)
            {
                return s => s.Match(m => m
                    .Field(f => f.AddressPermuted)
                    .Query(query)
                    .Operator(Operator.And)
                    .Boost(boost)
                );
            }

            // Nếu có nhiều từ, sử dụng MultiMatch với các trường phù hợp
            return s => s.MultiMatch(mm =>
            {
                mm.Query(query)
                .Fields(Fields.FromFields([
                    Field.FromString("fullAddress^5")!, // Ưu tiên cao nhất cho địa chỉ đầy đủ
                    Field.FromString("addressPermuted.suggest^2")!,
                    Field.FromString("addressPermuted^4")!, // Thay thế .keyword bằng boost cao hơn
                    Field.FromString("keywords^2")!
                ]))
                .Type(TextQueryType.BestFields) // Thay đổi từ most_fields sang best_fields
                .Operator(Operator.And)
                .MinimumShouldMatch("100%") // Phải khớp hoàn toàn
                .TieBreaker(0.3)
                .Boost(boost);
            });
        }

        /// <summary>
        /// Tạo truy vấn tiền tố cụm từ cho từ khóa
        /// </summary>
        private static Func<QueryDescriptor<PlacePairEs>, QueryDescriptor<PlacePairEs>> CreatePhrasePrefixQuery(string normalizedKeyword)
        {
            string[] terms = normalizedKeyword.Trim().Split(' ', StringSplitOptions.RemoveEmptyEntries);

            // Nếu từ khóa chỉ có 1 từ, sử dụng Prefix thay vì PhrasePrefix
            if (terms.Length == 1)
            {
                return s => s.Prefix(p => p
                    .Field(f => f.AddressPermuted)
                    .Value(normalizedKeyword)
                    .Boost(10.0f)
                );
            }

            // Với từ khóa dài, sử dụng PhrasePrefix với cài đặt tối ưu 
            return s => s.MultiMatch(mm =>
            {
                mm.Query(normalizedKeyword)
                .Fields(Fields.FromFields([
                    Field.FromString("fullAddress^5")!, // Ưu tiên cao nhất cho địa chỉ đầy đủ
                    Field.FromString("addressPermuted.suggest^2")!,
                    Field.FromString("addressPermuted^4")!, // Thay thế .keyword bằng boost cao hơn
                    Field.FromString("keywords^2")!
                ]))
                .Type(TextQueryType.PhrasePrefix)
                .MaxExpansions(2) // Tăng từ 1 lên 2 để cải thiện kết quả
                .Slop(1) // Cho phép linh hoạt hơn một chút trong thứ tự từ
                .Operator(Operator.And)
                .MinimumShouldMatch("100%")
                .TieBreaker(0.3)
                .Boost(10.0f);
            });
        }

        /// <summary>
        /// Creates an autocomplete search query for location data
        /// </summary>
        /// <param name="client">The Elasticsearch client</param>
        /// <param name="page">Page number (1-based)</param>
        /// <param name="userId">User ID</param>
        /// <param name="phoneNumber">Phone number</param>
        /// <param name="currentLocation">Current location as GeoLocation</param>
        /// <param name="keyword">Search keyword</param>
        /// <param name="pageSize">Number of results per page</param>
        /// <param name="indexName">Index name</param>
        /// <param name="h3Resolution">H3 resolution level (default: 8)</param>
        /// <param name="extendedRadius">Whether to use extended radius</param>
        /// <param name="forceSearch">Use fuzzy search after no results found with exact search</param>
        /// <returns>SearchRequest for the autocomplete query</returns>
        public static SearchRequestDescriptor<PlacePairEs> CreatePlacePairSearchQuery(this ElasticsearchClient client,
            int page,
            string? userId,
            string? phoneNumber,
            GeoLocation currentLocation,
            string keyword,
            int pageSize,
            string indexName,
            int? h3Resolution = null,
            bool extendedRadius = false,
            bool forceSearch = false
        )
        {
            // Loại bỏ khoảng trắng thừa và chuẩn hóa từ khóa
            var cleanKeyword = keyword?.Trim() ?? string.Empty;
            var normalizedKeyword = cleanKeyword;

            currentLocation.TryGetLatitudeLongitude(out var latlng); // Lấy tọa độ lat/lng từ vị trí hiện tại

            var descriptor = new SearchRequestDescriptor<PlacePairEs>()
                .Index(indexName) // Chỉ định index cần tìm kiếm
                .Size(pageSize); // Giới hạn số lượng kết quả trả về

            // Nếu từ khóa quá ngắn (1 ký tự), trả về kết quả trống
            if (cleanKeyword.Length <= 1)
            {
                descriptor.Size(0); // Không trả về kết quả nào
                return descriptor;
            }

            // Thêm query chính
            AddMainSearchQuery(descriptor, cleanKeyword, normalizedKeyword, currentLocation, userId, phoneNumber, h3Resolution, extendedRadius, forceSearch);

            // Thêm collapse, source config và script fields
            ConfigureResultOptions(descriptor, latlng);

            // Thêm sorting
            AddSorting(descriptor, currentLocation);

            return descriptor;
        }

        /// <summary>
        /// Thêm query chính cho tìm kiếm
        /// </summary>
        private static void AddMainSearchQuery(SearchRequestDescriptor<PlacePairEs> descriptor, string keyword,
            string normalizedKeyword, GeoLocation currentLocation, string? userId, string? phoneNumber,
            int? h3Resolution, bool extendedRadius, bool forceSearch = false)
        {
            descriptor.Query(q =>
            {
                var queryDescriptor = new QueryDescriptor<PlacePairEs>();
                var searchQuery = GetSearchConditions(keyword, normalizedKeyword, queryDescriptor, forceSearch);

                q.Bool(b =>
                {
                    b.Must(searchQuery);

                    void filterConfig(QueryDescriptor<PlacePairEs> fq)
                    {
                        if (!string.IsNullOrWhiteSpace(userId))
                        {
                            // Thay thế Term bằng Match cho CustomerId
                            fq.Match(m => m
                                .Field(x => x.CustomerId!.Suffix("keyword"))
                                .Query(userId)
                                .Operator(Operator.And));
                        }
                        else if (!string.IsNullOrWhiteSpace(phoneNumber))
                        {
                            // Thay thế Term bằng Match cho CustomerPhone
                            fq.Match(m => m
                                .Field(x => x.CustomerPhone!.Suffix("keyword"))
                                .Query(phoneNumber)
                                .Operator(Operator.And));
                        }
                    }

                    // Bỏ qua GeoDistance filter nếu extendedRadius = true
                    if (!extendedRadius && h3Resolution.HasValue)
                    {
                        // Tính toán bán kính từ h3Resolution
                        double radiusInKm = h3Resolution.Value.GetRadiusKmFromH3Res();

                        //// Lấy tọa độ lat/lng từ vị trí hiện tại
                        currentLocation.TryGetLatitudeLongitude(out var latlng);
                        var h3CellsInRadius = H3Helper.GetH3CellsInRadius(latlng.Lat, latlng.Lon, radiusInKm, out var res);


                        b.Filter(f => f
                            .Bool(bb => bb
                                .Must(
                                    filterConfig,
                                    mu => mu.Terms(t => t
                                        .Field($"h3.{res}.keyword")
                                        .Terms(new TermsQueryField(h3CellsInRadius.Select(h3Idx => (FieldValue)h3Idx).ToArray()))
                                    ),
                                    mu => mu.GeoDistance(g => g
                                        .Field(p => p.Location)
                                        .Distance($"{radiusInKm}km") // Bán kính 50km
                                        .Location(currentLocation)
                                    )
                                )
                            )
                        );
                    }
                    else
                    {
                        b.Filter(filterConfig);
                    }
                });
            });
        }

        /// <summary>
        /// Cấu hình các tùy chọn kết quả
        /// </summary>
        private static void ConfigureResultOptions(SearchRequestDescriptor<PlacePairEs> descriptor, LatLonGeoLocation latlng)
        {
            descriptor
                .Collapse(c => c.Field(x => x.FullAddress.Suffix("keyword")))
                //.Source(new SourceConfig(true)) // Trả về toàn bộ thông tin
                .SourceExcludes(Fields.FromFields(
                    [
                        "h3",
                        "addressPermuted",
                        "masterAddress",
                        "keywords"
                    ]
                )); // Loại bỏ các trường không cần thiết trong kết quả
                //.ScriptFields(sf => // Thêm trường tính toán
                //    sf.Add("distance", fd =>
                //    {
                //        fd.Script(s => s
                //            .Source("doc['location'].arcDistance(params.lat, params.lon)/1000") // Tính khoảng cách theo km
                //            .Params(p => p
                //                    .Add("lat", latlng.Lat) // Truyền lat vào params
                //                    .Add("lon", latlng.Lon) // Truyền lon vào params
                //            )
                //        );
                //    })
                //)
                ;
        }

        /// <summary>
        /// Thêm sắp xếp kết quả
        /// </summary>
        private static void AddSorting(SearchRequestDescriptor<PlacePairEs> descriptor, GeoLocation currentLocation)
        {
            List<Action<SortOptionsDescriptor<PlacePairEs>>> sorts = [
                x => // Sắp xếp kết quả
                {
                    x.Score(sc => sc.Order(SortOrder.Desc)); // Theo điểm số
                },
                x =>
                {
                    x.Field(f => f.BookingCount, o => o.Order(SortOrder.Desc)); // Theo số lần đặt xe (ưu tiên địa điểm đặt thường xuyên)
                },
                x =>
                {
                    x.Field(f => f.UpdateAt, o => o.Order(SortOrder.Desc)); // Theo thời gian đặt gần đây nhất
                },
                x =>
                {
                    x.GeoDistance(g => g // Theo khoảng cách
                        .Field(p => p.Location)
                        .DistanceType(GeoDistanceType.Plane) // Tính toán theo mặt phẳng
                        .Unit(DistanceUnit.Kilometers) // Đơn vị km
                        .Order(SortOrder.Asc) // Tăng dần
                        .Location(new List<GeoLocation> { currentLocation })
                    );
                }];

            descriptor.Sort(sorts.ToArray());
        }


        /// <summary>
        /// Tạo truy vấn tìm kiếm các cặp địa điểm gần đây của người dùng cho việc gợi ý hành trình
        /// </summary>
        /// <param name="client">Elasticsearch client</param>
        /// <param name="userId">ID người dùng</param>
        /// <param name="phoneNumber">Số điện thoại người dùng</param>
        /// <param name="queryDate">Thời gian tối thiểu để tìm kiếm</param>
        /// <param name="limit">Số lượng kết quả tối đa</param>
        /// <param name="page">Trang kết quả</param>
        /// <param name="indexName">Tên index Elasticsearch</param>
        /// <param name="currentLocation">Vị trí hiện tại của người dùng (latitude, longitude). Nếu cung cấp, kết quả sẽ được lọc và sắp xếp theo vị trí.</param>
        /// <param name="h3Resolution">H3 code</param>
        /// <param name="minDistanceMeters">Khoảng cách tối thiểu tính bằng mét (nếu có)</param>
        /// <returns>Mô tả truy vấn tìm kiếm cho cặp địa điểm</returns>
        public static SearchRequestDescriptor<PlacePairEs> CreateRecentRoutesQuery(
            this ElasticsearchClient client,
            string? userId,
            string? phoneNumber,
            DateTime queryDate,
            int limit,
            int page,
            string indexName,
            LatLonGeoLocation? currentLocation = null,
            int? h3Resolution = null,
            double? minDistanceMeters = null)
        {

            // Kiểm tra thông tin người dùng
            if (string.IsNullOrEmpty(userId) && string.IsNullOrEmpty(phoneNumber))
            {
                throw new ArgumentException("Either userId or phoneNumber must be provided");
            }

            // Tạo danh sách các bộ lọc chung
            var filters = new List<Action<QueryDescriptor<PlacePairEs>>>();

            // Thêm bộ lọc người dùng
            filters.Add(f =>
            {
                if (!string.IsNullOrEmpty(userId))
                {
                    f.Term(t => t.Field(x => x.CustomerId!.Suffix("keyword")).Value(userId));
                }
                else
                {
                    f.Term(t => t.Field(x => x.CustomerPhone!.Suffix("keyword")).Value(phoneNumber));
                }
            });

            // Thêm bộ lọc thời gian
            filters.Add(f => f.Range(r =>
            {
                r.DateRange(d => d.Field(x => x.LastSearchAt).Gte(queryDate));
            }));

            filters.Add(fq =>
            {
                fq.Bool(b => b
                    .MustNot(mn => mn
                        .Term(t => t.Field(x => x.PlaceId).Value("current_location"))
                    )
                );
            });
            if (currentLocation != null)
            {
                if (h3Resolution.HasValue)
                {
                    // Tính toán bán kính từ h3Resolution
                    double radiusInKm = h3Resolution.Value.GetRadiusKmFromH3Res();

                    var h3CellsInRadius = H3Helper.GetH3CellsInRadius(currentLocation.Lat, currentLocation.Lon, radiusInKm, out var res);


                    // Thêm bộ lọc H3 cells

                    filters.Add(mu => mu.Terms(t => t
                        .Field($"h3.{res}.keyword")
                        .Terms(new TermsQueryField(h3CellsInRadius.Select(h3Idx => (FieldValue)h3Idx).ToArray()))
                    ));

                    // Thêm filter GeoDistance cho khoảng cách tối đa
                    filters.Add(mu => mu.GeoDistance(g => g
                        .Field(p => p.Location)
                        .Distance($"{radiusInKm}km")
                        .Location(currentLocation)
                    ));
                }

                // Thêm filter GeoDistance cho khoảng cách tối thiểu nếu được chỉ định
                if (minDistanceMeters is > 0)
                {
                    filters.Add(mu => mu.Bool(b => b
                        .MustNot(mn => mn.GeoDistance(g => g
                            .Field(p => p.Location)
                            .Distance($"{minDistanceMeters.Value}m")
                            .Location(currentLocation)
                        ))
                    ));
                }

            }


            var from = (page - 1) * limit; // Tính vị trí bắt đầu dựa trên trang và kích thước trang

            var searchDescriptor = new SearchRequestDescriptor<PlacePairEs>()
                .Index(indexName)
                .Size(limit)
                .From(from) // Sử dụng phân trang
                .Collapse(c => c.Field(x => x.PlaceId))
                .Query(q => q
                    .Bool(b =>
                    {
                        // Áp dụng các bộ lọc chung
                        b.Filter(filters.ToArray());

                        b.Should(
                                // Ưu tiên các lộ trình đã đặt xe
                                s => s.Exists(e => e.Field(x => x.LastBookingAt)),
                                // Hoặc có thời gian tìm kiếm gần đây
                                s => s.Range(r => { r.DateRange(d => d.Field(x => x.LastSearchAt).Gte(DateTime.UtcNow.AddDays(-7))); }),
                                // Ưu tiên các lộ trình có số lần đặt xe cao
                                s => s.FunctionScore(fs => fs
                                    .Functions(f => f
                                        .FieldValueFactor(fvf => fvf
                                            .Field(x => x.BookingCount)
                                            .Factor(0.5)
                                            .Modifier(FieldValueFactorModifier.Log1p)
                                            .Missing(1)
                                        )
                                    )
                                    .BoostMode(FunctionBoostMode.Multiply)
                                    .ScoreMode(FunctionScoreMode.Sum)
                                )
                            )
                            .MinimumShouldMatch(MinimumShouldMatch.Fixed(1));
                    })
                )
                ;

            List<Action<SortOptionsDescriptor<PlacePairEs>>> sorts = [
                // Ưu tiên các lộ trình đã đặt gần đây nhất
                sort => sort.Field(f => f.LastBookingAt!, o => o
                    .Order(SortOrder.Desc)
                    .Missing("_last")
                ),
                // Sau đó là các lộ trình đã tìm kiếm gần đây
                sort => sort.Field(f => f.LastSearchAt!, o => o
                    .Order(SortOrder.Desc)
                ),
                // Ưu tiên các lộ trình có số lần đặt xe cao
                sort => sort.Field(f => f.BookingCount, o => o
                    .Order(SortOrder.Desc)
                ),
                // Ưu tiên theo điểm số từ các điều kiện should
                sort => sort.Score(sc => sc.Order(SortOrder.Desc))
            ];

            // Thêm sắp xếp theo khoảng cách nếu có vị trí hiện tại
            if (currentLocation != null)
            {
                sorts.Add(sort => sort.GeoDistance(g => g
                    .Field(p => p.Location)
                    .DistanceType(GeoDistanceType.Plane)
                    .Unit(DistanceUnit.Kilometers)
                    .Order(SortOrder.Asc) // Ưu tiên địa điểm gần hơn
                    .Location(new List<GeoLocation> { currentLocation })
                ));
            }

            searchDescriptor.Sort(sorts.ToArray());

            // Thêm H3 filter nếu có vị trí hiện tại
            if (currentLocation != null && h3Resolution.HasValue)
            {
                searchDescriptor.Query(q => q
                    .Bool(b =>
                    {
                        // Giữ nguyên bộ lọc hiện tại
                        b.Filter(filters.ToArray());

                        // Giữ lại phần should từ trước
                        b.Should(
                            s => s.Exists(e => e.Field(x => x.LastBookingAt)),
                            s => s.Range(r => { r.DateRange(d => d.Field(x => x.LastSearchAt).Gte(DateTime.UtcNow.AddDays(-7))); }),
                            s => s.FunctionScore(fs => fs
                                .Functions(f => f
                                    .FieldValueFactor(fvf => fvf
                                        .Field(x => x.BookingCount)
                                        .Factor(0.5)
                                        .Modifier(FieldValueFactorModifier.Log1p)
                                        .Missing(1)
                                    )
                                )
                                .BoostMode(FunctionBoostMode.Multiply)
                                .ScoreMode(FunctionScoreMode.Sum)
                            )
                        )
                        .MinimumShouldMatch(MinimumShouldMatch.Fixed(1));
                    })
                );
            }

            // Đảm bảo chỉ định rõ BookingPlacePairId trong source
            searchDescriptor.Source(new SourceConfig(new SourceFilter
            {
                Includes = Fields.FromFields([
                    Field.FromString("id")!,
                        Field.FromString("bookingPlacePairId")!,
                        Field.FromString("type")!,
                        Field.FromString("placeId")!,
                        Field.FromString("fullAddress")!,
                        Field.FromString("location")!,
                        Field.FromString("lastBookingAt")!,
                        Field.FromString("lastSearchAt")!,
                        Field.FromString("bookingCount")!,
                        Field.FromString("lastEstimateAmount")!,
                        Field.FromString("lastNote")!,
                        Field.FromString("customerId")!,
                        Field.FromString("customerPhone")!
                ])
            }));

            // Thêm script field để đánh dấu cặp địa điểm
            searchDescriptor.ScriptFields(sf =>
            {
                if (currentLocation != null)
                {
                    // Giữ nguyên script field distance nếu đã thêm
                    sf.Add("distance", fd => fd
                        .Script(s => s
                            .Source("doc['location'].arcDistance(params.lat, params.lon)") // Tính khoảng cách theo meters
                            .Params(p => p
                                .Add("lat", currentLocation.Lat) // Truyền lat vào params
                                .Add("lon", currentLocation.Lon) // Truyền lon vào params
                            )
                        )
                    );
                }

                // Thêm script field để làm rõ đây là điểm đi hay điểm đến
                sf.Add("placeType", fd => fd
                    .Script(s => s
                        .Source("doc['type'].value == 0 ? 'pickup' : 'arrival'")
                    )
                );

                return sf;
            });


            return searchDescriptor;
        }

        /// <summary>
        /// Tạo truy vấn để lấy các cặp địa điểm theo danh sách BookingPlacePairId
        /// </summary>
        /// <param name="client">Elasticsearch client</param>
        /// <param name="bookingPlacePairIds">Danh sách BookingPlacePairId cần tìm</param>
        /// <param name="indexName">Tên index Elasticsearch</param>
        /// <param name="limit">Số lượng kết quả tối đa</param>
        /// <returns>Mô tả truy vấn tìm kiếm cho cặp địa điểm</returns>
        public static SearchRequestDescriptor<PlacePairEs> GetByPlacePairIdsQuery(
            this ElasticsearchClient client,
            List<long> bookingPlacePairIds,
            string indexName,
            int limit = 100)
        {
            if (bookingPlacePairIds == null || !bookingPlacePairIds.Any())
            {
                throw new ArgumentException("BookingPlacePairIds list cannot be empty");
            }

            var searchDescriptor = new SearchRequestDescriptor<PlacePairEs>()
                .Index(indexName)
                .Size(limit)
                .Query(q => q
                    .Bool(b => b
                        .Filter(f => f
                            .Terms(t => t
                                .Field(ff => ff.BookingPlacePairId)
                                .Terms(new TermsQueryField(bookingPlacePairIds.Select(id => (FieldValue)id).ToArray()))
                            )
                        )
                    )
                )
                .Source(new SourceConfig(new SourceFilter
                {
                    Includes = Fields.FromFields([
                        Field.FromString("id")!,
                        Field.FromString("bookingPlacePairId")!,
                        Field.FromString("type")!,
                        Field.FromString("placeId")!,
                        Field.FromString("fullAddress")!,
                        Field.FromString("location")!,
                        Field.FromString("lastBookingAt")!,
                        Field.FromString("lastSearchAt")!,
                        Field.FromString("bookingCount")!,
                        Field.FromString("lastEstimateAmount")!,
                        Field.FromString("lastNote")!,
                        Field.FromString("customerId")!,
                        Field.FromString("customerPhone")!
                    ])
                }))
                //.ScriptFields(sf =>
                //{
                //    // Thêm script field để làm rõ đây là điểm đi hay điểm đến
                //    sf.Add("placeType", fd => fd
                //        .Script(s => s
                //            .Source("doc['type'].value == 0 ? 'pickup' : 'arrival'")
                //        )
                //    );
                //    return sf;
                //})
                ;

            return searchDescriptor;
        }

        /// <summary>
        /// Tạo đối tượng GeoLocation từ tọa độ
        /// </summary>
        private static GeoLocation CreateGeoLocation(double latitude, double longitude)
        {
            return GeoLocation.LatitudeLongitude(
                new LatLonGeoLocation { Lat = latitude, Lon = longitude });
        }

        /// <summary>
        /// Phương thức hỗ trợ để sắp xếp kết quả truy vấn thành các cặp điểm đi/đến
        /// </summary>
        /// <param name="documents">Danh sách tài liệu từ Elasticsearch</param>
        /// <returns>Từ điển chứa cặp điểm đi/đến theo BookingPlacePairId</returns>
        public static Dictionary<long, (PlacePairEs? Pickup, PlacePairEs? Arrival)> OrganizePlacePairs(this IReadOnlyCollection<PlacePairEs> documents)
        {
            // Tạo từ điển để lưu trữ cặp địa điểm theo BookingPlacePairId
            var result = new Dictionary<long, (PlacePairEs? Pickup, PlacePairEs? Arrival)>();

            // Nhóm các tài liệu theo BookingPlacePairId
            var groupedDocuments = documents.GroupBy(d => d.BookingPlacePairId);

            // Duyệt qua từng nhóm và tổ chức thành cặp điểm đi/đến
            foreach (var group in groupedDocuments)
            {
                var bookingPlacePairId = group.Key;
                PlacePairEs? pickup = null;
                PlacePairEs? arrival = null;

                // Tìm điểm đi và điểm đến trong nhóm
                foreach (var document in group)
                {
                    if (document.Type == 0) // Điểm đi
                    {
                        pickup = document;
                    }
                    else if (document.Type == 1) // Điểm đến
                    {
                        arrival = document;
                    }
                }

                // Thêm vào kết quả
                result[bookingPlacePairId] = (pickup, arrival);
            }

            return result;
        }

        /// <summary>
        /// Chuyển đổi danh sách cặp địa điểm thành danh sách đối tượng PlacePairRoute
        /// </summary>
        /// <param name="placePairs">Từ điển cặp địa điểm theo BookingPlacePairId</param>
        /// <returns>Danh sách PlacePairRoute</returns>
        public static List<PlacePairRoute> ToPlacePairRoutes(this Dictionary<long, (PlacePairEs? Pickup, PlacePairEs? Arrival)> placePairs)
        {
            var result = new List<PlacePairRoute>();

            foreach (var pair in placePairs)
            {
                var bookingPlacePairId = pair.Key;
                var (pickup, arrival) = pair.Value;

                // Chỉ thêm vào kết quả nếu có ít nhất một trong hai điểm
                if (pickup != null || arrival != null)
                {
                    var route = PlacePairRoute.FromPlacePairTuple(bookingPlacePairId, pickup, arrival);
                    result.Add(route);
                }
            }

            return result;
        }

        /// <summary>
        /// Trích xuất danh sách BookingPlacePairId từ kết quả tìm kiếm
        /// </summary>
        /// <param name="documents">Danh sách tài liệu từ Elasticsearch</param>
        /// <returns>Danh sách BookingPlacePairId</returns>
        public static List<long> ExtractBookingPlacePairIds(this IReadOnlyCollection<PlacePairEs> documents)
        {
            return documents
                .Select(d => d.BookingPlacePairId)
                .Distinct()
                .ToList();
        }
    }
}
