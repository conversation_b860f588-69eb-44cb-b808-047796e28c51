using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlaceDev;

[Keyless]
[Table("places_processing_clusters")]
[Index("PlaceId", "ContainStreet", "ContainStreetNumber", Name = "place_id&contain_street&contain_street_number")]
public partial class PlacesProcessingCluster
{
    [Column("id")]
    public long Id { get; set; }

    [Column("place_id")]
    public string PlaceId { get; set; } = null!;

    [Column("contain_street")]
    public bool? ContainStreet { get; set; }

    [Column("contain_street_number")]
    public bool? ContainStreetNumber { get; set; }

    [Column("way_distance")]
    public long? WayDistance { get; set; }

    [Column("way_id")]
    public long? WayId { get; set; }
}
