using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;
using NodaTime;
using NpgsqlTypes;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("booking_place_pairs")]
[Index("ArrivalPlaceId", Name = "IX_booking_place_pairs_arrivalPlaceId")]
[Index("BookingCount", Name = "IX_booking_place_pairs_bookingCount")]
[Index("CustomerId", Name = "IX_booking_place_pairs_customerId")]
[Index("CustomerPhone", Name = "IX_booking_place_pairs_customerPhone")]
[Index("Hash1", Name = "IX_booking_place_pairs_hash1")]
[Index("Hash2", Name = "IX_booking_place_pairs_hash2")]
[Index("PaidCount", Name = "IX_booking_place_pairs_paidCount")]
[Index("PickupPlaceId", Name = "IX_booking_place_pairs_pickupPlaceId")]
[Index("SearchCount", Name = "IX_booking_place_pairs_searchCount")]
[Index("Id", "CreatedAt", Name = "booking_place_pairs_id_idx")]
[Index("CreatedAt", Name = "created_at")]
public partial class BookingPlacePair
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("arrivalPlaceId")]
    [StringLength(1000)]
    public string? ArrivalPlaceId { get; set; }

    [Column("bookingCount")]
    public int BookingCount { get; set; }

    [Column("customerId")]
    [StringLength(1000)]
    public string? CustomerId { get; set; }

    [Column("customerPhone")]
    [StringLength(500)]
    public string? CustomerPhone { get; set; }

    [Column("distance")]
    public double Distance { get; set; }

    [Column("fromAddress")]
    [StringLength(1000)]
    public string? FromAddress { get; set; }

    [Column("fromCity")]
    [StringLength(1000)]
    public string? FromCity { get; set; }

    [Column("fromFullAddress")]
    [StringLength(10000)]
    public string? FromFullAddress { get; set; }

    [Column("hash1")]
    [StringLength(1000)]
    public string? Hash1 { get; set; }

    [Column("hash2")]
    [StringLength(1000)]
    public string? Hash2 { get; set; }

    [Column("location1")]
    public Point? Location1 { get; set; }

    [Column("location2")]
    public Point? Location2 { get; set; }

    [Column("paidCount")]
    public int PaidCount { get; set; }

    [Column("pickupPlaceId")]
    [StringLength(1000)]
    public string? PickupPlaceId { get; set; }

    [Column("searchCount")]
    public int SearchCount { get; set; }

    [Column("toAddress")]
    [StringLength(1000)]
    public string? ToAddress { get; set; }

    [Column("toCity")]
    [StringLength(1000)]
    public string? ToCity { get; set; }

    [Column("toFullAddress")]
    [StringLength(10000)]
    public string? ToFullAddress { get; set; }

    [Column("createdAt", TypeName = "timestamp(6) without time zone")]
    public DateTime CreatedAt { get; set; }

    [Column("updateAt", TypeName = "timestamp(6) without time zone")]
    public DateTime UpdateAt { get; set; }

    [Column("ts")]
    public NpgsqlTsVector? Ts { get; set; }

    [Column("ts2")]
    public NpgsqlTsVector? Ts2 { get; set; }

    [Column("lastBookingAt", TypeName = "timestamp(6) without time zone")]
    public DateTime? LastBookingAt { get; set; }

    [Column("lastSearchAt", TypeName = "timestamp(6) without time zone")]
    public DateTime? LastSearchAt { get; set; }

    [Column("estimateAmount")]
    public long? EstimateAmount { get; set; }

    [Column("lastEstimateAmount")]
    [StringLength(255)]
    public string? LastEstimateAmount { get; set; }

    [Column("lastNote")]
    [StringLength(255)]
    public string? LastNote { get; set; }
}
