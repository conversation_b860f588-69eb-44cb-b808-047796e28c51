using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace NDS.ETL.Entities.PostgresPlace;

[Table("provinces")]
[Index("Code", Name = "provinces_code_unique", IsUnique = true)]
public partial class Province
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("code", TypeName = "character varying")]
    public string Code { get; set; } = null!;

    [Column("name", TypeName = "character varying")]
    public string? Name { get; set; }

    [Column("type")]
    public int? Type { get; set; }

    [Column("boundary")]
    public string? Boundary { get; set; }

    [Column("center_location", TypeName = "character varying")]
    public string? CenterLocation { get; set; }

    [Column("processed")]
    public bool? Processed { get; set; }

    [Column("boundary_geom")]
    public string? BoundaryGeom { get; set; }

    [InverseProperty("ProvincesOldCodeNavigation")]
    public virtual ICollection<ProvincesBridge> ProvincesBridges { get; set; } = new List<ProvincesBridge>();
}
